#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 SegFormer 集成的脚本
验证 PixelAdapter 和 SegFormerHead 模块的集成是否正常工作
"""

import torch
import sys
from pathlib import Path

# 添加项目根目录到路径
ROOT = Path(__file__).resolve().parent
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

def test_individual_modules():
    """测试单独的模块"""
    print("\n=== 测试单独模块 ===")
    
    try:
        # 直接导入模块文件
        import importlib.util
        
        # 导入 PixelAdapter
        spec = importlib.util.spec_from_file_location("PixelAdapter", "models/pixel_adapter.py")
        pixel_adapter_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(pixel_adapter_module)
        PixelAdapter = pixel_adapter_module.PixelAdapter
        
        # 导入 SegFormerHead
        spec = importlib.util.spec_from_file_location("SegFormerHead", "models/SegFormerHeadhead.py")
        segformer_head_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(segformer_head_module)
        SegFormerHead = segformer_head_module.SegFormerHead
        
        print("✓ 模块导入成功")
        
        # 测试 PixelAdapter
        print("测试 PixelAdapter...")
        # 设置正确的输入通道数
        pixel_adapter = PixelAdapter(
            embed_dim=256, 
            mask_h=160, 
            mask_w=160,
            in_channels=(256, 512, 1024)  # 匹配特征图通道数
        )
        
        # 模拟多尺度特征输入
        p3 = torch.randn(2, 256, 80, 80)   # P3: 1/8
        p4 = torch.randn(2, 512, 40, 40)   # P4: 1/16  
        p5 = torch.randn(2, 1024, 20, 20)  # P5: 1/32
        
        with torch.no_grad():
            adapter_outputs = pixel_adapter([p3, p4, p5])
        
        print(f"✓ PixelAdapter 测试成功")
        print(f"  输出数量: {len(adapter_outputs)}")
        
        value, spatial_shapes, level_start_index, valid_ratios, mask_features = adapter_outputs
        print(f"  value shape: {value.shape}")
        print(f"  spatial_shapes: {spatial_shapes}")
        print(f"  level_start_index: {level_start_index}")
        print(f"  valid_ratios shape: {valid_ratios.shape}")
        print(f"  mask_features shape: {mask_features.shape}")
        
        # 测试 SegFormerHead
        print("测试 SegFormerHead...")
        segformer_head = SegFormerHead(nc=80, nq=100, dec_layers=6)
        
        with torch.no_grad():
            head_outputs = segformer_head(adapter_outputs)
        
        print(f"✓ SegFormerHead 测试成功")
        print(f"  输出类型: {type(head_outputs)}")
        
        if isinstance(head_outputs, (list, tuple)):
            print(f"  输出数量: {len(head_outputs)}")
            for i, output in enumerate(head_outputs):
                if hasattr(output, 'shape'):
                    print(f"    输出 {i}: {output.shape}")
                else:
                    print(f"    输出 {i}: {type(output)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 单独模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_construction():
    """测试模型构建"""
    print("\n=== 测试模型构建 ===")
    
    try:
        # 尝试导入并构建模型
        from models.yolo import SegmentationModel
        
        # 加载配置文件
        cfg_path = "models/segment/multi-yolov5n-mid-segformer.yaml"
        print(f"加载配置文件: {cfg_path}")
        
        # 创建模型
        model = SegmentationModel(cfg=cfg_path, ch=3, nc=80)
        print("✓ 模型构建成功")
        
        # 打印模型信息
        print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
        print(f"可训练参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
        
        return model
        
    except Exception as e:
        print(f"✗ 模型构建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主测试函数"""
    print("开始 SegFormer 集成测试...")
    
    # 测试单独模块
    modules_ok = test_individual_modules()
    
    # 如果单独模块测试通过，再测试完整模型
    model = None
    if modules_ok:
        model = test_model_construction()
    
    # 总结
    print("\n=== 测试总结 ===")
    print(f"单独模块测试: {'✓ 通过' if modules_ok else '✗ 失败'}")
    print(f"模型构建测试: {'✓ 通过' if model is not None else '✗ 失败'}")
    
    if modules_ok and model is not None:
        print("\n🎉 所有测试通过！SegFormer 集成成功！")
        return True
    elif modules_ok:
        print("\n⚠️ 单独模块测试通过，但模型构建失败")
        return False
    else:
        print("\n❌ 单独模块测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)