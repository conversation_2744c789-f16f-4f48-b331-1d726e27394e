#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双图像输入测试脚本
测试YOLOv5-SegFormer模型处理双图像输入的能力
"""

import torch
import torch.nn as nn
import sys
from pathlib import Path

# 添加项目根目录到路径
ROOT = Path(__file__).resolve().parent
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

from models.yolo import Model
from models.pixel_adapter import PixelAdapter
from models.SegFormerHead import SegFormerHead

def create_dual_image_processor():
    """创建双图像处理器"""
    class DualImageProcessor(nn.Module):
        def __init__(self):
            super().__init__()
            # 简单的图像预处理层
            self.rgb_norm = nn.BatchNorm2d(3)
            self.x_norm = nn.BatchNorm2d(3)
            
        def forward(self, rgb_image, x_image):
            # 对两个图像进行标准化
            rgb_normalized = self.rgb_norm(rgb_image)
            x_normalized = self.x_norm(x_image)
            return rgb_normalized, x_normalized
    
    return DualImageProcessor()



def test_dual_image_pipeline():
    """测试完整的双图像处理流水线"""
    print("=== 测试双图像输入处理 ===")
    
    try:
        # 1. 创建模型组件
        print("创建模型组件...")
        
        # 双图像处理器
        image_processor = create_dual_image_processor()
        
        # 视觉编码器（使用YOLOv5骨干网络）
        model_config = 'models/segment/multi-yolov5n-mid-segformer.yaml'
        model = Model(model_config)
        
        print("✓ 模型组件创建成功")
        
        # 准备输入数据
        print("\n准备测试数据...")
        batch_size = 2
        
        # RGB图像输入
        rgb_images = torch.randn(batch_size, 3, 640, 640)
        
        # X输入（另一种图像模态，比如深度图、热图等）
        x_images = torch.randn(batch_size, 3, 640, 640)
        
        print(f"✓ RGB图像输入形状: {rgb_images.shape}")
        print(f"✓ X图像输入形状: {x_images.shape}")
        
        # 3. 图像预处理测试
        print("\n执行图像预处理...")
        with torch.no_grad():
            # 双图像预处理
            rgb_processed, x_processed = image_processor(rgb_images, x_images)
            print(f"✓ RGB预处理后形状: {rgb_processed.shape}")
            print(f"✓ X预处理后形状: {x_processed.shape}")
        
        # 4. 前向传播测试
        print("\n执行前向传播...")
        
        with torch.no_grad():
            # 准备双图像输入：RGB图像 + X图像
            # 根据router.py，需要提供tuple格式: (rgb, x)
            dual_inputs = (rgb_processed, x_processed)
            
            # 双图像视觉特征提取
            visual_outputs = model(dual_inputs)
            print(f"✓ 双图像模型前向传播成功")
            
            # 分析输出结构
            if isinstance(visual_outputs, (list, tuple)):
                print(f"✓ 模型输出数量: {len(visual_outputs)}")
                for i, output in enumerate(visual_outputs):
                    if hasattr(output, 'shape'):
                        print(f"  输出 {i}: {output.shape}")
                    elif isinstance(output, dict):
                        print(f"  输出 {i}: dict with keys {list(output.keys())}")
                        for k, v in output.items():
                            if hasattr(v, 'shape'):
                                print(f"    {k}: {v.shape}")
                    else:
                        print(f"  输出 {i}: {type(output)}")
            else:
                print(f"✓ 模型输出形状: {visual_outputs.shape if hasattr(visual_outputs, 'shape') else type(visual_outputs)}")
            
        print("\n=== 双图像测试总结 ===")
        print("✓ 双图像模态输入: 成功")
        print("✓ 图像预处理: 成功")
        print("✓ 双图像模型前向传播: 成功")
        print("✓ 端到端双图像架构: 成功")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 双图像测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_segmentation_output():
    """测试分割输出格式"""
    print("\n=== 测试分割输出 ===")
    
    try:
        # 创建模型
        model_config = 'models/segment/multi-yolov5n-mid-segformer.yaml'
        model = Model(model_config)
        
        # 测试数据
        batch_size = 2
        rgb_images = torch.randn(batch_size, 3, 640, 640)
        x_images = torch.randn(batch_size, 3, 640, 640)
        
        with torch.no_grad():
            # 双图像输入
            dual_inputs = (rgb_images, x_images)
            
            # 前向传播
            outputs = model(dual_inputs)
            
            # 分析分割输出
            if isinstance(outputs, (list, tuple)) and len(outputs) > 1:
                seg_output = outputs[1]  # 通常分割输出在第二个位置
                if isinstance(seg_output, dict):
                    print("✓ 分割输出是字典格式:")
                    for key, value in seg_output.items():
                        if hasattr(value, 'shape'):
                            print(f"  {key}: {value.shape}")
                        else:
                            print(f"  {key}: {type(value)}")
                else:
                    print(f"✓ 分割输出形状: {seg_output.shape if hasattr(seg_output, 'shape') else type(seg_output)}")
            else:
                print(f"✓ 模型输出: {type(outputs)}")
            
        print("✓ 分割输出测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 分割输出测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始双图像输入可行性测试...\n")
    
    success_count = 0
    total_tests = 2
    
    # 测试1: 基础双图像流水线
    if test_dual_image_pipeline():
        success_count += 1
    
    # 测试2: 分割输出格式
    if test_segmentation_output():
        success_count += 1
    
    print(f"\n=== 最终测试结果 ===")
    print(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 所有双图像测试通过！")
        print("\n✅ 双图像输入可行性验证成功")
        print("- 支持RGB+X双图像输入")
        print("- 支持双图像特征提取")
        print("- 支持查询式分割任务")
        return True
    else:
        print("❌ 部分测试失败，需要进一步优化")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)