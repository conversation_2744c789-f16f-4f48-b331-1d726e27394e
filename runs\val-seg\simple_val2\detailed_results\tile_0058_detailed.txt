Image: tile_0058.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8716
  Bounding Box: [1651.20, 550.80, 1932.80, 751.60]
  Mask Area: 253 pixels
  Mask Ratio: 0.0090
  Mask BBox: [133, 48, 153, 62]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8706
  Bounding Box: [689.60, 217.20, 1017.60, 517.20]
  Mask Area: 465 pixels
  Mask Ratio: 0.0165
  Mask BBox: [58, 21, 83, 44]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8574
  Bounding Box: [743.20, 980.80, 952.80, 1248.00]
  Mask Area: 209 pixels
  Mask Ratio: 0.0074
  Mask BBox: [63, 83, 77, 101]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8486
  Bounding Box: [1091.20, 1204.80, 1292.80, 1417.60]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [90, 99, 104, 114]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8462
  Bounding Box: [1405.60, 116.70, 1600.00, 335.20]
  Mask Area: 196 pixels
  Mask Ratio: 0.0069
  Mask BBox: [114, 14, 128, 30]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8330
  Bounding Box: [1596.80, 0.00, 1798.40, 264.80]
  Mask Area: 202 pixels
  Mask Ratio: 0.0072
  Mask BBox: [130, 4, 143, 23]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8330
  Bounding Box: [1750.40, 279.60, 1996.80, 551.60]
  Mask Area: 283 pixels
  Mask Ratio: 0.0100
  Mask BBox: [141, 26, 158, 47]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8296
  Bounding Box: [1129.60, 678.00, 1278.40, 916.80]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [93, 57, 103, 74]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8252
  Bounding Box: [205.20, 1105.60, 444.40, 1385.60]
  Mask Area: 282 pixels
  Mask Ratio: 0.0100
  Mask BBox: [21, 91, 38, 112]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8218
  Bounding Box: [1844.80, 122.50, 2027.20, 314.40]
  Mask Area: 138 pixels
  Mask Ratio: 0.0049
  Mask BBox: [149, 14, 161, 26]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8218
  Bounding Box: [372.80, 592.00, 587.20, 812.80]
  Mask Area: 191 pixels
  Mask Ratio: 0.0068
  Mask BBox: [34, 51, 49, 65]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8184
  Bounding Box: [474.40, 1407.20, 611.20, 1544.80]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [42, 114, 51, 124]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.8130
  Bounding Box: [302.80, 1830.40, 556.40, 2035.20]
  Mask Area: 240 pixels
  Mask Ratio: 0.0085
  Mask BBox: [28, 147, 47, 162]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.8130
  Bounding Box: [1048.00, 862.40, 1249.60, 1139.20]
  Mask Area: 247 pixels
  Mask Ratio: 0.0088
  Mask BBox: [86, 72, 101, 92]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.8115
  Bounding Box: [240.40, 1585.60, 435.20, 1835.20]
  Mask Area: 219 pixels
  Mask Ratio: 0.0078
  Mask BBox: [23, 128, 37, 147]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.8105
  Bounding Box: [1304.00, 1450.40, 1502.40, 1710.40]
  Mask Area: 214 pixels
  Mask Ratio: 0.0076
  Mask BBox: [106, 118, 120, 137]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.8066
  Bounding Box: [1664.00, 1088.80, 1955.20, 1368.80]
  Mask Area: 358 pixels
  Mask Ratio: 0.0127
  Mask BBox: [134, 90, 154, 110]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7993
  Bounding Box: [918.40, 1665.60, 1059.20, 1860.80]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [76, 135, 86, 148]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7964
  Bounding Box: [0.00, 1568.00, 254.20, 1894.40]
  Mask Area: 274 pixels
  Mask Ratio: 0.0097
  Mask BBox: [4, 127, 23, 151]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7900
  Bounding Box: [924.80, 1873.60, 1177.60, 2048.00]
  Mask Area: 218 pixels
  Mask Ratio: 0.0077
  Mask BBox: [77, 151, 95, 164]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7896
  Bounding Box: [5.55, 442.40, 164.40, 668.80]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [5, 39, 16, 56]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7832
  Bounding Box: [570.40, 539.60, 816.80, 710.00]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [49, 47, 67, 59]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7798
  Bounding Box: [1740.80, 769.60, 1936.00, 993.60]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [140, 65, 154, 81]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7764
  Bounding Box: [112.20, 408.80, 243.00, 545.60]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [13, 36, 22, 46]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7744
  Bounding Box: [1068.00, 44.70, 1335.20, 322.00]
  Mask Area: 322 pixels
  Mask Ratio: 0.0114
  Mask BBox: [88, 8, 108, 29]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7686
  Bounding Box: [13.70, 782.40, 228.00, 910.40]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [6, 66, 21, 75]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7686
  Bounding Box: [1734.40, 1556.80, 2035.20, 1841.60]
  Mask Area: 390 pixels
  Mask Ratio: 0.0138
  Mask BBox: [140, 126, 162, 147]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7661
  Bounding Box: [1604.80, 800.00, 1761.60, 1043.20]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [130, 67, 140, 85]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7622
  Bounding Box: [13.30, 314.00, 140.30, 440.40]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [6, 29, 14, 38]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7573
  Bounding Box: [88.30, 169.60, 344.40, 428.80]
  Mask Area: 249 pixels
  Mask Ratio: 0.0088
  Mask BBox: [11, 18, 30, 37]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7559
  Bounding Box: [168.00, 1830.40, 307.20, 1993.60]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [18, 147, 27, 158]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7500
  Bounding Box: [1345.60, 1028.00, 1496.00, 1264.80]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [110, 85, 120, 102]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7500
  Bounding Box: [845.60, 1884.80, 954.40, 2044.80]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [71, 152, 77, 162]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7451
  Bounding Box: [743.20, 856.80, 863.20, 1045.60]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [63, 71, 71, 83]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7412
  Bounding Box: [0.00, 1159.20, 124.90, 1292.00]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [4, 95, 12, 104]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7412
  Bounding Box: [489.20, 1560.00, 816.00, 1924.80]
  Mask Area: 569 pixels
  Mask Ratio: 0.0202
  Mask BBox: [43, 126, 67, 154]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7393
  Bounding Box: [1330.40, 222.60, 1421.60, 370.80]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [108, 22, 115, 32]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7383
  Bounding Box: [457.20, 739.20, 608.40, 867.20]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [40, 63, 50, 71]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7383
  Bounding Box: [970.40, 256.80, 1154.40, 412.00]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [81, 25, 94, 36]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7368
  Bounding Box: [1580.80, 422.40, 1718.40, 585.60]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [128, 37, 138, 49]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7329
  Bounding Box: [48.25, 0.00, 209.40, 171.60]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [8, 5, 20, 17]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7290
  Bounding Box: [1790.40, 1015.20, 1886.40, 1101.60]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [144, 84, 151, 90]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7173
  Bounding Box: [1095.20, 1382.40, 1280.80, 1616.00]
  Mask Area: 215 pixels
  Mask Ratio: 0.0076
  Mask BBox: [90, 112, 103, 129]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7168
  Bounding Box: [2.70, 598.40, 79.40, 758.40]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [5, 51, 10, 63]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7153
  Bounding Box: [1216.00, 1144.80, 1316.80, 1260.00]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [99, 94, 106, 102]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7144
  Bounding Box: [582.00, 950.40, 763.60, 1115.20]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [50, 79, 63, 91]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7124
  Bounding Box: [748.00, 746.40, 893.60, 879.20]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [63, 63, 73, 72]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7119
  Bounding Box: [1028.00, 1137.60, 1188.00, 1252.80]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [85, 93, 96, 100]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7109
  Bounding Box: [1020.80, 1776.00, 1164.80, 1894.40]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [84, 143, 94, 151]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7109
  Bounding Box: [1152.80, 288.00, 1384.80, 564.80]
  Mask Area: 199 pixels
  Mask Ratio: 0.0071
  Mask BBox: [95, 27, 111, 48]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.7065
  Bounding Box: [44.10, 878.40, 349.60, 1192.00]
  Mask Area: 410 pixels
  Mask Ratio: 0.0145
  Mask BBox: [9, 73, 31, 97]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.7051
  Bounding Box: [515.20, 406.40, 623.20, 560.00]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [45, 36, 52, 47]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.7012
  Bounding Box: [552.00, 674.80, 741.60, 979.20]
  Mask Area: 232 pixels
  Mask Ratio: 0.0082
  Mask BBox: [48, 57, 61, 80]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.6992
  Bounding Box: [1291.20, 1256.80, 1406.40, 1357.60]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [105, 103, 112, 110]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.6987
  Bounding Box: [802.40, 1535.20, 928.80, 1660.80]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [67, 124, 75, 133]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.6987
  Bounding Box: [1614.40, 1583.20, 1752.00, 1731.20]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [131, 129, 140, 139]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6948
  Bounding Box: [1248.80, 1712.00, 1452.00, 1913.60]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [102, 138, 117, 152]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6929
  Bounding Box: [1915.20, 1848.00, 2043.20, 1998.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [154, 149, 163, 157]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6914
  Bounding Box: [1298.40, 858.40, 1472.80, 1080.80]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [106, 72, 118, 88]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6904
  Bounding Box: [43.05, 1248.80, 229.20, 1461.60]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [8, 102, 21, 118]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6870
  Bounding Box: [1470.40, 1528.00, 1611.20, 1710.40]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [119, 124, 128, 136]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6841
  Bounding Box: [126.50, 596.80, 354.80, 783.20]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [14, 51, 31, 63]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6753
  Bounding Box: [994.40, 1483.20, 1087.20, 1664.00]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [82, 120, 88, 133]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6748
  Bounding Box: [1835.20, 1367.20, 1992.00, 1562.40]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [148, 111, 159, 125]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6738
  Bounding Box: [6.35, 1403.20, 148.00, 1553.60]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [5, 114, 15, 125]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6719
  Bounding Box: [734.40, 592.00, 880.00, 776.80]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [62, 51, 72, 64]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6714
  Bounding Box: [1308.80, 579.60, 1396.80, 799.20]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [107, 50, 113, 66]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6704
  Bounding Box: [1817.60, 0.00, 1920.00, 123.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [146, 4, 153, 12]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6704
  Bounding Box: [1155.20, 1612.80, 1318.40, 1715.20]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [95, 130, 105, 137]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6704
  Bounding Box: [441.20, 1032.80, 670.80, 1396.00]
  Mask Area: 290 pixels
  Mask Ratio: 0.0103
  Mask BBox: [40, 86, 56, 110]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6665
  Bounding Box: [169.00, 1451.20, 293.80, 1564.80]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [18, 118, 26, 126]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6636
  Bounding Box: [1406.40, 311.60, 1643.20, 551.60]
  Mask Area: 172 pixels
  Mask Ratio: 0.0061
  Mask BBox: [114, 29, 132, 47]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6616
  Bounding Box: [406.40, 1204.80, 508.00, 1374.40]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [36, 99, 43, 111]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6597
  Bounding Box: [280.20, 1423.20, 462.80, 1678.40]
  Mask Area: 215 pixels
  Mask Ratio: 0.0076
  Mask BBox: [26, 116, 40, 135]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6582
  Bounding Box: [558.40, 262.40, 629.60, 366.00]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [48, 25, 53, 32]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6577
  Bounding Box: [2.30, 1849.60, 80.20, 1968.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [5, 149, 8, 157]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6528
  Bounding Box: [700.00, 1873.60, 893.60, 2048.00]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [59, 152, 73, 164]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6523
  Bounding Box: [1616.00, 1500.00, 1766.40, 1598.40]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [131, 122, 140, 128]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6519
  Bounding Box: [1261.60, 1939.20, 1445.60, 2035.20]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [103, 156, 116, 162]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6509
  Bounding Box: [1271.20, 1063.20, 1381.60, 1154.40]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [104, 88, 111, 94]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6489
  Bounding Box: [1987.20, 1537.60, 2044.80, 1659.20]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [160, 125, 163, 133]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6489
  Bounding Box: [1462.40, 808.00, 1608.00, 1227.20]
  Mask Area: 205 pixels
  Mask Ratio: 0.0073
  Mask BBox: [119, 70, 129, 98]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6475
  Bounding Box: [613.60, 1080.00, 756.00, 1483.20]
  Mask Area: 265 pixels
  Mask Ratio: 0.0094
  Mask BBox: [52, 89, 63, 119]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6440
  Bounding Box: [896.00, 710.00, 1113.60, 944.00]
  Mask Area: 246 pixels
  Mask Ratio: 0.0087
  Mask BBox: [74, 60, 90, 77]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6421
  Bounding Box: [1942.40, 3.80, 2048.00, 134.20]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [156, 5, 163, 14]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6406
  Bounding Box: [858.40, 857.60, 952.80, 1008.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [72, 71, 78, 82]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.6401
  Bounding Box: [660.80, 164.00, 744.00, 268.00]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [56, 17, 62, 24]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.6401
  Bounding Box: [995.20, 1259.20, 1099.20, 1462.40]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [82, 103, 89, 118]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.6367
  Bounding Box: [1057.60, 1654.40, 1177.60, 1788.80]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [87, 134, 95, 143]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.6367
  Bounding Box: [1526.40, 8.55, 1624.00, 154.00]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [124, 5, 130, 15]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.6343
  Bounding Box: [333.80, 178.80, 588.00, 501.20]
  Mask Area: 310 pixels
  Mask Ratio: 0.0110
  Mask BBox: [31, 18, 49, 43]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.6343
  Bounding Box: [1646.40, 1380.80, 1787.20, 1516.80]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [133, 112, 143, 122]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.6187
  Bounding Box: [1940.80, 1168.00, 2048.00, 1336.00]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [156, 96, 164, 108]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.6172
  Bounding Box: [1395.20, 1229.60, 1555.20, 1372.00]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [114, 101, 124, 110]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.6167
  Bounding Box: [118.70, 1561.60, 254.80, 1667.20]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [14, 126, 23, 134]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.6162
  Bounding Box: [700.80, 1335.20, 857.60, 1576.80]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [59, 109, 70, 127]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.6118
  Bounding Box: [1450.40, 553.60, 1624.00, 788.00]
  Mask Area: 182 pixels
  Mask Ratio: 0.0064
  Mask BBox: [118, 48, 130, 65]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.6060
  Bounding Box: [1003.20, 457.60, 1195.20, 680.00]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [83, 40, 97, 57]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.6060
  Bounding Box: [866.40, 1763.20, 932.00, 1881.60]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [72, 142, 76, 150]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.6030
  Bounding Box: [1143.20, 1873.60, 1300.00, 2048.00]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [94, 151, 105, 164]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.6021
  Bounding Box: [1995.20, 1800.00, 2048.00, 1902.40]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [160, 145, 164, 151]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.6001
  Bounding Box: [907.20, 1678.40, 1126.40, 1876.80]
  Mask Area: 205 pixels
  Mask Ratio: 0.0073
  Mask BBox: [75, 136, 91, 150]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5991
  Bounding Box: [342.40, 1366.40, 491.20, 1462.40]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [31, 111, 42, 118]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5986
  Bounding Box: [180.80, 4.45, 323.60, 143.20]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [19, 5, 29, 15]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5947
  Bounding Box: [56.40, 1838.40, 203.60, 2014.40]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [9, 148, 19, 161]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5884
  Bounding Box: [1157.60, 1764.80, 1256.80, 1886.40]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [95, 142, 101, 151]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5830
  Bounding Box: [1464.80, 1266.40, 1691.20, 1512.80]
  Mask Area: 223 pixels
  Mask Ratio: 0.0079
  Mask BBox: [119, 103, 136, 122]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5786
  Bounding Box: [1479.20, 1926.40, 1672.00, 2041.60]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [120, 155, 134, 163]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5762
  Bounding Box: [1416.00, 591.60, 1480.00, 691.60]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [115, 51, 119, 58]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5742
  Bounding Box: [1905.60, 1379.20, 2048.00, 1556.80]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [153, 112, 164, 125]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5732
  Bounding Box: [3.50, 141.40, 98.50, 295.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [5, 16, 11, 27]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5732
  Bounding Box: [305.20, 964.80, 470.80, 1102.40]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [28, 80, 40, 89]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5728
  Bounding Box: [940.80, 936.00, 1043.20, 1057.60]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [78, 78, 85, 86]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5703
  Bounding Box: [906.40, 1004.80, 988.00, 1088.00]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [75, 83, 81, 88]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5654
  Bounding Box: [1271.20, 619.60, 1380.00, 790.40]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [104, 53, 111, 64]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5645
  Bounding Box: [1720.00, 1862.40, 1998.40, 2044.80]
  Mask Area: 233 pixels
  Mask Ratio: 0.0083
  Mask BBox: [139, 150, 160, 163]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5615
  Bounding Box: [768.80, 1673.60, 892.00, 1897.60]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [65, 135, 73, 152]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5405
  Bounding Box: [733.60, 1168.00, 831.20, 1288.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [62, 96, 68, 104]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5371
  Bounding Box: [569.60, 157.80, 672.00, 267.40]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [49, 17, 56, 24]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.5337
  Bounding Box: [402.40, 860.00, 525.60, 1037.60]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [36, 72, 44, 85]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.5332
  Bounding Box: [629.20, 1947.20, 710.80, 2040.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [54, 157, 59, 162]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.5327
  Bounding Box: [1910.40, 958.40, 2048.00, 1104.00]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [154, 79, 163, 88]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.5303
  Bounding Box: [616.00, 420.80, 698.40, 540.00]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [53, 37, 58, 45]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.5298
  Bounding Box: [1355.20, 1045.60, 1523.20, 1333.60]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [110, 86, 122, 108]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.5269
  Bounding Box: [90.00, 1488.80, 228.40, 1646.40]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [12, 121, 21, 132]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.5244
  Bounding Box: [884.80, 471.20, 958.40, 547.20]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [74, 41, 78, 46]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.5176
  Bounding Box: [1931.20, 1330.40, 2048.00, 1432.80]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [155, 108, 164, 115]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.5044
  Bounding Box: [61.80, 936.00, 288.20, 1201.60]
  Mask Area: 269 pixels
  Mask Ratio: 0.0095
  Mask BBox: [9, 78, 26, 97]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.5020
  Bounding Box: [1982.40, 192.00, 2036.80, 363.20]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [159, 19, 163, 32]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4990
  Bounding Box: [1110.40, 478.00, 1291.20, 698.00]
  Mask Area: 177 pixels
  Mask Ratio: 0.0063
  Mask BBox: [91, 42, 104, 58]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4980
  Bounding Box: [924.00, 958.40, 1034.40, 1078.40]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [77, 79, 84, 88]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4844
  Bounding Box: [11.90, 1550.40, 112.50, 1656.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [5, 126, 12, 132]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4785
  Bounding Box: [931.20, 172.20, 1070.40, 269.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [77, 18, 86, 25]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4785
  Bounding Box: [932.00, 380.00, 1072.80, 551.20]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [77, 34, 87, 47]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4761
  Bounding Box: [1301.60, 397.20, 1388.00, 582.00]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [106, 36, 112, 49]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4761
  Bounding Box: [118.20, 1464.00, 293.00, 1603.20]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [14, 119, 26, 129]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4746
  Bounding Box: [1992.00, 742.40, 2048.00, 1043.20]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [160, 62, 165, 85]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4731
  Bounding Box: [797.60, 1280.80, 1039.20, 1565.60]
  Mask Area: 285 pixels
  Mask Ratio: 0.0101
  Mask BBox: [67, 105, 85, 125]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4727
  Bounding Box: [26.95, 924.80, 119.00, 1035.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [7, 77, 13, 84]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4692
  Bounding Box: [238.40, 146.80, 416.00, 305.20]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [23, 16, 34, 27]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4690
  Bounding Box: [714.80, 0.00, 886.40, 200.80]
  Mask Area: 182 pixels
  Mask Ratio: 0.0064
  Mask BBox: [60, 4, 73, 19]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4678
  Bounding Box: [916.00, 34.75, 1063.20, 145.80]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [76, 7, 87, 15]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4678
  Bounding Box: [8.15, 1924.80, 162.40, 2048.00]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [5, 155, 16, 164]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4668
  Bounding Box: [875.20, 570.00, 1046.40, 746.00]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [74, 49, 85, 62]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4663
  Bounding Box: [1277.60, 1349.60, 1378.40, 1468.00]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [104, 110, 111, 118]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4658
  Bounding Box: [1555.20, 1796.80, 1779.20, 1918.40]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [126, 145, 142, 153]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4619
  Bounding Box: [25.10, 337.60, 159.20, 456.80]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [6, 31, 16, 39]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4570
  Bounding Box: [767.60, 1886.40, 949.60, 2036.80]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [64, 152, 78, 163]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4539
  Bounding Box: [1713.60, 1354.40, 1825.60, 1460.00]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [138, 110, 146, 118]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4490
  Bounding Box: [1000.80, 1049.60, 1104.80, 1144.00]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [83, 86, 90, 93]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4470
  Bounding Box: [649.60, 283.60, 716.00, 370.00]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [55, 27, 59, 32]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4431
  Bounding Box: [859.20, 1766.40, 963.20, 1888.00]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [72, 142, 79, 151]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4421
  Bounding Box: [341.20, 545.60, 440.40, 647.20]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [31, 47, 37, 53]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4341
  Bounding Box: [724.00, 19.60, 844.00, 203.00]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [61, 6, 69, 19]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4331
  Bounding Box: [1555.20, 1165.60, 1638.40, 1304.80]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [126, 96, 131, 104]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4331
  Bounding Box: [620.80, 534.40, 842.40, 781.60]
  Mask Area: 249 pixels
  Mask Ratio: 0.0088
  Mask BBox: [53, 46, 69, 65]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4297
  Bounding Box: [422.00, 1728.00, 482.80, 1833.60]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [37, 139, 41, 147]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4287
  Bounding Box: [78.20, 1801.60, 199.80, 1932.80]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [12, 145, 19, 154]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4280
  Bounding Box: [1144.80, 1606.40, 1292.00, 1788.80]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [94, 130, 104, 143]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4233
  Bounding Box: [0.00, 1844.80, 103.00, 1988.80]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [4, 149, 12, 159]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4204
  Bounding Box: [1593.60, 299.60, 1782.40, 505.20]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [129, 28, 143, 43]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.4172
  Bounding Box: [1243.20, 608.00, 1342.40, 812.00]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [102, 52, 108, 67]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.4158
  Bounding Box: [837.60, 1899.20, 928.80, 2048.00]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [70, 153, 76, 165]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.4141
  Bounding Box: [1288.80, 0.00, 1421.60, 77.70]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [105, 4, 115, 9]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.4136
  Bounding Box: [1800.00, 1835.20, 2048.00, 2036.80]
  Mask Area: 230 pixels
  Mask Ratio: 0.0081
  Mask BBox: [145, 148, 164, 163]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.4116
  Bounding Box: [1027.20, 444.80, 1270.40, 718.40]
  Mask Area: 254 pixels
  Mask Ratio: 0.0090
  Mask BBox: [85, 39, 103, 60]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.4116
  Bounding Box: [1860.80, 1827.20, 2048.00, 2000.00]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [150, 147, 164, 160]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.4116
  Bounding Box: [620.40, 12.80, 754.00, 158.60]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [53, 5, 62, 16]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.4082
  Bounding Box: [1281.60, 773.60, 1382.40, 917.60]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [105, 65, 111, 75]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.4060
  Bounding Box: [918.40, 1576.80, 1016.00, 1686.40]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [76, 128, 83, 133]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.4021
  Bounding Box: [1979.20, 401.20, 2048.00, 683.60]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [159, 36, 164, 57]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.4011
  Bounding Box: [220.80, 1976.00, 353.60, 2036.80]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [22, 159, 31, 163]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.4004
  Bounding Box: [1046.40, 2.15, 1152.00, 102.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [86, 5, 93, 11]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3953
  Bounding Box: [319.80, 1358.40, 484.80, 1496.00]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [29, 111, 41, 120]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3914
  Bounding Box: [605.60, 1491.20, 720.00, 1563.20]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [52, 121, 60, 126]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3909
  Bounding Box: [1683.20, 1013.60, 1788.80, 1084.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [136, 84, 143, 88]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3896
  Bounding Box: [2.10, 1024.80, 71.30, 1156.00]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [5, 85, 8, 94]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3894
  Bounding Box: [108.90, 351.40, 276.80, 545.60]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [13, 32, 25, 46]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3892
  Bounding Box: [448.00, 1379.20, 614.40, 1520.00]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [39, 112, 51, 122]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3857
  Bounding Box: [1490.40, 1748.80, 1763.20, 1918.40]
  Mask Area: 241 pixels
  Mask Ratio: 0.0085
  Mask BBox: [121, 141, 141, 153]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3843
  Bounding Box: [1132.00, 330.20, 1341.60, 683.20]
  Mask Area: 336 pixels
  Mask Ratio: 0.0119
  Mask BBox: [93, 30, 108, 57]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3809
  Bounding Box: [249.80, 1985.60, 373.20, 2046.40]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [24, 160, 32, 163]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3792
  Bounding Box: [1633.60, 1432.80, 1784.00, 1592.00]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [132, 116, 143, 128]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3779
  Bounding Box: [486.00, 1974.40, 644.40, 2041.60]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [42, 159, 54, 163]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3774
  Bounding Box: [1302.40, 1240.00, 1425.60, 1342.40]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [106, 101, 115, 108]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3762
  Bounding Box: [1584.00, 1107.20, 1670.40, 1192.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [128, 91, 134, 97]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3757
  Bounding Box: [927.20, 1036.80, 994.40, 1150.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [77, 85, 81, 93]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3750
  Bounding Box: [1428.80, 1696.00, 1707.20, 1910.40]
  Mask Area: 265 pixels
  Mask Ratio: 0.0094
  Mask BBox: [116, 137, 137, 153]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3738
  Bounding Box: [66.90, 646.40, 151.70, 780.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [10, 55, 15, 64]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3735
  Bounding Box: [1876.80, 23.30, 2001.60, 141.10]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [151, 6, 160, 14]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3733
  Bounding Box: [12.95, 1321.60, 197.40, 1553.60]
  Mask Area: 200 pixels
  Mask Ratio: 0.0071
  Mask BBox: [6, 108, 19, 125]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3726
  Bounding Box: [417.60, 1093.60, 488.80, 1192.80]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [37, 90, 41, 97]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3694
  Bounding Box: [1772.80, 0.00, 1884.80, 146.00]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [143, 4, 151, 15]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3667
  Bounding Box: [1987.20, 764.00, 2048.00, 965.60]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [160, 64, 164, 79]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3652
  Bounding Box: [164.00, 534.40, 284.00, 604.00]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [17, 46, 26, 51]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3640
  Bounding Box: [64.20, 0.00, 288.60, 180.20]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [10, 3, 26, 18]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3638
  Bounding Box: [595.60, 1934.40, 720.40, 2030.40]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [51, 156, 60, 162]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3625
  Bounding Box: [1009.60, 1084.80, 1190.40, 1260.80]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [83, 89, 96, 102]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3611
  Bounding Box: [1398.40, 575.20, 1484.80, 679.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [114, 49, 119, 56]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3611
  Bounding Box: [1424.00, 575.20, 1510.40, 679.20]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [116, 50, 119, 56]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3606
  Bounding Box: [1277.60, 1450.40, 1351.20, 1578.40]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [104, 118, 109, 127]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3594
  Bounding Box: [877.60, 450.00, 976.80, 548.40]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [73, 40, 78, 46]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3589
  Bounding Box: [1574.40, 1127.20, 1657.60, 1260.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [127, 93, 133, 102]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3540
  Bounding Box: [1192.80, 1125.60, 1301.60, 1250.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [98, 92, 105, 101]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3535
  Bounding Box: [1907.20, 7.15, 2019.20, 132.40]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [153, 5, 161, 14]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3535
  Bounding Box: [1932.80, 32.75, 2044.80, 158.00]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [155, 7, 163, 14]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3535
  Bounding Box: [1764.80, 992.00, 1896.00, 1108.80]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [142, 82, 152, 90]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3528
  Bounding Box: [944.00, 279.60, 1113.60, 518.80]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [78, 26, 90, 44]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3523
  Bounding Box: [1800.00, 19.45, 1908.80, 145.00]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [145, 6, 153, 15]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3523
  Bounding Box: [237.20, 436.40, 320.00, 549.20]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [23, 39, 28, 46]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3518
  Bounding Box: [1398.40, 603.60, 1486.40, 702.80]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [114, 52, 119, 58]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3518
  Bounding Box: [1424.00, 603.60, 1512.00, 702.80]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [116, 52, 119, 58]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3503
  Bounding Box: [850.40, 1750.40, 922.40, 1865.60]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [71, 141, 76, 149]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3503
  Bounding Box: [876.00, 1750.40, 948.00, 1865.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [73, 141, 78, 149]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3491
  Bounding Box: [10.10, 632.00, 86.60, 784.80]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [5, 54, 10, 64]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3474
  Bounding Box: [1449.60, 4.95, 1616.00, 169.00]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [118, 5, 130, 14]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3455
  Bounding Box: [140.40, 406.80, 296.00, 540.40]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [15, 36, 27, 46]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3442
  Bounding Box: [845.60, 1776.00, 927.20, 1891.20]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [71, 143, 76, 151]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3433
  Bounding Box: [349.20, 839.20, 525.20, 1060.00]
  Mask Area: 165 pixels
  Mask Ratio: 0.0058
  Mask BBox: [32, 70, 45, 86]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3389
  Bounding Box: [890.40, 997.60, 978.40, 1074.40]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [75, 82, 80, 87]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3389
  Bounding Box: [916.00, 997.60, 1004.00, 1074.40]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [76, 82, 82, 87]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3389
  Bounding Box: [890.40, 1023.20, 978.40, 1100.00]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [75, 84, 80, 89]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3389
  Bounding Box: [916.00, 1023.20, 1004.00, 1100.00]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [76, 84, 80, 89]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3389
  Bounding Box: [878.40, 1777.60, 948.80, 1896.00]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [73, 143, 78, 152]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3386
  Bounding Box: [151.20, 901.60, 453.20, 1140.00]
  Mask Area: 251 pixels
  Mask Ratio: 0.0089
  Mask BBox: [16, 75, 39, 93]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3381
  Bounding Box: [1279.20, 541.20, 1333.60, 627.60]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [104, 47, 108, 53]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3381
  Bounding Box: [1841.60, 1101.60, 1950.40, 1183.20]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [148, 91, 156, 96]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3372
  Bounding Box: [1760.00, 1015.20, 1862.40, 1117.60]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [142, 84, 149, 91]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3354
  Bounding Box: [1144.80, 1742.40, 1240.80, 1876.80]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [94, 141, 100, 150]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3345
  Bounding Box: [1060.80, 1614.40, 1208.00, 1784.00]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [87, 131, 98, 143]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3328
  Bounding Box: [1259.20, 372.00, 1401.60, 587.20]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [103, 34, 113, 49]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3328
  Bounding Box: [1540.80, 815.20, 1748.80, 1101.60]
  Mask Area: 240 pixels
  Mask Ratio: 0.0085
  Mask BBox: [125, 68, 140, 90]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3320
  Bounding Box: [2000.00, 1534.40, 2048.00, 1662.40]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [161, 124, 163, 132]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3320
  Bounding Box: [1974.40, 1560.00, 2041.60, 1688.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [159, 126, 163, 132]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1153.60, 1756.80, 1283.20, 1920.00]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [95, 142, 104, 153]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3281
  Bounding Box: [510.40, 0.00, 630.40, 84.50]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [44, 4, 53, 10]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3276
  Bounding Box: [1411.20, 1217.60, 1659.20, 1481.60]
  Mask Area: 274 pixels
  Mask Ratio: 0.0097
  Mask BBox: [115, 100, 133, 119]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [356.40, 556.80, 457.20, 637.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [32, 48, 38, 53]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [304.00, 836.80, 376.00, 915.20]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [28, 70, 33, 74]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [903.20, 8.05, 1029.60, 106.40]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [75, 5, 84, 12]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.3218
  Bounding Box: [1012.00, 1452.00, 1104.80, 1646.40]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [84, 118, 90, 132]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.3218
  Bounding Box: [205.20, 1742.40, 287.60, 1835.20]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [21, 141, 26, 147]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.3208
  Bounding Box: [1632.00, 1905.60, 1756.80, 2017.60]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [132, 153, 141, 161]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.3201
  Bounding Box: [1387.20, 688.00, 1454.40, 808.80]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [113, 58, 117, 67]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.3196
  Bounding Box: [320.40, 442.40, 460.40, 565.60]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [30, 39, 39, 48]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.3157
  Bounding Box: [0.00, 2.40, 72.20, 154.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [4, 5, 9, 16]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.3154
  Bounding Box: [899.20, 0.00, 1008.00, 66.10]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [75, 4, 82, 8]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.3145
  Bounding Box: [532.00, 27.50, 636.80, 132.10]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [46, 7, 53, 14]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.3142
  Bounding Box: [955.20, 688.80, 1124.80, 906.40]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [79, 58, 91, 74]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.3140
  Bounding Box: [1266.40, 0.00, 1376.80, 69.80]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [103, 4, 111, 9]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.3140
  Bounding Box: [904.80, 1260.80, 1082.40, 1523.20]
  Mask Area: 235 pixels
  Mask Ratio: 0.0083
  Mask BBox: [75, 103, 88, 122]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.3123
  Bounding Box: [1294.40, 193.40, 1404.80, 355.00]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [106, 20, 113, 31]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.3123
  Bounding Box: [1937.60, 1546.40, 2048.00, 1660.80]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [156, 125, 164, 133]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.3115
  Bounding Box: [324.60, 472.80, 444.00, 579.20]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [30, 41, 38, 49]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.3108
  Bounding Box: [1894.40, 519.20, 2028.80, 857.60]
  Mask Area: 233 pixels
  Mask Ratio: 0.0083
  Mask BBox: [152, 45, 162, 70]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.3091
  Bounding Box: [1675.20, 1366.40, 1812.80, 1524.80]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [135, 111, 145, 123]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.3088
  Bounding Box: [1067.20, 1600.00, 1168.00, 1756.80]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [88, 129, 95, 141]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.3086
  Bounding Box: [1159.20, 496.80, 1300.00, 672.80]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [95, 43, 105, 56]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.3071
  Bounding Box: [1838.40, 1112.00, 1963.20, 1212.80]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [148, 91, 157, 98]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [18.10, 1383.20, 168.40, 1517.60]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [6, 113, 17, 122]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [32.70, 908.00, 155.60, 1002.40]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [7, 75, 15, 82]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [3.90, 1409.60, 186.00, 1593.60]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [5, 115, 18, 128]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.3052
  Bounding Box: [6.95, 107.40, 145.40, 274.20]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [5, 13, 15, 25]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.3049
  Bounding Box: [534.80, 59.00, 630.80, 148.60]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [46, 9, 53, 15]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.3049
  Bounding Box: [658.00, 137.00, 959.20, 570.40]
  Mask Area: 501 pixels
  Mask Ratio: 0.0178
  Mask BBox: [56, 15, 78, 48]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.3044
  Bounding Box: [1496.00, 0.00, 1609.60, 142.80]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [121, 4, 129, 15]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.3035
  Bounding Box: [621.60, 1460.00, 722.40, 1567.20]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [53, 119, 60, 126]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.3030
  Bounding Box: [933.60, 372.00, 1111.20, 598.40]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [77, 34, 90, 50]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.3025
  Bounding Box: [231.20, 718.40, 337.60, 875.20]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [23, 61, 30, 72]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.3018
  Bounding Box: [840.00, 558.00, 926.40, 645.20]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [70, 48, 76, 54]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [957.60, 945.60, 1060.00, 1075.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [79, 78, 86, 87]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.3013
  Bounding Box: [1549.60, 1193.60, 1625.60, 1296.00]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [126, 98, 130, 104]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.3005
  Bounding Box: [1491.20, 1304.80, 1753.60, 1522.40]
  Mask Area: 197 pixels
  Mask Ratio: 0.0070
  Mask BBox: [121, 106, 140, 122]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2996
  Bounding Box: [331.60, 0.00, 470.00, 58.80]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [30, 4, 40, 7]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2991
  Bounding Box: [6.35, 1865.60, 86.20, 2016.00]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [5, 150, 10, 161]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2974
  Bounding Box: [510.40, 8.60, 608.80, 114.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [44, 5, 51, 12]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2971
  Bounding Box: [382.40, 1228.80, 508.80, 1451.20]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [34, 100, 43, 117]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2964
  Bounding Box: [39.85, 1769.60, 223.80, 2019.20]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [8, 143, 21, 161]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2954
  Bounding Box: [746.40, 871.20, 916.00, 1202.40]
  Mask Area: 245 pixels
  Mask Ratio: 0.0087
  Mask BBox: [63, 73, 75, 97]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2949
  Bounding Box: [1740.80, 0.00, 1840.00, 124.80]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [140, 4, 147, 13]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2942
  Bounding Box: [1579.20, 1598.40, 1736.00, 1752.00]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [128, 129, 139, 140]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2935
  Bounding Box: [641.20, 269.20, 706.80, 360.00]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [55, 26, 59, 32]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2935
  Bounding Box: [666.80, 269.20, 732.40, 360.00]
  Mask Area: 16 pixels
  Mask Ratio: 0.0006
  Mask BBox: [57, 26, 60, 31]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2935
  Bounding Box: [641.20, 294.80, 706.80, 385.60]
  Mask Area: 19 pixels
  Mask Ratio: 0.0007
  Mask BBox: [55, 28, 59, 33]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2935
  Bounding Box: [666.80, 294.80, 732.40, 385.60]
  Mask Area: 8 pixels
  Mask Ratio: 0.0003
  Mask BBox: [57, 28, 59, 31]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2930
  Bounding Box: [921.60, 505.60, 1161.60, 728.00]
  Mask Area: 232 pixels
  Mask Ratio: 0.0082
  Mask BBox: [76, 44, 94, 60]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2915
  Bounding Box: [664.80, 855.20, 874.40, 1040.80]
  Mask Area: 191 pixels
  Mask Ratio: 0.0068
  Mask BBox: [56, 71, 72, 85]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2910
  Bounding Box: [1035.20, 1758.40, 1190.40, 1867.20]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [85, 142, 96, 149]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2910
  Bounding Box: [534.40, 289.40, 617.60, 392.00]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [47, 27, 52, 34]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2910
  Bounding Box: [560.00, 289.40, 643.20, 392.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [48, 27, 54, 34]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2903
  Bounding Box: [562.40, 0.00, 744.80, 157.60]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [48, 4, 62, 16]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2881
  Bounding Box: [0.00, 101.60, 122.00, 326.40]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [4, 12, 13, 29]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2866
  Bounding Box: [1595.20, 592.40, 1665.60, 695.60]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [129, 51, 133, 58]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2859
  Bounding Box: [318.40, 900.00, 496.00, 1100.00]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [29, 75, 42, 89]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2856
  Bounding Box: [522.40, 358.40, 658.40, 556.00]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [45, 32, 55, 47]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [1569.60, 247.20, 1755.20, 469.60]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [127, 24, 141, 40]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2817
  Bounding Box: [1953.60, 197.20, 2048.00, 338.40]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [157, 20, 164, 30]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2815
  Bounding Box: [0.00, 1852.80, 52.20, 1968.00]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [3, 149, 8, 157]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2810
  Bounding Box: [1964.80, 333.20, 2028.80, 435.60]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [158, 31, 162, 38]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2805
  Bounding Box: [622.80, 146.50, 754.00, 268.80]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [53, 16, 62, 24]

