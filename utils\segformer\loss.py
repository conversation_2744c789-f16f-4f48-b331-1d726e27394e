# utils/segformer/criterion.py
# -*- coding: utf-8 -*-
"""
SegFormer/Mask2Former 风格统一损失（单文件版本）
- 统一包含：匈牙利匹配 + 分类 CE(含no-object) + 掩码 BCE(Logits) + 掩码 Dice
- 支持 aux_outputs（每层解码器的中间输出）监督
- 仅依赖 PyTorch；若安装了 SciPy，则匹配使用匈牙利算法；否则回退贪心匹配
- 多模态对损失透明：只看 model 输出与 GT（labels/masks）
"""

from typing import List, Dict, Tuple
import logging

import torch
import torch.nn as nn
import torch.nn.functional as F

LOGGER = logging.getLogger("segformer")


# =========================
# 基础掩码损失
# =========================
def dice_loss(prob: torch.Tensor, target: torch.Tensor, eps: float = 1.0) -> torch.Tensor:
    """
    Soft Dice：对每个样本计算再取均值
      1 - (2*|P∩G| + eps) / (|P| + |G| + eps)
    Args:
        prob:   [N,H,W]  已过 sigmoid 的概率
        target: [N,H,W]  0/1 掩码
    """
    prob = prob.flatten(1)    # [N,HW]
    target = target.flatten(1)
    inter = (prob * target).sum(1)
    union = prob.sum(1) + target.sum(1)
    loss = 1.0 - (2.0 * inter + eps) / (union + eps)
    return loss.mean()


def bce_logits_loss(logits: torch.Tensor, target: torch.Tensor, pos_weight: float = 1.0) -> torch.Tensor:
    """
    二值掩码的 BCE（logits 版本，数值稳定）
    """
    loss_fn = nn.BCEWithLogitsLoss(pos_weight=torch.tensor(pos_weight, device=logits.device), reduction="mean")
    return loss_fn(logits, target)


@torch.no_grad()
def _upsample_tgt_if_needed(pred_logits: torch.Tensor, tgt_mask: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    若大小不一致，把 GT 掩码最近邻缩放到与预测一致（避免插值产生新值）
    Args:
        pred_logits: [N,H1,W1]
        tgt_mask:    [N,H2,W2]
    """
    if pred_logits.shape[-2:] == tgt_mask.shape[-2:]:
        return pred_logits, tgt_mask
    tgt_up = F.interpolate(tgt_mask.unsqueeze(1), size=pred_logits.shape[-2:], mode="nearest").squeeze(1)
    return pred_logits, tgt_up


# =========================
# 匹配器（优先 SciPy 匈牙利，回退贪心）
# =========================
class HungarianMatcher(nn.Module):
    """
    代价 = w_cls * cls_cost + w_dice * dice_cost + w_bce * bce_cost
      - cls_cost: 使用 -P(class)（不含 no-object 列）
      - dice_cost: 1 - dice(P_mask, G_mask)
      - bce_cost:  像素 BCE 均值
    """

    def __init__(self, cost_class: float = 2.0, cost_dice: float = 1.0, cost_bce: float = 1.0):
        super().__init__()
        self.cost_class = cost_class
        self.cost_dice = cost_dice
        self.cost_bce = cost_bce

        try:
            from scipy.optimize import linear_sum_assignment  # noqa: F401
            self.has_scipy = True
        except Exception:
            self.has_scipy = False
            LOGGER.warning("未检测到 SciPy，将使用贪心匹配（建议安装 scipy 获得更稳的匹配）。")

    @torch.no_grad()
    def forward(self, outputs: Dict[str, torch.Tensor], targets: List[Dict[str, torch.Tensor]]) \
            -> List[Tuple[torch.Tensor, torch.Tensor]]:
        """
        Args:
            outputs: {'pred_logits':[B,Q,C+1], 'pred_masks':[B,Q,H,W]}
            targets: [{'labels':[Ng], 'masks':[Ng,H,W]}, ...]
        Returns:
            indices: [(idx_q[B_i], idx_g[B_i]), ...]  每图一对索引
        """
        B, Q, _ = outputs["pred_logits"].shape
        out_prob = outputs["pred_logits"].softmax(-1)     # [B,Q,C+1]
        out_mask_logits = outputs["pred_masks"]           # [B,Q,H,W]
        out_mask_prob = out_mask_logits.sigmoid()

        all_indices: List[Tuple[torch.Tensor, torch.Tensor]] = []

        for b in range(B):
            tgt_ids = targets[b]["labels"].long()         # [Ng]
            tgt_masks = targets[b]["masks"].float()       # [Ng,H,W]
            Ng = tgt_ids.numel()

            if Ng == 0:
                all_indices.append((
                    torch.as_tensor([], dtype=torch.int64),
                    torch.as_tensor([], dtype=torch.int64)
                ))
                continue

            # 分类代价（越小越好）：取每个 query 对应目标类概率的负值
            # out_prob[b]: [Q,C+1]，去掉最后一列 no-object
            cls_cost = -out_prob[b][:, :-1][:, tgt_ids]   # [Q,Ng]

            # 掩码代价
            pmask = out_mask_prob[b]      # [Q,H,W]
            plogit = out_mask_logits[b]   # [Q,H,W]
            Q_, H, W = pmask.shape
            Ng_ = Ng
            
            # 确保目标掩码与预测掩码尺寸一致
            if tgt_masks.shape[-2:] != (H, W):
                tgt_masks = F.interpolate(tgt_masks.unsqueeze(0), size=(H, W), mode='nearest').squeeze(0)

            # Dice 代价（1 - dice）
            inter = torch.matmul(pmask.reshape(Q_, -1), tgt_masks.reshape(Ng_, -1).T)   # [Q,Ng]
            p_sum = pmask.reshape(Q_, -1).sum(-1, keepdim=True)                         # [Q,1]
            g_sum = tgt_masks.reshape(Ng_, -1).sum(-1, keepdim=True).T                  # [1,Ng]
            dice_cost = 1.0 - (2.0 * inter + 1.0) / (p_sum + g_sum + 1.0)               # [Q,Ng]

            # BCE 代价：对每个 (q,g) 的像素 BCE 均值，分块以控显存
            with torch.amp.autocast('cuda', enabled=False):
                bce_cost = torch.zeros((Q_, Ng_), device=pmask.device, dtype=torch.float32)
                step = 64
                for i in range(0, Q_, step):
                    logits_blk = plogit[i:i + step]              # [k,H,W]
                    logits_blk = logits_blk[:, None].repeat(1, Ng_, 1, 1)   # [k,Ng,H,W]
                    tgt_blk = tgt_masks[None].repeat(logits_blk.shape[0], 1, 1, 1)
                    loss_blk = F.binary_cross_entropy_with_logits(logits_blk, tgt_blk, reduction="none").mean(dim=(2, 3))
                    bce_cost[i:i + step] = loss_blk

            # 组合总代价
            C = self.cost_class * cls_cost + self.cost_dice * dice_cost + self.cost_bce * bce_cost  # [Q,Ng]
            C_cpu = C.detach().cpu()

            # 线性分配
            if self.has_scipy:
                from scipy.optimize import linear_sum_assignment
                i_q, i_g = linear_sum_assignment(C_cpu)
                idx_q = torch.as_tensor(i_q, dtype=torch.int64)
                idx_g = torch.as_tensor(i_g, dtype=torch.int64)
            else:
                # 简单贪心（每次取全局最小，屏蔽行列）
                C_np = C_cpu.numpy()
                used_q, used_g = set(), set()
                sel_q, sel_g = [], []
                for _ in range(min(Q_, Ng_)):
                    pos = divmod(C_np.argmin(), C_np.shape[1])
                    i, j = int(pos[0]), int(pos[1])
                    if i in used_q or j in used_g:
                        C_np[i, j] = float("inf")
                        continue
                    sel_q.append(i); sel_g.append(j)
                    used_q.add(i);  used_g.add(j)
                    C_np[i, :] = float("inf")
                    C_np[:, j] = float("inf")
                idx_q = torch.as_tensor(sel_q, dtype=torch.int64)
                idx_g = torch.as_tensor(sel_g, dtype=torch.int64)

            all_indices.append((idx_q, idx_g))

        return all_indices


# =========================
# SetCriterion（统一损失）
# =========================
class SetCriterion(nn.Module):
    """
    统一损失：loss = λ_ce * CE + λ_bce * BCE + λ_dice * Dice  （主分支 + 可选 Aux 分支）
    - CE 对所有 Q（含 no-object）
    - BCE/Dice 仅对匹配到的正样本 queries
    """

    def __init__(
        self,
        num_classes: int,
        eos_coef: float = 0.1,              # no-object 类权重
        cost_class: float = 2.0,            # 匹配代价权重
        cost_dice: float = 1.0,
        cost_bce: float = 1.0,
        lambda_ce: float = 1.0,             # 损失权重
        lambda_bce: float = 1.0,
        lambda_dice: float = 1.0,
        aux_loss: bool = True,              # 是否对 aux_outputs 也计算同构损失
        pos_weight_bce: float = 1.0         # BCE 前景调权（前景稀疏可调大）
    ):
        super().__init__()
        self.num_classes = num_classes
        self.eos_coef = eos_coef
        self.lambda_ce = lambda_ce
        self.lambda_bce = lambda_bce
        self.lambda_dice = lambda_dice
        self.aux_loss = aux_loss
        self.pos_weight_bce = pos_weight_bce

        # 分类权重：最后一类为 no-object
        empty_weight = torch.ones(num_classes + 1)
        empty_weight[-1] = eos_coef
        self.register_buffer("empty_weight", empty_weight)

        # 匹配器
        self.matcher = HungarianMatcher(cost_class=cost_class, cost_dice=cost_dice, cost_bce=cost_bce)

    def _loss_labels(self, pred_logits: torch.Tensor, targets: List[Dict[str, torch.Tensor]],
                     indices: List[Tuple[torch.Tensor, torch.Tensor]]) -> torch.Tensor:
        """
        交叉熵（含 no-object）
        Args:
            pred_logits: [B,Q,C+1]
        """
        B, Q, C_plus_1 = pred_logits.shape
        target_classes = torch.full((B, Q), self.num_classes, dtype=torch.long, device=pred_logits.device)  # 全部 no-object
        for i, (idx_q, idx_g) in enumerate(indices):
            if idx_q.numel():
                target_classes[i, idx_q] = targets[i]["labels"][idx_g].long()

        # 确保 empty_weight 与预测类别数匹配
        if self.empty_weight.size(0) != C_plus_1:
            # 动态调整权重张量大小
            empty_weight = torch.ones(C_plus_1, device=pred_logits.device)
            empty_weight[-1] = self.empty_weight[-1].item()  # 保持 eos_coef
        else:
            empty_weight = self.empty_weight

        loss_ce = F.cross_entropy(pred_logits.transpose(1, 2), target_classes, weight=empty_weight)
        return loss_ce

    def _loss_masks(self, pred_masks: torch.Tensor, targets: List[Dict[str, torch.Tensor]],
                    indices: List[Tuple[torch.Tensor, torch.Tensor]]) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        掩码 BCE + Dice（仅对 matched queries）
        Args:
            pred_masks: [B,Q,H,W] logits
        Returns:
            (loss_bce, loss_dice)
        """
        loss_bce_all, loss_dice_all = [], []
        for i, (idx_q, idx_g) in enumerate(indices):
            if idx_q.numel() == 0:
                continue
            pred_i = pred_masks[i, idx_q]                    # [M,H,W] logits
            tgt_i = targets[i]["masks"][idx_g].float()       # [M,H,W] 0/1

            pred_i, tgt_i = _upsample_tgt_if_needed(pred_i, tgt_i)

            bce = bce_logits_loss(pred_i, tgt_i, pos_weight=self.pos_weight_bce)
            dsc = dice_loss(pred_i.sigmoid(), tgt_i)
            loss_bce_all.append(bce)
            loss_dice_all.append(dsc)

        if len(loss_bce_all) == 0:
            device = pred_masks.device
            return torch.tensor(0.0, device=device), torch.tensor(0.0, device=device)

        return torch.stack(loss_bce_all).mean(), torch.stack(loss_dice_all).mean()

    def _compute_branch_loss(self, outputs: Dict[str, torch.Tensor], targets: List[Dict[str, torch.Tensor]]) \
            -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        单个分支（主分支或 aux 分支）损失计算，返回（标量总损失，明细字典）
        """
        # 1) 匹配
        indices = self.matcher(outputs, targets)

        # 2) 分类
        loss_ce = self._loss_labels(outputs["pred_logits"], targets, indices)

        # 3) 掩码
        loss_bce, loss_dice = self._loss_masks(outputs["pred_masks"], targets, indices)

        # 4) 线性组合
        total = self.lambda_ce * loss_ce + self.lambda_bce * loss_bce + self.lambda_dice * loss_dice
        details = {
            "loss_ce": loss_ce.detach(),
            "loss_mask_bce": loss_bce.detach(),
            "loss_mask_dice": loss_dice.detach(),
        }
        return total, details

    def forward(self, outputs: Dict[str, torch.Tensor], targets: List[Dict[str, torch.Tensor]]) -> Dict[str, torch.Tensor]:
        """
        入口：
        Args:
            outputs: {
                'pred_logits': [B,Q,C+1],
                'pred_masks':  [B,Q,H,W],
                'aux_outputs': Optional[List[{'pred_logits','pred_masks'}]]  # 可无
            }
            targets: List[{'labels':[Ng], 'masks':[Ng,H,W]}]
        Returns:
            {'loss': 标量, 以及若干明细（可用于日志）}
        """
        # 主分支
        loss_main, details = self._compute_branch_loss(outputs, targets)
        total = loss_main

        # Aux 分支
        if self.aux_loss and "aux_outputs" in outputs and outputs["aux_outputs"] is not None:
            for i, aux in enumerate(outputs["aux_outputs"]):
                loss_aux, det_aux = self._compute_branch_loss(aux, targets)
                # 这里可按层衰减权重，如：0.5
                total = total + 0.5 * loss_aux
                # 明细带上后缀
                for k, v in det_aux.items():
                    details[f"{k}_aux{i}"] = v

        details["loss"] = total
        return details