Image: tile_0201.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8867
  Bounding Box: [333.60, 395.20, 559.20, 673.60]
  Mask Area: 238 pixels
  Mask Ratio: 0.0084
  Mask BBox: [31, 35, 46, 56]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8618
  Bounding Box: [828.00, 1271.20, 1056.80, 1511.20]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [69, 104, 86, 122]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8579
  Bounding Box: [866.40, 1632.00, 1095.20, 1888.00]
  Mask Area: 312 pixels
  Mask Ratio: 0.0111
  Mask BBox: [72, 132, 89, 151]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8569
  Bounding Box: [488.00, 578.00, 924.80, 1062.40]
  Mask Area: 738 pixels
  Mask Ratio: 0.0261
  Mask BBox: [43, 50, 76, 86]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8555
  Bounding Box: [1432.80, 325.80, 1844.80, 852.80]
  Mask Area: 749 pixels
  Mask Ratio: 0.0265
  Mask BBox: [116, 30, 148, 70]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8438
  Bounding Box: [173.20, 1817.60, 347.60, 2028.80]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [18, 146, 31, 162]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8315
  Bounding Box: [546.80, 1273.60, 744.40, 1512.00]
  Mask Area: 228 pixels
  Mask Ratio: 0.0081
  Mask BBox: [47, 104, 62, 122]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8281
  Bounding Box: [196.20, 0.00, 336.20, 174.40]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [20, 4, 30, 17]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8242
  Bounding Box: [848.80, 25.15, 1047.20, 204.80]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [71, 6, 85, 19]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8223
  Bounding Box: [1288.00, 962.40, 1435.20, 1108.00]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [105, 80, 115, 88]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8169
  Bounding Box: [1362.40, 1115.20, 1673.60, 1372.80]
  Mask Area: 338 pixels
  Mask Ratio: 0.0120
  Mask BBox: [111, 92, 134, 111]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8145
  Bounding Box: [1315.20, 789.60, 1416.00, 908.00]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [107, 66, 114, 74]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.8140
  Bounding Box: [1107.20, 1.95, 1310.40, 170.80]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [91, 5, 105, 17]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.8086
  Bounding Box: [997.60, 1412.00, 1340.00, 1740.80]
  Mask Area: 521 pixels
  Mask Ratio: 0.0185
  Mask BBox: [82, 115, 108, 139]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.8071
  Bounding Box: [1593.60, 1864.00, 1763.20, 2048.00]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [129, 150, 141, 165]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.8052
  Bounding Box: [1441.60, 1659.20, 1657.60, 1854.40]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [117, 134, 133, 148]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.8047
  Bounding Box: [976.00, 842.40, 1132.80, 1036.00]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [81, 70, 92, 84]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.8018
  Bounding Box: [992.80, 109.20, 1216.80, 371.60]
  Mask Area: 264 pixels
  Mask Ratio: 0.0094
  Mask BBox: [82, 13, 99, 33]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7910
  Bounding Box: [280.40, 98.90, 400.80, 256.80]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [26, 12, 35, 24]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7910
  Bounding Box: [83.40, 417.60, 248.60, 616.00]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [11, 37, 23, 52]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7900
  Bounding Box: [1153.60, 1172.00, 1382.40, 1364.00]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [96, 96, 111, 110]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7896
  Bounding Box: [1356.80, 1739.20, 1529.60, 1947.20]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [110, 140, 123, 156]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7881
  Bounding Box: [1067.20, 1768.00, 1217.60, 1969.60]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [88, 143, 99, 157]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7817
  Bounding Box: [807.20, 1416.00, 986.40, 1612.80]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [68, 115, 81, 129]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7734
  Bounding Box: [180.80, 350.80, 346.00, 488.40]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [19, 32, 30, 42]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7720
  Bounding Box: [615.20, 334.60, 927.20, 620.00]
  Mask Area: 393 pixels
  Mask Ratio: 0.0139
  Mask BBox: [53, 31, 76, 52]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7710
  Bounding Box: [174.00, 1075.20, 378.40, 1260.80]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [18, 88, 33, 101]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7676
  Bounding Box: [735.20, 1744.00, 877.60, 1926.40]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [62, 141, 72, 153]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7676
  Bounding Box: [26.90, 547.60, 229.20, 824.80]
  Mask Area: 245 pixels
  Mask Ratio: 0.0087
  Mask BBox: [7, 47, 21, 68]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7676
  Bounding Box: [290.40, 1715.20, 488.80, 1923.20]
  Mask Area: 202 pixels
  Mask Ratio: 0.0072
  Mask BBox: [27, 138, 42, 154]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7666
  Bounding Box: [985.60, 1180.80, 1172.80, 1340.80]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [81, 97, 93, 108]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7666
  Bounding Box: [108.60, 1258.40, 293.80, 1426.40]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [13, 103, 26, 114]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7646
  Bounding Box: [667.60, 1016.80, 864.00, 1306.40]
  Mask Area: 248 pixels
  Mask Ratio: 0.0088
  Mask BBox: [57, 84, 71, 106]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7637
  Bounding Box: [379.60, 924.80, 570.00, 1161.60]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [34, 78, 48, 92]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7598
  Bounding Box: [657.60, 145.00, 815.20, 339.80]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [56, 16, 67, 29]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7598
  Bounding Box: [344.80, 1894.40, 525.60, 2032.00]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [32, 152, 45, 162]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7573
  Bounding Box: [894.40, 323.20, 1070.40, 574.40]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [74, 30, 87, 48]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7534
  Bounding Box: [1643.20, 0.00, 1812.80, 224.80]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [133, 4, 145, 19]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7524
  Bounding Box: [389.20, 91.60, 558.80, 279.20]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [35, 12, 47, 24]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7515
  Bounding Box: [456.40, 1689.60, 787.20, 2016.00]
  Mask Area: 431 pixels
  Mask Ratio: 0.0153
  Mask BBox: [40, 136, 65, 159]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7500
  Bounding Box: [773.60, 138.20, 919.20, 278.60]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [65, 15, 75, 25]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7500
  Bounding Box: [1126.40, 1074.40, 1240.00, 1192.80]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [92, 88, 100, 96]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7500
  Bounding Box: [1800.00, 56.00, 2008.00, 318.00]
  Mask Area: 226 pixels
  Mask Ratio: 0.0080
  Mask BBox: [145, 9, 160, 28]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7378
  Bounding Box: [844.00, 935.20, 1071.20, 1200.80]
  Mask Area: 224 pixels
  Mask Ratio: 0.0079
  Mask BBox: [70, 78, 87, 97]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7363
  Bounding Box: [1167.20, 1365.60, 1301.60, 1527.20]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [96, 111, 104, 122]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7354
  Bounding Box: [1221.60, 1788.80, 1360.80, 2048.00]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [100, 144, 110, 163]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7349
  Bounding Box: [303.60, 824.80, 464.40, 943.20]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [28, 69, 38, 77]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7339
  Bounding Box: [1415.20, 900.00, 1640.00, 1146.40]
  Mask Area: 252 pixels
  Mask Ratio: 0.0089
  Mask BBox: [115, 75, 131, 93]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7319
  Bounding Box: [1030.40, 0.00, 1150.40, 125.40]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [85, 4, 93, 13]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7300
  Bounding Box: [1461.60, 1902.40, 1596.80, 2027.20]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [119, 153, 128, 162]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.7271
  Bounding Box: [5.45, 816.80, 120.00, 1055.20]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [5, 68, 13, 86]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.7227
  Bounding Box: [1560.80, 1076.80, 1736.00, 1222.40]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [127, 89, 138, 97]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.7183
  Bounding Box: [898.40, 580.80, 1042.40, 787.20]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [75, 50, 85, 65]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.7178
  Bounding Box: [1271.20, 1332.80, 1530.40, 1598.40]
  Mask Area: 308 pixels
  Mask Ratio: 0.0109
  Mask BBox: [104, 109, 123, 128]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.7173
  Bounding Box: [227.20, 498.40, 342.00, 616.00]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [22, 43, 29, 52]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.7168
  Bounding Box: [1710.40, 1216.80, 1844.80, 1405.60]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [138, 100, 148, 113]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.7153
  Bounding Box: [1539.20, 1413.60, 1817.60, 1628.80]
  Mask Area: 237 pixels
  Mask Ratio: 0.0084
  Mask BBox: [125, 115, 145, 131]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.7139
  Bounding Box: [1752.00, 1945.60, 1880.00, 2032.00]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [141, 156, 149, 162]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.7119
  Bounding Box: [577.20, 1023.20, 663.60, 1148.00]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [50, 84, 55, 92]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.7109
  Bounding Box: [319.60, 1172.00, 514.00, 1468.00]
  Mask Area: 274 pixels
  Mask Ratio: 0.0097
  Mask BBox: [29, 96, 44, 118]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.7085
  Bounding Box: [1712.00, 239.60, 1894.40, 511.60]
  Mask Area: 210 pixels
  Mask Ratio: 0.0074
  Mask BBox: [138, 23, 151, 43]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6958
  Bounding Box: [1302.40, 1638.40, 1374.40, 1708.80]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [106, 132, 111, 137]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6934
  Bounding Box: [218.60, 217.00, 327.40, 339.80]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [22, 21, 29, 29]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6929
  Bounding Box: [16.25, 1020.80, 167.40, 1312.00]
  Mask Area: 191 pixels
  Mask Ratio: 0.0068
  Mask BBox: [6, 84, 17, 106]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6890
  Bounding Box: [1454.40, 281.00, 1652.80, 470.40]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [118, 26, 132, 40]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6880
  Bounding Box: [0.00, 1817.60, 105.40, 1977.60]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [4, 146, 12, 156]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6841
  Bounding Box: [1124.80, 1958.40, 1262.40, 2038.40]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [92, 157, 100, 163]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6802
  Bounding Box: [204.20, 620.00, 522.40, 864.80]
  Mask Area: 304 pixels
  Mask Ratio: 0.0108
  Mask BBox: [20, 53, 44, 71]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6772
  Bounding Box: [520.00, 1140.80, 693.60, 1283.20]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [45, 94, 57, 102]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6733
  Bounding Box: [1536.00, 1547.20, 1699.20, 1707.20]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [124, 126, 136, 137]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6704
  Bounding Box: [1562.40, 824.80, 1660.80, 946.40]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [127, 69, 133, 76]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6670
  Bounding Box: [1280.00, 1635.20, 1361.60, 1705.60]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [105, 132, 110, 137]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6650
  Bounding Box: [1505.60, 5.10, 1660.80, 223.20]
  Mask Area: 138 pixels
  Mask Ratio: 0.0049
  Mask BBox: [123, 5, 133, 21]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6650
  Bounding Box: [648.80, 0.20, 793.60, 122.80]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [55, 5, 65, 13]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6641
  Bounding Box: [1958.40, 395.60, 2044.80, 576.40]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [157, 35, 163, 49]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6626
  Bounding Box: [1136.00, 910.40, 1244.80, 1025.60]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [93, 76, 100, 84]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6626
  Bounding Box: [1.70, 1436.80, 189.60, 1734.40]
  Mask Area: 236 pixels
  Mask Ratio: 0.0084
  Mask BBox: [5, 117, 18, 139]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6606
  Bounding Box: [1825.60, 1267.20, 2014.40, 1520.00]
  Mask Area: 244 pixels
  Mask Ratio: 0.0086
  Mask BBox: [147, 103, 161, 122]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6572
  Bounding Box: [1323.20, 1723.20, 1425.60, 1806.40]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [108, 139, 115, 145]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6562
  Bounding Box: [223.80, 1418.40, 404.80, 1699.20]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [22, 115, 35, 136]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6538
  Bounding Box: [70.50, 1916.80, 221.20, 2041.60]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [10, 154, 21, 163]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6436
  Bounding Box: [508.40, 33.00, 626.00, 182.20]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [44, 7, 52, 18]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6396
  Bounding Box: [1492.80, 722.00, 1601.60, 859.20]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [121, 61, 129, 71]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6382
  Bounding Box: [1302.40, 1066.40, 1387.20, 1196.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [106, 88, 112, 96]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6333
  Bounding Box: [31.45, 1692.80, 148.20, 1830.40]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [7, 137, 13, 146]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6313
  Bounding Box: [784.80, 1912.00, 914.40, 2033.60]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [66, 154, 75, 162]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.6294
  Bounding Box: [488.00, 595.20, 607.20, 737.60]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [43, 51, 50, 61]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.6260
  Bounding Box: [1292.80, 10.00, 1540.80, 392.40]
  Mask Area: 388 pixels
  Mask Ratio: 0.0137
  Mask BBox: [105, 5, 124, 34]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.6196
  Bounding Box: [808.80, 271.60, 927.20, 390.80]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [68, 26, 75, 33]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.6167
  Bounding Box: [1984.00, 1704.00, 2038.40, 1857.60]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [159, 138, 163, 149]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.6133
  Bounding Box: [1872.00, 4.00, 1987.20, 102.00]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [151, 5, 159, 11]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.6025
  Bounding Box: [7.00, 335.20, 150.60, 515.20]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [5, 31, 15, 44]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.6006
  Bounding Box: [964.80, 1966.40, 1112.00, 2040.00]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [80, 158, 90, 163]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5991
  Bounding Box: [786.40, 0.00, 928.80, 72.40]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [66, 4, 76, 9]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5938
  Bounding Box: [7.60, 0.00, 135.00, 89.80]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [5, 4, 14, 11]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5933
  Bounding Box: [109.00, 833.60, 340.60, 1139.20]
  Mask Area: 271 pixels
  Mask Ratio: 0.0096
  Mask BBox: [13, 70, 30, 92]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5825
  Bounding Box: [408.40, 1536.00, 669.20, 1753.60]
  Mask Area: 214 pixels
  Mask Ratio: 0.0076
  Mask BBox: [36, 124, 56, 140]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5781
  Bounding Box: [1712.00, 1646.40, 2000.00, 2020.80]
  Mask Area: 465 pixels
  Mask Ratio: 0.0165
  Mask BBox: [138, 133, 160, 161]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5767
  Bounding Box: [1707.20, 1034.40, 1860.80, 1234.40]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [138, 85, 149, 100]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5635
  Bounding Box: [266.20, 1249.60, 343.00, 1336.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [25, 102, 30, 108]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5630
  Bounding Box: [1841.60, 783.20, 2046.40, 1037.60]
  Mask Area: 256 pixels
  Mask Ratio: 0.0091
  Mask BBox: [148, 66, 163, 85]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5615
  Bounding Box: [1525.60, 1348.00, 1640.00, 1429.60]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [124, 110, 132, 115]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5615
  Bounding Box: [1891.20, 262.40, 2028.80, 456.80]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [152, 25, 162, 39]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5605
  Bounding Box: [1437.60, 801.60, 1554.40, 899.20]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [117, 67, 125, 73]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5601
  Bounding Box: [896.00, 832.80, 988.80, 952.80]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [74, 70, 81, 78]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5557
  Bounding Box: [1312.00, 1955.20, 1480.00, 2035.20]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [107, 157, 119, 162]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5527
  Bounding Box: [550.00, 0.00, 657.20, 67.20]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [47, 4, 55, 8]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5527
  Bounding Box: [1651.20, 874.40, 1817.60, 1024.80]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [134, 73, 145, 84]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5459
  Bounding Box: [111.70, 1659.20, 289.20, 1819.20]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [13, 134, 26, 146]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5405
  Bounding Box: [1370.40, 689.60, 1440.80, 780.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [112, 58, 116, 64]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5347
  Bounding Box: [1066.40, 364.40, 1266.40, 625.20]
  Mask Area: 221 pixels
  Mask Ratio: 0.0078
  Mask BBox: [88, 33, 102, 52]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5327
  Bounding Box: [150.40, 285.80, 237.60, 371.60]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [16, 27, 22, 33]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5293
  Bounding Box: [1654.40, 808.00, 1776.00, 924.80]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [134, 68, 142, 76]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5239
  Bounding Box: [8.90, 1375.20, 71.80, 1492.00]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [5, 112, 8, 120]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5215
  Bounding Box: [1058.40, 1062.40, 1125.60, 1182.40]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [87, 87, 91, 96]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5215
  Bounding Box: [1262.40, 662.80, 1382.40, 803.20]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [103, 56, 111, 66]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5186
  Bounding Box: [1739.20, 1383.20, 1825.60, 1456.80]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [140, 113, 146, 117]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5186
  Bounding Box: [1116.80, 1006.40, 1243.20, 1088.00]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [92, 83, 101, 88]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5176
  Bounding Box: [1953.60, 1964.80, 2048.00, 2035.20]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [157, 158, 164, 162]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.5083
  Bounding Box: [1012.00, 684.80, 1111.20, 785.60]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [84, 58, 90, 65]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.5044
  Bounding Box: [1237.60, 1044.80, 1316.00, 1161.60]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [101, 86, 106, 94]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.5039
  Bounding Box: [276.20, 907.20, 375.60, 1155.20]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [26, 75, 33, 94]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.5005
  Bounding Box: [329.20, 14.10, 433.20, 107.90]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [30, 6, 37, 11]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.4971
  Bounding Box: [1976.00, 1678.40, 2036.80, 1915.20]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [159, 136, 163, 153]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.4956
  Bounding Box: [724.80, 1312.00, 849.60, 1598.40]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [61, 107, 70, 128]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.4932
  Bounding Box: [1848.00, 1452.80, 2048.00, 1724.80]
  Mask Area: 270 pixels
  Mask Ratio: 0.0096
  Mask BBox: [149, 118, 164, 138]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.4917
  Bounding Box: [0.00, 168.80, 153.60, 477.60]
  Mask Area: 249 pixels
  Mask Ratio: 0.0088
  Mask BBox: [4, 18, 15, 41]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4871
  Bounding Box: [532.40, 1718.40, 871.20, 1977.60]
  Mask Area: 418 pixels
  Mask Ratio: 0.0148
  Mask BBox: [46, 139, 72, 158]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.4868
  Bounding Box: [910.40, 1136.00, 1028.80, 1268.80]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [76, 93, 84, 103]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4849
  Bounding Box: [1968.00, 1696.00, 2048.00, 1833.60]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [158, 137, 164, 147]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4805
  Bounding Box: [0.00, 263.80, 172.40, 526.40]
  Mask Area: 235 pixels
  Mask Ratio: 0.0083
  Mask BBox: [4, 25, 17, 45]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4785
  Bounding Box: [1366.40, 440.80, 1534.40, 766.40]
  Mask Area: 171 pixels
  Mask Ratio: 0.0061
  Mask BBox: [111, 39, 121, 63]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4775
  Bounding Box: [216.60, 1737.60, 471.20, 2006.40]
  Mask Area: 332 pixels
  Mask Ratio: 0.0118
  Mask BBox: [21, 140, 40, 160]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4771
  Bounding Box: [1694.40, 1222.40, 1892.80, 1430.40]
  Mask Area: 173 pixels
  Mask Ratio: 0.0061
  Mask BBox: [137, 100, 151, 115]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4761
  Bounding Box: [1280.00, 505.60, 1372.80, 664.00]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [104, 44, 110, 55]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4746
  Bounding Box: [1391.20, 1676.80, 1601.60, 1920.00]
  Mask Area: 228 pixels
  Mask Ratio: 0.0081
  Mask BBox: [113, 135, 129, 153]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4736
  Bounding Box: [524.00, 287.40, 656.80, 502.40]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [45, 27, 55, 43]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4702
  Bounding Box: [878.40, 855.20, 1113.60, 1130.40]
  Mask Area: 270 pixels
  Mask Ratio: 0.0096
  Mask BBox: [73, 71, 90, 92]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4509
  Bounding Box: [1160.00, 286.20, 1344.00, 427.20]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [95, 27, 108, 37]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4490
  Bounding Box: [1312.80, 0.00, 1428.00, 96.80]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [107, 4, 115, 9]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4480
  Bounding Box: [161.60, 838.40, 385.20, 1169.60]
  Mask Area: 297 pixels
  Mask Ratio: 0.0105
  Mask BBox: [17, 70, 34, 95]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4431
  Bounding Box: [671.60, 140.80, 903.20, 330.40]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [57, 15, 74, 29]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4407
  Bounding Box: [728.80, 1667.20, 888.80, 1907.20]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [61, 135, 73, 152]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4402
  Bounding Box: [1988.80, 1803.20, 2046.40, 1953.60]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [160, 145, 163, 156]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4377
  Bounding Box: [1644.80, 1347.20, 1731.20, 1435.20]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [133, 110, 139, 115]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4377
  Bounding Box: [1755.20, 1932.80, 1841.60, 2041.60]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [142, 155, 147, 163]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4377
  Bounding Box: [949.60, 459.60, 1077.60, 606.80]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [79, 40, 87, 51]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4368
  Bounding Box: [56.20, 358.40, 239.80, 590.40]
  Mask Area: 197 pixels
  Mask Ratio: 0.0070
  Mask BBox: [9, 32, 22, 50]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4346
  Bounding Box: [1985.60, 992.00, 2043.20, 1102.40]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [160, 82, 163, 90]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4297
  Bounding Box: [1202.40, 903.20, 1282.40, 992.80]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [98, 75, 104, 81]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4292
  Bounding Box: [6.20, 1271.20, 96.80, 1400.80]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [5, 104, 11, 113]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4248
  Bounding Box: [1955.20, 380.80, 2035.20, 526.40]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [157, 34, 162, 45]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4233
  Bounding Box: [0.80, 1284.80, 131.00, 1419.20]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [5, 105, 14, 114]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4231
  Bounding Box: [183.20, 569.60, 292.00, 690.40]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [19, 49, 26, 56]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4231
  Bounding Box: [327.20, 0.00, 454.40, 80.80]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [30, 3, 39, 10]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4231
  Bounding Box: [1516.80, 0.00, 1760.00, 211.60]
  Mask Area: 256 pixels
  Mask Ratio: 0.0091
  Mask BBox: [123, 4, 141, 20]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4216
  Bounding Box: [213.80, 0.00, 368.00, 143.20]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [21, 4, 32, 15]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4209
  Bounding Box: [265.60, 679.20, 497.60, 959.20]
  Mask Area: 258 pixels
  Mask Ratio: 0.0091
  Mask BBox: [25, 58, 42, 78]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4202
  Bounding Box: [896.80, 206.80, 1023.20, 332.00]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [75, 21, 82, 29]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4197
  Bounding Box: [119.80, 990.40, 202.20, 1174.40]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [14, 82, 19, 95]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4180
  Bounding Box: [1351.20, 1510.40, 1517.60, 1676.80]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [110, 124, 122, 134]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.4163
  Bounding Box: [1691.20, 1023.20, 1924.80, 1242.40]
  Mask Area: 264 pixels
  Mask Ratio: 0.0094
  Mask BBox: [137, 84, 154, 101]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.4116
  Bounding Box: [742.80, 1641.60, 875.20, 1785.60]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [63, 133, 70, 143]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.4087
  Bounding Box: [448.80, 871.20, 532.80, 957.60]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [40, 73, 45, 77]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.4060
  Bounding Box: [1870.40, 0.00, 2033.60, 86.50]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [151, 3, 162, 10]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.4045
  Bounding Box: [279.60, 850.40, 458.00, 962.40]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [27, 71, 38, 79]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.4036
  Bounding Box: [2000.00, 1382.40, 2048.00, 1520.00]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [161, 112, 164, 122]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.4016
  Bounding Box: [570.40, 194.60, 644.00, 307.40]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [49, 20, 54, 25]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.3977
  Bounding Box: [200.40, 508.40, 349.20, 647.60]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [20, 44, 31, 54]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.3950
  Bounding Box: [764.80, 28.50, 854.40, 133.70]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [64, 7, 70, 14]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.3943
  Bounding Box: [236.20, 198.40, 349.80, 335.20]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [23, 20, 31, 29]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3928
  Bounding Box: [800.00, 55.50, 889.60, 152.50]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [67, 9, 72, 15]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3918
  Bounding Box: [518.00, 7.20, 646.80, 135.80]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [45, 5, 54, 14]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3918
  Bounding Box: [1104.80, 1702.40, 1269.60, 1811.20]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [91, 137, 103, 145]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3906
  Bounding Box: [0.00, 0.00, 148.60, 118.00]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [2, 4, 15, 13]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3867
  Bounding Box: [1053.60, 1300.80, 1189.60, 1411.20]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [87, 106, 96, 114]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3862
  Bounding Box: [866.40, 1980.80, 988.00, 2048.00]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [72, 159, 81, 163]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3840
  Bounding Box: [1362.40, 1766.40, 1578.40, 2006.40]
  Mask Area: 201 pixels
  Mask Ratio: 0.0071
  Mask BBox: [111, 142, 127, 160]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3821
  Bounding Box: [770.40, 1361.60, 960.80, 1622.40]
  Mask Area: 233 pixels
  Mask Ratio: 0.0083
  Mask BBox: [65, 111, 79, 130]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3794
  Bounding Box: [217.40, 1485.60, 384.80, 1747.20]
  Mask Area: 206 pixels
  Mask Ratio: 0.0073
  Mask BBox: [21, 121, 34, 140]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3762
  Bounding Box: [213.40, 223.00, 351.40, 376.40]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [21, 22, 31, 33]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3752
  Bounding Box: [772.00, 15.95, 887.20, 123.60]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [65, 6, 73, 13]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3743
  Bounding Box: [1.70, 1238.40, 84.30, 1374.40]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [5, 101, 10, 111]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3740
  Bounding Box: [1380.00, 411.20, 1670.40, 785.60]
  Mask Area: 482 pixels
  Mask Ratio: 0.0171
  Mask BBox: [112, 37, 134, 65]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3706
  Bounding Box: [429.20, 1456.00, 584.40, 1587.20]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [38, 118, 48, 126]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3679
  Bounding Box: [0.00, 176.40, 146.20, 376.40]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [4, 18, 15, 33]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3667
  Bounding Box: [1630.40, 822.40, 1755.20, 907.20]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [132, 69, 141, 74]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3667
  Bounding Box: [820.00, 28.00, 1029.60, 300.80]
  Mask Area: 199 pixels
  Mask Ratio: 0.0071
  Mask BBox: [69, 7, 84, 26]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3652
  Bounding Box: [1365.60, 666.40, 1456.80, 785.60]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [111, 57, 117, 65]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3638
  Bounding Box: [1112.00, 691.20, 1324.80, 916.80]
  Mask Area: 231 pixels
  Mask Ratio: 0.0082
  Mask BBox: [91, 58, 107, 75]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3635
  Bounding Box: [1424.00, 657.60, 1572.80, 840.80]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [116, 56, 126, 69]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3625
  Bounding Box: [62.20, 1819.20, 193.40, 2020.80]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [9, 147, 19, 161]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3618
  Bounding Box: [412.80, 1441.60, 609.60, 1628.80]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [37, 117, 51, 131]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3611
  Bounding Box: [902.40, 1875.20, 1008.00, 1984.00]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [75, 151, 82, 158]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3596
  Bounding Box: [1287.20, 941.60, 1543.20, 1112.80]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [105, 78, 124, 90]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3594
  Bounding Box: [360.40, 1113.60, 499.60, 1408.00]
  Mask Area: 221 pixels
  Mask Ratio: 0.0078
  Mask BBox: [33, 91, 43, 113]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3594
  Bounding Box: [1867.20, 507.60, 2048.00, 756.40]
  Mask Area: 279 pixels
  Mask Ratio: 0.0099
  Mask BBox: [150, 44, 164, 63]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3594
  Bounding Box: [1251.20, 77.80, 1537.60, 335.80]
  Mask Area: 339 pixels
  Mask Ratio: 0.0120
  Mask BBox: [102, 11, 124, 30]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3591
  Bounding Box: [420.00, 26.80, 619.20, 238.00]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [37, 7, 52, 22]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3584
  Bounding Box: [542.40, 1092.00, 679.20, 1277.60]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [47, 90, 57, 102]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3577
  Bounding Box: [1816.00, 1600.00, 1902.40, 1715.20]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [146, 129, 152, 137]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3523
  Bounding Box: [100.20, 1572.80, 295.00, 1809.60]
  Mask Area: 243 pixels
  Mask Ratio: 0.0086
  Mask BBox: [12, 127, 27, 145]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3513
  Bounding Box: [1420.80, 343.60, 1472.00, 431.60]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [115, 31, 118, 37]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3513
  Bounding Box: [1457.60, 0.00, 1654.40, 277.60]
  Mask Area: 276 pixels
  Mask Ratio: 0.0098
  Mask BBox: [118, 3, 133, 25]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3501
  Bounding Box: [314.00, 96.40, 546.00, 266.00]
  Mask Area: 203 pixels
  Mask Ratio: 0.0072
  Mask BBox: [29, 12, 46, 24]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3440
  Bounding Box: [768.00, 152.30, 923.20, 370.40]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [64, 16, 76, 32]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3440
  Bounding Box: [119.80, 1252.80, 380.00, 1436.80]
  Mask Area: 198 pixels
  Mask Ratio: 0.0070
  Mask BBox: [14, 102, 33, 116]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3416
  Bounding Box: [1188.80, 464.80, 1369.60, 680.00]
  Mask Area: 202 pixels
  Mask Ratio: 0.0072
  Mask BBox: [97, 41, 110, 57]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3401
  Bounding Box: [222.00, 383.60, 352.80, 626.80]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [22, 34, 31, 52]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3372
  Bounding Box: [458.80, 1100.00, 576.40, 1175.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [41, 90, 49, 95]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3369
  Bounding Box: [1127.20, 918.40, 1253.60, 1072.00]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [93, 76, 100, 87]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3350
  Bounding Box: [317.20, 0.00, 433.20, 51.50]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [29, 3, 37, 8]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3350
  Bounding Box: [317.20, 8.90, 433.20, 77.10]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [29, 5, 37, 10]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3345
  Bounding Box: [32.25, 1416.80, 172.60, 1501.60]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [7, 115, 17, 120]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3337
  Bounding Box: [1862.40, 1001.60, 1996.80, 1118.40]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [150, 83, 158, 90]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1969.60, 81.00, 2048.00, 287.80]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [158, 11, 164, 26]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1518.40, 223.20, 1675.20, 393.60]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [123, 22, 134, 34]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1947.20, 60.70, 2048.00, 225.60]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [157, 9, 164, 21]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3296
  Bounding Box: [41.35, 1654.40, 211.00, 1830.40]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [8, 134, 20, 146]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3293
  Bounding Box: [2.85, 651.20, 67.00, 784.80]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [5, 55, 8, 65]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [1130.40, 864.00, 1266.40, 1027.20]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [93, 72, 102, 84]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3254
  Bounding Box: [1299.20, 1707.20, 1404.80, 1803.20]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [106, 138, 113, 144]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3254
  Bounding Box: [1299.20, 1732.80, 1404.80, 1828.80]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [106, 140, 113, 145]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3247
  Bounding Box: [1638.40, 968.80, 1696.00, 1069.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [132, 80, 136, 87]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3242
  Bounding Box: [1548.00, 848.80, 1651.20, 957.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [125, 71, 132, 76]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3235
  Bounding Box: [1575.20, 846.40, 1673.60, 958.40]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [128, 71, 134, 76]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [1806.40, 1025.60, 2001.60, 1286.40]
  Mask Area: 209 pixels
  Mask Ratio: 0.0074
  Mask BBox: [146, 85, 158, 104]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3201
  Bounding Box: [220.00, 464.40, 326.00, 593.20]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [22, 41, 29, 50]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3201
  Bounding Box: [943.20, 1944.00, 1088.80, 2030.40]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [78, 156, 89, 162]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3186
  Bounding Box: [1392.80, 656.00, 1516.00, 782.40]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [113, 56, 122, 65]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3186
  Bounding Box: [18.05, 1407.20, 135.00, 1508.00]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [6, 114, 14, 121]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3169
  Bounding Box: [1538.40, 239.00, 1664.00, 355.80]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [125, 23, 133, 31]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3167
  Bounding Box: [909.60, 821.60, 1012.00, 944.80]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [76, 69, 81, 77]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3147
  Bounding Box: [1375.20, 564.00, 1538.40, 789.60]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [112, 49, 124, 65]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3127
  Bounding Box: [1426.40, 1913.60, 1581.60, 2048.00]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [116, 154, 127, 164]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3120
  Bounding Box: [1138.40, 279.20, 1365.60, 491.20]
  Mask Area: 260 pixels
  Mask Ratio: 0.0092
  Mask BBox: [93, 26, 110, 42]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3115
  Bounding Box: [25.20, 832.80, 162.40, 1101.60]
  Mask Area: 220 pixels
  Mask Ratio: 0.0078
  Mask BBox: [6, 70, 16, 90]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3108
  Bounding Box: [760.40, 161.20, 902.40, 307.60]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [64, 17, 74, 28]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.3101
  Bounding Box: [930.40, 542.80, 981.60, 603.60]
  Mask Area: 11 pixels
  Mask Ratio: 0.0004
  Mask BBox: [77, 47, 79, 51]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [1373.60, 438.40, 1540.00, 650.40]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [112, 39, 121, 54]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.3091
  Bounding Box: [558.40, 1524.80, 745.60, 1675.20]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [48, 124, 62, 134]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.3069
  Bounding Box: [1379.20, 685.20, 1460.80, 770.40]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [112, 58, 118, 64]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.3069
  Bounding Box: [1353.60, 710.80, 1435.20, 796.00]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [110, 60, 116, 66]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.3069
  Bounding Box: [1379.20, 710.80, 1460.80, 796.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [112, 60, 118, 66]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.3052
  Bounding Box: [1404.80, 618.40, 1537.60, 776.80]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [114, 53, 124, 64]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.3049
  Bounding Box: [1593.60, 840.80, 1772.80, 973.60]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [129, 70, 142, 80]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.3044
  Bounding Box: [242.40, 470.40, 354.40, 592.80]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [23, 41, 29, 50]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.3044
  Bounding Box: [1228.80, 896.00, 1304.00, 974.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [100, 74, 105, 80]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.3044
  Bounding Box: [1228.80, 921.60, 1304.00, 1000.00]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [100, 76, 105, 81]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.3044
  Bounding Box: [92.40, 440.00, 286.00, 699.20]
  Mask Area: 198 pixels
  Mask Ratio: 0.0070
  Mask BBox: [12, 39, 26, 58]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.3040
  Bounding Box: [630.00, 1979.20, 756.40, 2033.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [54, 159, 63, 162]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.3030
  Bounding Box: [1323.20, 774.40, 1521.60, 896.00]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [108, 65, 122, 73]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.3025
  Bounding Box: [1750.40, 1843.20, 1932.80, 2048.00]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [141, 148, 154, 165]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.3020
  Bounding Box: [200.00, 358.80, 376.80, 520.40]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [20, 33, 30, 44]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.3005
  Bounding Box: [146.40, 156.80, 213.20, 275.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [16, 17, 20, 25]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.3005
  Bounding Box: [222.60, 1433.60, 494.40, 1644.80]
  Mask Area: 291 pixels
  Mask Ratio: 0.0103
  Mask BBox: [22, 116, 42, 132]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.2998
  Bounding Box: [1987.20, 1153.60, 2048.00, 1372.80]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [160, 95, 164, 111]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.2998
  Bounding Box: [518.40, 9.15, 672.00, 188.80]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [45, 5, 56, 18]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.2981
  Bounding Box: [1454.40, 734.00, 1596.80, 888.00]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [118, 62, 128, 73]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.2979
  Bounding Box: [42.95, 1897.60, 200.00, 2022.40]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [8, 153, 19, 161]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.2966
  Bounding Box: [1644.80, 795.20, 1756.80, 884.80]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [133, 67, 141, 73]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.2966
  Bounding Box: [1441.60, 634.80, 1545.60, 768.00]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [117, 54, 124, 63]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2966
  Bounding Box: [1179.20, 492.80, 1323.20, 652.00]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [97, 43, 107, 54]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2966
  Bounding Box: [834.40, 237.20, 996.00, 379.60]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [70, 23, 81, 33]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2966
  Bounding Box: [1632.00, 1540.80, 2048.00, 1992.00]
  Mask Area: 622 pixels
  Mask Ratio: 0.0220
  Mask BBox: [132, 125, 163, 159]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2961
  Bounding Box: [110.20, 828.80, 252.60, 1067.20]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [13, 69, 23, 87]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2961
  Bounding Box: [1133.60, 1192.80, 1242.40, 1301.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [93, 98, 101, 105]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2959
  Bounding Box: [1676.80, 790.40, 1779.20, 880.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [135, 66, 142, 72]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2949
  Bounding Box: [351.00, 0.00, 449.60, 44.95]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [32, 3, 39, 7]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2949
  Bounding Box: [351.00, 8.85, 449.60, 70.60]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [32, 5, 39, 9]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2949
  Bounding Box: [996.80, 727.20, 1096.00, 850.40]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [82, 61, 89, 70]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2942
  Bounding Box: [1132.80, 898.40, 1249.60, 989.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [93, 75, 100, 81]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2942
  Bounding Box: [1158.40, 924.00, 1275.20, 1015.20]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [95, 77, 103, 83]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2930
  Bounding Box: [1204.80, 378.40, 1420.80, 648.80]
  Mask Area: 243 pixels
  Mask Ratio: 0.0086
  Mask BBox: [99, 34, 114, 54]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2910
  Bounding Box: [1515.20, 702.80, 1620.80, 847.20]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [123, 60, 130, 70]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2891
  Bounding Box: [1276.80, 730.40, 1419.20, 901.60]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [104, 62, 114, 73]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2891
  Bounding Box: [12.40, 1721.60, 135.60, 1984.00]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [5, 139, 14, 158]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2886
  Bounding Box: [1230.40, 166.20, 1371.20, 279.40]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [101, 17, 109, 25]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2883
  Bounding Box: [1208.00, 180.60, 1307.20, 285.80]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [99, 19, 106, 26]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2878
  Bounding Box: [1939.20, 1934.40, 2048.00, 2048.00]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [156, 156, 164, 164]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2869
  Bounding Box: [1918.40, 993.60, 2048.00, 1084.80]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [154, 82, 163, 88]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2866
  Bounding Box: [1825.60, 1092.80, 1976.00, 1283.20]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [147, 90, 158, 104]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2854
  Bounding Box: [1404.80, 951.20, 1664.00, 1229.60]
  Mask Area: 305 pixels
  Mask Ratio: 0.0108
  Mask BBox: [114, 79, 133, 100]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2849
  Bounding Box: [187.20, 828.00, 441.60, 1037.60]
  Mask Area: 204 pixels
  Mask Ratio: 0.0072
  Mask BBox: [19, 69, 38, 85]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2847
  Bounding Box: [1266.40, 1632.00, 1372.00, 1737.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [105, 132, 111, 139]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2847
  Bounding Box: [565.60, 1984.00, 694.40, 2038.40]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [49, 159, 58, 163]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2837
  Bounding Box: [1202.40, 282.80, 1436.00, 529.20]
  Mask Area: 299 pixels
  Mask Ratio: 0.0106
  Mask BBox: [98, 27, 116, 45]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2834
  Bounding Box: [1656.00, 872.80, 1844.80, 1136.80]
  Mask Area: 224 pixels
  Mask Ratio: 0.0079
  Mask BBox: [134, 73, 148, 92]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2832
  Bounding Box: [829.60, 797.60, 909.60, 912.80]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [69, 67, 75, 75]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2827
  Bounding Box: [656.00, 1984.00, 792.80, 2032.00]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [56, 159, 65, 162]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2822
  Bounding Box: [1905.60, 386.40, 2048.00, 597.60]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [153, 35, 164, 50]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2820
  Bounding Box: [859.20, 820.00, 971.20, 936.80]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [72, 69, 79, 77]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2815
  Bounding Box: [1168.00, 890.40, 1268.80, 984.80]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [96, 74, 103, 80]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2815
  Bounding Box: [1301.60, 804.80, 1420.00, 1036.80]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [106, 67, 114, 84]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2810
  Bounding Box: [1049.60, 8.85, 1166.40, 151.60]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [86, 5, 95, 13]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2803
  Bounding Box: [1468.00, 1553.60, 1541.60, 1672.00]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [119, 126, 124, 134]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2798
  Bounding Box: [51.15, 33.90, 198.20, 222.80]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [8, 7, 19, 21]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2791
  Bounding Box: [436.80, 1494.40, 742.40, 1737.60]
  Mask Area: 227 pixels
  Mask Ratio: 0.0080
  Mask BBox: [39, 121, 60, 139]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2786
  Bounding Box: [15.75, 2.20, 180.60, 187.80]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [6, 5, 18, 18]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2773
  Bounding Box: [1900.80, 297.20, 2048.00, 526.80]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [153, 28, 164, 45]

