Image: tile_0116.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8755
  Bounding Box: [786.40, 1532.80, 1122.40, 2000.00]
  Mask Area: 681 pixels
  Mask Ratio: 0.0241
  Mask BBox: [66, 124, 90, 159]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8721
  Bounding Box: [24.05, 759.20, 216.40, 983.20]
  Mask Area: 200 pixels
  Mask Ratio: 0.0071
  Mask BBox: [6, 64, 20, 80]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8594
  Bounding Box: [1339.20, 1443.20, 1644.80, 1652.80]
  Mask Area: 262 pixels
  Mask Ratio: 0.0093
  Mask BBox: [109, 117, 132, 133]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8501
  Bounding Box: [384.80, 1572.80, 624.80, 1768.00]
  Mask Area: 214 pixels
  Mask Ratio: 0.0076
  Mask BBox: [35, 127, 52, 142]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8457
  Bounding Box: [1742.40, 1571.20, 1908.80, 1756.80]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [141, 127, 153, 141]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8433
  Bounding Box: [139.30, 889.60, 332.00, 1134.40]
  Mask Area: 219 pixels
  Mask Ratio: 0.0078
  Mask BBox: [15, 74, 29, 92]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8364
  Bounding Box: [300.40, 5.55, 478.00, 163.00]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [28, 5, 41, 16]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8281
  Bounding Box: [916.80, 1308.80, 1048.00, 1590.40]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [76, 107, 85, 127]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8276
  Bounding Box: [1444.00, 1308.00, 1657.60, 1416.80]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [117, 107, 130, 114]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8271
  Bounding Box: [1399.20, 1630.40, 1606.40, 1825.60]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [114, 133, 129, 146]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8271
  Bounding Box: [1053.60, 630.40, 1303.20, 963.20]
  Mask Area: 329 pixels
  Mask Ratio: 0.0117
  Mask BBox: [87, 54, 105, 78]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8242
  Bounding Box: [1472.80, 21.00, 1662.40, 222.60]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [120, 6, 133, 21]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.8174
  Bounding Box: [1522.40, 1168.00, 1624.00, 1321.60]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [123, 96, 130, 107]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.8169
  Bounding Box: [1592.00, 844.00, 1819.20, 1071.20]
  Mask Area: 212 pixels
  Mask Ratio: 0.0075
  Mask BBox: [129, 70, 146, 87]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.8145
  Bounding Box: [6.50, 1475.20, 320.40, 1824.00]
  Mask Area: 496 pixels
  Mask Ratio: 0.0176
  Mask BBox: [5, 120, 29, 145]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.8066
  Bounding Box: [763.20, 210.00, 1044.80, 414.40]
  Mask Area: 276 pixels
  Mask Ratio: 0.0098
  Mask BBox: [64, 21, 85, 36]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.8047
  Bounding Box: [992.00, 1204.80, 1196.80, 1449.60]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [82, 99, 97, 117]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7993
  Bounding Box: [1635.20, 236.80, 1897.60, 374.80]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [135, 23, 152, 32]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7964
  Bounding Box: [1785.60, 604.00, 2038.40, 984.80]
  Mask Area: 436 pixels
  Mask Ratio: 0.0154
  Mask BBox: [144, 52, 163, 80]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7935
  Bounding Box: [500.80, 234.00, 645.60, 444.00]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [44, 23, 53, 37]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7935
  Bounding Box: [925.60, 652.00, 1050.40, 831.20]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [77, 55, 84, 68]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7881
  Bounding Box: [558.40, 439.20, 871.20, 856.80]
  Mask Area: 521 pixels
  Mask Ratio: 0.0185
  Mask BBox: [48, 41, 70, 70]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7871
  Bounding Box: [165.60, 174.00, 419.60, 381.20]
  Mask Area: 223 pixels
  Mask Ratio: 0.0079
  Mask BBox: [17, 18, 36, 33]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7856
  Bounding Box: [1568.80, 1387.20, 1812.80, 1577.60]
  Mask Area: 201 pixels
  Mask Ratio: 0.0071
  Mask BBox: [127, 113, 145, 127]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7803
  Bounding Box: [574.80, 1883.20, 740.40, 2048.00]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [49, 152, 61, 164]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7788
  Bounding Box: [143.00, 1851.20, 310.20, 2011.20]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [16, 149, 28, 160]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7734
  Bounding Box: [129.80, 3.20, 309.00, 228.80]
  Mask Area: 177 pixels
  Mask Ratio: 0.0063
  Mask BBox: [15, 5, 28, 21]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7734
  Bounding Box: [1447.20, 231.60, 1648.00, 414.80]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [118, 23, 131, 35]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7700
  Bounding Box: [336.80, 297.60, 489.60, 464.00]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [31, 29, 42, 40]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7686
  Bounding Box: [291.60, 711.20, 447.60, 978.40]
  Mask Area: 205 pixels
  Mask Ratio: 0.0073
  Mask BBox: [27, 60, 38, 80]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7622
  Bounding Box: [808.00, 106.00, 984.00, 241.60]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [68, 13, 79, 22]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7617
  Bounding Box: [1255.20, 323.60, 1548.00, 617.20]
  Mask Area: 309 pixels
  Mask Ratio: 0.0109
  Mask BBox: [103, 30, 124, 52]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7598
  Bounding Box: [1907.20, 1333.60, 2038.40, 1472.80]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [153, 109, 160, 119]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7559
  Bounding Box: [1148.00, 1886.40, 1312.80, 2043.20]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [94, 152, 106, 163]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7534
  Bounding Box: [1471.20, 417.20, 1777.60, 718.00]
  Mask Area: 357 pixels
  Mask Ratio: 0.0126
  Mask BBox: [119, 37, 142, 58]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7529
  Bounding Box: [1835.20, 1771.20, 2024.00, 2004.80]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [148, 143, 162, 159]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7422
  Bounding Box: [306.40, 1688.00, 487.20, 1921.60]
  Mask Area: 189 pixels
  Mask Ratio: 0.0067
  Mask BBox: [28, 136, 42, 154]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7407
  Bounding Box: [388.40, 1005.60, 528.40, 1181.60]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [35, 83, 45, 96]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7383
  Bounding Box: [1312.80, 1833.60, 1575.20, 2048.00]
  Mask Area: 278 pixels
  Mask Ratio: 0.0098
  Mask BBox: [107, 148, 127, 164]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7319
  Bounding Box: [1635.20, 1127.20, 1827.20, 1359.20]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [132, 93, 145, 110]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7305
  Bounding Box: [1389.60, 1120.00, 1533.60, 1289.60]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [113, 92, 123, 104]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7305
  Bounding Box: [1793.60, 0.00, 2043.20, 268.00]
  Mask Area: 260 pixels
  Mask Ratio: 0.0092
  Mask BBox: [145, 4, 163, 20]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7290
  Bounding Box: [1881.60, 1638.40, 2048.00, 1884.80]
  Mask Area: 232 pixels
  Mask Ratio: 0.0082
  Mask BBox: [151, 132, 164, 151]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7285
  Bounding Box: [793.60, 1272.80, 928.00, 1421.60]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [66, 104, 76, 115]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7251
  Bounding Box: [1052.00, 153.30, 1248.80, 462.40]
  Mask Area: 226 pixels
  Mask Ratio: 0.0080
  Mask BBox: [87, 16, 101, 38]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7148
  Bounding Box: [408.40, 884.80, 725.20, 1120.00]
  Mask Area: 324 pixels
  Mask Ratio: 0.0115
  Mask BBox: [36, 74, 60, 91]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7109
  Bounding Box: [1020.00, 880.00, 1165.60, 1040.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [84, 73, 93, 85]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7085
  Bounding Box: [324.20, 966.40, 414.40, 1088.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [30, 80, 36, 88]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7070
  Bounding Box: [1379.20, 820.80, 1531.20, 1068.80]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [112, 69, 122, 86]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7056
  Bounding Box: [1242.40, 567.60, 1352.80, 694.00]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [102, 49, 109, 57]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.6997
  Bounding Box: [0.00, 295.40, 103.00, 512.00]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [4, 28, 12, 43]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.6982
  Bounding Box: [1172.80, 1053.60, 1316.80, 1216.80]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [96, 87, 106, 99]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.6953
  Bounding Box: [1772.80, 1353.60, 1926.40, 1457.60]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [143, 110, 154, 117]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.6948
  Bounding Box: [765.60, 1384.80, 936.80, 1528.80]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [64, 113, 76, 122]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.6841
  Bounding Box: [117.00, 1226.40, 274.20, 1349.60]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [14, 100, 25, 108]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.6841
  Bounding Box: [1814.40, 1165.60, 1929.60, 1364.00]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [146, 96, 154, 110]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6807
  Bounding Box: [1581.60, 1056.80, 1692.80, 1178.40]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [128, 87, 135, 96]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6787
  Bounding Box: [905.60, 382.80, 1131.20, 754.00]
  Mask Area: 323 pixels
  Mask Ratio: 0.0114
  Mask BBox: [75, 34, 92, 62]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6714
  Bounding Box: [1087.20, 480.00, 1191.20, 578.40]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [90, 42, 96, 48]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6699
  Bounding Box: [712.40, 1982.40, 813.60, 2040.00]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [60, 159, 67, 163]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6626
  Bounding Box: [932.80, 908.00, 1150.40, 1143.20]
  Mask Area: 196 pixels
  Mask Ratio: 0.0069
  Mask BBox: [77, 75, 93, 93]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6616
  Bounding Box: [6.05, 524.00, 166.80, 765.60]
  Mask Area: 159 pixels
  Mask Ratio: 0.0056
  Mask BBox: [5, 45, 17, 63]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6616
  Bounding Box: [1616.00, 700.00, 1792.00, 863.20]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [131, 59, 142, 71]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6611
  Bounding Box: [615.60, 6.30, 785.60, 124.30]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [53, 5, 65, 12]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6597
  Bounding Box: [0.00, 501.20, 93.80, 630.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [4, 44, 11, 52]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6582
  Bounding Box: [1268.80, 1312.00, 1464.00, 1505.60]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [104, 107, 118, 121]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6562
  Bounding Box: [1533.60, 0.00, 1668.80, 81.90]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [124, 4, 134, 10]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6548
  Bounding Box: [305.80, 1851.20, 441.60, 2020.80]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [28, 149, 38, 161]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6528
  Bounding Box: [124.70, 0.00, 344.80, 287.00]
  Mask Area: 275 pixels
  Mask Ratio: 0.0097
  Mask BBox: [14, 3, 30, 26]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6489
  Bounding Box: [1926.40, 1050.40, 2016.00, 1386.40]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [155, 87, 161, 112]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6465
  Bounding Box: [585.20, 816.80, 711.60, 925.60]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [50, 68, 58, 75]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6455
  Bounding Box: [52.60, 1771.20, 162.60, 1908.80]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [9, 143, 16, 153]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6401
  Bounding Box: [54.00, 1920.00, 178.60, 2044.80]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [9, 154, 17, 163]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6367
  Bounding Box: [210.60, 372.40, 384.80, 521.20]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [21, 34, 34, 44]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6367
  Bounding Box: [638.00, 309.80, 800.00, 485.60]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [54, 29, 66, 41]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6299
  Bounding Box: [770.40, 0.00, 906.40, 87.20]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [65, 4, 74, 10]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6265
  Bounding Box: [290.00, 580.80, 376.40, 692.80]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [27, 50, 32, 58]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6265
  Bounding Box: [374.40, 1934.40, 520.00, 2030.40]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [34, 156, 44, 162]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6255
  Bounding Box: [1601.60, 1577.60, 1752.00, 1836.80]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [130, 128, 140, 147]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6167
  Bounding Box: [449.60, 1170.40, 638.40, 1415.20]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [40, 96, 52, 114]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6104
  Bounding Box: [1753.60, 1966.40, 1891.20, 2036.80]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [141, 158, 151, 163]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6099
  Bounding Box: [1356.00, 697.60, 1484.00, 816.80]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [110, 59, 119, 67]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6084
  Bounding Box: [1474.40, 1039.20, 1576.80, 1160.80]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [120, 86, 127, 94]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6060
  Bounding Box: [1225.60, 1681.60, 1419.20, 1864.00]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [100, 136, 114, 149]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6060
  Bounding Box: [464.00, 448.00, 599.20, 637.60]
  Mask Area: 112 pixels
  Mask Ratio: 0.0040
  Mask BBox: [41, 39, 49, 53]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6030
  Bounding Box: [1102.40, 1422.40, 1224.00, 1548.80]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [91, 116, 98, 124]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.6030
  Bounding Box: [1525.60, 1812.80, 1608.00, 1883.20]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [124, 146, 129, 151]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.6030
  Bounding Box: [976.00, 875.20, 1158.40, 1107.20]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [81, 73, 94, 90]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.6016
  Bounding Box: [225.80, 1324.80, 416.40, 1612.80]
  Mask Area: 190 pixels
  Mask Ratio: 0.0067
  Mask BBox: [22, 108, 36, 129]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.5996
  Bounding Box: [216.80, 1121.60, 394.40, 1294.40]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [21, 92, 34, 105]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.5986
  Bounding Box: [1382.40, 570.00, 1526.40, 714.80]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [112, 49, 123, 59]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.5942
  Bounding Box: [500.00, 1910.40, 592.00, 2038.40]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [44, 154, 50, 162]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5903
  Bounding Box: [1776.00, 422.40, 1996.80, 549.60]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [143, 37, 159, 46]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5894
  Bounding Box: [491.20, 0.00, 630.40, 88.80]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [43, 4, 53, 10]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5889
  Bounding Box: [1254.40, 1782.40, 1436.80, 1920.00]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [102, 144, 114, 153]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5889
  Bounding Box: [1187.20, 1547.20, 1395.20, 1768.00]
  Mask Area: 204 pixels
  Mask Ratio: 0.0072
  Mask BBox: [97, 125, 112, 142]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5854
  Bounding Box: [1329.60, 1212.80, 1451.20, 1299.20]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [108, 99, 117, 105]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5806
  Bounding Box: [1188.00, 1352.80, 1349.60, 1549.60]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [98, 110, 109, 125]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5659
  Bounding Box: [1969.60, 497.60, 2046.40, 660.80]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [158, 43, 163, 54]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5635
  Bounding Box: [1700.80, 1083.20, 1841.60, 1198.40]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [138, 89, 146, 96]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5601
  Bounding Box: [1652.80, 51.80, 1803.20, 204.60]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [134, 9, 144, 19]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5591
  Bounding Box: [856.80, 1.35, 1023.20, 89.80]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [71, 5, 83, 11]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5581
  Bounding Box: [379.60, 174.00, 509.20, 303.60]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [34, 18, 43, 26]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5571
  Bounding Box: [368.80, 1315.20, 480.80, 1481.60]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [33, 107, 41, 119]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5566
  Bounding Box: [1011.20, 1800.00, 1230.40, 1992.00]
  Mask Area: 157 pixels
  Mask Ratio: 0.0056
  Mask BBox: [83, 145, 100, 159]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5532
  Bounding Box: [1608.00, 1755.20, 1857.60, 2027.20]
  Mask Area: 245 pixels
  Mask Ratio: 0.0087
  Mask BBox: [130, 142, 149, 161]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5522
  Bounding Box: [812.00, 740.80, 984.80, 948.80]
  Mask Area: 177 pixels
  Mask Ratio: 0.0063
  Mask BBox: [68, 62, 80, 78]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5503
  Bounding Box: [467.20, 1428.00, 567.20, 1524.00]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [41, 116, 48, 123]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5488
  Bounding Box: [638.00, 140.20, 780.00, 291.80]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [55, 15, 64, 26]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5488
  Bounding Box: [828.00, 396.80, 919.20, 475.20]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [69, 35, 75, 41]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5469
  Bounding Box: [77.70, 57.00, 156.60, 254.20]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [11, 9, 16, 23]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5464
  Bounding Box: [63.10, 844.00, 296.00, 1109.60]
  Mask Area: 286 pixels
  Mask Ratio: 0.0101
  Mask BBox: [9, 70, 27, 90]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5459
  Bounding Box: [766.40, 1075.20, 1062.40, 1318.40]
  Mask Area: 262 pixels
  Mask Ratio: 0.0093
  Mask BBox: [64, 88, 86, 106]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5449
  Bounding Box: [1923.20, 1481.60, 2003.20, 1560.00]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [155, 120, 160, 125]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5449
  Bounding Box: [1252.80, 691.60, 1388.80, 928.00]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [103, 59, 112, 76]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5405
  Bounding Box: [89.70, 1144.80, 168.60, 1250.40]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [12, 94, 17, 101]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5396
  Bounding Box: [1993.60, 968.80, 2048.00, 1071.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [160, 80, 163, 87]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5366
  Bounding Box: [1111.20, 1684.80, 1236.00, 1793.60]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [91, 136, 99, 144]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5356
  Bounding Box: [1982.40, 857.60, 2036.80, 953.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [159, 71, 163, 78]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.5356
  Bounding Box: [2.90, 1136.00, 79.00, 1251.20]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 93, 8, 101]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.5347
  Bounding Box: [59.30, 429.60, 157.20, 536.80]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [9, 38, 16, 45]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.5308
  Bounding Box: [0.00, 1350.40, 82.80, 1590.40]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [4, 110, 10, 128]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.5308
  Bounding Box: [1240.00, 157.00, 1441.60, 464.00]
  Mask Area: 202 pixels
  Mask Ratio: 0.0072
  Mask BBox: [101, 17, 116, 40]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.5308
  Bounding Box: [1380.80, 1473.60, 1611.20, 1800.00]
  Mask Area: 347 pixels
  Mask Ratio: 0.0123
  Mask BBox: [112, 120, 129, 144]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.5288
  Bounding Box: [1931.20, 1096.00, 2048.00, 1366.40]
  Mask Area: 199 pixels
  Mask Ratio: 0.0071
  Mask BBox: [155, 90, 164, 110]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.5273
  Bounding Box: [1066.40, 451.20, 1181.60, 564.00]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [88, 40, 96, 48]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.5273
  Bounding Box: [1215.20, 1707.20, 1439.20, 1912.00]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [99, 138, 116, 153]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.5220
  Bounding Box: [103.60, 1374.40, 251.20, 1502.40]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [13, 112, 23, 119]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.5195
  Bounding Box: [0.00, 891.20, 74.20, 1019.20]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [4, 74, 9, 83]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.5186
  Bounding Box: [1782.40, 935.20, 1971.20, 1152.80]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [144, 78, 157, 94]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.5054
  Bounding Box: [1006.40, 0.00, 1246.40, 192.40]
  Mask Area: 259 pixels
  Mask Ratio: 0.0092
  Mask BBox: [83, 4, 101, 19]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.5015
  Bounding Box: [1030.40, 1961.60, 1180.80, 2032.00]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [85, 158, 96, 162]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4995
  Bounding Box: [0.00, 509.60, 106.00, 668.80]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [3, 44, 12, 56]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4966
  Bounding Box: [264.00, 549.60, 366.80, 690.40]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [25, 47, 32, 57]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4893
  Bounding Box: [5.20, 145.90, 86.20, 270.80]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [5, 16, 10, 25]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4868
  Bounding Box: [925.60, 1964.80, 1037.60, 2041.60]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [77, 158, 85, 163]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4863
  Bounding Box: [34.70, 238.60, 143.70, 347.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [7, 23, 14, 31]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4819
  Bounding Box: [1776.00, 573.60, 1856.00, 674.40]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [143, 49, 148, 56]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4812
  Bounding Box: [803.20, 108.20, 1011.20, 299.80]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [67, 13, 82, 27]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4800
  Bounding Box: [845.60, 1343.20, 1058.40, 1568.80]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [71, 109, 86, 126]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4736
  Bounding Box: [694.80, 1872.00, 776.80, 1964.80]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [59, 151, 64, 157]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4707
  Bounding Box: [1510.40, 916.80, 1638.40, 1076.80]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [122, 76, 131, 88]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4707
  Bounding Box: [554.40, 1665.60, 773.60, 1876.80]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [48, 135, 64, 149]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4702
  Bounding Box: [488.80, 77.20, 653.60, 279.60]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [43, 11, 55, 25]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4683
  Bounding Box: [1506.40, 698.40, 1641.60, 874.40]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [122, 59, 132, 72]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4639
  Bounding Box: [1316.80, 7.65, 1484.80, 188.40]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [107, 5, 119, 18]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4629
  Bounding Box: [1974.40, 1913.60, 2041.60, 2035.20]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [159, 154, 163, 162]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4629
  Bounding Box: [1836.80, 1681.60, 2048.00, 1988.80]
  Mask Area: 298 pixels
  Mask Ratio: 0.0106
  Mask BBox: [148, 136, 164, 159]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4624
  Bounding Box: [1188.00, 71.80, 1288.80, 141.00]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [97, 10, 104, 15]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4609
  Bounding Box: [618.00, 1144.00, 704.40, 1244.80]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [53, 94, 59, 101]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4595
  Bounding Box: [155.40, 545.60, 283.80, 752.80]
  Mask Area: 138 pixels
  Mask Ratio: 0.0049
  Mask BBox: [17, 47, 26, 62]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4590
  Bounding Box: [727.60, 1096.00, 823.20, 1188.80]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [61, 90, 68, 96]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4590
  Bounding Box: [1953.60, 172.20, 2048.00, 299.40]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [157, 18, 164, 27]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4539
  Bounding Box: [1283.20, 1038.40, 1430.40, 1188.80]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [105, 87, 115, 96]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4529
  Bounding Box: [854.40, 652.00, 918.40, 773.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [71, 55, 75, 62]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4524
  Bounding Box: [1073.60, 1143.20, 1289.60, 1314.40]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [88, 94, 104, 106]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4509
  Bounding Box: [633.60, 1664.00, 793.60, 1808.00]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [54, 134, 64, 145]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4500
  Bounding Box: [1263.20, 1320.80, 1424.80, 1464.80]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [103, 108, 115, 118]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4470
  Bounding Box: [1996.80, 1400.80, 2048.00, 1544.80]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [160, 114, 163, 124]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4470
  Bounding Box: [155.40, 8.50, 440.80, 207.20]
  Mask Area: 273 pixels
  Mask Ratio: 0.0097
  Mask BBox: [17, 5, 38, 20]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4460
  Bounding Box: [683.20, 896.00, 776.00, 987.20]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [58, 74, 64, 81]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.4409
  Bounding Box: [1398.40, 1146.40, 1616.00, 1304.80]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [114, 94, 130, 105]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.4397
  Bounding Box: [1416.80, 1052.80, 1501.60, 1139.20]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [115, 87, 121, 92]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.4387
  Bounding Box: [1801.60, 1188.80, 1929.60, 1441.60]
  Mask Area: 171 pixels
  Mask Ratio: 0.0061
  Mask BBox: [145, 97, 154, 116]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.4382
  Bounding Box: [570.40, 1488.80, 625.60, 1554.40]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [49, 121, 52, 125]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.4341
  Bounding Box: [1908.80, 1566.40, 2014.40, 1656.00]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [154, 127, 161, 132]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.4336
  Bounding Box: [0.00, 1958.40, 60.80, 2048.00]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [4, 157, 8, 163]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.4321
  Bounding Box: [616.00, 1288.00, 808.00, 1523.20]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [53, 105, 67, 122]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.4316
  Bounding Box: [1068.00, 1510.40, 1242.40, 1699.20]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [88, 122, 101, 135]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.4307
  Bounding Box: [1649.60, 345.20, 1780.80, 425.20]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [133, 31, 143, 37]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.4285
  Bounding Box: [816.80, 10.10, 1007.20, 108.30]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [68, 5, 82, 11]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.4282
  Bounding Box: [1185.60, 348.80, 1252.80, 437.60]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [97, 32, 101, 38]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.4263
  Bounding Box: [487.20, 1740.80, 559.20, 1862.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [43, 140, 47, 149]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.4243
  Bounding Box: [638.80, 77.90, 800.00, 284.40]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [55, 11, 66, 26]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.4231
  Bounding Box: [574.40, 413.60, 660.80, 482.40]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [49, 37, 55, 41]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.4172
  Bounding Box: [1016.80, 1155.20, 1268.00, 1390.40]
  Mask Area: 285 pixels
  Mask Ratio: 0.0101
  Mask BBox: [84, 95, 103, 112]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.4158
  Bounding Box: [296.20, 592.80, 398.40, 712.00]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [28, 51, 32, 59]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.4153
  Bounding Box: [1278.40, 1784.00, 1555.20, 2014.40]
  Mask Area: 274 pixels
  Mask Ratio: 0.0097
  Mask BBox: [104, 144, 125, 161]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.4111
  Bounding Box: [1656.00, 2.60, 1803.20, 163.40]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [134, 5, 144, 16]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.4092
  Bounding Box: [1758.40, 1486.40, 1854.40, 1577.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [142, 121, 148, 127]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.4036
  Bounding Box: [650.40, 1503.20, 798.40, 1699.20]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [55, 122, 66, 136]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3994
  Bounding Box: [1575.20, 1878.40, 1648.00, 1996.80]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [128, 151, 131, 159]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3992
  Bounding Box: [1192.00, 541.20, 1340.80, 676.40]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [98, 47, 108, 56]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3984
  Bounding Box: [1163.20, 537.60, 1270.40, 656.00]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [95, 46, 103, 55]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3928
  Bounding Box: [270.60, 594.00, 367.20, 713.20]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [26, 51, 32, 59]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3904
  Bounding Box: [5.10, 982.40, 63.80, 1113.60]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [5, 81, 8, 90]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3899
  Bounding Box: [8.00, 884.80, 68.40, 1076.80]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [5, 74, 9, 88]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3899
  Bounding Box: [413.60, 434.80, 594.40, 726.80]
  Mask Area: 249 pixels
  Mask Ratio: 0.0088
  Mask BBox: [37, 38, 50, 60]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3896
  Bounding Box: [1940.80, 1910.40, 2048.00, 2038.40]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [156, 154, 165, 163]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3857
  Bounding Box: [1972.80, 869.60, 2043.20, 976.80]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [159, 72, 163, 80]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3857
  Bounding Box: [146.00, 1849.60, 428.40, 2009.60]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [16, 149, 37, 160]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3838
  Bounding Box: [309.60, 1872.00, 486.40, 2038.40]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [29, 151, 41, 162]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3835
  Bounding Box: [559.20, 1494.40, 638.40, 1590.40]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [48, 121, 53, 128]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3835
  Bounding Box: [1396.00, 504.80, 1628.80, 714.40]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [114, 47, 131, 59]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3831
  Bounding Box: [1060.80, 418.80, 1184.00, 496.40]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [87, 37, 96, 42]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3794
  Bounding Box: [23.80, 981.60, 115.80, 1117.60]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [6, 81, 13, 91]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3767
  Bounding Box: [75.90, 1102.40, 187.60, 1254.40]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [10, 91, 18, 101]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3762
  Bounding Box: [748.00, 1158.40, 858.40, 1251.20]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [63, 95, 70, 101]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3738
  Bounding Box: [1491.20, 661.20, 1624.00, 816.00]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [121, 56, 130, 67]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3716
  Bounding Box: [238.00, 1159.20, 392.80, 1349.60]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [23, 95, 34, 107]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3696
  Bounding Box: [1448.00, 1243.20, 1654.40, 1440.00]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [118, 102, 133, 116]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3689
  Bounding Box: [397.20, 1924.80, 566.80, 2043.20]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [36, 155, 48, 162]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3684
  Bounding Box: [600.40, 1204.00, 704.40, 1333.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [51, 99, 57, 108]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3667
  Bounding Box: [7.60, 1167.20, 83.90, 1292.00]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 96, 8, 104]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3647
  Bounding Box: [1857.60, 1453.60, 1969.60, 1560.80]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [150, 118, 157, 125]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3638
  Bounding Box: [1041.60, 1304.00, 1214.40, 1542.40]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [86, 106, 98, 124]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3635
  Bounding Box: [1488.80, 847.20, 1632.00, 1066.40]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [121, 71, 131, 87]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3579
  Bounding Box: [376.40, 1349.60, 490.80, 1536.80]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [34, 110, 42, 124]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3562
  Bounding Box: [627.20, 10.40, 804.80, 219.00]
  Mask Area: 187 pixels
  Mask Ratio: 0.0066
  Mask BBox: [53, 5, 66, 21]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3562
  Bounding Box: [1718.40, 1000.00, 1916.80, 1193.60]
  Mask Area: 173 pixels
  Mask Ratio: 0.0061
  Mask BBox: [139, 83, 153, 97]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3523
  Bounding Box: [1822.40, 1451.20, 1924.80, 1566.40]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [147, 118, 154, 126]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3518
  Bounding Box: [767.20, 902.40, 919.20, 1080.00]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [64, 75, 75, 88]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3518
  Bounding Box: [1156.80, 1636.80, 1412.80, 1864.00]
  Mask Area: 202 pixels
  Mask Ratio: 0.0072
  Mask BBox: [95, 132, 114, 149]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3491
  Bounding Box: [1466.40, 18.65, 1776.00, 216.40]
  Mask Area: 282 pixels
  Mask Ratio: 0.0100
  Mask BBox: [119, 6, 142, 20]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3479
  Bounding Box: [509.20, 756.40, 695.60, 936.00]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [44, 64, 58, 77]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3452
  Bounding Box: [743.60, 1224.00, 833.60, 1366.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [63, 100, 69, 110]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3440
  Bounding Box: [1361.60, 596.80, 1508.80, 790.40]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [111, 51, 121, 65]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3433
  Bounding Box: [819.20, 464.00, 897.60, 582.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [68, 41, 74, 49]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3430
  Bounding Box: [343.20, 1908.80, 503.20, 2033.60]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [31, 154, 43, 162]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3398
  Bounding Box: [1913.60, 1486.40, 2009.60, 1598.40]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [154, 121, 160, 128]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3381
  Bounding Box: [7.90, 1017.60, 63.10, 1136.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [5, 84, 8, 92]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3367
  Bounding Box: [1402.40, 1050.40, 1488.80, 1124.00]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [114, 87, 120, 91]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3367
  Bounding Box: [1402.40, 1076.00, 1488.80, 1149.60]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [114, 89, 120, 92]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3367
  Bounding Box: [1428.00, 1076.00, 1514.40, 1149.60]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [116, 89, 122, 93]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3320
  Bounding Box: [1256.00, 580.80, 1374.40, 713.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [103, 50, 111, 59]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3315
  Bounding Box: [433.20, 1446.40, 546.00, 1561.60]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [38, 117, 46, 125]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3315
  Bounding Box: [7.70, 239.20, 130.70, 414.40]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [5, 23, 14, 36]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3306
  Bounding Box: [1399.20, 169.40, 1492.00, 266.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [114, 18, 120, 24]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3306
  Bounding Box: [1212.80, 221.60, 1473.60, 552.00]
  Mask Area: 329 pixels
  Mask Ratio: 0.0117
  Mask BBox: [99, 22, 119, 47]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1908.80, 1260.00, 2043.20, 1485.60]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [154, 103, 163, 120]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3301
  Bounding Box: [465.20, 835.20, 719.60, 1086.40]
  Mask Area: 263 pixels
  Mask Ratio: 0.0093
  Mask BBox: [41, 70, 60, 88]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3298
  Bounding Box: [1189.60, 1073.60, 1338.40, 1192.00]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [97, 88, 108, 97]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3286
  Bounding Box: [1179.20, 378.00, 1294.40, 561.20]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [97, 34, 105, 47]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3267
  Bounding Box: [1333.60, 689.60, 1487.20, 886.40]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [109, 58, 120, 73]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3264
  Bounding Box: [1640.00, 187.20, 1928.00, 361.60]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [134, 19, 154, 32]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3259
  Bounding Box: [517.60, 1356.80, 628.00, 1488.00]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [45, 110, 53, 120]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3254
  Bounding Box: [1961.60, 339.60, 2041.60, 510.00]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [158, 31, 163, 43]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3254
  Bounding Box: [305.40, 612.00, 380.80, 748.80]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [28, 52, 32, 62]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.3240
  Bounding Box: [50.00, 1362.40, 245.80, 1528.80]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [8, 111, 23, 123]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.3237
  Bounding Box: [9.40, 1299.20, 71.30, 1388.80]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [5, 106, 9, 112]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.3237
  Bounding Box: [546.40, 1715.20, 726.40, 1881.60]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [47, 138, 60, 150]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.3235
  Bounding Box: [1792.00, 1363.20, 1955.20, 1476.80]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [144, 111, 156, 119]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [398.00, 1195.20, 464.40, 1305.60]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [36, 98, 40, 105]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.3213
  Bounding Box: [1873.60, 1992.00, 1992.00, 2040.00]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [151, 160, 159, 163]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.3201
  Bounding Box: [1490.40, 667.20, 1573.60, 750.40]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [121, 57, 126, 62]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.3196
  Bounding Box: [874.40, 1051.20, 996.00, 1128.00]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [73, 87, 81, 92]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.3191
  Bounding Box: [589.60, 25.50, 765.60, 142.70]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [51, 6, 63, 15]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.3184
  Bounding Box: [1915.20, 1451.20, 2001.60, 1545.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [154, 118, 159, 124]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.3184
  Bounding Box: [1940.80, 1451.20, 2027.20, 1545.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [156, 118, 162, 124]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.3184
  Bounding Box: [1940.80, 1476.80, 2027.20, 1571.20]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [156, 120, 162, 126]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.3169
  Bounding Box: [1606.40, 1310.40, 1715.20, 1398.40]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [130, 107, 137, 113]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.3149
  Bounding Box: [1183.20, 43.45, 1306.40, 151.40]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [97, 8, 106, 15]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.3147
  Bounding Box: [1029.60, 626.00, 1114.40, 762.80]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [85, 53, 91, 63]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.3145
  Bounding Box: [1652.80, 292.00, 1835.20, 418.80]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [134, 27, 147, 36]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.3140
  Bounding Box: [1182.40, 524.00, 1296.00, 667.20]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [97, 45, 105, 56]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.3137
  Bounding Box: [1179.20, 337.00, 1270.40, 493.60]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [97, 31, 103, 42]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.3132
  Bounding Box: [1112.00, 1444.00, 1243.20, 1568.80]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [91, 117, 101, 126]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.3123
  Bounding Box: [1680.00, 360.40, 1776.00, 468.40]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [136, 33, 142, 40]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.3120
  Bounding Box: [1961.60, 946.40, 2038.40, 1053.60]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [158, 78, 163, 86]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.3105
  Bounding Box: [730.00, 0.00, 923.20, 142.40]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [62, 3, 76, 15]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.3098
  Bounding Box: [776.00, 972.00, 894.40, 1106.40]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [65, 80, 73, 90]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [1123.20, 1378.40, 1324.80, 1603.20]
  Mask Area: 218 pixels
  Mask Ratio: 0.0077
  Mask BBox: [92, 112, 107, 129]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.3091
  Bounding Box: [1900.80, 1979.20, 2025.60, 2048.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [153, 159, 162, 163]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.3086
  Bounding Box: [1501.60, 1183.20, 1604.80, 1336.80]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [122, 97, 129, 108]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.3081
  Bounding Box: [1595.20, 1774.40, 1694.40, 1854.40]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [129, 143, 136, 148]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.3079
  Bounding Box: [852.80, 556.40, 916.80, 744.40]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [71, 48, 75, 62]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.3079
  Bounding Box: [1256.80, 794.40, 1408.80, 1045.60]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [103, 67, 114, 85]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [1263.20, 885.60, 1404.00, 1037.60]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [103, 74, 113, 85]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [274.40, 1235.20, 380.80, 1344.00]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [26, 101, 33, 108]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.3062
  Bounding Box: [67.80, 1164.00, 161.00, 1263.20]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [11, 95, 16, 102]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.3062
  Bounding Box: [93.40, 1164.00, 186.60, 1263.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [12, 95, 18, 102]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.3047
  Bounding Box: [1260.80, 137.70, 1428.80, 375.60]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [103, 15, 115, 30]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.3037
  Bounding Box: [1105.60, 464.40, 1212.80, 566.00]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [91, 41, 96, 48]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.3037
  Bounding Box: [1105.60, 490.00, 1212.80, 591.60]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [91, 43, 98, 50]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.3032
  Bounding Box: [1796.80, 1340.80, 1953.60, 1446.40]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [145, 109, 156, 116]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.3025
  Bounding Box: [1217.60, 268.20, 1291.20, 378.80]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [100, 25, 104, 33]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.3022
  Bounding Box: [1980.80, 1558.40, 2044.80, 1651.20]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [159, 126, 163, 131]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.3020
  Bounding Box: [1209.60, 1339.20, 1451.20, 1520.00]
  Mask Area: 209 pixels
  Mask Ratio: 0.0074
  Mask BBox: [99, 109, 117, 122]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [364.40, 161.40, 487.60, 281.80]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [34, 17, 42, 26]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.3013
  Bounding Box: [476.80, 1920.00, 580.80, 2048.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [42, 154, 49, 162]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2996
  Bounding Box: [1337.60, 675.60, 1465.60, 792.00]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [109, 57, 118, 65]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2996
  Bounding Box: [451.20, 263.60, 515.20, 380.80]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [40, 25, 44, 32]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2983
  Bounding Box: [47.90, 978.40, 142.50, 1106.40]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [8, 81, 15, 90]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2964
  Bounding Box: [46.20, 1841.60, 181.60, 2040.00]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [8, 148, 18, 163]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2961
  Bounding Box: [126.20, 390.40, 222.20, 505.60]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [14, 35, 21, 43]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2954
  Bounding Box: [1923.20, 1020.80, 2048.00, 1307.20]
  Mask Area: 214 pixels
  Mask Ratio: 0.0076
  Mask BBox: [155, 84, 164, 106]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2949
  Bounding Box: [1971.20, 1947.20, 2048.00, 2048.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [158, 157, 163, 164]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2947
  Bounding Box: [612.00, 825.60, 730.40, 944.00]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [52, 69, 61, 77]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2942
  Bounding Box: [734.80, 1948.80, 836.80, 2048.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [62, 157, 69, 163]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2920
  Bounding Box: [1570.40, 1774.40, 1662.40, 1860.80]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [127, 143, 133, 149]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2920
  Bounding Box: [1870.40, 1074.40, 2033.60, 1368.80]
  Mask Area: 228 pixels
  Mask Ratio: 0.0081
  Mask BBox: [151, 88, 162, 110]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2915
  Bounding Box: [108.00, 1126.40, 220.00, 1238.40]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [13, 92, 21, 100]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2905
  Bounding Box: [776.80, 255.00, 1018.40, 450.40]
  Mask Area: 255 pixels
  Mask Ratio: 0.0090
  Mask BBox: [65, 24, 83, 39]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2903
  Bounding Box: [1556.80, 0.00, 1691.20, 62.80]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [126, 3, 136, 8]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2898
  Bounding Box: [1538.40, 0.00, 1662.40, 49.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [125, 3, 133, 7]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2898
  Bounding Box: [1564.00, 3.20, 1688.00, 74.80]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [127, 5, 135, 9]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2891
  Bounding Box: [1277.60, 1804.80, 1458.40, 1939.20]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [104, 145, 117, 152]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2883
  Bounding Box: [476.80, 1240.00, 628.00, 1456.00]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [42, 101, 53, 117]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2881
  Bounding Box: [794.40, 1971.20, 903.20, 2028.80]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [67, 158, 74, 162]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2866
  Bounding Box: [812.80, 444.80, 907.20, 547.20]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [68, 39, 74, 46]

