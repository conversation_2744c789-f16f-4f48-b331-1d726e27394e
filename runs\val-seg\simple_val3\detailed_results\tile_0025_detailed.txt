Image: tile_0025.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8804
  Bounding Box: [12.60, 662.80, 424.80, 996.80]
  Mask Area: 555 pixels
  Mask Ratio: 0.0197
  Mask BBox: [7, 56, 37, 81]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8804
  Bounding Box: [1683.20, 1110.40, 1929.60, 1324.80]
  Mask Area: 224 pixels
  Mask Ratio: 0.0079
  Mask BBox: [137, 91, 154, 107]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8745
  Bounding Box: [313.20, 1495.20, 464.40, 1638.40]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [29, 121, 40, 131]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8735
  Bounding Box: [1525.60, 327.20, 1795.20, 568.80]
  Mask Area: 272 pixels
  Mask Ratio: 0.0096
  Mask BBox: [124, 30, 144, 48]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8486
  Bounding Box: [1512.80, 1657.60, 1708.80, 1852.80]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [123, 134, 136, 148]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8335
  Bounding Box: [521.60, 1216.00, 742.40, 1494.40]
  Mask Area: 287 pixels
  Mask Ratio: 0.0102
  Mask BBox: [45, 99, 61, 120]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8325
  Bounding Box: [1816.00, 886.40, 2043.20, 1120.00]
  Mask Area: 267 pixels
  Mask Ratio: 0.0095
  Mask BBox: [146, 74, 163, 91]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8286
  Bounding Box: [454.80, 672.00, 673.20, 896.00]
  Mask Area: 224 pixels
  Mask Ratio: 0.0079
  Mask BBox: [40, 57, 56, 73]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8257
  Bounding Box: [672.00, 546.00, 950.40, 939.20]
  Mask Area: 457 pixels
  Mask Ratio: 0.0162
  Mask BBox: [57, 47, 78, 77]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8237
  Bounding Box: [1232.00, 1771.20, 1444.80, 2048.00]
  Mask Area: 317 pixels
  Mask Ratio: 0.0112
  Mask BBox: [101, 143, 116, 164]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8213
  Bounding Box: [824.00, 1340.00, 1020.80, 1638.40]
  Mask Area: 237 pixels
  Mask Ratio: 0.0084
  Mask BBox: [69, 109, 83, 131]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8208
  Bounding Box: [685.20, 1001.60, 842.40, 1214.40]
  Mask Area: 157 pixels
  Mask Ratio: 0.0056
  Mask BBox: [58, 83, 69, 98]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.8066
  Bounding Box: [1420.00, 1358.40, 1619.20, 1556.80]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [115, 111, 130, 125]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.8008
  Bounding Box: [1316.80, 1420.80, 1467.20, 1617.60]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [107, 115, 118, 130]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7949
  Bounding Box: [1309.60, 1108.80, 1519.20, 1380.80]
  Mask Area: 233 pixels
  Mask Ratio: 0.0083
  Mask BBox: [108, 91, 122, 111]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7930
  Bounding Box: [902.40, 1923.20, 1126.40, 2019.20]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [75, 155, 91, 161]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7910
  Bounding Box: [238.80, 1592.00, 370.80, 1729.60]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [23, 129, 32, 138]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7891
  Bounding Box: [10.05, 502.40, 178.40, 688.80]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [5, 44, 17, 57]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7822
  Bounding Box: [98.60, 304.40, 241.00, 438.00]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [12, 28, 22, 38]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7803
  Bounding Box: [995.20, 735.20, 1089.60, 946.40]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [82, 62, 89, 77]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7803
  Bounding Box: [1152.00, 305.00, 1334.40, 477.60]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [94, 28, 108, 41]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7778
  Bounding Box: [657.60, 1390.40, 881.60, 1692.80]
  Mask Area: 326 pixels
  Mask Ratio: 0.0116
  Mask BBox: [56, 113, 72, 136]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7769
  Bounding Box: [1504.80, 647.20, 1638.40, 814.40]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [122, 55, 131, 67]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7725
  Bounding Box: [274.40, 842.40, 505.60, 1199.20]
  Mask Area: 328 pixels
  Mask Ratio: 0.0116
  Mask BBox: [26, 70, 43, 97]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7720
  Bounding Box: [1079.20, 613.20, 1268.00, 974.40]
  Mask Area: 295 pixels
  Mask Ratio: 0.0105
  Mask BBox: [89, 52, 103, 80]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7710
  Bounding Box: [1740.80, 283.20, 2006.40, 495.20]
  Mask Area: 268 pixels
  Mask Ratio: 0.0095
  Mask BBox: [140, 27, 159, 42]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7681
  Bounding Box: [508.40, 913.60, 686.80, 1192.00]
  Mask Area: 221 pixels
  Mask Ratio: 0.0078
  Mask BBox: [44, 76, 57, 97]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7573
  Bounding Box: [1030.40, 912.80, 1193.60, 1084.00]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [85, 76, 97, 87]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7549
  Bounding Box: [1614.40, 1296.00, 1774.40, 1470.40]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [131, 106, 142, 118]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7549
  Bounding Box: [1030.40, 1372.00, 1299.20, 1636.80]
  Mask Area: 242 pixels
  Mask Ratio: 0.0086
  Mask BBox: [85, 112, 103, 131]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7505
  Bounding Box: [1630.40, 1380.00, 2014.40, 1913.60]
  Mask Area: 887 pixels
  Mask Ratio: 0.0314
  Mask BBox: [132, 112, 161, 153]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7485
  Bounding Box: [2.70, 1256.80, 190.40, 1524.00]
  Mask Area: 208 pixels
  Mask Ratio: 0.0074
  Mask BBox: [5, 103, 18, 123]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7471
  Bounding Box: [989.60, 1752.00, 1133.60, 1947.20]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [82, 141, 91, 156]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7456
  Bounding Box: [1507.20, 852.00, 1728.00, 1040.80]
  Mask Area: 157 pixels
  Mask Ratio: 0.0056
  Mask BBox: [122, 71, 138, 83]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7441
  Bounding Box: [1049.60, 393.20, 1238.40, 590.00]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [86, 35, 100, 50]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7427
  Bounding Box: [1204.80, 435.20, 1401.60, 654.40]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [100, 38, 113, 55]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7412
  Bounding Box: [1638.40, 1058.40, 1760.00, 1176.80]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [132, 87, 140, 95]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7407
  Bounding Box: [518.00, 1860.80, 673.20, 2048.00]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [45, 150, 56, 164]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7358
  Bounding Box: [1497.60, 1228.80, 1699.20, 1385.60]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [121, 100, 136, 112]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7324
  Bounding Box: [744.40, 1686.40, 947.20, 1961.60]
  Mask Area: 260 pixels
  Mask Ratio: 0.0092
  Mask BBox: [63, 136, 77, 157]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7310
  Bounding Box: [824.00, 933.60, 1051.20, 1152.80]
  Mask Area: 211 pixels
  Mask Ratio: 0.0075
  Mask BBox: [69, 77, 86, 94]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7305
  Bounding Box: [8.45, 1141.60, 195.60, 1285.60]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [6, 94, 18, 104]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7300
  Bounding Box: [1411.20, 1009.60, 1665.60, 1240.00]
  Mask Area: 228 pixels
  Mask Ratio: 0.0081
  Mask BBox: [115, 83, 133, 100]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7275
  Bounding Box: [1320.00, 279.00, 1425.60, 488.00]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [108, 26, 115, 42]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7271
  Bounding Box: [735.20, 1140.00, 904.80, 1396.00]
  Mask Area: 171 pixels
  Mask Ratio: 0.0061
  Mask BBox: [62, 94, 74, 113]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7217
  Bounding Box: [1916.80, 1107.20, 2019.20, 1299.20]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [154, 91, 161, 105]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7188
  Bounding Box: [1113.60, 1729.60, 1276.80, 2036.80]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [91, 140, 103, 163]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7119
  Bounding Box: [1316.80, 11.20, 1470.40, 88.70]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [107, 5, 118, 10]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7104
  Bounding Box: [424.00, 269.00, 604.80, 527.20]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [38, 26, 51, 45]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7070
  Bounding Box: [96.60, 1848.00, 284.20, 2027.20]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [12, 149, 26, 162]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.7056
  Bounding Box: [1388.00, 425.20, 1474.40, 569.20]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [113, 38, 119, 48]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.7021
  Bounding Box: [206.80, 1734.40, 378.80, 1958.40]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [21, 140, 32, 155]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.7007
  Bounding Box: [1902.40, 1270.40, 2046.40, 1457.60]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [153, 104, 163, 117]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.6992
  Bounding Box: [1625.60, 1051.20, 1737.60, 1156.80]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [131, 87, 139, 94]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.6973
  Bounding Box: [1715.20, 900.00, 1817.60, 1063.20]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [138, 75, 145, 87]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.6938
  Bounding Box: [1227.20, 1151.20, 1352.00, 1285.60]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [100, 94, 108, 104]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6938
  Bounding Box: [1961.60, 1399.20, 2044.80, 1551.20]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [158, 114, 163, 125]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6904
  Bounding Box: [993.60, 1553.60, 1187.20, 1787.20]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [82, 126, 96, 143]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6899
  Bounding Box: [1627.20, 534.00, 1764.80, 650.80]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [132, 46, 141, 54]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6826
  Bounding Box: [1021.60, 269.00, 1138.40, 440.80]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [84, 26, 92, 38]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6797
  Bounding Box: [1614.40, 564.80, 1851.20, 915.20]
  Mask Area: 325 pixels
  Mask Ratio: 0.0115
  Mask BBox: [131, 49, 148, 75]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6792
  Bounding Box: [8.55, 1756.80, 150.00, 1971.20]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [5, 142, 15, 157]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6772
  Bounding Box: [1196.00, 1593.60, 1325.60, 1756.80]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [98, 130, 107, 140]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6772
  Bounding Box: [859.20, 1095.20, 1150.40, 1383.20]
  Mask Area: 313 pixels
  Mask Ratio: 0.0111
  Mask BBox: [72, 90, 91, 112]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6694
  Bounding Box: [29.75, 839.20, 175.00, 1095.20]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [7, 70, 17, 89]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6689
  Bounding Box: [1271.20, 857.60, 1432.80, 1113.60]
  Mask Area: 173 pixels
  Mask Ratio: 0.0061
  Mask BBox: [104, 71, 115, 90]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6680
  Bounding Box: [289.00, 259.20, 425.60, 396.00]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [27, 25, 36, 34]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6665
  Bounding Box: [1560.80, 535.20, 1630.40, 632.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [126, 46, 131, 53]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6562
  Bounding Box: [1022.40, 1328.80, 1150.40, 1458.40]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [84, 108, 92, 117]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6558
  Bounding Box: [1444.80, 1795.20, 1622.40, 2038.40]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [117, 145, 130, 163]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6543
  Bounding Box: [1009.60, 570.40, 1070.40, 654.40]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [83, 49, 87, 55]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6538
  Bounding Box: [164.20, 550.40, 289.80, 652.80]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [17, 47, 26, 54]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6445
  Bounding Box: [527.60, 264.80, 833.60, 488.80]
  Mask Area: 296 pixels
  Mask Ratio: 0.0105
  Mask BBox: [46, 25, 69, 42]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6401
  Bounding Box: [0.00, 1476.80, 113.20, 1761.60]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [4, 120, 12, 141]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6372
  Bounding Box: [880.00, 1937.60, 1155.20, 2048.00]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [73, 156, 94, 164]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6372
  Bounding Box: [470.00, 1436.80, 669.20, 1691.20]
  Mask Area: 223 pixels
  Mask Ratio: 0.0079
  Mask BBox: [41, 117, 56, 136]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6338
  Bounding Box: [190.80, 1105.60, 350.00, 1219.20]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [19, 91, 30, 99]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6255
  Bounding Box: [1196.00, 900.80, 1340.00, 1126.40]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [98, 75, 108, 91]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6196
  Bounding Box: [1430.40, 1586.40, 1528.00, 1779.20]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [116, 128, 123, 142]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6182
  Bounding Box: [1822.40, 1892.80, 1918.40, 2046.40]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [147, 152, 153, 163]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6104
  Bounding Box: [1304.80, 1556.80, 1476.00, 1774.40]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [106, 126, 119, 141]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6074
  Bounding Box: [1969.60, 699.60, 2040.00, 993.60]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [158, 59, 163, 81]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6060
  Bounding Box: [399.20, 548.40, 570.40, 699.60]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [36, 47, 48, 58]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.5991
  Bounding Box: [589.60, 574.40, 696.80, 714.40]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [51, 49, 57, 59]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.5957
  Bounding Box: [376.00, 1240.80, 514.40, 1485.60]
  Mask Area: 181 pixels
  Mask Ratio: 0.0064
  Mask BBox: [34, 101, 44, 120]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.5952
  Bounding Box: [640.40, 481.60, 739.60, 589.60]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [55, 42, 60, 50]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.5933
  Bounding Box: [1355.20, 1391.20, 1582.40, 1596.80]
  Mask Area: 202 pixels
  Mask Ratio: 0.0072
  Mask BBox: [110, 113, 127, 128]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.5913
  Bounding Box: [924.00, 680.80, 1034.40, 848.00]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [77, 58, 84, 70]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.5879
  Bounding Box: [1439.20, 448.00, 1568.80, 636.80]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [117, 39, 124, 53]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.5869
  Bounding Box: [394.40, 1721.60, 469.60, 1827.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [35, 139, 40, 146]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.5840
  Bounding Box: [375.60, 1748.80, 573.20, 1969.60]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [35, 141, 48, 157]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.5703
  Bounding Box: [1126.40, 1237.60, 1302.40, 1357.60]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [92, 101, 105, 110]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5640
  Bounding Box: [381.20, 23.95, 508.40, 111.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [34, 6, 43, 12]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5610
  Bounding Box: [1207.20, 868.80, 1407.20, 1147.20]
  Mask Area: 264 pixels
  Mask Ratio: 0.0094
  Mask BBox: [99, 72, 113, 93]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5605
  Bounding Box: [1873.60, 504.00, 2048.00, 695.20]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [151, 44, 164, 58]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5566
  Bounding Box: [514.80, 28.10, 620.40, 89.50]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [45, 7, 50, 10]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5562
  Bounding Box: [610.00, 901.60, 691.60, 991.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [52, 75, 58, 81]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5527
  Bounding Box: [177.80, 1222.40, 377.60, 1465.60]
  Mask Area: 270 pixels
  Mask Ratio: 0.0096
  Mask BBox: [18, 100, 33, 118]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5449
  Bounding Box: [0.00, 0.00, 83.60, 113.20]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [4, 4, 10, 12]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5435
  Bounding Box: [1132.80, 1049.60, 1270.40, 1212.80]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [93, 86, 103, 98]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5410
  Bounding Box: [1609.60, 1766.40, 1824.00, 2003.20]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [130, 142, 146, 160]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5347
  Bounding Box: [116.10, 980.80, 297.20, 1188.80]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [14, 81, 27, 96]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5327
  Bounding Box: [1356.80, 611.20, 1532.80, 846.40]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [110, 52, 122, 70]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5195
  Bounding Box: [1.10, 153.80, 78.90, 299.80]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [5, 17, 8, 27]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5049
  Bounding Box: [1700.80, 1849.60, 1864.00, 2028.80]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [137, 149, 149, 162]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5024
  Bounding Box: [1769.60, 1302.40, 1884.80, 1411.20]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [143, 106, 151, 112]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.4980
  Bounding Box: [903.20, 859.20, 1012.00, 968.00]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [75, 72, 83, 79]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.4932
  Bounding Box: [1880.00, 97.00, 2024.00, 173.80]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [151, 12, 162, 17]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.4917
  Bounding Box: [860.80, 418.40, 934.40, 525.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [72, 37, 76, 45]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.4868
  Bounding Box: [1248.00, 1785.60, 1552.00, 2028.80]
  Mask Area: 369 pixels
  Mask Ratio: 0.0131
  Mask BBox: [102, 144, 125, 162]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.4849
  Bounding Box: [1961.60, 656.80, 2038.40, 842.40]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [158, 56, 163, 69]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.4819
  Bounding Box: [0.00, 1987.20, 114.00, 2048.00]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [4, 160, 12, 163]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.4805
  Bounding Box: [7.30, 1088.80, 69.80, 1199.20]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [5, 90, 8, 97]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.4800
  Bounding Box: [185.80, 1295.20, 385.60, 1528.80]
  Mask Area: 265 pixels
  Mask Ratio: 0.0094
  Mask BBox: [19, 106, 34, 123]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.4761
  Bounding Box: [1974.40, 221.00, 2041.60, 343.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [159, 22, 163, 30]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.4761
  Bounding Box: [199.20, 1389.60, 377.60, 1522.40]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [20, 113, 33, 122]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.4741
  Bounding Box: [386.40, 717.60, 466.40, 839.20]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [35, 61, 40, 69]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.4722
  Bounding Box: [1532.80, 1656.00, 1776.00, 1956.80]
  Mask Area: 252 pixels
  Mask Ratio: 0.0089
  Mask BBox: [124, 134, 142, 156]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.4673
  Bounding Box: [672.00, 92.90, 796.00, 169.40]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [57, 12, 65, 17]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.4653
  Bounding Box: [104.80, 429.60, 340.00, 552.80]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [13, 38, 30, 47]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.4644
  Bounding Box: [1185.60, 5.00, 1312.00, 118.80]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [97, 5, 106, 13]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.4580
  Bounding Box: [566.80, 1664.00, 662.00, 1731.20]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [49, 134, 55, 139]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.4580
  Bounding Box: [1443.20, 1045.60, 1779.20, 1215.20]
  Mask Area: 236 pixels
  Mask Ratio: 0.0084
  Mask BBox: [117, 86, 142, 98]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.4570
  Bounding Box: [36.65, 1088.00, 231.40, 1267.20]
  Mask Area: 190 pixels
  Mask Ratio: 0.0067
  Mask BBox: [7, 89, 22, 102]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.4546
  Bounding Box: [590.80, 1715.20, 804.80, 1952.00]
  Mask Area: 241 pixels
  Mask Ratio: 0.0085
  Mask BBox: [51, 138, 66, 156]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.4509
  Bounding Box: [840.00, 290.00, 1051.20, 532.40]
  Mask Area: 228 pixels
  Mask Ratio: 0.0081
  Mask BBox: [70, 27, 86, 45]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.4397
  Bounding Box: [997.60, 1753.60, 1223.20, 1958.40]
  Mask Area: 204 pixels
  Mask Ratio: 0.0072
  Mask BBox: [82, 141, 99, 156]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4392
  Bounding Box: [847.20, 932.00, 1159.20, 1132.00]
  Mask Area: 278 pixels
  Mask Ratio: 0.0098
  Mask BBox: [71, 77, 94, 92]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.4363
  Bounding Box: [893.60, 1635.20, 1020.00, 1900.80]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [74, 132, 83, 152]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4307
  Bounding Box: [1883.20, 736.00, 1972.80, 865.60]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [152, 62, 158, 71]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4307
  Bounding Box: [20.80, 1772.80, 228.80, 2003.20]
  Mask Area: 186 pixels
  Mask Ratio: 0.0066
  Mask BBox: [6, 143, 21, 160]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4268
  Bounding Box: [2016.00, 1593.60, 2048.00, 1696.00]
  Mask Area: 15 pixels
  Mask Ratio: 0.0005
  Mask BBox: [162, 129, 163, 136]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4226
  Bounding Box: [1324.80, 3.60, 1497.60, 130.80]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [108, 5, 120, 13]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4219
  Bounding Box: [130.50, 536.80, 298.40, 664.80]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [15, 46, 27, 55]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4211
  Bounding Box: [977.60, 526.00, 1078.40, 638.00]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [81, 46, 87, 53]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4202
  Bounding Box: [1996.80, 338.60, 2041.60, 429.60]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [160, 31, 163, 37]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4172
  Bounding Box: [6.60, 184.00, 68.50, 349.60]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [5, 19, 8, 31]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4172
  Bounding Box: [800.00, 541.20, 916.80, 695.60]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [67, 47, 75, 57]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4153
  Bounding Box: [1192.00, 274.40, 1388.80, 483.20]
  Mask Area: 184 pixels
  Mask Ratio: 0.0065
  Mask BBox: [98, 26, 112, 41]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4136
  Bounding Box: [651.20, 1910.40, 795.20, 2012.80]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [55, 154, 66, 161]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4131
  Bounding Box: [651.20, 1974.40, 849.60, 2048.00]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [55, 159, 70, 165]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4121
  Bounding Box: [1755.20, 1862.40, 1892.80, 2038.40]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [142, 150, 151, 163]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4102
  Bounding Box: [1708.80, 491.20, 1929.60, 726.40]
  Mask Area: 157 pixels
  Mask Ratio: 0.0056
  Mask BBox: [138, 43, 154, 60]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4065
  Bounding Box: [1512.00, 1533.60, 1584.00, 1619.20]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [123, 124, 127, 130]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4050
  Bounding Box: [575.20, 1716.80, 728.80, 1873.60]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [49, 139, 60, 150]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4036
  Bounding Box: [1255.20, 1340.00, 1356.00, 1548.00]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [103, 109, 109, 124]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4031
  Bounding Box: [1323.20, 217.60, 1396.80, 296.00]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [108, 21, 111, 27]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4006
  Bounding Box: [362.40, 1809.60, 418.40, 1921.60]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [33, 146, 36, 154]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.3975
  Bounding Box: [1852.80, 667.20, 1964.80, 749.60]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [149, 57, 157, 62]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.3960
  Bounding Box: [1448.00, 1048.80, 1673.60, 1303.20]
  Mask Area: 283 pixels
  Mask Ratio: 0.0100
  Mask BBox: [118, 86, 134, 105]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.3950
  Bounding Box: [1238.40, 1546.40, 1446.40, 1761.60]
  Mask Area: 184 pixels
  Mask Ratio: 0.0065
  Mask BBox: [101, 125, 116, 141]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.3923
  Bounding Box: [683.60, 939.20, 777.60, 1008.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [58, 78, 64, 82]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.3887
  Bounding Box: [1448.00, 20.75, 1556.80, 140.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [118, 6, 125, 14]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.3872
  Bounding Box: [1432.00, 642.80, 1656.00, 819.20]
  Mask Area: 187 pixels
  Mask Ratio: 0.0066
  Mask BBox: [116, 55, 132, 67]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.3826
  Bounding Box: [86.00, 0.00, 239.60, 108.70]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [11, 4, 22, 12]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.3816
  Bounding Box: [1421.60, 1769.60, 1504.80, 1891.20]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [116, 143, 121, 151]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.3811
  Bounding Box: [113.70, 327.20, 260.00, 456.80]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [13, 30, 24, 39]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.3762
  Bounding Box: [4.70, 734.00, 64.10, 843.20]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [5, 62, 8, 69]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.3743
  Bounding Box: [2.00, 844.80, 83.30, 1032.00]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [5, 70, 8, 84]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.3735
  Bounding Box: [459.60, 1790.40, 654.80, 2043.20]
  Mask Area: 232 pixels
  Mask Ratio: 0.0082
  Mask BBox: [40, 144, 55, 163]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.3711
  Bounding Box: [896.00, 1825.60, 1168.00, 2048.00]
  Mask Area: 280 pixels
  Mask Ratio: 0.0099
  Mask BBox: [74, 147, 95, 164]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.3689
  Bounding Box: [1558.40, 1955.20, 1680.00, 2041.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [126, 157, 135, 163]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.3689
  Bounding Box: [1980.80, 778.40, 2041.60, 983.20]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [159, 65, 163, 79]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.3667
  Bounding Box: [244.00, 949.60, 304.80, 1034.40]
  Mask Area: 21 pixels
  Mask Ratio: 0.0007
  Mask BBox: [24, 79, 27, 84]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.3647
  Bounding Box: [233.20, 1576.00, 358.40, 1857.60]
  Mask Area: 165 pixels
  Mask Ratio: 0.0058
  Mask BBox: [23, 128, 31, 149]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.3647
  Bounding Box: [184.60, 1763.20, 400.80, 2009.60]
  Mask Area: 231 pixels
  Mask Ratio: 0.0082
  Mask BBox: [19, 142, 35, 160]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.3638
  Bounding Box: [1500.00, 648.00, 1726.40, 832.00]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [122, 55, 138, 68]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.3635
  Bounding Box: [1583.20, 1020.00, 1660.80, 1076.00]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [128, 84, 133, 88]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.3625
  Bounding Box: [1993.60, 1872.00, 2048.00, 2003.20]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [160, 151, 163, 160]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.3616
  Bounding Box: [372.40, 1777.60, 517.20, 1963.20]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [34, 143, 44, 157]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.3579
  Bounding Box: [651.20, 496.80, 757.60, 604.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [55, 43, 63, 51]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3579
  Bounding Box: [12.05, 259.60, 64.80, 370.80]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [5, 25, 8, 32]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3579
  Bounding Box: [944.80, 664.40, 1064.80, 910.40]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [78, 56, 87, 75]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3572
  Bounding Box: [1421.60, 508.00, 1556.00, 624.00]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [116, 44, 125, 52]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [626.40, 498.00, 728.00, 603.60]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [53, 43, 60, 51]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3552
  Bounding Box: [172.20, 490.40, 384.80, 662.40]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [18, 43, 34, 55]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3550
  Bounding Box: [256.00, 298.20, 417.20, 456.80]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [24, 28, 36, 39]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3540
  Bounding Box: [1652.80, 511.60, 1918.40, 789.60]
  Mask Area: 255 pixels
  Mask Ratio: 0.0090
  Mask BBox: [134, 44, 153, 65]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3535
  Bounding Box: [63.70, 1987.20, 199.20, 2041.60]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [9, 160, 19, 163]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3530
  Bounding Box: [1335.20, 1404.00, 1493.60, 1587.20]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [109, 114, 120, 127]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3508
  Bounding Box: [1550.40, 530.40, 1652.80, 619.20]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [126, 46, 133, 52]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3508
  Bounding Box: [1065.60, 403.20, 1385.60, 629.60]
  Mask Area: 307 pixels
  Mask Ratio: 0.0109
  Mask BBox: [88, 36, 112, 53]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3494
  Bounding Box: [120.40, 440.00, 342.80, 608.80]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [14, 39, 30, 51]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3491
  Bounding Box: [1864.00, 556.00, 2020.80, 701.60]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [150, 48, 161, 58]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3491
  Bounding Box: [1240.80, 1124.80, 1503.20, 1339.20]
  Mask Area: 256 pixels
  Mask Ratio: 0.0091
  Mask BBox: [101, 92, 121, 108]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3477
  Bounding Box: [57.20, 305.20, 232.80, 475.60]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [9, 28, 22, 41]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3423
  Bounding Box: [26.70, 1145.60, 287.20, 1280.00]
  Mask Area: 184 pixels
  Mask Ratio: 0.0065
  Mask BBox: [7, 94, 26, 103]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3408
  Bounding Box: [831.20, 528.40, 994.40, 702.00]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [69, 46, 81, 58]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3408
  Bounding Box: [415.20, 1470.40, 636.00, 1736.00]
  Mask Area: 283 pixels
  Mask Ratio: 0.0100
  Mask BBox: [37, 119, 53, 139]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3398
  Bounding Box: [660.80, 486.00, 768.80, 569.20]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [56, 42, 64, 48]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3394
  Bounding Box: [849.60, 1968.00, 947.20, 2028.80]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [71, 158, 75, 162]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3386
  Bounding Box: [376.40, 1700.80, 468.40, 1812.80]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [34, 137, 40, 145]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3386
  Bounding Box: [402.00, 1700.80, 494.00, 1812.80]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [36, 137, 41, 145]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3372
  Bounding Box: [1275.20, 802.40, 1432.00, 900.00]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [104, 67, 115, 74]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3350
  Bounding Box: [1804.80, 736.00, 1945.60, 897.60]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [145, 62, 155, 74]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3347
  Bounding Box: [1633.60, 1801.60, 1857.60, 2035.20]
  Mask Area: 210 pixels
  Mask Ratio: 0.0074
  Mask BBox: [132, 145, 149, 162]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3325
  Bounding Box: [1793.60, 737.20, 1912.00, 859.20]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [145, 62, 153, 71]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3306
  Bounding Box: [1836.80, 1886.40, 1958.40, 2043.20]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [148, 152, 156, 163]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3289
  Bounding Box: [1809.60, 744.00, 1889.60, 851.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [146, 63, 151, 70]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3286
  Bounding Box: [590.40, 502.40, 728.00, 668.00]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [51, 44, 60, 56]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3271
  Bounding Box: [1798.40, 785.60, 1926.40, 899.20]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [145, 66, 154, 74]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3252
  Bounding Box: [1659.20, 514.00, 1784.00, 626.00]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [134, 46, 143, 52]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3237
  Bounding Box: [1403.20, 434.00, 1539.20, 618.80]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [114, 38, 124, 52]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [590.80, 888.80, 675.60, 986.40]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [51, 74, 56, 80]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [590.80, 914.40, 675.60, 1012.00]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [51, 76, 56, 80]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [616.40, 914.40, 701.20, 1012.00]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [53, 76, 58, 81]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [1931.20, 1097.60, 2048.00, 1286.40]
  Mask Area: 112 pixels
  Mask Ratio: 0.0040
  Mask BBox: [155, 90, 164, 104]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [1018.40, 1418.40, 1104.80, 1480.80]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [84, 115, 90, 119]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3203
  Bounding Box: [341.60, 5.80, 518.40, 117.80]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [31, 5, 44, 13]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3184
  Bounding Box: [1689.60, 1995.20, 1801.60, 2043.20]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [136, 160, 144, 163]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3176
  Bounding Box: [310.00, 1923.20, 534.80, 2048.00]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [29, 155, 45, 163]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3154
  Bounding Box: [1580.00, 521.60, 1659.20, 628.80]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [128, 45, 133, 53]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3149
  Bounding Box: [909.60, 1326.40, 1144.80, 1539.20]
  Mask Area: 197 pixels
  Mask Ratio: 0.0070
  Mask BBox: [76, 108, 93, 124]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3140
  Bounding Box: [0.00, 1104.80, 104.00, 1221.60]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [4, 91, 12, 99]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3137
  Bounding Box: [1267.20, 0.00, 1459.20, 122.20]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [103, 4, 117, 13]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3123
  Bounding Box: [172.40, 1588.80, 354.00, 1745.60]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [18, 129, 31, 140]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3110
  Bounding Box: [178.20, 523.60, 304.60, 641.20]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [18, 45, 25, 54]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3103
  Bounding Box: [1702.40, 928.00, 1801.60, 1089.60]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [137, 77, 144, 89]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3103
  Bounding Box: [1728.00, 928.00, 1827.20, 1089.60]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [139, 77, 145, 89]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3103
  Bounding Box: [0.00, 388.40, 105.80, 554.80]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [4, 35, 12, 47]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3091
  Bounding Box: [474.40, 1184.00, 541.60, 1299.20]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [42, 97, 46, 105]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [131.90, 1884.80, 294.00, 2041.60]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [15, 152, 26, 163]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3052
  Bounding Box: [195.40, 1112.00, 357.00, 1347.20]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [20, 91, 31, 109]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3049
  Bounding Box: [270.20, 1912.00, 482.40, 2048.00]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [26, 154, 41, 164]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3025
  Bounding Box: [1755.20, 1297.60, 1860.80, 1392.00]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [142, 106, 149, 112]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3025
  Bounding Box: [1755.20, 1323.20, 1860.80, 1417.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [142, 108, 149, 113]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3003
  Bounding Box: [1977.60, 327.00, 2038.40, 420.40]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [159, 30, 163, 36]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3003
  Bounding Box: [2003.20, 327.00, 2048.00, 420.40]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [161, 30, 163, 36]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3003
  Bounding Box: [1977.60, 352.60, 2038.40, 446.00]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [159, 32, 163, 38]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3003
  Bounding Box: [2003.20, 352.60, 2048.00, 446.00]
  Mask Area: 21 pixels
  Mask Ratio: 0.0007
  Mask BBox: [161, 32, 163, 38]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3003
  Bounding Box: [711.20, 1347.20, 940.00, 1654.40]
  Mask Area: 341 pixels
  Mask Ratio: 0.0121
  Mask BBox: [60, 110, 77, 133]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.2988
  Bounding Box: [1035.20, 1267.20, 1224.00, 1438.40]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [85, 103, 99, 116]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.2986
  Bounding Box: [852.00, 393.20, 956.00, 530.00]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [71, 35, 78, 45]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.2966
  Bounding Box: [635.20, 1840.00, 812.80, 2019.20]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [54, 148, 67, 161]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.2964
  Bounding Box: [609.20, 1107.20, 700.40, 1225.60]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [52, 91, 58, 99]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.2954
  Bounding Box: [217.40, 1088.00, 372.00, 1204.80]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [21, 89, 30, 98]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.2949
  Bounding Box: [1908.80, 1112.80, 2040.00, 1248.80]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [154, 91, 163, 101]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.2944
  Bounding Box: [1328.00, 643.60, 1524.80, 901.60]
  Mask Area: 283 pixels
  Mask Ratio: 0.0100
  Mask BBox: [108, 55, 123, 74]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.2927
  Bounding Box: [263.20, 1440.80, 464.80, 1646.40]
  Mask Area: 206 pixels
  Mask Ratio: 0.0073
  Mask BBox: [25, 117, 40, 132]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.2920
  Bounding Box: [1494.40, 1782.40, 1728.00, 2038.40]
  Mask Area: 257 pixels
  Mask Ratio: 0.0091
  Mask BBox: [121, 144, 138, 163]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.2915
  Bounding Box: [645.60, 1881.60, 798.40, 1990.40]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [55, 151, 66, 159]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.2910
  Bounding Box: [1942.40, 707.60, 2048.00, 961.60]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [156, 60, 164, 79]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.2869
  Bounding Box: [1550.40, 546.40, 1636.80, 652.80]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [126, 47, 131, 53]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.2869
  Bounding Box: [1576.00, 546.40, 1662.40, 652.80]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [128, 47, 133, 54]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.2866
  Bounding Box: [607.60, 884.80, 702.80, 971.20]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [52, 74, 58, 79]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.2859
  Bounding Box: [1254.40, 1129.60, 1376.00, 1259.20]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [102, 93, 111, 102]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.2837
  Bounding Box: [316.80, 279.00, 569.60, 508.80]
  Mask Area: 252 pixels
  Mask Ratio: 0.0089
  Mask BBox: [29, 26, 48, 43]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.2822
  Bounding Box: [1561.60, 1006.40, 1644.80, 1065.60]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [126, 84, 132, 87]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.2822
  Bounding Box: [1587.20, 1006.40, 1670.40, 1065.60]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [128, 83, 134, 87]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.2822
  Bounding Box: [1561.60, 1032.00, 1644.80, 1091.20]
  Mask Area: 17 pixels
  Mask Ratio: 0.0006
  Mask BBox: [126, 85, 132, 87]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.2822
  Bounding Box: [1587.20, 1032.00, 1670.40, 1091.20]
  Mask Area: 15 pixels
  Mask Ratio: 0.0005
  Mask BBox: [128, 85, 134, 87]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.2805
  Bounding Box: [1699.20, 861.60, 1811.20, 1013.60]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [137, 72, 145, 83]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.2800
  Bounding Box: [1367.20, 1371.20, 1432.80, 1448.00]
  Mask Area: 17 pixels
  Mask Ratio: 0.0006
  Mask BBox: [111, 112, 115, 115]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.2786
  Bounding Box: [423.20, 592.00, 640.80, 889.60]
  Mask Area: 288 pixels
  Mask Ratio: 0.0102
  Mask BBox: [38, 51, 54, 73]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.2781
  Bounding Box: [342.40, 518.00, 568.00, 716.40]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [31, 45, 48, 59]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.2751
  Bounding Box: [6.80, 1753.60, 58.50, 1872.00]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [5, 141, 8, 148]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.2737
  Bounding Box: [96.30, 1627.20, 185.00, 1736.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [12, 132, 18, 139]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.2732
  Bounding Box: [454.80, 1012.00, 518.00, 1151.20]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [40, 84, 44, 93]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.2725
  Bounding Box: [1859.20, 738.40, 1987.20, 903.20]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [150, 62, 159, 74]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.2722
  Bounding Box: [1064.00, 1198.40, 1166.40, 1318.40]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [88, 98, 95, 106]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.2715
  Bounding Box: [400.80, 1955.20, 548.80, 2048.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [36, 157, 46, 163]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.2698
  Bounding Box: [210.60, 1575.20, 344.20, 1716.80]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [21, 128, 30, 138]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2698
  Bounding Box: [1176.00, 870.40, 1313.60, 1105.60]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [96, 72, 106, 90]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2688
  Bounding Box: [11.45, 914.40, 73.80, 1050.40]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 76, 8, 86]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2686
  Bounding Box: [73.40, 1264.00, 185.00, 1332.80]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [10, 103, 18, 107]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2683
  Bounding Box: [1361.60, 1012.00, 1614.40, 1285.60]
  Mask Area: 326 pixels
  Mask Ratio: 0.0116
  Mask BBox: [111, 84, 130, 104]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2681
  Bounding Box: [1428.00, 543.20, 1527.20, 624.80]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [116, 47, 123, 52]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2673
  Bounding Box: [78.20, 1827.20, 253.00, 1996.80]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [11, 147, 23, 159]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2666
  Bounding Box: [215.00, 1612.80, 343.80, 1747.20]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [22, 130, 30, 140]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2664
  Bounding Box: [1046.40, 1349.60, 1164.80, 1476.00]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [86, 110, 92, 117]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2659
  Bounding Box: [854.40, 570.40, 1027.20, 864.80]
  Mask Area: 191 pixels
  Mask Ratio: 0.0068
  Mask BBox: [71, 49, 84, 71]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2649
  Bounding Box: [693.20, 1100.00, 888.00, 1364.00]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [59, 90, 73, 110]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2605
  Bounding Box: [216.60, 1220.80, 391.20, 1393.60]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [21, 100, 34, 112]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2603
  Bounding Box: [174.00, 1209.60, 361.60, 1374.40]
  Mask Area: 171 pixels
  Mask Ratio: 0.0061
  Mask BBox: [18, 99, 32, 111]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2581
  Bounding Box: [1352.00, 415.20, 1460.80, 548.00]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [110, 37, 118, 46]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2576
  Bounding Box: [915.20, 1651.20, 1100.80, 1900.80]
  Mask Area: 257 pixels
  Mask Ratio: 0.0091
  Mask BBox: [76, 133, 89, 152]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2573
  Bounding Box: [1884.80, 499.60, 1971.20, 586.80]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [152, 44, 157, 48]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2563
  Bounding Box: [1996.80, 364.00, 2044.80, 504.80]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [160, 33, 163, 43]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2563
  Bounding Box: [399.20, 1720.00, 533.60, 1851.20]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [36, 139, 45, 148]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2537
  Bounding Box: [0.00, 105.40, 87.60, 274.20]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [4, 13, 9, 25]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2529
  Bounding Box: [1476.80, 1860.80, 1630.40, 2048.00]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [120, 150, 131, 164]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2527
  Bounding Box: [1380.80, 190.20, 1587.20, 433.60]
  Mask Area: 244 pixels
  Mask Ratio: 0.0086
  Mask BBox: [112, 19, 127, 37]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2520
  Bounding Box: [409.20, 462.00, 534.80, 544.40]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [36, 41, 45, 46]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2505
  Bounding Box: [0.00, 1116.00, 169.60, 1272.80]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [3, 92, 17, 103]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2502
  Bounding Box: [1974.40, 402.40, 2041.60, 546.40]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [159, 36, 163, 46]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2498
  Bounding Box: [970.40, 1780.80, 1108.00, 1976.00]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [80, 144, 90, 158]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2496
  Bounding Box: [1527.20, 1630.40, 1606.40, 1713.60]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [124, 132, 129, 137]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2489
  Bounding Box: [366.80, 14.30, 593.20, 115.90]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [33, 6, 50, 13]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2482
  Bounding Box: [0.00, 1111.20, 75.00, 1240.80]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [4, 91, 8, 100]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2477
  Bounding Box: [1416.00, 987.20, 1609.60, 1161.60]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [115, 82, 129, 94]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2466
  Bounding Box: [907.20, 452.40, 1011.20, 581.20]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [75, 40, 82, 49]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2451
  Bounding Box: [842.40, 445.60, 924.00, 546.40]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [70, 39, 76, 45]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2451
  Bounding Box: [868.00, 445.60, 949.60, 546.40]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [72, 39, 78, 46]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2448
  Bounding Box: [258.40, 251.40, 396.80, 376.80]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [25, 24, 34, 33]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2434
  Bounding Box: [421.20, 338.80, 601.20, 594.80]
  Mask Area: 215 pixels
  Mask Ratio: 0.0076
  Mask BBox: [37, 31, 50, 50]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2429
  Bounding Box: [0.00, 1093.60, 46.60, 1189.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [3, 90, 7, 96]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2429
  Bounding Box: [0.00, 1119.20, 46.60, 1215.20]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [3, 92, 7, 98]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2429
  Bounding Box: [150.90, 420.00, 423.60, 640.80]
  Mask Area: 239 pixels
  Mask Ratio: 0.0085
  Mask BBox: [16, 37, 37, 54]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2428
  Bounding Box: [0.00, 1764.80, 108.60, 1940.80]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [4, 142, 12, 155]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2405
  Bounding Box: [1657.60, 1031.20, 1795.20, 1157.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [134, 85, 144, 94]

