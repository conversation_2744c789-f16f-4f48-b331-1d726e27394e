# -*- coding: utf-8 -*-
"""
SegFormerHead（查询式解码 + 掩码生成）
------------------------------------
输入：PixelAdapter 返回的多尺度 memory 四元组 + mask_features
输出：
  - pred_logits: [B, Nq, C+1]  （包含 no-object 类）
  - pred_masks:  [B, Nq, Hm, Wm]
  - aux_outputs: list(与解码层等长)，每层一个 dict(pred_logits, pred_masks) 用于 aux loss

说明：
- 这里默认用“标准”多头注意力实现 cross-attention（keys=flattened memory），不依赖自定义 CUDA op。
  在有 MSDeformAttn 的环境下，你只需将 TransformerDecoderLayer 中 cross_attn 替换为
  MultiScaleDeformableAttention 即可（注释处已标注）。
"""

from typing import Dict, List, Tuple, Optional

import math
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.nn.init as init


class MLP(nn.Module):
    """简单 3 层 MLP，用于 mask embedding / FFN"""

    def __init__(self, input_dim, hidden_dim, output_dim, num_layers):
        super().__init__()
        layers = []
        for i in range(num_layers):
            in_c = input_dim if i == 0 else hidden_dim
            out_c = output_dim if i == num_layers - 1 else hidden_dim
            layers += [nn.Linear(in_c, out_c)]
            if i < num_layers - 1:
                layers += [nn.ReLU(inplace=True)]
        self.mlp = nn.Sequential(*layers)

    def forward(self, x):
        return self.mlp(x)


class TransformerDecoderLayer(nn.Module):
    """
    单层解码：Self-Attn → Cross-Attn → FFN
    - 这里 cross-attn 用标准 MHA（Q: queries, K/V: memory+pos）
    - 若使用 MSDeformAttn，请在此处替换 cross-attn（见注释）
    """

    def __init__(self, d_model=256, nhead=8, dim_feedforward=1024, dropout=0.0):
        super().__init__()
        self.self_attn = nn.MultiheadAttention(d_model, nhead, dropout=dropout, batch_first=True)
        self.cross_attn = nn.MultiheadAttention(d_model, nhead, dropout=dropout, batch_first=True)

        self.linear1 = nn.Linear(d_model, dim_feedforward)
        self.linear2 = nn.Linear(dim_feedforward, d_model)

        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.norm3 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        self.activation = nn.ReLU(inplace=True)

    def forward(self, tgt: torch.Tensor, memory: torch.Tensor, memory_pos: Optional[torch.Tensor] = None):
        """
        Args:
            tgt:     [B, Nq, C]
            memory:  [B, Nmem, C]
            memory_pos: [B, Nmem, C] 或 None
        """
        # Self-Attention
        q = k = self.norm1(tgt)
        tgt2, _ = self.self_attn(q, k, value=tgt)
        tgt = tgt + self.dropout(tgt2)

        # Cross-Attention  (替换点：可换成 MSDeformAttn)
        tgt_norm = self.norm2(tgt)
        mem_k = memory if memory_pos is None else (memory + memory_pos)
        cross, _ = self.cross_attn(query=tgt_norm, key=mem_k, value=memory)
        tgt = tgt + self.dropout(cross)

        # FFN
        y = self.norm3(tgt)
        y = self.linear2(self.activation(self.linear1(y)))
        tgt = tgt + self.dropout(y)
        return tgt


def _build_2d_sincos_pos_embed(h: int, w: int, dim: int, device=None, dtype=torch.float32) -> torch.Tensor:
    """为 memory 再构建一次 2D 正余弦位置编码（若 PixelAdapter 已加过也没问题）"""
    if dim % 4 != 0:
        raise ValueError(f"pos embed dim {dim} must be divisible by 4.")

    y, x = torch.meshgrid(
        torch.arange(h, device=device, dtype=dtype),
        torch.arange(w, device=device, dtype=dtype),
        indexing="ij",
    )
    omega = torch.arange(dim // 4, device=device, dtype=dtype)
    omega = 1.0 / (10000 ** (omega / (dim // 4)))

    out_y = torch.einsum("hw,c->hwc", y, omega)
    out_x = torch.einsum("hw,c->hwc", x, omega)

    pos = torch.cat([torch.sin(out_x), torch.cos(out_x), torch.sin(out_y), torch.cos(out_y)], dim=-1)  # [h,w,dim]
    pos = pos.reshape(h * w, dim)
    return pos


class SegFormerHead(nn.Module):
    """
    查询式分割头：
      - learnable queries + 解码器
      - 分类头（C+1，含 no-object）
      - 掩码头：query -> mask embedding，与 mask_features 做点积得到高分辨率掩码
    """

    def __init__(
        self,
        nc: int,
        nq: int = 100,
        dec_layers: int = 6,
        nheads: int = 8,
        num_points: int = 4,        # 预留：若切换到 MSDeformAttn 可用
        mask_stride: int = 4,       # 输入 640 -> 掩码 160 x 160
        no_object_weight: float = 0.1,
        aux_loss: bool = True,
        d_model: int = 256,
        dim_feedforward: int = 1024,
        dropout: float = 0.0,
    ):
        super().__init__()
        self.nc = nc
        self.nq = nq
        self.aux_loss = aux_loss
        self.mask_stride = mask_stride
        self.no_object_weight = no_object_weight
        self.d_model = d_model

        # 查询向量 & 初始参考点（L=3 尺度，每个 query 一个 2D 参考，sigmoid 到 [0,1]）
        self.query_embed = nn.Embedding(nq, d_model)
        self.reference_points = nn.Embedding(nq * 3, 2)  # (nq, L, 2) 展平存
        init.uniform_(self.reference_points.weight, a=0.0, b=1.0)

        # 解码器
        self.decoder = nn.ModuleList([
            TransformerDecoderLayer(d_model=d_model, nhead=nheads, dim_feedforward=dim_feedforward, dropout=dropout)
            for _ in range(dec_layers)
        ])

        # 预测头
        self.class_head = nn.Linear(d_model, nc + 1)             # 包含 no-object
        self.mask_embed_head = MLP(d_model, d_model, d_model, 3) # 3 层 MLP，输出维度与 mask_features 通道一致

    @staticmethod
    def _split_memory_by_levels(memory: torch.Tensor, spatial_shapes: torch.Tensor) -> List[torch.Tensor]:
        """
        将 [B, Nmem, C] 切回各尺度 feature map: [B, C, H, W]
        """
        b, n, c = memory.shape
        outs = []
        start = 0
        for (h, w) in spatial_shapes.tolist():
            length = h * w
            m = memory[:, start:start + length, :]  # [B, h*w, C]
            m = m.transpose(1, 2).contiguous().reshape(b, c, h, w)  # [B,C,H,W]
            outs.append(m)
            start += length
        return outs

    @staticmethod
    def _build_memory_pos(spatial_shapes: torch.Tensor, c: int, device, dtype) -> torch.Tensor:
        """
        为拼接后的 memory 构造位置编码（按各尺度顺序拼接）
        """
        pos_list = []
        for (h, w) in spatial_shapes.tolist():
            pos = _build_2d_sincos_pos_embed(h, w, c, device=device, dtype=dtype)  # [h*w, c]
            pos_list.append(pos)
        pos = torch.cat(pos_list, dim=0).unsqueeze(0)  # [1, Nmem, c]
        return pos

    def _layer_outputs(self, queries: torch.Tensor, memory: torch.Tensor, memory_pos: torch.Tensor):
        """
        通过所有解码层，保存每层 aux 输出（用于 aux loss）
        """
        intermediate = []
        for layer in self.decoder:
            queries = layer(queries, memory, memory_pos)
            if self.aux_loss:
                cls_logits = self.class_head(queries)                # [B,Nq,C+1]
                mask_embed = self.mask_embed_head(queries)           # [B,Nq,C]
                intermediate.append({"pred_logits": cls_logits, "mask_embed": mask_embed})
        return queries, intermediate

    def forward(self, x):
        """
        Args:
            x: PixelAdapter 的输出 tuple:
               (value, spatial_shapes, level_start_index, valid_ratios, mask_features)
               value:           [B, 8400, 256]
               spatial_shapes:  [3, 2] -> [[20,20],[40,40],[80,80]]
               level_start_index:[3]    -> [0,400,2000]
               valid_ratios:    [B, 3, 2]
               mask_features:   [B, 256, Hm, Wm]（通常 Hm=160, Wm=160）
        Returns:
            dict = {
              'pred_logits': [B,Nq,C+1],
              'pred_masks':  [B,Nq,Hm,Wm],
              'aux_outputs': list(dict) or None
            }
        """
        (memory, spatial_shapes, level_start_index, valid_ratios, mask_features) = x
        b, nmem, c = memory.shape
        device, dtype = memory.device, memory.dtype
        hm, wm = mask_features.shape[-2:]

        # memory 的位置编码（若 PixelAdapter 已经加过，这里再加等价 residual 提示；也可设为 0）
        memory_pos = self._build_memory_pos(spatial_shapes, c, device, dtype).expand(b, -1, -1)  # [B,Nmem,C]

        # 初始化 queries（可加上参考点编码用于引导）
        queries = self.query_embed.weight.view(1, self.nq, c).expand(b, -1, -1)  # [B,Nq,C]
        # 参考点（未在本实现中用于 deformable 采样；若切换 MSDeformAttn 可直接使用）
        _ = torch.sigmoid(self.reference_points.weight.view(self.nq, 3, 2)).unsqueeze(0).expand(b, -1, -1, -1)

        # 通过解码器（Self-Attn + Cross-Attn (to memory) + FFN）
        queries, intermediate = self._layer_outputs(queries, memory, memory_pos)  # [B,Nq,C]

        # 最终预测
        pred_logits = self.class_head(queries)      # [B,Nq,C+1]
        mask_embed = self.mask_embed_head(queries)  # [B,Nq,C]
        # 掩码生成：与高分辨率 mask_features 做点积
        pred_masks = torch.einsum("bqc,bchw->bqhw", mask_embed, mask_features)  # [B,Nq,Hm,Wm]

        out = {
            "pred_logits": pred_logits,
            "pred_masks": pred_masks,
        }

        if self.aux_loss and len(intermediate):
            # 将中间层的 mask_embed 转换为掩码，便于 aux loss
            aux = []
            for i, item in enumerate(intermediate):
                aux_masks = torch.einsum("bqc,bchw->bqhw", item["mask_embed"], mask_features)
                aux.append({
                    "pred_logits": item["pred_logits"],
                    "pred_masks": aux_masks
                })
            out["aux_outputs"] = aux

        return out
