# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license
"""
SegFormer 分割评估指标（实例级）:
- mAP@[0.50:0.95] / AP50 / AP75（按掩码 IoU，COCO 风格）
- Precision / Recall / F1（基于 IoU>0.5 贪心匹配）
- 匹配对的平均 Dice / 平均 IoU
"""

from __future__ import annotations
import math
from typing import Dict, List, Tuple, Optional

import numpy as np
import torch
import torch.nn.functional as F


# ----------------------------- 基础工具 -----------------------------

def _to_numpy(x: torch.Tensor | np.ndarray) -> np.ndarray:
    if isinstance(x, np.ndarray):
        return x
    return x.detach().cpu().numpy()


def _binarize(m: torch.Tensor, thr: float = 0.5) -> torch.Tensor:
    # 输入 [N,H,W]，输出 bool
    if m.dtype.is_floating_point:
        return (m >= thr)
    return (m > 0)


def _mask_iou_matrix(pred: torch.Tensor, gt: torch.Tensor, eps: float = 1e-7) -> torch.Tensor:
    """
    计算掩码 IoU 矩阵：pred [Np,H,W] (bool), gt [Ng,H,W] (bool) -> [Np,Ng]
    """
    if pred.numel() == 0 or gt.numel() == 0:
        return pred.new_zeros((pred.shape[0], gt.shape[0]))
    pred = pred.bool()
    gt = gt.bool()
    Np, Ng = pred.shape[0], gt.shape[0]
    pred_flat = pred.view(Np, -1).float()
    gt_flat = gt.view(Ng, -1).float()
    inter = pred_flat @ gt_flat.t()  # [Np,Ng]
    pred_area = pred_flat.sum(dim=1, keepdim=True)  # [Np,1]
    gt_area = gt_flat.sum(dim=1, keepdim=True).t()  # [1,Ng]
    union = pred_area + gt_area - inter
    return inter / (union + eps)


def _dice_from_iou(iou: torch.Tensor, eps: float = 1e-7) -> torch.Tensor:
    # Dice = 2*IoU / (1+IoU)
    return (2.0 * iou) / (1.0 + iou + eps)


def _interpolate_to(m: torch.Tensor, size_hw: Tuple[int, int]) -> torch.Tensor:
    # 将 [Q,Hp,Wp] 上采样到 [Q,Hg,Wg]
    if m.shape[-2:] == size_hw:
        return m
    m = m.unsqueeze(1).float()  # [Q,1,H,W]
    m = F.interpolate(m, size_hw, mode="bilinear", align_corners=False)
    return m[:, 0]


def _softmax_scores(logits: torch.Tensor, num_classes: int) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    对 pred_logits 做 softmax：
      - 若 logits.shape[-1] == num_classes + 1 ：最后一类视作背景类
      - 若 logits.shape[-1] == num_classes     ：无背景类
    返回：
      - scores: [Q] 每个查询的置信度（obj_prob * best_class_prob 或 best_class_prob）
      - labels: [Q] 预测类别（排除背景）
    """
    C_or_Cp1 = logits.shape[-1]
    prob = logits.float().softmax(-1)  # [Q,C or C+1]

    if C_or_Cp1 == num_classes + 1:
        obj_prob = 1.0 - prob[..., -1]                # 非背景概率
        cls_prob, labels = prob[..., :-1].max(dim=-1) # 选取前 num_classes
        scores = obj_prob * cls_prob
    elif C_or_Cp1 == num_classes:
        cls_prob, labels = prob.max(dim=-1)
        scores = cls_prob
    else:
        raise ValueError(f"pred_logits last dim={C_or_Cp1} not match nc({num_classes}) or nc+1")
    return scores, labels


# ----------------------------- AP 计算 -----------------------------

def _compute_precision_recall(tp: np.ndarray, fp: np.ndarray, npos: int) -> Tuple[np.ndarray, np.ndarray]:
    """基于排好序的 tp/fp 序列计算 P/R 曲线"""
    tp_cum = np.cumsum(tp)
    fp_cum = np.cumsum(fp)
    precision = tp_cum / np.maximum(tp_cum + fp_cum, 1e-12)
    recall = tp_cum / max(npos, 1e-12)
    return precision, recall


def _compute_ap_from_pr(precision: np.ndarray, recall: np.ndarray) -> float:
    """
    11/101 点插值 AP：这里采用 COCO 常用的 101 点插值（r=0..1 step 0.01）
    """
    # 对 recall 去重并取最大精度的后向包络
    mrec = np.concatenate(([0.0], recall, [1.0]))
    mpre = np.concatenate(([0.0], precision, [0.0]))
    for i in range(mpre.size - 1, 0, -1):
        mpre[i - 1] = max(mpre[i - 1], mpre[i])

    # 101-point
    recall_points = np.linspace(0, 1, 101)
    ap = 0.0
    for r in recall_points:
        p = mpre[mrec >= r].max() if np.any(mrec >= r) else 0
        ap += p
    return ap / 101.0


def _ap_per_class_for_threshold(
    preds_per_cls: List[Tuple[int, float, np.ndarray]],
    gts_per_img: Dict[int, List[np.ndarray]],
    iou_thr: float,
) -> Tuple[float, np.ndarray, np.ndarray, int]:
    """
    单个 IoU 阈值下的 AP 计算（对某一类别）：
    preds_per_cls: [(img_id, score, mask_bool[H,W]), ...] 已按 score 降序排序
    gts_per_img:   {img_id: [mask_bool, ...]}
    返回：ap, precision, recall, npos
    """
    if len(preds_per_cls) == 0 and all(len(v) == 0 for v in gts_per_img.values()):
        return 0.0, np.array([1.0]), np.array([0.0]), 0

    # 统计正样本数
    npos = sum(len(v) for v in gts_per_img.values())

    # 每张图对该 IoU 阈值的 GT 匹配标记（防复配）
    matched: Dict[int, np.ndarray] = {}
    for img_id, masks in gts_per_img.items():
        matched[img_id] = np.zeros((len(masks),), dtype=bool)

    tp = np.zeros((len(preds_per_cls),), dtype=float)
    fp = np.zeros_like(tp)

    # 遍历预测（已按 score 排序）
    for i, (img_id, score, pmask) in enumerate(preds_per_cls):
        gtmasks = gts_per_img.get(img_id, [])
        if len(gtmasks) == 0:
            fp[i] = 1.0
            continue

        # 计算与该图所有 GT 的 IoU，选最大未匹配的
        ious = []
        for gm in gtmasks:
            inter = (pmask & gm).sum()
            union = (pmask | gm).sum()
            ious.append(inter / (union + 1e-7))
        ious = np.asarray(ious, dtype=float)

        best_gt = int(ious.argmax())
        best_iou = float(ious[best_gt])
        if best_iou >= iou_thr and not matched[img_id][best_gt]:
            tp[i] = 1.0
            matched[img_id][best_gt] = True
        else:
            fp[i] = 1.0

    precision, recall = _compute_precision_recall(tp, fp, npos)
    ap = _compute_ap_from_pr(precision, recall)
    return ap, precision, recall, npos


# --------------------------- 主评估器类 ----------------------------

class SegmentationEvaluator:
    """
    面向 SegFormerHead 输出的实例分割评估器：
      - update(outputs, targets) 批次累积
      - compute() 计算整体指标
    约定：
      outputs: {'pred_logits':[B,Q,C or C+1], 'pred_masks':[B,Q,Hm,Wm]}
      targets: List[{'labels':[Ni], 'masks':[Ni,Hg,Wg]}]，各样本实例顺序任意
    """

    def __init__(
        self,
        num_classes: int,
        mask_thr: float = 0.5,
        conf_thr: float = 0.05,
        iou_thresholds: Optional[List[float]] = None,
        topk: Optional[int] = None,
    ):
        """
        Args:
            num_classes: 类别数（不含背景）
            mask_thr:    掩码二值化阈值（用于 IoU/Dice）
            conf_thr:    置信度过滤阈值（用于评估）
            iou_thresholds: AP 评估 IoU 阈值集合，默认 0.50:0.95 步长 0.05
            topk:        每图最多保留的预测数（None 表示不限）
        """
        self.nc = num_classes
        self.mask_thr = float(mask_thr)
        self.conf_thr = float(conf_thr)
        self.iou_thrs = (
            np.arange(0.50, 0.96, 0.05) if iou_thresholds is None else np.asarray(iou_thresholds, dtype=float)
        )
        self.topk = topk

        self.reset()

    def reset(self):
        # 存放评估所需的“全量”记录
        self._records: List[Dict] = []  # 每个样本一条：{'pred':{...}, 'gt':{...}}
        # 便于计算整体 P/R/F1 & Dice/IoU
        self._global_stats = {
            "matched_iou": [],
            "matched_dice": [],
            "num_pred": 0,
            "num_gt": 0,
            "num_matched_05": 0,  # IoU>=0.5 的匹配数
        }

    def update(self, outputs: Dict[str, torch.Tensor], targets: List[Dict[str, torch.Tensor]]) -> None:
        """
        累积一个批次：
          - 自动将预测掩码上采样到 GT 尺度
          - 预测 logits 做 softmax，取每查询的 (score,label)
          - 过滤 conf < conf_thr，按 score 排序并可裁到 topk
        """
        pred_logits = outputs["pred_logits"]  # [B,Q,C or C+1]
        pred_masks = outputs["pred_masks"]    # [B,Q,Hm,Wm]
        B, Q = pred_logits.shape[:2]

        for b in range(B):
            tgt = targets[b]
            gt_labels: torch.Tensor = tgt["labels"].to(pred_logits.device)    # [Ng]
            gt_masks: torch.Tensor = tgt["masks"].to(pred_masks.device)       # [Ng,Hg,Wg]

            Hg, Wg = (int(gt_masks.shape[-2]), int(gt_masks.shape[-1])) if gt_masks.numel() else pred_masks.shape[-2:]

            # 1) 预测处理：得分/类别 + 上采样到 GT 尺寸 + 二值化
            scores, labels = _softmax_scores(pred_logits[b], self.nc)  # [Q],[Q]
            pm = _interpolate_to(pred_masks[b], (Hg, Wg)).sigmoid()    # [Q,Hg,Wg]，保险起见做 sigmoid
            if self.topk is not None and self.topk < Q:
                # 先做置信度过滤再 topk，减少无意义开销
                keep = scores >= self.conf_thr
                scores, labels, pm = scores[keep], labels[keep], pm[keep]
                if scores.numel() > self.topk:
                    topk_idx = torch.topk(scores, self.topk).indices
                    scores, labels, pm = scores[topk_idx], labels[topk_idx], pm[topk_idx]
            else:
                keep = scores >= self.conf_thr
                scores, labels, pm = scores[keep], labels[keep], pm[keep]

            pm_bin = _binarize(pm, self.mask_thr)  # bool [Np,Hg,Wg]

            # 2) 统计全局匹配（IoU>=0.5 的贪心匹配，用于 P/R/F1 与均值 Dice/IoU）
            num_pred = int(pm_bin.shape[0])
            num_gt = int(gt_masks.shape[0])
            self._global_stats["num_pred"] += num_pred
            self._global_stats["num_gt"] += num_gt

            if num_pred > 0 and num_gt > 0:
                giou = _mask_iou_matrix(pm_bin, (gt_masks > 0))  # [Np,Ng]
                giou_np = _to_numpy(giou)
                matched_pred = np.zeros((num_pred,), dtype=bool)
                matched_gt = np.zeros((num_gt,), dtype=bool)

                # 贪心：每次取全局最大 IoU
                while True:
                    i, j = np.unravel_index(np.argmax(giou_np, axis=None), giou_np.shape)
                    max_iou = giou_np[i, j]
                    if max_iou < 0.5:
                        break
                    if matched_pred[i] or matched_gt[j]:
                        giou_np[i, j] = -1.0
                        continue
                    matched_pred[i] = True
                    matched_gt[j] = True
                    self._global_stats["num_matched_05"] += 1
                    self._global_stats["matched_iou"].append(float(max_iou))
                    self._global_stats["matched_dice"].append(float(_to_numpy(_dice_from_iou(giou[i, j]))))
                    giou_np[i, :] = -1.0
                    giou_np[:, j] = -1.0

            # 3) 保存用于 mAP 的记录（按类别拆分，延后统一算 AP）
            #    每张图片记录：各类别的预测（img_id, score, mask_bool）与 GT（mask_bool 列表）
            rec = {
                "img_id": len(self._records),  # 连续编号
                "pred_by_cls": {c: [] for c in range(self.nc)},
                "gt_by_cls": {c: [] for c in range(self.nc)},
            }

            # GT 分类拆分
            if num_gt > 0:
                gt_bin = (gt_masks > 0)  # [Ng,Hg,Wg]
                for gi in range(num_gt):
                    c = int(gt_labels[gi].item())
                    if 0 <= c < self.nc:
                        rec["gt_by_cls"][c].append(_to_numpy(gt_bin[gi]))

            # PRED 分类拆分（过滤背景预测；labels 已不含背景编号）
            if num_pred > 0:
                pm_np = _to_numpy(pm_bin)
                scores_np = _to_numpy(scores)
                labels_np = _to_numpy(labels)
                for pi in range(num_pred):
                    c = int(labels_np[pi])
                    if 0 <= c < self.nc:
                        rec["pred_by_cls"][c].append((rec["img_id"], float(scores_np[pi]), pm_np[pi]))

            self._records.append(rec)

    # ------------------------------- 汇总 -------------------------------

    def compute(self) -> Dict[str, float]:
        """
        汇总所有 update() 的记录，计算：
          - mAP@[.50:.95]、AP50、AP75
          - Precision / Recall / F1（IoU>=0.5）
          - mean Dice / mean IoU（仅对匹配对）
        """
        if len(self._records) == 0:
            return dict(mAP=0.0, AP50=0.0, AP75=0.0, precision=0.0, recall=0.0, f1=0.0, dice=0.0, iou=0.0)

        # 1) 为 AP 计算聚合所有图片的 per-class 预测与 GT
        preds_all: Dict[int, List[Tuple[int, float, np.ndarray]]] = {c: [] for c in range(self.nc)}
        gts_all: Dict[int, Dict[int, List[np.ndarray]]] = {c: {} for c in range(self.nc)}  # {c: {img_id: [gm,...]}}
        for rec in self._records:
            img_id = rec["img_id"]
            for c in range(self.nc):
                # 预测
                if len(rec["pred_by_cls"][c]) > 0:
                    preds_all[c].extend(rec["pred_by_cls"][c])
                # GT
                gts = rec["gt_by_cls"][c]
                if len(gts) > 0:
                    gts_all[c][img_id] = gts

        # 2) 逐类别、逐 IoU 阈值计算 AP
        aps_per_cls_thr = {thr: [] for thr in self.iou_thrs}
        ap50_per_cls = []
        ap75_per_cls = []

        for c in range(self.nc):
            preds_c = preds_all[c]
            # 按 score 排序（降序）
            if len(preds_c) > 0:
                preds_c = sorted(preds_c, key=lambda x: -x[1])
            # 该类 GT
            gtc = gts_all[c]

            # IoU 阈值扫
            for thr in self.iou_thrs:
                ap, _, _, npos = _ap_per_class_for_threshold(preds_c, gtc, float(thr))
                if npos > 0:
                    aps_per_cls_thr[thr].append(ap)

            # 记录 AP50 / AP75
            ap50, _, _, npos50 = _ap_per_class_for_threshold(preds_c, gtc, 0.50)
            ap75, _, _, npos75 = _ap_per_class_for_threshold(preds_c, gtc, 0.75)
            if npos50 > 0:
                ap50_per_cls.append(ap50)
            if npos75 > 0:
                ap75_per_cls.append(ap75)

        # 聚合 mAP
        ap_thr_vals = []
        for thr in self.iou_thrs:
            clist = aps_per_cls_thr[thr]
            if len(clist) > 0:
                ap_thr_vals.append(float(np.mean(clist)))
        mAP = float(np.mean(ap_thr_vals)) if len(ap_thr_vals) > 0 else 0.0
        AP50 = float(np.mean(ap50_per_cls)) if len(ap50_per_cls) > 0 else 0.0
        AP75 = float(np.mean(ap75_per_cls)) if len(ap75_per_cls) > 0 else 0.0

        # 3) 全局 P/R/F1（IoU>=0.5 贪心匹配统计）
        pr_num_pred = self._global_stats["num_pred"]
        pr_num_gt = self._global_stats["num_gt"]
        pr_num_matched = self._global_stats["num_matched_05"]

        precision = (pr_num_matched / pr_num_pred) if pr_num_pred > 0 else 0.0
        recall = (pr_num_matched / pr_num_gt) if pr_num_gt > 0 else 0.0
        f1 = (2 * precision * recall / (precision + recall + 1e-12)) if (precision + recall) > 0 else 0.0

        # 4) 匹配对上的均值 Dice/IoU
        mean_dice = float(np.mean(self._global_stats["matched_dice"])) if self._global_stats["matched_dice"] else 0.0
        mean_iou = float(np.mean(self._global_stats["matched_iou"])) if self._global_stats["matched_iou"] else 0.0

        return dict(
            mAP=mAP,
            AP50=AP50,
            AP75=AP75,
            precision=precision,
            recall=recall,
            f1=f1,
            dice=mean_dice,
            iou=mean_iou,
        )

    # 便捷打印
    @staticmethod
    def pretty(metrics: Dict[str, float]) -> str:
        return (
            f"mAP@.50:.95 {metrics['mAP']:.4f} | "
            f"AP50 {metrics['AP50']:.4f} | AP75 {metrics['AP75']:.4f} | "
            f"P {metrics['precision']:.4f} R {metrics['recall']:.4f} F1 {metrics['f1']:.4f} | "
            f"Dice {metrics['dice']:.4f} IoU {metrics['iou']:.4f}"
        )
