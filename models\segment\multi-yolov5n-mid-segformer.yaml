# =========================
# SegFormer-style YOLOv5 (dual-input, anchor-free)
# =========================

# Parameters
nc: 80
depth_multiple: 0.33
width_multiple: 0.25
anchors: null   # ⚠️ 查询式头不需要 anchors

# Backbone
backbone:
  # ====== 显式取两路输入 ======
  - [-1, 1, InputRouter, ['RGB']]                # 0: 取 RGB 输入
  - [-1, 1, InputRouter, ['X']]                  # 1: 取 X   输入

  # --- RGB Path ---
  - [0, 1, Conv, [64, 3, 2]]                     # 2  P1/2 (RGB)
  - [-1, 1, Conv, [128, 3, 2]]                   # 3  P2/4
  - [-1, 3, C3,  [128]]
  - [-1, 1, Conv, [256, 3, 2]]                   # 5  P3/8
  - [-1, 6, C3,  [256]]                          # 6  RGB P3

  # --- X Path ---
  - [1, 1, Conv, [64, 3, 2]]                     # 7  P1/2 (X) —— 注意：from = 1（X 输入）
  - [-1, 1, Conv, [128, 3, 2]]                   # 8  P2/4
  - [-1, 3, C3,  [128]]
  - [-1, 1, Conv, [256, 3, 2]]                   # 10 P3/8
  - [-1, 6, C3,  [256]]                          # 11 X  P3

  # --- Mid Fusion 1 (P3) ---
  - [[6, 11], 1, Concat, [1]]                    # 12: 融合 RGB+X 的 P3
  - [-1, 3, C3, [256]]                           # 13 fused P3

  # 各自下采样到 P4
  - [6,  1, Conv, [512, 3, 2]]                   # 14 RGB -> P4/16
  - [-1, 6, C3,  [512]]                          # 15 RGB P4
  - [11, 1, Conv, [512, 3, 2]]                   # 16 X   -> P4/16
  - [-1, 6, C3,  [512]]                          # 17 X   P4

  # --- Mid Fusion 2 (P4) ---
  - [[15, 17], 1, Concat, [1]]                   # 18: 融合 RGB+X 的 P4
  - [-1, 3, C3, [512]]                           # 19 fused P4

  # P5 from fused P4
  - [-1, 1, Conv, [1024, 3, 2]]                  # 20 P5/32
  - [-1, 3, C3,  [1024]]                         # 21
  - [-1, 1, SPPF, [1024, 5]]                     # 22 fused P5

# Neck（与前文一致，得到 P3=30, P4=33, P5=36）
head:
  - [22, 1, Conv, [512, 1, 1]]                    # 23
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]    # 24
  - [[-1, 19], 1, Concat, [1]]                    # 25
  - [-1, 3, C3, [512, False]]                     # 26

  - [-1, 1, Conv, [256, 1, 1]]                    # 27
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]    # 28
  - [[-1, 13], 1, Concat, [1]]                    # 29
  - [-1, 3, C3, [256, False]]                     # 30  ← P3/8

  - [-1, 1, Conv, [256, 3, 2]]                    # 31
  - [[-1, 24], 1, Concat, [1]]                    # 32
  - [-1, 3, C3, [512, False]]                     # 33  ← P4/16

  - [-1, 1, Conv, [512, 3, 2]]                    # 34
  - [[-1, 22], 1, Concat, [1]]                    # 35
  - [-1, 3, C3, [1024, False]]                    # 36  ← P5/32

  # ====== SegFormer 头（强表现：d_model=256，直接接入原始通道）======
  # PixelAdapter: P3/P4/P5 -> memory 四元组 + 高分辨率 mask_features
  # args: [embed_dim, mask_h, mask_w, in_channels]
  - [[30, 33, 36], 1, PixelAdapter, [256, 160, 160, [64, 128, 256]]]   # 37: (value[B,8400,256], ... , mask_features[B,256,160,160])

  # SegFormerHead: 查询式解码 + 掩码生成
  # args: [nc, nq, dec_layers, nheads, num_points, mask_stride, no_object_weight, aux_loss, d_model]
  - [37, 1, SegFormerHead, [80, 100, 6, 8, 4, 4, 0.1, True, 256]]       # 输出 pred_logits/pred_masks
