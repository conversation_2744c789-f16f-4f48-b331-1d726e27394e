# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license
"""自动批处理工具集"""

from copy import deepcopy

import numpy as np
import torch

from utils.segment.general import LOGGER, colorstr
from utils.torch_utils import profile


def check_train_batch_size(model, imgsz=640, amp=True):
    """检查并计算YOLOv5模型的最优训练批次大小，给定图像尺寸和AMP设置"""
    with torch.cuda.amp.autocast(amp):
        return autobatch(deepcopy(model).train(), imgsz)  # 计算最优批次大小


def autobatch(model, imgsz=640, fraction=0.8, batch_size=16):
    """使用CUDA内存的`fraction`部分估算最优的YOLOv5批次大小"""

    prefix = colorstr("AutoBatch: ")
    LOGGER.info(f"{prefix}正在为 --imgsz {imgsz} 计算最优批次大小")
    device = next(model.parameters()).device  # 获取模型设备
    if device.type == "cpu":
        LOGGER.info(f"{prefix}未检测到CUDA，使用默认CPU批次大小 {batch_size}")
        return batch_size
    if torch.backends.cudnn.benchmark:
        LOGGER.info(f"{prefix} ⚠️ 需要 torch.backends.cudnn.benchmark=False，使用默认批次大小 {batch_size}")
        return batch_size

    # 检查CUDA内存
    gb = 1 << 30  # 字节转GiB (1024 ** 3)
    d = str(device).upper()  # 'CUDA:0'
    properties = torch.cuda.get_device_properties(device)  # 设备属性
    t = properties.total_memory / gb  # GiB 总量
    r = torch.cuda.memory_reserved(device) / gb  # GiB 预留
    a = torch.cuda.memory_allocated(device) / gb  # GiB 已分配
    f = t - (r + a)  # GiB 空闲
    LOGGER.info(f"{prefix}{d} ({properties.name}) {t:.2f}G 总量, {r:.2f}G 预留, {a:.2f}G 已分配, {f:.2f}G 空闲")

    # 分析批次大小
    batch_sizes = [1, 2, 4, 8, 16]
    try:
        img = [torch.empty(b, 3, imgsz, imgsz) for b in batch_sizes]
        results = profile(img, model, n=3, device=device)
    except Exception as e:
        LOGGER.warning(f"{prefix}{e}")

    # 拟合解决方案
    y = [x[2] for x in results if x]  # 内存 [2]
    p = np.polyfit(batch_sizes[: len(y)], y, deg=1)  # 一次多项式拟合
    b = int((f * fraction - p[1]) / p[0])  # y轴截距 (最优批次大小)
    if None in results:  # 部分大小失败
        i = results.index(None)  # 第一个失败的索引
        if b >= batch_sizes[i]:  # y轴截距高于失败点
            b = batch_sizes[max(i - 1, 0)]  # 选择之前的安全点
    if b < 1 or b > 1024:  # b超出安全范围
        b = batch_size
        LOGGER.warning(f"{prefix}警告 ⚠️ 检测到CUDA异常，建议重启环境并重试命令。")

    fraction = (np.polyval(p, b) + r + a) / t  # 预测的实际分数
    LOGGER.info(f"{prefix}为 {d} 使用批次大小 {b}，占用 {t * fraction:.2f}G/{t:.2f}G ({fraction * 100:.0f}%) ✅")
    return b
