Image: tile_0114.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8521
  Bounding Box: [324.40, 1013.60, 537.20, 1250.40]
  Mask Area: 225 pixels
  Mask Ratio: 0.0080
  Mask BBox: [30, 84, 45, 101]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8374
  Bounding Box: [631.60, 739.20, 832.80, 979.20]
  Mask Area: 209 pixels
  Mask Ratio: 0.0074
  Mask BBox: [54, 62, 68, 80]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8345
  Bounding Box: [1063.20, 1886.40, 1215.20, 2048.00]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [88, 152, 97, 164]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8325
  Bounding Box: [176.80, 10.60, 379.20, 264.00]
  Mask Area: 226 pixels
  Mask Ratio: 0.0080
  Mask BBox: [18, 5, 32, 24]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8232
  Bounding Box: [1148.80, 1475.20, 1280.00, 1604.80]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [94, 120, 102, 129]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8174
  Bounding Box: [164.00, 316.80, 380.00, 551.20]
  Mask Area: 219 pixels
  Mask Ratio: 0.0078
  Mask BBox: [17, 29, 33, 47]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8149
  Bounding Box: [5.60, 1068.80, 244.60, 1251.20]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [5, 88, 21, 101]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8071
  Bounding Box: [228.80, 1483.20, 353.60, 1635.20]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [22, 120, 31, 131]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8042
  Bounding Box: [1689.60, 1274.40, 1817.60, 1452.00]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [136, 104, 145, 116]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8022
  Bounding Box: [329.60, 579.20, 489.60, 754.40]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [30, 50, 42, 62]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.7974
  Bounding Box: [0.00, 1561.60, 187.40, 1792.00]
  Mask Area: 212 pixels
  Mask Ratio: 0.0075
  Mask BBox: [4, 126, 18, 143]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.7964
  Bounding Box: [1598.40, 632.80, 1870.40, 967.20]
  Mask Area: 382 pixels
  Mask Ratio: 0.0135
  Mask BBox: [129, 54, 150, 78]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.7959
  Bounding Box: [0.00, 342.40, 163.00, 577.60]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [4, 31, 15, 49]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.7939
  Bounding Box: [462.00, 674.00, 601.20, 816.00]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [41, 58, 50, 66]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7915
  Bounding Box: [327.80, 1289.60, 510.00, 1539.20]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [30, 105, 42, 124]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7910
  Bounding Box: [503.60, 458.40, 744.40, 764.00]
  Mask Area: 305 pixels
  Mask Ratio: 0.0108
  Mask BBox: [44, 41, 62, 63]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7876
  Bounding Box: [874.40, 860.00, 997.60, 1053.60]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [73, 72, 81, 85]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7832
  Bounding Box: [1522.40, 1428.80, 1752.00, 1864.00]
  Mask Area: 496 pixels
  Mask Ratio: 0.0176
  Mask BBox: [123, 116, 140, 149]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7817
  Bounding Box: [170.00, 1598.40, 302.80, 1732.80]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [18, 129, 27, 138]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7803
  Bounding Box: [1600.00, 410.40, 1737.60, 572.00]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [129, 37, 139, 48]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7788
  Bounding Box: [0.00, 1782.40, 357.40, 2044.80]
  Mask Area: 415 pixels
  Mask Ratio: 0.0147
  Mask BBox: [2, 144, 31, 163]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7764
  Bounding Box: [863.20, 6.90, 1010.40, 232.80]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [72, 5, 82, 20]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7749
  Bounding Box: [1356.80, 1215.20, 1465.60, 1381.60]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [111, 99, 118, 110]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7729
  Bounding Box: [574.40, 274.80, 733.60, 465.20]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [49, 26, 61, 39]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7720
  Bounding Box: [658.40, 35.80, 906.40, 392.00]
  Mask Area: 373 pixels
  Mask Ratio: 0.0132
  Mask BBox: [56, 7, 74, 33]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7720
  Bounding Box: [1486.40, 712.00, 1638.40, 928.00]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [121, 60, 130, 76]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7705
  Bounding Box: [575.20, 1528.00, 872.80, 1787.20]
  Mask Area: 314 pixels
  Mask Ratio: 0.0111
  Mask BBox: [49, 124, 72, 143]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7671
  Bounding Box: [444.80, 383.60, 553.60, 508.40]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [39, 34, 47, 43]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7622
  Bounding Box: [308.60, 1592.00, 488.00, 1768.00]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [29, 129, 41, 142]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7617
  Bounding Box: [848.00, 1315.20, 984.00, 1499.20]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [71, 107, 80, 121]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7593
  Bounding Box: [334.60, 243.20, 452.80, 473.60]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [31, 24, 39, 40]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7588
  Bounding Box: [500.40, 1279.20, 677.20, 1581.60]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [44, 104, 56, 126]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7544
  Bounding Box: [1652.80, 7.75, 1758.40, 144.00]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [134, 5, 141, 14]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7515
  Bounding Box: [1862.40, 1427.20, 2035.20, 1617.60]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [150, 116, 162, 130]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7500
  Bounding Box: [784.80, 359.20, 909.60, 516.80]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [66, 33, 74, 44]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7471
  Bounding Box: [6.70, 1260.00, 233.20, 1608.00]
  Mask Area: 322 pixels
  Mask Ratio: 0.0114
  Mask BBox: [5, 104, 22, 129]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7427
  Bounding Box: [648.40, 1859.20, 908.80, 2048.00]
  Mask Area: 200 pixels
  Mask Ratio: 0.0071
  Mask BBox: [55, 150, 74, 164]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7412
  Bounding Box: [1.65, 801.60, 154.80, 1038.40]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [5, 67, 16, 85]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7373
  Bounding Box: [800.80, 1809.60, 914.40, 1947.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [67, 146, 74, 153]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7344
  Bounding Box: [164.60, 476.80, 363.20, 758.40]
  Mask Area: 252 pixels
  Mask Ratio: 0.0089
  Mask BBox: [17, 42, 32, 62]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7339
  Bounding Box: [1795.20, 1832.00, 1990.40, 1985.60]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [145, 148, 158, 158]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7314
  Bounding Box: [1004.80, 489.60, 1177.60, 641.60]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [83, 43, 94, 54]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7290
  Bounding Box: [971.20, 880.00, 1153.60, 1233.60]
  Mask Area: 262 pixels
  Mask Ratio: 0.0093
  Mask BBox: [80, 75, 94, 100]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7285
  Bounding Box: [372.80, 1219.20, 482.40, 1316.80]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [34, 100, 41, 106]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7285
  Bounding Box: [418.80, 1873.60, 589.20, 2046.40]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [37, 151, 50, 161]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7275
  Bounding Box: [1407.20, 1708.80, 1576.80, 2019.20]
  Mask Area: 244 pixels
  Mask Ratio: 0.0086
  Mask BBox: [114, 138, 127, 161]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7275
  Bounding Box: [191.00, 816.00, 402.40, 1059.20]
  Mask Area: 214 pixels
  Mask Ratio: 0.0076
  Mask BBox: [19, 68, 35, 86]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7241
  Bounding Box: [832.00, 746.00, 926.40, 863.20]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [69, 63, 76, 71]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7236
  Bounding Box: [1113.60, 185.60, 1256.00, 386.00]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [91, 19, 102, 34]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7236
  Bounding Box: [914.40, 1684.80, 1095.20, 1924.80]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [76, 136, 89, 154]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.7222
  Bounding Box: [95.70, 0.00, 186.40, 75.20]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [12, 4, 18, 9]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.7222
  Bounding Box: [1120.00, 640.40, 1398.40, 848.00]
  Mask Area: 208 pixels
  Mask Ratio: 0.0074
  Mask BBox: [92, 55, 112, 70]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.7212
  Bounding Box: [479.20, 856.80, 642.40, 1024.80]
  Mask Area: 112 pixels
  Mask Ratio: 0.0040
  Mask BBox: [42, 71, 54, 84]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.7183
  Bounding Box: [1696.00, 534.00, 1878.40, 693.20]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [138, 46, 149, 57]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.7178
  Bounding Box: [1222.40, 176.00, 1291.20, 252.80]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [100, 18, 104, 23]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.7173
  Bounding Box: [1318.40, 1921.60, 1436.80, 2048.00]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [108, 155, 115, 164]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.7168
  Bounding Box: [1987.20, 385.60, 2035.20, 508.80]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [160, 35, 162, 42]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.7168
  Bounding Box: [731.20, 1040.00, 889.60, 1404.80]
  Mask Area: 227 pixels
  Mask Ratio: 0.0080
  Mask BBox: [62, 86, 73, 111]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.7158
  Bounding Box: [1654.40, 968.00, 1814.40, 1180.80]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [134, 80, 145, 96]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.7158
  Bounding Box: [1814.40, 25.25, 1894.40, 213.60]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [146, 6, 151, 20]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.7148
  Bounding Box: [856.00, 507.20, 1041.60, 787.20]
  Mask Area: 226 pixels
  Mask Ratio: 0.0080
  Mask BBox: [71, 44, 85, 65]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.7109
  Bounding Box: [567.20, 1764.80, 688.80, 1860.80]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [49, 142, 55, 148]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.7100
  Bounding Box: [373.60, 33.20, 561.60, 304.00]
  Mask Area: 218 pixels
  Mask Ratio: 0.0077
  Mask BBox: [34, 7, 47, 27]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.7085
  Bounding Box: [1291.20, 1576.00, 1404.80, 1761.60]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [105, 130, 112, 141]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.7085
  Bounding Box: [900.80, 206.40, 1144.00, 515.20]
  Mask Area: 250 pixels
  Mask Ratio: 0.0089
  Mask BBox: [75, 21, 90, 44]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.7075
  Bounding Box: [931.20, 1960.00, 1062.40, 2030.40]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [77, 158, 85, 162]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.7070
  Bounding Box: [921.60, 1440.80, 1067.20, 1586.40]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [76, 117, 86, 127]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.7031
  Bounding Box: [1157.60, 498.00, 1349.60, 652.40]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [95, 43, 108, 54]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6997
  Bounding Box: [128.30, 981.60, 212.00, 1093.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [15, 81, 20, 88]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6958
  Bounding Box: [1462.40, 1228.80, 1694.40, 1392.00]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [119, 100, 136, 112]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6914
  Bounding Box: [1160.00, 56.50, 1281.60, 166.40]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [95, 9, 104, 16]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6904
  Bounding Box: [1266.40, 0.00, 1593.60, 438.00]
  Mask Area: 701 pixels
  Mask Ratio: 0.0248
  Mask BBox: [103, 4, 128, 38]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6890
  Bounding Box: [1772.80, 1442.40, 1868.80, 1694.40]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [143, 117, 149, 136]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6875
  Bounding Box: [1544.00, 1803.20, 1755.20, 2043.20]
  Mask Area: 258 pixels
  Mask Ratio: 0.0091
  Mask BBox: [125, 145, 141, 163]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6841
  Bounding Box: [1860.80, 11.40, 2043.20, 400.80]
  Mask Area: 312 pixels
  Mask Ratio: 0.0111
  Mask BBox: [150, 5, 163, 35]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6812
  Bounding Box: [107.80, 1232.80, 260.20, 1327.20]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [13, 101, 24, 107]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6802
  Bounding Box: [0.00, 1003.20, 90.20, 1102.40]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [4, 83, 11, 90]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6797
  Bounding Box: [792.00, 1459.20, 974.40, 1678.40]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [66, 118, 78, 135]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6787
  Bounding Box: [754.40, 1692.80, 919.20, 1824.00]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [63, 137, 75, 146]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6724
  Bounding Box: [1204.00, 180.80, 1287.20, 267.20]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [99, 19, 104, 23]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6704
  Bounding Box: [197.80, 1364.00, 324.60, 1500.00]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [20, 111, 28, 121]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6694
  Bounding Box: [218.40, 274.80, 351.20, 390.00]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [22, 26, 31, 34]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6694
  Bounding Box: [549.20, 55.00, 654.00, 211.40]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [47, 9, 55, 20]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6680
  Bounding Box: [1551.20, 901.60, 1696.00, 1023.20]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [126, 75, 135, 83]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6650
  Bounding Box: [198.20, 1704.00, 448.80, 1912.00]
  Mask Area: 177 pixels
  Mask Ratio: 0.0063
  Mask BBox: [20, 138, 39, 153]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6626
  Bounding Box: [25.10, 571.20, 178.40, 732.80]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [6, 49, 17, 61]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.6621
  Bounding Box: [1969.60, 732.80, 2043.20, 956.80]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [158, 62, 163, 78]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.6611
  Bounding Box: [1391.20, 1544.00, 1512.80, 1707.20]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [113, 125, 122, 136]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.6543
  Bounding Box: [499.20, 1560.00, 592.00, 1633.60]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [43, 127, 50, 131]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.6509
  Bounding Box: [1084.00, 1125.60, 1242.40, 1365.60]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [89, 92, 101, 110]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.6450
  Bounding Box: [1037.60, 1540.80, 1124.00, 1678.40]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [86, 125, 91, 135]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.6436
  Bounding Box: [3.00, 2.20, 88.80, 297.80]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [5, 5, 10, 27]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.6406
  Bounding Box: [412.00, 1524.80, 489.60, 1614.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [37, 124, 42, 130]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.6401
  Bounding Box: [1857.60, 621.60, 1960.00, 858.40]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [150, 53, 157, 71]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.6377
  Bounding Box: [8.90, 265.00, 90.20, 339.80]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [5, 25, 10, 30]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.6357
  Bounding Box: [1779.20, 852.00, 1913.60, 989.60]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [143, 71, 153, 81]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.6338
  Bounding Box: [573.60, 1875.20, 662.40, 2035.20]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [49, 151, 55, 162]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.6250
  Bounding Box: [1785.60, 969.60, 1932.80, 1126.40]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [144, 80, 153, 91]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.6240
  Bounding Box: [882.40, 198.80, 1020.00, 367.20]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [73, 20, 82, 32]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.6226
  Bounding Box: [563.20, 1112.00, 716.80, 1246.40]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [48, 91, 59, 101]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.6138
  Bounding Box: [728.80, 483.60, 840.80, 745.20]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [61, 42, 69, 62]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.6104
  Bounding Box: [240.00, 1964.80, 319.20, 2048.00]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [23, 158, 28, 163]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.6104
  Bounding Box: [601.20, 0.00, 746.80, 106.60]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [51, 4, 62, 12]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.6099
  Bounding Box: [610.40, 943.20, 685.60, 1058.40]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [52, 78, 57, 86]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.6084
  Bounding Box: [1154.40, 988.80, 1336.80, 1227.20]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [95, 82, 108, 99]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.6074
  Bounding Box: [1200.80, 1966.40, 1327.20, 2030.40]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [98, 158, 107, 162]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.6074
  Bounding Box: [384.80, 785.60, 466.40, 937.60]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [35, 66, 40, 77]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.6074
  Bounding Box: [855.20, 1138.40, 988.00, 1336.80]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [71, 93, 81, 108]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.6064
  Bounding Box: [1044.00, 1265.60, 1197.60, 1531.20]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [86, 103, 97, 121]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.6050
  Bounding Box: [964.00, 1351.20, 1050.40, 1423.20]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [80, 110, 85, 115]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.6016
  Bounding Box: [884.80, 15.80, 1016.00, 331.40]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [74, 6, 82, 29]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.6011
  Bounding Box: [1411.20, 887.20, 1510.40, 996.00]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [115, 74, 121, 80]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.6011
  Bounding Box: [1234.40, 1241.60, 1436.00, 1480.00]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [101, 101, 116, 119]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5947
  Bounding Box: [823.20, 1044.80, 917.60, 1134.40]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [69, 86, 75, 91]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5947
  Bounding Box: [1918.40, 1017.60, 2048.00, 1332.80]
  Mask Area: 246 pixels
  Mask Ratio: 0.0087
  Mask BBox: [154, 84, 164, 108]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5942
  Bounding Box: [1825.60, 1630.40, 1992.00, 1848.00]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [147, 132, 159, 148]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5923
  Bounding Box: [1103.20, 1598.40, 1245.60, 1832.00]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [91, 129, 101, 147]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5918
  Bounding Box: [1202.40, 796.80, 1392.80, 1054.40]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [98, 67, 112, 86]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5898
  Bounding Box: [1920.00, 892.00, 2048.00, 1053.60]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [155, 74, 164, 86]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.5889
  Bounding Box: [683.60, 1242.40, 784.80, 1543.20]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [58, 102, 65, 122]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.5874
  Bounding Box: [203.20, 1225.60, 370.00, 1364.80]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [20, 100, 32, 110]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.5850
  Bounding Box: [914.40, 1593.60, 1034.40, 1753.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [76, 129, 82, 140]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.5820
  Bounding Box: [441.20, 291.40, 566.00, 398.00]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [39, 27, 48, 35]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.5811
  Bounding Box: [505.20, 1226.40, 593.20, 1356.00]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [44, 100, 49, 109]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.5771
  Bounding Box: [214.40, 1044.00, 338.80, 1191.20]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [21, 86, 30, 97]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.5747
  Bounding Box: [1378.40, 1072.00, 1471.20, 1211.20]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [112, 88, 118, 98]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.5659
  Bounding Box: [73.00, 176.40, 205.40, 351.60]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [10, 18, 19, 31]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.5620
  Bounding Box: [1595.20, 575.20, 1688.00, 659.20]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [129, 49, 135, 55]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.5610
  Bounding Box: [4.00, 518.00, 70.10, 634.80]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [5, 45, 9, 53]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.5581
  Bounding Box: [1715.20, 1147.20, 1888.00, 1310.40]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [138, 94, 151, 106]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.5430
  Bounding Box: [450.80, 1784.00, 550.00, 1873.60]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [40, 144, 46, 150]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.5425
  Bounding Box: [1875.20, 502.80, 1958.40, 654.00]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [151, 44, 156, 55]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.5420
  Bounding Box: [1724.80, 1720.00, 1840.00, 1867.20]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [139, 139, 147, 149]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.5420
  Bounding Box: [436.40, 682.80, 586.80, 856.80]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [39, 58, 49, 70]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.5396
  Bounding Box: [1300.00, 1742.40, 1410.40, 1918.40]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [106, 141, 113, 152]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.5386
  Bounding Box: [1257.60, 1488.80, 1345.60, 1620.80]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [103, 121, 108, 130]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.5337
  Bounding Box: [1564.00, 259.20, 1689.60, 406.80]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [127, 25, 135, 35]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.5327
  Bounding Box: [1979.20, 1963.20, 2048.00, 2040.00]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [159, 158, 164, 163]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.5327
  Bounding Box: [19.30, 1312.00, 310.00, 1564.80]
  Mask Area: 371 pixels
  Mask Ratio: 0.0131
  Mask BBox: [6, 107, 28, 126]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.5322
  Bounding Box: [462.40, 0.00, 586.40, 79.00]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [41, 4, 49, 10]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.5298
  Bounding Box: [459.60, 957.60, 529.20, 1037.60]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [40, 79, 45, 85]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.5225
  Bounding Box: [1980.80, 31.00, 2038.40, 146.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [159, 7, 163, 14]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.5220
  Bounding Box: [1575.20, 1148.00, 1692.80, 1279.20]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [128, 94, 136, 102]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.5215
  Bounding Box: [933.60, 1939.20, 1050.40, 2048.00]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [77, 156, 86, 163]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.5215
  Bounding Box: [530.80, 1076.00, 690.00, 1247.20]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [46, 89, 57, 101]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.5200
  Bounding Box: [943.20, 801.60, 1128.80, 918.40]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [78, 67, 92, 75]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.5195
  Bounding Box: [916.80, 1223.20, 1086.40, 1396.00]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [76, 100, 88, 113]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.5151
  Bounding Box: [251.60, 1182.40, 385.60, 1324.80]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [24, 97, 34, 107]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.5107
  Bounding Box: [449.60, 776.00, 576.00, 884.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [40, 65, 48, 72]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.5107
  Bounding Box: [1476.80, 1123.20, 1598.40, 1224.00]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [120, 92, 128, 99]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.5093
  Bounding Box: [1129.60, 346.60, 1272.00, 513.20]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [93, 32, 103, 44]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.5054
  Bounding Box: [1414.40, 1368.80, 1524.80, 1445.60]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [115, 111, 123, 116]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.5044
  Bounding Box: [968.80, 1430.40, 1125.60, 1569.60]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [80, 116, 91, 126]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.5000
  Bounding Box: [1958.40, 1867.20, 2048.00, 1963.20]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [157, 150, 163, 157]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4961
  Bounding Box: [1913.60, 1665.60, 2048.00, 1809.60]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [154, 135, 163, 145]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4961
  Bounding Box: [1880.00, 1028.80, 2036.80, 1379.20]
  Mask Area: 312 pixels
  Mask Ratio: 0.0111
  Mask BBox: [151, 85, 163, 111]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4795
  Bounding Box: [0.55, 285.20, 93.60, 354.40]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [5, 27, 10, 31]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4766
  Bounding Box: [648.00, 1016.80, 748.00, 1106.40]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [55, 84, 62, 90]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4761
  Bounding Box: [179.00, 275.20, 359.60, 473.60]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [18, 26, 32, 40]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4736
  Bounding Box: [621.60, 952.00, 716.00, 1056.00]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [53, 79, 59, 86]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4688
  Bounding Box: [1697.60, 115.40, 1931.20, 552.80]
  Mask Area: 472 pixels
  Mask Ratio: 0.0167
  Mask BBox: [137, 14, 154, 47]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.4644
  Bounding Box: [1088.80, 880.00, 1168.80, 1022.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [90, 73, 95, 83]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.4639
  Bounding Box: [868.00, 1953.60, 938.40, 2040.00]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [72, 157, 77, 163]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.4590
  Bounding Box: [1958.40, 373.60, 2048.00, 506.40]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [157, 34, 164, 43]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.4565
  Bounding Box: [411.60, 1808.00, 474.00, 1881.60]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [37, 146, 41, 150]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.4556
  Bounding Box: [95.80, 60.20, 185.40, 283.00]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [12, 9, 18, 26]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.4551
  Bounding Box: [298.60, 1889.60, 426.40, 2046.40]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [28, 152, 37, 163]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.4485
  Bounding Box: [1716.80, 1696.00, 1819.20, 1840.00]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [139, 137, 146, 147]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.4451
  Bounding Box: [1207.20, 895.20, 1356.00, 1100.00]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [99, 74, 109, 89]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.4426
  Bounding Box: [1154.40, 1317.60, 1292.00, 1482.40]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [95, 107, 104, 119]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.4421
  Bounding Box: [503.60, 1236.00, 633.20, 1367.20]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [44, 101, 53, 109]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.4412
  Bounding Box: [1135.20, 1016.80, 1194.40, 1120.80]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [93, 84, 96, 90]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.4407
  Bounding Box: [773.60, 348.80, 972.00, 507.20]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [65, 32, 79, 43]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.4402
  Bounding Box: [1937.60, 1646.40, 2036.80, 1777.60]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [156, 133, 163, 142]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.4373
  Bounding Box: [1964.80, 1604.80, 2041.60, 1720.00]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [158, 130, 163, 138]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.4368
  Bounding Box: [1188.80, 1950.40, 1313.60, 2048.00]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [97, 157, 106, 163]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.4336
  Bounding Box: [1144.00, 226.20, 1259.20, 379.20]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [94, 22, 102, 33]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.4321
  Bounding Box: [1012.80, 1440.80, 1139.20, 1549.60]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [84, 117, 91, 125]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.4282
  Bounding Box: [154.80, 770.40, 369.20, 1042.40]
  Mask Area: 228 pixels
  Mask Ratio: 0.0081
  Mask BBox: [17, 65, 32, 85]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.4277
  Bounding Box: [863.20, 203.40, 1064.80, 391.60]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [72, 20, 87, 34]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.4272
  Bounding Box: [1168.00, 0.00, 1281.60, 69.90]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [96, 4, 104, 9]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.4253
  Bounding Box: [1258.40, 1928.00, 1445.60, 2030.40]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [103, 155, 116, 162]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.4231
  Bounding Box: [459.60, 582.00, 667.60, 828.80]
  Mask Area: 199 pixels
  Mask Ratio: 0.0071
  Mask BBox: [40, 50, 56, 68]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.4216
  Bounding Box: [1310.40, 1575.20, 1409.60, 1859.20]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [107, 128, 114, 149]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.4211
  Bounding Box: [0.00, 1995.20, 121.40, 2048.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [4, 160, 11, 164]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.4202
  Bounding Box: [1275.20, 217.20, 1592.00, 627.60]
  Mask Area: 631 pixels
  Mask Ratio: 0.0224
  Mask BBox: [104, 21, 128, 53]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.4192
  Bounding Box: [1760.00, 1429.60, 1849.60, 1660.80]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [142, 116, 148, 133]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.4192
  Bounding Box: [1023.20, 19.55, 1146.40, 139.20]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [84, 6, 93, 14]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.4189
  Bounding Box: [51.80, 111.00, 203.80, 364.00]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [9, 13, 19, 32]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.4141
  Bounding Box: [860.00, 0.00, 1092.00, 226.80]
  Mask Area: 211 pixels
  Mask Ratio: 0.0075
  Mask BBox: [72, 4, 89, 21]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.4106
  Bounding Box: [109.20, 745.60, 244.40, 944.00]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [13, 63, 23, 77]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.4087
  Bounding Box: [1408.80, 976.00, 1614.40, 1156.80]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [115, 81, 129, 94]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.4050
  Bounding Box: [19.65, 749.20, 223.40, 989.60]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [6, 63, 21, 81]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.4045
  Bounding Box: [1017.60, 0.00, 1113.60, 75.60]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [84, 4, 90, 9]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.4036
  Bounding Box: [1396.00, 757.20, 1477.60, 889.60]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [114, 64, 119, 73]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.4031
  Bounding Box: [1327.20, 1846.40, 1490.40, 2048.00]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [108, 149, 120, 164]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.4016
  Bounding Box: [1580.80, 0.00, 1670.40, 66.70]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [128, 4, 134, 9]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3994
  Bounding Box: [1716.80, 1262.40, 1835.20, 1424.00]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [139, 103, 147, 115]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3992
  Bounding Box: [505.60, 1221.60, 667.20, 1511.20]
  Mask Area: 190 pixels
  Mask Ratio: 0.0067
  Mask BBox: [44, 100, 55, 122]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3970
  Bounding Box: [1555.20, 657.60, 1625.60, 729.60]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [126, 56, 130, 60]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3965
  Bounding Box: [908.00, 1571.20, 1069.60, 1779.20]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [75, 127, 87, 142]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3955
  Bounding Box: [951.20, 1312.00, 1053.60, 1441.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [79, 107, 86, 116]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3950
  Bounding Box: [0.00, 871.20, 162.00, 1093.60]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [4, 73, 16, 89]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3933
  Bounding Box: [948.00, 1636.80, 1117.60, 1867.20]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [79, 132, 91, 149]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3933
  Bounding Box: [376.80, 1292.00, 682.40, 1556.00]
  Mask Area: 328 pixels
  Mask Ratio: 0.0116
  Mask BBox: [34, 105, 57, 125]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3928
  Bounding Box: [1180.80, 937.60, 1363.20, 1195.20]
  Mask Area: 215 pixels
  Mask Ratio: 0.0076
  Mask BBox: [97, 78, 109, 97]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3923
  Bounding Box: [1480.00, 678.00, 1780.80, 940.00]
  Mask Area: 395 pixels
  Mask Ratio: 0.0140
  Mask BBox: [120, 57, 143, 77]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3918
  Bounding Box: [1892.80, 1614.40, 1979.20, 1688.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [152, 131, 158, 135]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3918
  Bounding Box: [1103.20, 1729.60, 1319.20, 1972.80]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [91, 140, 107, 158]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3887
  Bounding Box: [542.80, 389.60, 637.20, 493.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [47, 35, 53, 40]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3887
  Bounding Box: [1248.00, 1224.00, 1398.40, 1419.20]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [102, 100, 113, 114]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3872
  Bounding Box: [1044.00, 627.20, 1160.80, 772.00]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [86, 53, 93, 64]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3857
  Bounding Box: [9.70, 1201.60, 81.80, 1302.40]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [5, 98, 10, 105]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3853
  Bounding Box: [475.60, 1552.80, 576.40, 1640.00]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [42, 127, 49, 132]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3853
  Bounding Box: [475.60, 1578.40, 576.40, 1665.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [42, 128, 49, 134]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3853
  Bounding Box: [501.20, 1578.40, 602.00, 1665.60]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [44, 128, 49, 134]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3848
  Bounding Box: [1196.80, 768.00, 1404.80, 952.00]
  Mask Area: 191 pixels
  Mask Ratio: 0.0068
  Mask BBox: [98, 64, 113, 78]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3845
  Bounding Box: [204.60, 293.60, 332.20, 403.60]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [21, 27, 29, 33]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3811
  Bounding Box: [1907.20, 416.00, 2041.60, 677.60]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [153, 37, 163, 56]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3726
  Bounding Box: [1272.80, 1596.80, 1376.80, 1753.60]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [104, 129, 111, 140]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3694
  Bounding Box: [1761.60, 963.20, 1934.40, 1192.00]
  Mask Area: 184 pixels
  Mask Ratio: 0.0065
  Mask BBox: [142, 80, 155, 97]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3679
  Bounding Box: [419.60, 368.00, 542.80, 488.80]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [37, 33, 46, 42]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3667
  Bounding Box: [797.60, 845.60, 868.00, 946.40]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [67, 71, 71, 77]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3647
  Bounding Box: [1737.60, 1689.60, 1804.80, 1792.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [140, 136, 144, 143]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3635
  Bounding Box: [1203.20, 1715.20, 1300.80, 1868.80]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [98, 138, 105, 149]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3625
  Bounding Box: [1603.20, 560.00, 1715.20, 676.80]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [130, 48, 136, 55]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3623
  Bounding Box: [1689.60, 1192.80, 1881.60, 1452.00]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [136, 98, 150, 117]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3616
  Bounding Box: [686.00, 980.00, 764.40, 1044.00]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [58, 81, 63, 85]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3567
  Bounding Box: [1046.40, 1162.40, 1238.40, 1488.80]
  Mask Area: 267 pixels
  Mask Ratio: 0.0095
  Mask BBox: [86, 95, 100, 120]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3562
  Bounding Box: [1812.80, 1386.40, 1892.80, 1480.80]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [146, 113, 151, 118]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3550
  Bounding Box: [1110.40, 357.20, 1201.60, 452.40]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [91, 32, 97, 39]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3540
  Bounding Box: [1676.80, 1416.00, 1756.80, 1499.20]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [135, 115, 141, 121]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3530
  Bounding Box: [1667.20, 0.00, 1776.00, 122.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [135, 4, 142, 13]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3503
  Bounding Box: [405.20, 1777.60, 469.20, 1889.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [36, 143, 40, 151]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3501
  Bounding Box: [664.80, 1776.00, 792.00, 1868.80]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [56, 143, 65, 149]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3496
  Bounding Box: [442.00, 2.65, 566.00, 85.20]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [39, 5, 48, 10]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3496
  Bounding Box: [1915.20, 1603.20, 2043.20, 1705.60]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [154, 130, 163, 137]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3489
  Bounding Box: [568.80, 16.90, 701.60, 176.80]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [49, 6, 58, 17]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.3486
  Bounding Box: [1828.80, 6.65, 1918.40, 204.40]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [147, 5, 153, 19]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.3459
  Bounding Box: [783.20, 925.60, 876.00, 1044.00]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [66, 77, 72, 85]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.3435
  Bounding Box: [109.30, 1228.80, 322.00, 1350.40]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [13, 100, 29, 109]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.3430
  Bounding Box: [1365.60, 926.40, 1578.40, 1184.00]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [111, 79, 127, 96]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.3425
  Bounding Box: [1960.00, 1918.40, 2046.40, 2048.00]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [158, 154, 163, 164]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.3425
  Bounding Box: [524.80, 1851.20, 666.40, 2046.40]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [45, 149, 56, 162]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.3425
  Bounding Box: [808.00, 1102.40, 990.40, 1328.00]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [68, 91, 81, 107]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.3420
  Bounding Box: [1100.80, 1124.80, 1286.40, 1470.40]
  Mask Area: 273 pixels
  Mask Ratio: 0.0097
  Mask BBox: [90, 92, 104, 118]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.3398
  Bounding Box: [591.60, 930.40, 669.20, 1042.40]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [51, 77, 56, 85]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.3398
  Bounding Box: [666.40, 984.80, 756.80, 1079.20]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [57, 81, 63, 88]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.3389
  Bounding Box: [1197.60, 855.20, 1370.40, 1031.20]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [98, 71, 111, 84]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.3389
  Bounding Box: [1304.00, 1358.40, 1483.20, 1512.00]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [106, 111, 119, 122]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.3369
  Bounding Box: [1356.00, 1503.20, 1503.20, 1683.20]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [110, 122, 121, 135]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.3364
  Bounding Box: [586.00, 1792.00, 698.00, 1888.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [50, 144, 58, 151]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.3359
  Bounding Box: [1984.00, 348.40, 2044.80, 490.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [159, 32, 163, 42]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.3354
  Bounding Box: [1232.00, 194.20, 1304.00, 282.60]
  Mask Area: 15 pixels
  Mask Ratio: 0.0005
  Mask BBox: [101, 20, 104, 24]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.3350
  Bounding Box: [590.80, 959.20, 669.20, 1069.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [51, 79, 56, 87]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.3347
  Bounding Box: [612.00, 1.25, 800.80, 176.00]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [52, 5, 66, 17]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.3345
  Bounding Box: [451.60, 924.80, 572.40, 1041.60]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [40, 77, 48, 85]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.3342
  Bounding Box: [1236.00, 1609.60, 1306.40, 1737.60]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [101, 130, 105, 139]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.3333
  Bounding Box: [2003.20, 352.80, 2048.00, 501.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [161, 32, 165, 42]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.3333
  Bounding Box: [198.40, 788.80, 274.00, 888.00]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [20, 66, 25, 73]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.3325
  Bounding Box: [1152.00, 162.00, 1289.60, 325.20]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [94, 17, 104, 29]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.3296
  Bounding Box: [327.60, 1056.80, 506.00, 1338.40]
  Mask Area: 237 pixels
  Mask Ratio: 0.0084
  Mask BBox: [30, 87, 43, 108]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.3293
  Bounding Box: [1232.00, 956.00, 1348.80, 1109.60]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [101, 79, 109, 90]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.3286
  Bounding Box: [451.60, 969.60, 547.60, 1062.40]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [40, 80, 46, 86]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.3281
  Bounding Box: [1090.40, 897.60, 1192.80, 1046.40]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [90, 75, 97, 85]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.3281
  Bounding Box: [1552.00, 1150.40, 1712.00, 1307.20]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [126, 94, 137, 106]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.3276
  Bounding Box: [1111.20, 343.00, 1234.40, 484.80]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [91, 31, 100, 41]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [584.80, 1753.60, 709.60, 1849.60]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [50, 142, 59, 148]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [1803.20, 1430.40, 1979.20, 1665.60]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [145, 116, 158, 134]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.3262
  Bounding Box: [836.80, 752.00, 976.00, 873.60]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [70, 63, 80, 72]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.3252
  Bounding Box: [528.00, 42.20, 631.20, 193.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [46, 8, 53, 19]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.3252
  Bounding Box: [1848.00, 857.60, 2040.00, 1049.60]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [149, 71, 163, 85]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.3252
  Bounding Box: [1149.60, 427.60, 1328.80, 663.60]
  Mask Area: 207 pixels
  Mask Ratio: 0.0073
  Mask BBox: [94, 38, 107, 55]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.3247
  Bounding Box: [1400.00, 901.60, 1494.40, 1016.80]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [114, 75, 120, 83]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.3247
  Bounding Box: [1425.60, 901.60, 1520.00, 1016.80]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [116, 75, 121, 80]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.3245
  Bounding Box: [1609.60, 2.50, 1756.80, 182.40]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [130, 5, 141, 18]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.3245
  Bounding Box: [650.40, 722.40, 908.00, 948.00]
  Mask Area: 251 pixels
  Mask Ratio: 0.0089
  Mask BBox: [55, 61, 74, 78]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.3235
  Bounding Box: [896.00, 846.40, 1012.80, 1016.00]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [74, 71, 83, 83]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [388.40, 1503.20, 470.80, 1595.20]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [35, 122, 40, 128]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [414.00, 1503.20, 496.40, 1595.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [37, 122, 42, 128]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [388.40, 1528.80, 470.80, 1620.80]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [35, 124, 40, 129]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [878.40, 793.60, 1065.60, 1004.80]
  Mask Area: 192 pixels
  Mask Ratio: 0.0068
  Mask BBox: [73, 66, 87, 82]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.3218
  Bounding Box: [1205.60, 204.40, 1277.60, 278.80]
  Mask Area: 18 pixels
  Mask Ratio: 0.0006
  Mask BBox: [99, 20, 103, 24]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.3213
  Bounding Box: [1514.40, 1940.80, 1588.80, 2036.80]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [123, 156, 126, 162]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.3191
  Bounding Box: [134.60, 967.20, 243.40, 1093.60]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [15, 80, 23, 89]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.3191
  Bounding Box: [109.00, 992.80, 217.80, 1119.20]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [13, 82, 21, 91]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.3184
  Bounding Box: [0.00, 528.80, 98.70, 648.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [4, 46, 11, 54]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.3184
  Bounding Box: [154.40, 1468.00, 369.60, 1632.00]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [17, 119, 32, 131]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.3164
  Bounding Box: [1068.00, 1835.20, 1298.40, 2048.00]
  Mask Area: 236 pixels
  Mask Ratio: 0.0084
  Mask BBox: [88, 148, 105, 164]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.3157
  Bounding Box: [1557.60, 0.00, 1644.80, 50.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [126, 2, 132, 7]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.3157
  Bounding Box: [1583.20, 0.00, 1670.40, 50.60]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [128, 2, 133, 7]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.3157
  Bounding Box: [1557.60, 0.00, 1644.80, 76.20]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [126, 4, 132, 7]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.3157
  Bounding Box: [580.40, 1776.00, 763.60, 1881.60]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [50, 143, 63, 149]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.3154
  Bounding Box: [841.60, 1748.80, 1070.40, 1947.20]
  Mask Area: 186 pixels
  Mask Ratio: 0.0066
  Mask BBox: [70, 141, 87, 156]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.3154
  Bounding Box: [1822.40, 1761.60, 2008.00, 1979.20]
  Mask Area: 177 pixels
  Mask Ratio: 0.0063
  Mask BBox: [147, 142, 160, 158]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.3127
  Bounding Box: [1976.00, 376.40, 2043.20, 527.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [159, 34, 163, 45]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.3127
  Bounding Box: [330.80, 1881.60, 560.40, 2044.80]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [30, 151, 47, 163]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.3120
  Bounding Box: [1364.80, 981.60, 1537.60, 1212.00]
  Mask Area: 215 pixels
  Mask Ratio: 0.0076
  Mask BBox: [111, 81, 124, 98]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.3091
  Bounding Box: [1596.80, 6.80, 1696.00, 83.00]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [129, 5, 136, 10]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.3086
  Bounding Box: [1308.00, 1220.80, 1480.80, 1436.80]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [107, 100, 119, 116]

