#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练集推理检测脚本
使用训练好的YOLOv5分割模型对训练集进行推理检测
支持多模态（PPL+XPL）推理
"""

import os
import sys
from pathlib import Path
import yaml

# 添加YOLOv5根目录到Python路径
FILE = Path(__file__).resolve()
ROOT = FILE.parent  # YOLOv5 root directory
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))  # add ROOT to PATH

# 导入YOLOv5分割推理模块
from segment.predict import run
from utils.segment.general import LOGGER

def load_dataset_config(config_path):
    """加载数据集配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def main():
    """主函数：执行训练集推理检测"""
    # 配置路径
    weights_path = ROOT / "runs/train-seg/seg_experiment6/weights/best.pt"
    data_config = ROOT / "dataset-seg/datasets.yaml"
    
    # 检查文件是否存在
    if not weights_path.exists():
        raise FileNotFoundError(f"模型权重文件不存在: {weights_path}")
    if not data_config.exists():
        raise FileNotFoundError(f"数据集配置文件不存在: {data_config}")
    
    # 加载数据集配置
    dataset_config = load_dataset_config(data_config)
    
    # 构建训练集路径
    dataset_root = Path(dataset_config['path'])
    train_images_path = dataset_root / dataset_config['train']  # images/train
    
    # 多模态配置
    multimodal = dataset_config.get('multimodal', False)
    xpl_path = None
    if multimodal and 'train_xpl' in dataset_config:
        xpl_path = str(dataset_root / dataset_config['train_xpl'])  # images_xpl/train
    
    # 检查训练集路径是否存在
    if not train_images_path.exists():
        raise FileNotFoundError(f"训练集图像路径不存在: {train_images_path}")
    
    if multimodal and xpl_path and not Path(xpl_path).exists():
        raise FileNotFoundError(f"训练集XPL图像路径不存在: {xpl_path}")
    
    # 输出目录
    output_dir = ROOT / "runs/predict-seg/train_detection"
    
    # 推理参数配置
    inference_args = {
        'weights': str(weights_path),           # 模型权重路径
        'source': str(train_images_path),      # 训练集图像路径
        'data': str(data_config),              # 数据集配置文件
        'imgsz': [640, 640],                   # 推理图像尺寸
        'conf_thres': 0.25,                    # 置信度阈值
        'iou_thres': 0.45,                     # NMS IoU阈值
        'max_det': 1000,                       # 每张图像最大检测数
        'device': '',                          # 设备（空字符串表示自动选择）
        'save_txt': True,                      # 保存检测结果到txt文件
        'save_conf': True,                     # 在txt文件中保存置信度
        'save_crop': False,                    # 不保存裁剪的检测框
        'nosave': False,                       # 保存检测结果图像
        'classes': None,                       # 检测所有类别
        'agnostic_nms': False,                 # 类别相关的NMS
        'augment': False,                      # 不使用测试时增强
        'visualize': False,                    # 不可视化特征
        'project': str(output_dir.parent),     # 输出项目目录
        'name': output_dir.name,               # 输出实验名称
        'exist_ok': True,                      # 允许覆盖现有输出目录
        'line_thickness': 3,                   # 边界框线条粗细
        'hide_labels': False,                  # 显示标签
        'hide_conf': False,                    # 显示置信度
        'half': False,                         # 不使用FP16半精度推理
        'dnn': False,                          # 不使用OpenCV DNN
        'retina_masks': False,                 # 不使用原生分辨率掩码
        'multimodal': multimodal,              # 多模态推理开关
        'xpl_path': xpl_path,                  # XPL图像路径（多模态时使用）
    }
    
    # 打印推理配置信息
    LOGGER.info("=" * 60)
    LOGGER.info("训练集推理检测配置:")
    LOGGER.info(f"模型权重: {weights_path}")
    LOGGER.info(f"数据集配置: {data_config}")
    LOGGER.info(f"训练集路径: {train_images_path}")
    LOGGER.info(f"多模态模式: {multimodal}")
    if multimodal and xpl_path:
        LOGGER.info(f"XPL图像路径: {xpl_path}")
    LOGGER.info(f"输出目录: {output_dir}")
    LOGGER.info(f"图像尺寸: {inference_args['imgsz']}")
    LOGGER.info(f"置信度阈值: {inference_args['conf_thres']}")
    LOGGER.info(f"IoU阈值: {inference_args['iou_thres']}")
    LOGGER.info("=" * 60)
    
    try:
        # 执行推理
        LOGGER.info("开始执行训练集推理检测...")
        run(**inference_args)
        LOGGER.info("训练集推理检测完成!")
        LOGGER.info(f"结果保存在: {output_dir}")
        
    except Exception as e:
        LOGGER.error(f"推理过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    main()