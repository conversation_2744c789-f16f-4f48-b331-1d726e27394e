# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license
"""通用模块。"""

import ast
import contextlib
import json
import math
import platform
import warnings
import zipfile
from collections import OrderedDict, namedtuple
from copy import copy
from pathlib import Path

import cv2
import numpy as np
import pandas as pd
import requests
import torch
import torch.nn as nn
from PIL import Image
from torch.cuda import amp

# Import 'ultralytics' package or install if missing
try:
    import ultralytics

    assert hasattr(ultralytics, "__version__")  # 验证包不是目录
except (ImportError, AssertionError):
    import os

    os.system("pip install -U ultralytics")
    import ultralytics

from ultralytics.utils.plotting import Annotator, colors, save_one_box
from ultralytics.utils.checks import check_requirements
from utils import TryExcept
from utils.segment.dataloaders import exif_transpose, letterbox
from utils.segment.general import (
    LOGGER,
    ROOT,
    Profile,
    check_requirements,
    check_suffix,
    check_version,
    colorstr,
    increment_path,
    is_jupyter,
    make_divisible,
    non_max_suppression,
    scale_boxes,
    xywh2xyxy,
    xyxy2xywh,
    yaml_load,
)
from utils.torch_utils import copy_attr, smart_inference_mode


def autopad(k, p=None, d=1):
    """自动计算填充大小以实现'same'填充效果。"""
    if d > 1:
        # 如果有膨胀卷积，需要计算实际的感受野大小
        # e.g. k=3, d=2 → 实际感受野 = 2*(3-1)+1 = 5
        k = d * (k - 1) + 1 if isinstance(k, int) else [d * (x - 1) + 1 for x in k]
    
    if p is None:
        # 如果没指定 padding，则自动取 kernel_size//2，实现“same padding”
        p = k // 2 if isinstance(k, int) else [x // 2 for x in k]
    
    return p



class Conv(nn.Module):
    """在神经网络中对输入张量应用卷积、批归一化和激活函数。"""

    default_act = nn.SiLU()  # 默认激活函数

    def __init__(self, c1, c2, k=1, s=1, p=None, g=1, d=1, act=True):
        """初始化标准卷积层，可选批归一化和激活函数。"""
        super().__init__()
        self.conv = nn.Conv2d(c1, c2, k, s, autopad(k, p, d), groups=g, dilation=d, bias=False)
        self.bn = nn.BatchNorm2d(c2)
        self.act = self.default_act if act is True else act if isinstance(act, nn.Module) else nn.Identity()

    def forward(self, x):
        """对输入张量`x`应用卷积，然后进行批归一化和激活函数。"""
        return self.act(self.bn(self.conv(x)))

    def forward_fuse(self, x):
        """对输入张量`x`应用融合的卷积和激活函数。"""
        return self.act(self.conv(x))


class DWConv(Conv):
    """实现深度可分离卷积层，可选激活函数，用于高效的空间滤波。"""

    def __init__(self, c1, c2, k=1, s=1, d=1, act=True):
        """初始化深度可分离卷积层，可选激活函数；参数：输入通道数(c1)，输出通道数(c2)，
        卷积核大小(k)，步长(s)，膨胀率(d)，激活函数标志(act)。
        """
        super().__init__(c1, c2, k, s, g=math.gcd(c1, c2), d=d, act=act)


class DWConvTranspose2d(nn.ConvTranspose2d):
    """用于神经网络上采样的深度可分离转置卷积层，特别适用于YOLOv5模型。"""

    def __init__(self, c1, c2, k=1, s=1, p1=0, p2=0):
        """初始化YOLOv5的深度可分离转置卷积层；参数：输入通道数(c1)，输出通道数(c2)，
        卷积核大小(k)，步长(s)，输入填充(p1)，输出填充(p2)。
        """
        super().__init__(c1, c2, k, s, p1, p2, groups=math.gcd(c1, c2))


class TransformerLayer(nn.Module):
    """具有多头注意力和线性层的Transformer层，通过移除LayerNorm进行优化。"""

    def __init__(self, c, num_heads):
        """
        初始化transformer层，为了性能去除LayerNorm，包含多头注意力和线性层。

        参见 https://arxiv.org/abs/2010.11929 中的描述。
        """
        super().__init__()
        self.q = nn.Linear(c, c, bias=False)
        self.k = nn.Linear(c, c, bias=False)
        self.v = nn.Linear(c, c, bias=False)
        self.ma = nn.MultiheadAttention(embed_dim=c, num_heads=num_heads)
        self.fc1 = nn.Linear(c, c, bias=False)
        self.fc2 = nn.Linear(c, c, bias=False)

    def forward(self, x):
        """使用多头注意力和两个线性变换进行前向传播，包含残差连接。"""
        x = self.ma(self.q(x), self.k(x), self.v(x))[0] + x
        x = self.fc2(self.fc1(x)) + x
        return x


class TransformerBlock(nn.Module):
    """用于视觉任务的Transformer块，包含卷积、位置嵌入和Transformer层。"""

    def __init__(self, c1, c2, num_heads, num_layers):
        """初始化用于视觉任务的Transformer块，必要时调整维度并堆叠指定的层。
        """
        super().__init__()
        self.conv = None
        if c1 != c2:
            self.conv = Conv(c1, c2)
        self.linear = nn.Linear(c2, c2)  # 可学习的位置嵌入
        self.tr = nn.Sequential(*(TransformerLayer(c2, num_heads) for _ in range(num_layers)))
        self.c2 = c2

    def forward(self, x):
        """通过可选的卷积处理输入，然后通过Transformer层和位置嵌入进行目标检测。
        """
        if self.conv is not None:
            x = self.conv(x)
        b, _, w, h = x.shape
        p = x.flatten(2).permute(2, 0, 1)
        return self.tr(p + self.linear(p)).permute(1, 2, 0).reshape(b, self.c2, w, h)


class Bottleneck(nn.Module):
    """具有可选快捷连接和分组卷积的瓶颈层，用于高效特征提取。"""

    def __init__(self, c1, c2, shortcut=True, g=1, e=0.5):
        """初始化标准瓶颈层，可选快捷连接和分组卷积，支持通道扩展。
        """
        super().__init__()
        c_ = int(c2 * e)  # 隐藏通道数
        self.cv1 = Conv(c1, c_, 1, 1)
        self.cv2 = Conv(c_, c2, 3, 1, g=g)
        self.add = shortcut and c1 == c2

    def forward(self, x):
        """通过两个卷积处理输入，如果通道维度匹配则可选地添加快捷连接；输入是张量。
        """
        return x + self.cv2(self.cv1(x)) if self.add else self.cv2(self.cv1(x))


class BottleneckCSP(nn.Module):
    """用于特征提取的CSP瓶颈层，具有跨阶段部分连接和可选快捷连接。"""

    def __init__(self, c1, c2, n=1, shortcut=True, g=1, e=0.5):
        """初始化CSP瓶颈层，可选快捷连接；参数：输入通道，输出通道，重复次数，快捷连接布尔值，
        分组，扩展率。
        """
        super().__init__()
        c_ = int(c2 * e)  # 隐藏通道数
        self.cv1 = Conv(c1, c_, 1, 1)
        self.cv2 = nn.Conv2d(c1, c_, 1, 1, bias=False)
        self.cv3 = nn.Conv2d(c_, c_, 1, 1, bias=False)
        self.cv4 = Conv(2 * c_, c2, 1, 1)
        self.bn = nn.BatchNorm2d(2 * c_)  # 应用于cat(cv2, cv3)
        self.act = nn.SiLU()
        self.m = nn.Sequential(*(Bottleneck(c_, c_, shortcut, g, e=1.0) for _ in range(n)))

    def forward(self, x):
        """通过对输入x应用层、激活和连接来执行前向传播，返回特征增强的输出。
        """
        y1 = self.cv3(self.m(self.cv1(x)))
        y2 = self.cv2(x)
        return self.cv4(self.act(self.bn(torch.cat((y1, y2), 1))))


class CrossConv(nn.Module):
    """实现具有下采样、扩展和可选快捷连接的交叉卷积层。"""

    def __init__(self, c1, c2, k=3, s=1, g=1, e=1.0, shortcut=False):
        """
        初始化CrossConv，具有下采样、扩展和可选快捷连接；`c1`输入，`c2`输出通道。

        输入参数为输入通道，输出通道，卷积核，步长，分组，扩展率，快捷连接。
        """
        super().__init__()
        c_ = int(c2 * e)  # 隐藏通道数
        self.cv1 = Conv(c1, c_, (1, k), (1, s))
        self.cv2 = Conv(c_, c2, (k, 1), (s, 1), g=g)
        self.add = shortcut and c1 == c2

    def forward(self, x):
        """执行特征采样、扩展，如果通道匹配则应用快捷连接；期望`x`输入张量。"""
        return x + self.cv2(self.cv1(x)) if self.add else self.cv2(self.cv1(x))


class C3(nn.Module):
    """实现具有三个卷积的CSP瓶颈模块，用于神经网络中的增强特征提取。"""

    def __init__(self, c1, c2, n=1, shortcut=True, g=1, e=0.5):
        """初始化C3模块，可选择通道数、瓶颈重复、快捷连接使用、分组卷积和扩展。
        """
        super().__init__()
        c_ = int(c2 * e)  # 隐藏通道数
        self.cv1 = Conv(c1, c_, 1, 1)
        self.cv2 = Conv(c1, c_, 1, 1)
        self.cv3 = Conv(2 * c_, c2, 1)  # 可选 act=FReLU(c2)
        self.m = nn.Sequential(*(Bottleneck(c_, c_, shortcut, g, e=1.0) for _ in range(n)))

    def forward(self, x):
        """使用两个卷积和瓶颈序列的连接输出执行前向传播。"""
        return self.cv3(torch.cat((self.m(self.cv1(x)), self.cv2(x)), 1))


class C3x(C3):
    """使用交叉卷积扩展C3模块，用于神经网络中的增强特征提取。"""

    def __init__(self, c1, c2, n=1, shortcut=True, g=1, e=0.5):
        """初始化C3x模块，使用交叉卷积，扩展C3，具有可定制的通道维度、分组和扩展。
        """
        super().__init__(c1, c2, n, shortcut, g, e)
        c_ = int(c2 * e)
        self.m = nn.Sequential(*(CrossConv(c_, c_, 3, 1, g, 1.0, shortcut) for _ in range(n)))


class C3TR(C3):
    """带有TransformerBlock的C3模块，用于目标检测模型中的增强特征提取。"""

    def __init__(self, c1, c2, n=1, shortcut=True, g=1, e=0.5):
        """初始化带有TransformerBlock的C3模块，用于增强特征提取，接受通道大小、快捷连接配置、分组和扩展。
        """
        super().__init__(c1, c2, n, shortcut, g, e)
        c_ = int(c2 * e)
        self.m = TransformerBlock(c_, c_, 4, n)


class C3SPP(C3):
    """使用SPP层扩展C3模块，用于增强空间特征提取和可定制通道。"""

    def __init__(self, c1, c2, k=(5, 9, 13), n=1, shortcut=True, g=1, e=0.5):
        """初始化带有SPP层的C3模块，用于高级空间特征提取，给定通道大小、卷积核大小、快捷连接、分组和扩展比。
        """
        super().__init__(c1, c2, n, shortcut, g, e)
        c_ = int(c2 * e)
        self.m = SPP(c_, c_, k)


class C3Ghost(C3):
    """实现带有Ghost瓶颈的C3模块，用于YOLOv5中的高效特征提取。"""

    def __init__(self, c1, c2, n=1, shortcut=True, g=1, e=0.5):
        """初始化YOLOv5的带有Ghost瓶颈的C3模块，用于高效特征提取。"""
        super().__init__(c1, c2, n, shortcut, g, e)
        c_ = int(c2 * e)  # 隐藏通道数
        self.m = nn.Sequential(*(GhostBottleneck(c_, c_) for _ in range(n)))


class SPP(nn.Module):
    """实现空间金字塔池化(SPP)用于特征提取，参考：https://arxiv.org/abs/1406.4729。"""

    def __init__(self, c1, c2, k=(5, 9, 13)):
        """初始化SPP层，使用空间金字塔池化，参考：https://arxiv.org/abs/1406.4729，参数：c1(输入通道)，c2(输出通道)，k(卷积核大小)。"""
        super().__init__()
        c_ = c1 // 2  # 隐藏通道数
        self.cv1 = Conv(c1, c_, 1, 1)
        self.cv2 = Conv(c_ * (len(k) + 1), c2, 1, 1)
        self.m = nn.ModuleList([nn.MaxPool2d(kernel_size=x, stride=1, padding=x // 2) for x in k])

    def forward(self, x):
        """对输入张量`x`应用卷积和最大池化层，连接结果并返回输出张量。
        """
        x = self.cv1(x)
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")  # 抑制torch 1.9.0 max_pool2d()警告
            return self.cv2(torch.cat([x] + [m(x) for m in self.m], 1))


class SPPF(nn.Module):
    """实现快速空间金字塔池化(SPPF)层，用于YOLOv5模型中的高效特征提取。"""

    def __init__(self, c1, c2, k=5):
        """
        初始化YOLOv5 SPPF层，给定通道和卷积核大小，结合卷积和最大池化。

        等效于SPP(k=(5, 9, 13))。
        """
        super().__init__()
        c_ = c1 // 2  # 隐藏通道数
        self.cv1 = Conv(c1, c_, 1, 1)
        self.cv2 = Conv(c_ * 4, c2, 1, 1)
        self.m = nn.MaxPool2d(kernel_size=k, stride=1, padding=k // 2)

    def forward(self, x):
        """通过一系列卷积和最大池化操作处理输入以进行特征提取。"""
        x = self.cv1(x)
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")  # 抑制torch 1.9.0 max_pool2d()警告
            y1 = self.m(x)
            y2 = self.m(y1)
            return self.cv2(torch.cat((x, y1, y2, self.m(y2)), 1))


class Focus(nn.Module):
    """使用切片和卷积将空间信息聚焦到通道空间，用于高效特征提取。"""

    def __init__(self, c1, c2, k=1, s=1, p=None, g=1, act=True):
        """初始化Focus模块，将宽高信息集中到通道空间，具有可配置的卷积参数。
        """
        super().__init__()
        self.conv = Conv(c1 * 4, c2, k, s, p, g, act=act)
        # self.contract = Contract(gain=2)

    def forward(self, x):
        """通过Focus机制处理输入，将(b,c,w,h)重塑为(b,4c,w/2,h/2)然后应用卷积。"""
        return self.conv(torch.cat((x[..., ::2, ::2], x[..., 1::2, ::2], x[..., ::2, 1::2], x[..., 1::2, 1::2]), 1))
        # return self.conv(self.contract(x))


class GhostConv(nn.Module):
    """实现Ghost卷积用于高效特征提取，参见 https://github.com/huawei-noah/ghostnet。"""

    def __init__(self, c1, c2, k=1, s=1, g=1, act=True):
        """初始化GhostConv，设置输入/输出通道、卷积核大小、步长、分组和激活；为了效率将输出通道减半。
        """
        super().__init__()
        c_ = c2 // 2  # 隐藏通道数
        self.cv1 = Conv(c1, c_, k, s, None, g, act=act)
        self.cv2 = Conv(c_, c_, 5, 1, None, c_, act=act)

    def forward(self, x):
        """执行前向传播，连接两个卷积在输入`x`上的输出：形状(B,C,H,W)。"""
        y = self.cv1(x)
        return torch.cat((y, self.cv2(y)), 1)


class GhostBottleneck(nn.Module):
    """使用Ghost卷积的高效瓶颈层，参见 https://github.com/huawei-noah/ghostnet。"""

    def __init__(self, c1, c2, k=3, s=1):
        """初始化GhostBottleneck，输入通道`c1`，输出通道`c2`，卷积核大小`k`，步长`s`；参见 https://github.com/huawei-noah/ghostnet。"""
        super().__init__()
        c_ = c2 // 2
        self.conv = nn.Sequential(
            GhostConv(c1, c_, 1, 1),  # pw
            DWConv(c_, c_, k, s, act=False) if s == 2 else nn.Identity(),  # dw
            GhostConv(c_, c2, 1, 1, act=False),
        )  # pw-linear
        self.shortcut = (
            nn.Sequential(DWConv(c1, c1, k, s, act=False), Conv(c1, c2, 1, 1, act=False)) if s == 2 else nn.Identity()
        )

    def forward(self, x):
        """通过卷积和快捷连接层处理输入，返回它们的求和输出。"""
        return self.conv(x) + self.shortcut(x)


class Contract(nn.Module):
    """将空间维度收缩到通道维度，用于神经网络中的高效处理。"""

    def __init__(self, gain=2):
        """初始化一个层来将空间维度(宽高)收缩到通道中，例如输入形状(1,64,80,80)到(1,256,40,40)。
        """
        super().__init__()
        self.gain = gain

    def forward(self, x):
        """处理输入张量，通过收缩空间维度来扩展通道维度，产生输出形状`(b, c*s*s, h//s, w//s)`。
        """
        b, c, h, w = x.size()  # assert (h / s == 0) and (W / s == 0), 'Indivisible gain'
        s = self.gain
        x = x.view(b, c, h // s, s, w // s, s)  # x(1,64,40,2,40,2)
        x = x.permute(0, 3, 5, 1, 2, 4).contiguous()  # x(1,2,2,64,40,40)
        return x.view(b, c * s * s, h // s, w // s)  # x(1,256,40,40)


class Expand(nn.Module):
    """通过重新分配通道来扩展空间维度，例如从(1,64,80,80)到(1,16,160,160)。"""

    def __init__(self, gain=2):
        """
        初始化Expand模块，通过重新分配通道来增加空间维度，带有可选的增益因子。

        示例：x(1,64,80,80)到x(1,16,160,160)。
        """
        super().__init__()
        self.gain = gain

    def forward(self, x):
        """处理输入张量x，通过重新分配通道来扩展空间维度，要求C / gain^2 == 0。
        """
        b, c, h, w = x.size()  # assert C / s ** 2 == 0, 'Indivisible gain'
        s = self.gain
        x = x.view(b, s, s, c // s**2, h, w)  # x(1,2,2,16,80,80)
        x = x.permute(0, 3, 4, 1, 5, 2).contiguous()  # x(1,16,80,2,80,2)
        return x.view(b, c // s**2, h * s, w * s)  # x(1,16,160,160)


class Concat(nn.Module):
    """沿指定维度连接张量，用于神经网络中的高效张量操作。"""

    def __init__(self, dimension=1):
        """初始化Concat模块，沿指定维度连接张量。"""
        super().__init__()
        self.d = dimension

    def forward(self, x):
        """沿指定维度连接张量列表；`x`是张量列表，`dimension`是整数。
        """
        return torch.cat(x, self.d)


class DetectMultiBackend(nn.Module):
    """YOLOv5简化后端类，仅支持PyTorch推理。"""

    def __init__(self, weights="yolov5s.pt", device=torch.device("cpu"), dnn=False, data=None, fp16=False, fuse=True):
        """初始化DetectMultiBackend，仅支持PyTorch后端。"""
        from models.experimental import attempt_load  # scoped to avoid circular import

        super().__init__()
        w = str(weights[0] if isinstance(weights, list) else weights)
        
        # 仅支持PyTorch (.pt)文件
        if not w.endswith('.pt'):
            raise ValueError(f"Only PyTorch (.pt) weights are supported, got: {w}")
        
        stride = 32  # 默认步长
        
        # PyTorch
        model = attempt_load(weights if isinstance(weights, list) else w, device=device, inplace=True, fuse=fuse)
        stride = max(int(model.stride.max()), 32)  # 模型步长
        names = model.module.names if hasattr(model, "module") else model.names  # 获取类别名称
        model.half() if fp16 else model.float()
        self.model = model  # 显式分配用于to(), cpu(), cuda(), half()
        
        # 设置后端属性
        self.pt = True  # PyTorch模型
        self.jit = False
        self.onnx = False
        self.engine = False
        self.saved_model = False
        self.pb = False
        self.fp16 = fp16
        self.device = device
        # 如果不可用则设置默认名称
        if 'names' not in locals():
            names = {i: f"class{i}" for i in range(1000)}  # 默认类别名称

        # 类别名称
        if "names" not in locals():
            names = yaml_load(data)["names"] if data else {i: f"class{i}" for i in range(999)}
        if names[0] == "n01440764" and len(names) == 1000:  # ImageNet
            names = yaml_load(ROOT / "data/ImageNet.yaml")["names"]  # 人类可读的名称

        self.__dict__.update(locals())  # 将所有变量分配给self

    def forward(self, im, augment=False, visualize=False):
        """对输入图像执行YOLOv5推理，具有数据增强和可视化选项。"""
        # 只支持 PyTorch 推理
        if self.fp16:
            if isinstance(im, (list, tuple)):
                # 处理多模态输入（list格式）
                im = [x.half() if x.dtype != torch.float16 else x for x in im]
            elif im.dtype != torch.float16:
                im = im.half()  # 转换为FP16
        
        # PyTorch推理
        y = self.model(im, augment=augment, visualize=visualize) if augment or visualize else self.model(im)
        
        if isinstance(y, (list, tuple)):
            return self.from_numpy(y[0]) if len(y) == 1 else [self.from_numpy(x) for x in y]
        else:
            return self.from_numpy(y)

    def from_numpy(self, x):
        """将NumPy数组转换为torch张量，保持设备兼容性。"""
        return torch.from_numpy(x).to(self.device) if isinstance(x, np.ndarray) else x

    def warmup(self, imgsz=(1, 3, 640, 640)):
        """执行单次推理预热以初始化模型权重，接受`imgsz`元组作为图像大小。"""
        warmup_types = self.pt, self.jit, self.onnx, self.engine, self.saved_model, self.pb
        if any(warmup_types) and self.device.type != 'cpu':
            # 检查模型是否包含用于双模态输入的InputRouter
            has_input_router = False
            if hasattr(self.model, 'model'):
                for module in self.model.model.modules():
                    if module.__class__.__name__ == 'InputRouter':
                        has_input_router = True
                        break
            
            if has_input_router:
                # 为InputRouter创建双模态输入
                im_rgb = torch.empty(*imgsz, dtype=torch.half if self.fp16 else torch.float, device=self.device)
                im_xpl = torch.empty(*imgsz, dtype=torch.half if self.fp16 else torch.float, device=self.device)
                im = [im_rgb, im_xpl]  # 双输入
            else:
                im = torch.empty(*imgsz, dtype=torch.half if self.fp16 else torch.float, device=self.device)  # 单输入
            
            for _ in range(2 if self.jit else 1):  #
                self.forward(im)  # 预热

    @staticmethod
    def _model_type(p="path/to/model.pt"):
        """
        从文件路径或URL确定模型类型，支持各种导出格式。

        示例：path='path/to/model.onnx' -> type=onnx
        """
        # 类型 = [pt, jit, onnx, xml, engine, coreml, saved_model, pb, tflite, edgetpu, tfjs, paddle]
        from export import export_formats
        from utils.downloads import is_url

        sf = list(export_formats().Suffix)  # 导出后缀
        if not is_url(p, check=False):
             check_suffix(p, sf)  # 检查
        types = [s in Path(p).name for s in sf]
        types[8] &= not types[9]  # tflite &= not edgetpu
        return types

    @staticmethod
    def _load_metadata(f=Path("path/to/meta.yaml")):
        """从YAML文件加载元数据，如果文件存在则返回步长和名称，否则返回`None`。"""
        if f.exists():
            d = yaml_load(f)
            return d["stride"], d["names"]  # 分配步长、名称
        return None, None


class AutoShape(nn.Module):
    """AutoShape类，用于强大的YOLOv5推理，具有预处理、NMS和对各种输入格式的支持。"""

    conf = 0.25  # NMS置信度阈值
    iou = 0.45  # NMS IoU阈值
    agnostic = False  # NMS类别无关
    multi_label = False  # NMS每个框多标签
    classes = None  # (可选列表)按类别过滤，例如= [0, 15, 16]用于COCO人、猫和狗
    max_det = 1000  # 每张图像的最大检测数
    amp = False  # 自动混合精度(AMP)推理

    def __init__(self, model, verbose=True):
        """初始化YOLOv5模型用于推理，设置属性并准备模型进行评估。"""
        super().__init__()
        if verbose:
            LOGGER.info("Adding AutoShape... ")
        copy_attr(self, model, include=("yaml", "nc", "hyp", "names", "stride", "abc"), exclude=())  # 复制属性
        self.dmb = isinstance(model, DetectMultiBackend)  # DetectMultiBackend()实例
        self.pt = not self.dmb or model.pt  # PyTorch模型
        self.model = model.eval()
        if self.pt:
            m = self.model.model.model[-1] if self.dmb else self.model.model[-1]  # Detect()
            m.inplace = False  # Detect.inplace=False用于安全的多线程推理
            m.export = True  # 不输出损失值

    def _apply(self, fn):
        """
        应用 to(), cpu(), cuda(), half() 等操作。

        对模型张量应用函数，但排除参数或注册的缓冲区。
        """
        self = super()._apply(fn)
        if self.pt:
            m = self.model.model.model[-1] if self.dmb else self.model.model[-1]  # Detect()
            m.stride = fn(m.stride)
            m.grid = list(map(fn, m.grid))
            if isinstance(m.anchor_grid, list):
                m.anchor_grid = list(map(fn, m.anchor_grid))
        return self

    @smart_inference_mode()
    def forward(self, ims, size=640, augment=False, profile=False):
        """
        对输入执行推理，支持可选的数据增强和性能分析。

        支持多种格式，包括文件、URI、OpenCV、PIL、numpy、torch。
        """

        dt = (Profile(), Profile(), Profile())
        with dt[0]:
            if isinstance(size, int):  # 扩展尺寸
                size = (size, size)
            p = next(self.model.parameters()) if self.pt else torch.empty(1, device=self.model.device)  # 参数
            autocast = self.amp and (p.device.type != "cpu")  # 自动混合精度(AMP)推理
            if isinstance(ims, torch.Tensor):  # torch张量
                with amp.autocast(autocast):
                    return self.model(ims.to(p.device).type_as(p), augment=augment)  # 推理

            # 预处理
            n, ims = (len(ims), list(ims)) if isinstance(ims, (list, tuple)) else (1, [ims])  # 图像数量和列表
            shape0, shape1, files = [], [], []  # 图像和推理形状，文件名
            for i, im in enumerate(ims):
                f = f"image{i}"  # 文件名
                if isinstance(im, (str, Path)):  # 文件名或URI
                    im, f = Image.open(requests.get(im, stream=True).raw if str(im).startswith("http") else im), im
                    im = np.asarray(exif_transpose(im))
                elif isinstance(im, Image.Image):  # PIL图像
                    im, f = np.asarray(exif_transpose(im)), getattr(im, "filename", f) or f
                files.append(Path(f).with_suffix(".jpg").name)
                if im.shape[0] < 5:  # CHW格式的图像
                    im = im.transpose((1, 2, 0))  # 反转数据加载器的.transpose(2, 0, 1)
                im = im[..., :3] if im.ndim == 3 else cv2.cvtColor(im, cv2.COLOR_GRAY2BGR)  # 强制3通道输入
                s = im.shape[:2]  # HWC
                shape0.append(s)  # 图像形状
                g = max(size) / max(s)  # 增益
                shape1.append([int(y * g) for y in s])
                ims[i] = im if im.data.contiguous else np.ascontiguousarray(im)  # 更新
            shape1 = [make_divisible(x, self.stride) for x in np.array(shape1).max(0)]  # 推理形状
            x = [letterbox(im, shape1, auto=False)[0] for im in ims]  # 填充
            x = np.ascontiguousarray(np.array(x).transpose((0, 3, 1, 2)))  # 堆叠并从BHWC转换为BCHW
            x = torch.from_numpy(x).to(p.device).type_as(p) / 255  # uint8转换为fp16/32

        with amp.autocast(autocast):
            # 推理
            with dt[1]:
                y = self.model(x, augment=augment)  # 前向传播

            # 后处理
            with dt[2]:
                y = non_max_suppression(
                    y if self.dmb else y[0],
                    self.conf,
                    self.iou,
                    self.classes,
                    self.agnostic,
                    self.multi_label,
                    max_det=self.max_det,
                )  # 非极大值抑制
                for i in range(n):
                    scale_boxes(shape1, y[i][:, :4], shape0[i])

            return Detections(ims, y, files, dt, self.names, x.shape)


class Detections:
    """YOLOv5 检测结果管理类，提供可视化、保存、裁剪、导出等方法。"""

    def __init__(self, ims, pred, files, times=(0, 0, 0), names=None, shape=None):
        """
        初始化检测结果类
        参数:
            ims:   图像列表 (numpy 数组)
            pred:  预测结果 (list of tensor)，格式 (xyxy, conf, cls)
            files: 图像文件名列表
            times: 处理耗时 (预处理, 推理, NMS)
            names: 类别名称
            shape: 推理时输入图像的 shape (B, C, H, W)
        """
        super().__init__()
        d = pred[0].device  # 当前预测结果所在设备 (CPU/GPU)

        # 归一化参数，每张图像对应一个 [w, h, w, h, 1, 1]
        gn = [torch.tensor([*(im.shape[i] for i in [1, 0, 1, 0]), 1, 1], device=d) for im in ims]

        self.ims = ims      # 图像 (numpy 格式)
        self.pred = pred    # 预测结果 (tensor 列表)
        self.names = names  # 类别名
        self.files = files  # 文件名
        self.times = times  # 耗时 (pre, infer, NMS)
        self.xyxy = pred    # 边界框 (xyxy 格式，像素坐标)
        self.xywh = [xyxy2xywh(x) for x in pred]       # 转为 xywh (像素坐标)
        self.xyxyn = [x / g for x, g in zip(self.xyxy, gn)]  # xyxy 归一化
        self.xywhn = [x / g for x, g in zip(self.xywh, gn)]  # xywh 归一化
        self.n = len(self.pred)  # 图像数量
        self.t = tuple(x.t / self.n * 1e3 for x in times)  # 平均耗时 (ms)
        self.s = tuple(shape)  # 推理输入 shape

    def _run(self, pprint=False, show=False, save=False, crop=False, render=False, labels=True, save_dir=Path("")):
        """
        执行检测结果处理，包括可视化、保存、裁剪、渲染等。
        """
        s, crops = "", []
        for i, (im, pred) in enumerate(zip(self.ims, self.pred)):
            # 打印图像基本信息
            s += f"\nimage {i + 1}/{len(self.pred)}: {im.shape[0]}x{im.shape[1]} "

            if pred.shape[0]:  # 如果有检测结果
                for c in pred[:, -1].unique():  # 遍历类别
                    n = (pred[:, -1] == c).sum()  # 每个类别的数量
                    s += f"{n} {self.names[int(c)]}{'s' * (n > 1)}, "  # 添加类别统计信息
                s = s.rstrip(", ")

                # 可视化相关操作
                if show or save or render or crop:
                    annotator = Annotator(im, example=str(self.names))
                    for *box, conf, cls in reversed(pred):  # 遍历每个检测框
                        label = f"{self.names[int(cls)]} {conf:.2f}"  # 类别+置信度
                        if crop:  # 裁剪保存目标
                            file = save_dir / "crops" / self.names[int(cls)] / self.files[i] if save else None
                            crops.append({
                                "box": box,
                                "conf": conf,
                                "cls": cls,
                                "label": label,
                                "im": save_one_box(box, im, file=file, save=save),
                            })
                        else:  # 普通画框
                            annotator.box_label(box, label if labels else "", color=colors(cls))
                    im = annotator.im
            else:
                s += "(no detections)"  # 没有检测结果

            # 处理图像格式 (np → PIL)
            im = Image.fromarray(im.astype(np.uint8)) if isinstance(im, np.ndarray) else im

            # 显示
            if show:
                if is_jupyter():
                    from IPython.display import display
                    display(im)
                else:
                    im.show(self.files[i])

            # 保存
            if save:
                f = self.files[i]
                im.save(save_dir / f)
                if i == self.n - 1:
                    LOGGER.info(f"Saved {self.n} image{'s' * (self.n > 1)} to {colorstr('bold', save_dir)}")

            # 渲染结果到 self.ims
            if render:
                self.ims[i] = np.asarray(im)

        # 打印输出
        if pprint:
            s = s.lstrip("\n")
            return f"{s}\nSpeed: %.1fms pre-process, %.1fms inference, %.1fms NMS per image at shape {self.s}" % self.t

        # 裁剪结果返回
        if crop:
            if save:
                LOGGER.info(f"Saved results to {save_dir}\n")
            return crops

    # ------------------ 对外接口 ------------------
    @TryExcept("当前环境不支持显示图像")
    def show(self, labels=True):
        self._run(show=True, labels=labels)

    def save(self, labels=True, save_dir="runs/detect/exp", exist_ok=False):
        save_dir = increment_path(save_dir, exist_ok, mkdir=True)
        self._run(save=True, labels=labels, save_dir=save_dir)

    def crop(self, save=True, save_dir="runs/detect/exp", exist_ok=False):
        save_dir = increment_path(save_dir, exist_ok, mkdir=True) if save else None
        return self._run(crop=True, save=save, save_dir=save_dir)

    def render(self, labels=True):
        """在图像上渲染检测框，并返回 numpy 格式的图像列表"""
        self._run(render=True, labels=labels)
        return self.ims

    def pandas(self):
        """将检测结果转为 pandas.DataFrame (xyxy, xyxyn, xywh, xywhn 四种格式)"""
        new = copy(self)
        ca = "xmin", "ymin", "xmax", "ymax", "confidence", "class", "name"
        cb = "xcenter", "ycenter", "width", "height", "confidence", "class", "name"
        for k, c in zip(["xyxy", "xyxyn", "xywh", "xywhn"], [ca, ca, cb, cb]):
            a = [[x[:5] + [int(x[5]), self.names[int(x[5])]] for x in x.tolist()] for x in getattr(self, k)]
            setattr(new, k, [pd.DataFrame(x, columns=c) for x in a])
        return new

    def tolist(self):
        """将检测结果拆分为单图像的 Detections 对象列表"""
        r = range(self.n)
        return [
            Detections([self.ims[i]], [self.pred[i]], [self.files[i]], self.times, self.names, self.s)
            for i in r
        ]

    def print(self):
        """打印检测结果"""
        LOGGER.info(self.__str__())

    def __len__(self):
        """返回检测结果的图像数"""
        return self.n

    def __str__(self):
        """返回字符串形式的检测结果"""
        return self._run(pprint=True)

    def __repr__(self):
        """返回对象的字符串表示"""
        return f"YOLOv5 {self.__class__} instance\n" + self.__str__()

class Proto(nn.Module):
    """YOLOv5 分割模型的掩码原型生成模块 (Proto)，通过卷积和上采样生成掩码特征。"""

    def __init__(self, c1, c_=256, c2=32):
        """
        参数:
            c1: 输入通道数
            c_: 中间特征通道数 (默认 256)
            c2: 掩码原型输出通道数 (默认 32)
        """
        super().__init__()
        self.cv1 = Conv(c1, c_, k=3)                # 第一次卷积，升维到 c_
        self.upsample = nn.Upsample(scale_factor=2, mode="nearest")  # 上采样，放大特征图
        self.cv2 = Conv(c_, c_, k=3)                # 第二次卷积，保持通道数
        self.cv3 = Conv(c_, c2)                     # 第三次卷积，输出 c2 个 mask 原型

    def forward(self, x):
        """前向传播: Conv → Upsample → Conv → Conv"""
        return self.cv3(self.cv2(self.upsample(self.cv1(x))))
