Image: tile_0138.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8638
  Bounding Box: [1752.00, 1256.80, 1998.40, 1536.80]
  Mask Area: 358 pixels
  Mask Ratio: 0.0127
  Mask BBox: [141, 103, 160, 124]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8555
  Bounding Box: [1100.80, 1689.60, 1296.00, 1932.80]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [90, 136, 105, 153]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8540
  Bounding Box: [1473.60, 995.20, 1726.40, 1406.40]
  Mask Area: 487 pixels
  Mask Ratio: 0.0173
  Mask BBox: [120, 82, 138, 113]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8379
  Bounding Box: [207.00, 1044.80, 372.80, 1267.20]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [21, 87, 33, 102]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8359
  Bounding Box: [0.00, 1279.20, 203.20, 1549.60]
  Mask Area: 233 pixels
  Mask Ratio: 0.0083
  Mask BBox: [4, 104, 19, 123]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8257
  Bounding Box: [1854.40, 403.60, 2043.20, 768.00]
  Mask Area: 330 pixels
  Mask Ratio: 0.0117
  Mask BBox: [149, 36, 163, 63]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8198
  Bounding Box: [414.40, 876.80, 617.60, 1088.00]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [37, 73, 51, 87]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8159
  Bounding Box: [625.60, 209.80, 812.00, 403.60]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [53, 21, 67, 35]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8140
  Bounding Box: [1912.00, 879.20, 2048.00, 1101.60]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [154, 73, 164, 90]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8115
  Bounding Box: [720.00, 1085.60, 945.60, 1330.40]
  Mask Area: 263 pixels
  Mask Ratio: 0.0093
  Mask BBox: [61, 89, 77, 107]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8101
  Bounding Box: [1856.00, 1736.00, 2032.00, 2017.60]
  Mask Area: 190 pixels
  Mask Ratio: 0.0067
  Mask BBox: [149, 140, 161, 161]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8091
  Bounding Box: [749.60, 668.40, 901.60, 791.20]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [63, 57, 74, 65]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.8062
  Bounding Box: [457.60, 535.60, 632.80, 667.60]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [40, 46, 53, 56]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.7993
  Bounding Box: [797.60, 7.30, 1114.40, 246.40]
  Mask Area: 364 pixels
  Mask Ratio: 0.0129
  Mask BBox: [67, 5, 90, 23]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7974
  Bounding Box: [1254.40, 1555.20, 1444.80, 1763.20]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [102, 126, 115, 141]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7949
  Bounding Box: [840.00, 855.20, 1040.00, 1085.60]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [70, 71, 85, 88]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7935
  Bounding Box: [1076.80, 100.00, 1198.40, 287.60]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [89, 12, 96, 26]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7920
  Bounding Box: [1177.60, 1300.00, 1328.00, 1500.00]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [96, 106, 106, 120]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7900
  Bounding Box: [1726.40, 90.80, 1924.80, 254.40]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [139, 12, 154, 23]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7891
  Bounding Box: [465.60, 697.20, 644.00, 904.00]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [41, 59, 54, 74]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7861
  Bounding Box: [845.60, 1281.60, 1015.20, 1540.80]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [71, 105, 83, 124]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7856
  Bounding Box: [1556.80, 1584.80, 1844.80, 1945.60]
  Mask Area: 409 pixels
  Mask Ratio: 0.0145
  Mask BBox: [126, 128, 148, 155]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7837
  Bounding Box: [1502.40, 1348.80, 1641.60, 1552.00]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [122, 111, 130, 124]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7822
  Bounding Box: [218.80, 697.60, 487.20, 902.40]
  Mask Area: 233 pixels
  Mask Ratio: 0.0083
  Mask BBox: [22, 59, 42, 74]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7817
  Bounding Box: [1056.00, 1355.20, 1259.20, 1750.40]
  Mask Area: 366 pixels
  Mask Ratio: 0.0130
  Mask BBox: [87, 110, 102, 140]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7803
  Bounding Box: [1715.20, 1006.40, 1971.20, 1259.20]
  Mask Area: 272 pixels
  Mask Ratio: 0.0096
  Mask BBox: [138, 83, 157, 102]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7764
  Bounding Box: [1822.40, 1458.40, 1960.00, 1734.40]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [147, 118, 157, 139]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7754
  Bounding Box: [95.00, 1552.00, 378.40, 2032.00]
  Mask Area: 590 pixels
  Mask Ratio: 0.0209
  Mask BBox: [12, 126, 33, 162]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7744
  Bounding Box: [936.00, 873.60, 1163.20, 1262.40]
  Mask Area: 339 pixels
  Mask Ratio: 0.0120
  Mask BBox: [78, 73, 94, 101]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7720
  Bounding Box: [727.20, 1468.00, 943.20, 1774.40]
  Mask Area: 306 pixels
  Mask Ratio: 0.0108
  Mask BBox: [61, 119, 77, 142]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7686
  Bounding Box: [593.60, 360.00, 843.20, 654.40]
  Mask Area: 275 pixels
  Mask Ratio: 0.0097
  Mask BBox: [51, 33, 69, 55]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7651
  Bounding Box: [1907.20, 280.40, 2019.20, 491.60]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [153, 26, 161, 42]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7651
  Bounding Box: [1192.80, 79.20, 1426.40, 379.60]
  Mask Area: 309 pixels
  Mask Ratio: 0.0109
  Mask BBox: [98, 11, 115, 33]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7632
  Bounding Box: [508.40, 103.20, 696.40, 291.20]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [44, 13, 58, 26]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7627
  Bounding Box: [395.60, 1148.80, 542.00, 1332.80]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [35, 94, 46, 107]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7627
  Bounding Box: [202.80, 1273.60, 470.00, 1547.20]
  Mask Area: 325 pixels
  Mask Ratio: 0.0115
  Mask BBox: [20, 104, 40, 124]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7588
  Bounding Box: [1019.20, 584.40, 1168.00, 834.40]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [84, 50, 95, 69]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7578
  Bounding Box: [1283.20, 1766.40, 1616.00, 2048.00]
  Mask Area: 531 pixels
  Mask Ratio: 0.0188
  Mask BBox: [105, 142, 130, 165]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7568
  Bounding Box: [1.35, 1726.40, 125.80, 1892.80]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [5, 139, 12, 150]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7568
  Bounding Box: [75.60, 882.40, 213.00, 1092.00]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [10, 74, 19, 88]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7539
  Bounding Box: [675.20, 19.70, 829.60, 224.80]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [57, 6, 68, 21]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7451
  Bounding Box: [1635.20, 2.30, 1779.20, 146.70]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [132, 5, 142, 15]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7441
  Bounding Box: [1649.60, 904.80, 1774.40, 1092.00]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [133, 75, 142, 88]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7402
  Bounding Box: [1161.60, 476.40, 1374.40, 738.80]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [95, 42, 111, 61]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7383
  Bounding Box: [962.40, 1918.40, 1079.20, 2048.00]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [80, 154, 88, 163]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7373
  Bounding Box: [1002.40, 1238.40, 1100.00, 1344.00]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [83, 101, 89, 108]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7319
  Bounding Box: [626.00, 1540.80, 739.60, 1662.40]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [53, 125, 59, 133]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7314
  Bounding Box: [0.00, 1531.20, 145.40, 1672.00]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [4, 124, 14, 134]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7251
  Bounding Box: [1347.20, 356.20, 1737.60, 786.40]
  Mask Area: 604 pixels
  Mask Ratio: 0.0214
  Mask BBox: [110, 32, 139, 65]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7217
  Bounding Box: [725.20, 1324.80, 851.20, 1467.20]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [61, 108, 70, 117]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.7173
  Bounding Box: [1549.60, 768.80, 1638.40, 964.00]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [126, 65, 131, 79]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.7168
  Bounding Box: [1889.60, 157.60, 2048.00, 289.60]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [152, 17, 164, 26]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.7144
  Bounding Box: [1134.40, 693.60, 1281.60, 924.00]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [93, 59, 104, 76]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.7124
  Bounding Box: [42.10, 71.50, 211.60, 263.20]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [8, 10, 20, 24]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.7080
  Bounding Box: [1094.40, 1238.40, 1265.60, 1340.80]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [90, 101, 101, 108]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.7021
  Bounding Box: [132.40, 18.55, 281.60, 151.00]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [15, 6, 25, 15]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6997
  Bounding Box: [1076.80, 0.00, 1276.80, 115.40]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [89, 4, 103, 13]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6968
  Bounding Box: [194.00, 830.40, 396.00, 1052.80]
  Mask Area: 157 pixels
  Mask Ratio: 0.0056
  Mask BBox: [20, 69, 34, 83]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6953
  Bounding Box: [1012.80, 1684.80, 1152.00, 1822.40]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [84, 136, 93, 146]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6948
  Bounding Box: [1379.20, 74.10, 1660.80, 297.60]
  Mask Area: 275 pixels
  Mask Ratio: 0.0097
  Mask BBox: [112, 10, 133, 27]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6929
  Bounding Box: [386.40, 1816.00, 524.00, 2017.60]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [35, 146, 44, 161]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6914
  Bounding Box: [1859.20, 8.25, 2022.40, 171.40]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [150, 5, 161, 17]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6865
  Bounding Box: [406.40, 1616.00, 554.40, 1811.20]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [36, 131, 47, 145]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6855
  Bounding Box: [85.80, 688.40, 175.80, 884.80]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [11, 58, 17, 72]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6855
  Bounding Box: [872.00, 200.60, 1033.60, 584.80]
  Mask Area: 275 pixels
  Mask Ratio: 0.0097
  Mask BBox: [73, 20, 84, 49]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6851
  Bounding Box: [476.40, 1393.60, 552.40, 1475.20]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [42, 113, 46, 119]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6846
  Bounding Box: [1025.60, 370.80, 1259.20, 586.80]
  Mask Area: 217 pixels
  Mask Ratio: 0.0077
  Mask BBox: [85, 33, 102, 49]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6807
  Bounding Box: [594.40, 1894.40, 796.00, 2044.80]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [51, 152, 66, 163]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6802
  Bounding Box: [780.80, 1888.00, 892.80, 2041.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [65, 152, 72, 162]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6792
  Bounding Box: [1692.80, 1910.40, 1862.40, 2041.60]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [138, 154, 149, 163]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6782
  Bounding Box: [1633.60, 1439.20, 1848.00, 1668.80]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [132, 117, 148, 134]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6709
  Bounding Box: [1744.00, 1662.40, 1891.20, 1796.80]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [141, 134, 151, 143]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6641
  Bounding Box: [824.80, 1651.20, 1114.40, 2012.80]
  Mask Area: 362 pixels
  Mask Ratio: 0.0128
  Mask BBox: [69, 133, 91, 161]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6621
  Bounding Box: [1584.80, 166.60, 1736.00, 408.00]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [128, 18, 139, 33]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6606
  Bounding Box: [324.00, 499.20, 477.60, 660.80]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [30, 43, 41, 55]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6548
  Bounding Box: [0.00, 0.00, 85.40, 161.60]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [4, 4, 10, 16]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6494
  Bounding Box: [1952.00, 1931.20, 2044.80, 2048.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [157, 155, 163, 164]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6465
  Bounding Box: [537.60, 1331.20, 661.60, 1457.60]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [46, 108, 55, 116]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6460
  Bounding Box: [404.00, 1468.00, 661.60, 1636.80]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [36, 119, 53, 131]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6416
  Bounding Box: [518.00, 1105.60, 720.40, 1348.80]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [45, 91, 60, 109]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6333
  Bounding Box: [1744.00, 0.00, 1865.60, 111.50]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [141, 4, 149, 12]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6318
  Bounding Box: [430.80, 928.80, 642.80, 1162.40]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [38, 77, 54, 94]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6309
  Bounding Box: [508.40, 1019.20, 674.80, 1187.20]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [44, 84, 56, 95]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6299
  Bounding Box: [753.20, 503.60, 898.40, 678.80]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [63, 44, 74, 57]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6294
  Bounding Box: [440.80, 248.00, 655.20, 458.40]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [39, 24, 55, 39]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6265
  Bounding Box: [278.40, 175.60, 452.00, 464.40]
  Mask Area: 241 pixels
  Mask Ratio: 0.0085
  Mask BBox: [26, 18, 39, 40]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.6255
  Bounding Box: [1184.80, 277.80, 1280.80, 406.40]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [97, 26, 103, 35]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.6250
  Bounding Box: [1.25, 622.00, 105.60, 916.80]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [5, 53, 12, 75]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.6245
  Bounding Box: [151.00, 1497.60, 247.80, 1566.40]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [16, 121, 23, 126]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.6128
  Bounding Box: [1412.00, 621.60, 1604.80, 1048.80]
  Mask Area: 310 pixels
  Mask Ratio: 0.0110
  Mask BBox: [115, 53, 129, 85]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.6104
  Bounding Box: [1364.00, 1344.00, 1528.80, 1561.60]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [111, 109, 123, 125]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.6079
  Bounding Box: [858.40, 459.20, 933.60, 652.00]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [72, 40, 76, 54]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.6021
  Bounding Box: [564.00, 0.00, 699.20, 84.40]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [49, 4, 58, 10]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.6021
  Bounding Box: [1595.20, 1907.20, 1723.20, 2032.00]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [129, 154, 138, 162]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.6021
  Bounding Box: [1713.60, 644.80, 1912.00, 982.40]
  Mask Area: 302 pixels
  Mask Ratio: 0.0107
  Mask BBox: [138, 55, 153, 80]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.6016
  Bounding Box: [970.40, 1564.80, 1064.80, 1686.40]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [80, 127, 87, 135]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.6006
  Bounding Box: [1.80, 149.50, 73.60, 282.40]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [5, 16, 9, 26]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5991
  Bounding Box: [596.80, 1729.60, 819.20, 1908.80]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [51, 140, 67, 153]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5977
  Bounding Box: [620.80, 718.40, 756.80, 1006.40]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [53, 61, 63, 82]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5913
  Bounding Box: [1301.60, 1750.40, 1383.20, 1830.40]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [106, 141, 112, 146]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5859
  Bounding Box: [404.80, 169.40, 505.60, 233.80]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [36, 18, 43, 22]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5806
  Bounding Box: [3.45, 240.80, 195.80, 472.80]
  Mask Area: 191 pixels
  Mask Ratio: 0.0068
  Mask BBox: [5, 23, 18, 40]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5752
  Bounding Box: [1346.40, 651.20, 1466.40, 836.00]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [110, 55, 118, 69]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5693
  Bounding Box: [0.00, 1867.20, 93.00, 1992.00]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [4, 150, 11, 159]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5649
  Bounding Box: [898.40, 1052.80, 964.00, 1132.80]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [75, 87, 79, 92]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5640
  Bounding Box: [1014.40, 253.40, 1110.40, 376.80]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [84, 24, 90, 33]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5586
  Bounding Box: [1093.60, 877.60, 1196.00, 1044.00]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [90, 73, 97, 85]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5542
  Bounding Box: [59.40, 362.40, 349.40, 696.80]
  Mask Area: 463 pixels
  Mask Ratio: 0.0164
  Mask BBox: [9, 33, 31, 58]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5537
  Bounding Box: [254.60, 60.10, 317.40, 192.00]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [24, 9, 28, 18]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5469
  Bounding Box: [1872.00, 729.20, 1993.60, 910.40]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [151, 61, 159, 75]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5449
  Bounding Box: [1628.80, 588.80, 1740.80, 848.80]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [132, 50, 139, 70]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5308
  Bounding Box: [345.20, 1577.60, 550.00, 1814.40]
  Mask Area: 187 pixels
  Mask Ratio: 0.0066
  Mask BBox: [31, 128, 46, 144]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5200
  Bounding Box: [652.40, 427.20, 902.40, 692.00]
  Mask Area: 295 pixels
  Mask Ratio: 0.0105
  Mask BBox: [55, 38, 74, 58]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5190
  Bounding Box: [1152.80, 999.20, 1277.60, 1132.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [95, 83, 102, 92]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5186
  Bounding Box: [16.40, 560.00, 74.70, 664.00]
  Mask Area: 19 pixels
  Mask Ratio: 0.0007
  Mask BBox: [6, 48, 9, 54]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5142
  Bounding Box: [911.20, 708.40, 1026.40, 796.80]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [76, 60, 84, 66]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5078
  Bounding Box: [676.40, 1315.20, 742.00, 1417.60]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [57, 107, 61, 114]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5000
  Bounding Box: [25.00, 1971.20, 254.00, 2048.00]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [6, 158, 23, 164]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.4932
  Bounding Box: [3.35, 528.40, 78.00, 664.40]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 46, 9, 55]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.4927
  Bounding Box: [507.20, 1016.80, 706.40, 1255.20]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [44, 84, 59, 102]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.4907
  Bounding Box: [1100.80, 276.40, 1188.80, 382.80]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [90, 26, 96, 33]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.4893
  Bounding Box: [885.60, 1038.40, 978.40, 1120.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [74, 86, 80, 91]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.4880
  Bounding Box: [1146.40, 1918.40, 1303.20, 2036.80]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [94, 154, 103, 163]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.4844
  Bounding Box: [1608.00, 1439.20, 1793.60, 1640.00]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [130, 117, 144, 132]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.4819
  Bounding Box: [368.00, 0.00, 600.00, 165.20]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [33, 4, 50, 16]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.4810
  Bounding Box: [1620.80, 1388.00, 1736.00, 1488.80]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [131, 113, 139, 118]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.4800
  Bounding Box: [904.00, 764.00, 1072.00, 920.80]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [75, 64, 87, 75]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4800
  Bounding Box: [1961.60, 1476.00, 2041.60, 1756.80]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [158, 120, 163, 141]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.4741
  Bounding Box: [1382.40, 358.00, 1662.40, 677.20]
  Mask Area: 406 pixels
  Mask Ratio: 0.0144
  Mask BBox: [112, 32, 133, 56]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4717
  Bounding Box: [1734.40, 240.20, 1804.80, 347.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [140, 23, 144, 31]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4707
  Bounding Box: [0.00, 962.40, 228.60, 1280.80]
  Mask Area: 337 pixels
  Mask Ratio: 0.0119
  Mask BBox: [4, 80, 21, 104]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4680
  Bounding Box: [750.40, 680.40, 979.20, 804.00]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [63, 58, 80, 65]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4629
  Bounding Box: [1278.40, 1487.20, 1395.20, 1596.80]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [104, 121, 112, 127]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4614
  Bounding Box: [26.70, 632.80, 139.50, 919.20]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [7, 54, 14, 75]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4595
  Bounding Box: [649.60, 706.40, 828.80, 1026.40]
  Mask Area: 258 pixels
  Mask Ratio: 0.0091
  Mask BBox: [55, 60, 68, 84]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4595
  Bounding Box: [422.00, 1966.40, 582.80, 2040.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [37, 158, 48, 163]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4504
  Bounding Box: [1987.20, 1176.00, 2038.40, 1368.00]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [160, 96, 163, 110]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4504
  Bounding Box: [96.50, 1014.40, 391.60, 1288.00]
  Mask Area: 252 pixels
  Mask Ratio: 0.0089
  Mask BBox: [12, 84, 34, 104]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4412
  Bounding Box: [2001.60, 1817.60, 2048.00, 1945.60]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [161, 146, 164, 155]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4377
  Bounding Box: [1118.40, 1124.00, 1196.80, 1224.80]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [92, 92, 97, 99]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4353
  Bounding Box: [969.60, 1673.60, 1171.20, 1862.40]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [80, 135, 95, 149]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4351
  Bounding Box: [1089.60, 0.00, 1238.40, 217.40]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [90, 4, 100, 20]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4346
  Bounding Box: [157.80, 726.00, 240.60, 828.80]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [17, 61, 22, 68]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4336
  Bounding Box: [1198.40, 705.20, 1419.20, 1060.80]
  Mask Area: 321 pixels
  Mask Ratio: 0.0114
  Mask BBox: [98, 60, 114, 86]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4321
  Bounding Box: [471.20, 1369.60, 536.80, 1494.40]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [41, 111, 45, 120]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4321
  Bounding Box: [1052.80, 414.80, 1334.40, 674.80]
  Mask Area: 354 pixels
  Mask Ratio: 0.0125
  Mask BBox: [87, 37, 108, 56]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4277
  Bounding Box: [1873.60, 0.00, 2048.00, 208.40]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [151, 4, 164, 20]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4277
  Bounding Box: [483.60, 1873.60, 568.40, 1992.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [42, 151, 48, 159]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4258
  Bounding Box: [108.60, 722.00, 223.40, 856.00]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [13, 61, 21, 70]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4226
  Bounding Box: [1619.20, 1471.20, 1734.40, 1583.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [131, 120, 136, 127]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4216
  Bounding Box: [1720.00, 257.40, 1812.80, 362.40]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [139, 25, 145, 32]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4202
  Bounding Box: [1294.40, 1247.20, 1374.40, 1336.80]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [106, 102, 111, 108]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4187
  Bounding Box: [1107.20, 90.20, 1401.60, 349.00]
  Mask Area: 318 pixels
  Mask Ratio: 0.0113
  Mask BBox: [91, 12, 113, 31]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4153
  Bounding Box: [920.00, 719.20, 1084.80, 880.80]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [76, 61, 88, 72]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4106
  Bounding Box: [724.00, 648.80, 885.60, 925.60]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [61, 55, 73, 76]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4077
  Bounding Box: [1418.40, 1344.80, 1632.00, 1551.20]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [115, 110, 131, 125]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4065
  Bounding Box: [1329.60, 1.80, 1499.20, 91.60]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [108, 5, 121, 11]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4055
  Bounding Box: [2003.20, 776.00, 2048.00, 881.60]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [161, 65, 164, 71]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4050
  Bounding Box: [490.40, 428.00, 585.60, 524.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [43, 38, 49, 44]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4011
  Bounding Box: [122.80, 667.20, 240.80, 735.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [14, 57, 22, 61]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4004
  Bounding Box: [1501.60, 943.20, 1583.20, 1037.60]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [122, 78, 127, 84]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.3999
  Bounding Box: [1175.20, 1136.80, 1288.80, 1240.80]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [96, 93, 104, 100]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.3994
  Bounding Box: [462.40, 1896.00, 569.60, 2020.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [41, 153, 48, 161]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.3989
  Bounding Box: [1425.60, 289.40, 1566.40, 408.80]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [116, 27, 126, 35]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.3984
  Bounding Box: [1928.00, 262.40, 2048.00, 478.40]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [155, 25, 164, 41]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.3960
  Bounding Box: [3.00, 442.40, 87.60, 621.60]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [5, 39, 9, 52]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.3955
  Bounding Box: [527.20, 1251.20, 689.60, 1470.40]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [46, 102, 57, 118]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.3911
  Bounding Box: [325.60, 1549.60, 476.80, 1790.40]
  Mask Area: 181 pixels
  Mask Ratio: 0.0064
  Mask BBox: [30, 126, 41, 143]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.3896
  Bounding Box: [923.20, 468.00, 1040.00, 708.80]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [77, 41, 85, 59]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.3860
  Bounding Box: [1713.60, 1473.60, 1944.00, 1694.40]
  Mask Area: 244 pixels
  Mask Ratio: 0.0086
  Mask BBox: [138, 120, 155, 136]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.3853
  Bounding Box: [480.00, 1307.20, 560.80, 1419.20]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [42, 107, 47, 114]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3831
  Bounding Box: [1534.40, 720.40, 1643.20, 987.20]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [124, 61, 132, 81]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3831
  Bounding Box: [593.20, 1686.40, 784.80, 1862.40]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [51, 136, 65, 149]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3826
  Bounding Box: [1688.00, 1312.00, 1748.80, 1403.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [136, 107, 140, 113]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3826
  Bounding Box: [1291.20, 1130.40, 1385.60, 1266.40]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [105, 93, 112, 102]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3809
  Bounding Box: [1950.40, 1102.40, 2048.00, 1240.00]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [157, 91, 164, 100]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3743
  Bounding Box: [472.80, 398.80, 608.80, 535.60]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [41, 36, 51, 45]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3743
  Bounding Box: [1920.00, 1148.00, 2035.20, 1253.60]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [154, 94, 162, 101]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3735
  Bounding Box: [790.40, 255.80, 867.20, 414.80]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [66, 24, 71, 36]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3701
  Bounding Box: [1881.60, 946.40, 1987.20, 1106.40]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [151, 78, 159, 88]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3684
  Bounding Box: [1316.80, 1329.60, 1432.00, 1414.40]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [107, 108, 114, 114]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3652
  Bounding Box: [233.60, 39.40, 332.00, 190.00]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [23, 8, 29, 18]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3652
  Bounding Box: [1732.80, 559.60, 1796.80, 673.20]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [140, 48, 144, 56]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3652
  Bounding Box: [410.00, 1878.40, 562.80, 2048.00]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [37, 151, 47, 164]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3613
  Bounding Box: [1736.00, 756.00, 1902.40, 1023.20]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [140, 64, 152, 83]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3611
  Bounding Box: [673.20, 987.20, 776.80, 1094.40]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [57, 82, 64, 89]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3611
  Bounding Box: [983.20, 1600.00, 1085.60, 1683.20]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [81, 129, 88, 135]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3579
  Bounding Box: [984.00, 1260.80, 1089.60, 1361.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [81, 103, 89, 108]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3572
  Bounding Box: [743.20, 1340.00, 864.80, 1495.20]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [63, 109, 70, 117]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3562
  Bounding Box: [804.80, 460.80, 915.20, 659.20]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [67, 40, 75, 55]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3552
  Bounding Box: [1008.80, 1257.60, 1112.80, 1366.40]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [83, 103, 89, 108]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3545
  Bounding Box: [843.20, 1996.80, 953.60, 2044.80]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [70, 160, 78, 163]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3486
  Bounding Box: [818.40, 792.00, 896.80, 905.60]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [68, 66, 74, 74]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3477
  Bounding Box: [411.20, 1156.00, 588.00, 1381.60]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [37, 95, 49, 111]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3459
  Bounding Box: [594.80, 904.80, 669.20, 986.40]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [51, 75, 56, 81]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3420
  Bounding Box: [646.00, 38.10, 834.40, 344.00]
  Mask Area: 243 pixels
  Mask Ratio: 0.0086
  Mask BBox: [55, 7, 69, 30]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3372
  Bounding Box: [726.40, 724.80, 857.60, 1056.00]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [61, 61, 70, 86]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3367
  Bounding Box: [158.40, 1508.00, 261.20, 1580.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [17, 122, 24, 126]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3364
  Bounding Box: [270.80, 50.65, 354.80, 178.80]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [26, 8, 31, 17]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3350
  Bounding Box: [1971.20, 1402.40, 2038.40, 1630.40]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [158, 114, 163, 131]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3345
  Bounding Box: [1288.00, 4.80, 1444.80, 92.20]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [105, 5, 116, 11]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3342
  Bounding Box: [1648.00, 1984.00, 1782.40, 2041.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [133, 159, 143, 163]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [324.40, 537.60, 454.00, 681.60]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [30, 46, 39, 57]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3203
  Bounding Box: [469.60, 1346.40, 552.00, 1468.00]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [41, 110, 47, 118]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3201
  Bounding Box: [855.20, 777.60, 1063.20, 1040.00]
  Mask Area: 252 pixels
  Mask Ratio: 0.0089
  Mask BBox: [71, 65, 87, 85]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3179
  Bounding Box: [44.45, 1910.40, 212.00, 2038.40]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [8, 154, 20, 163]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3164
  Bounding Box: [376.80, 1509.60, 616.80, 1796.80]
  Mask Area: 304 pixels
  Mask Ratio: 0.0108
  Mask BBox: [34, 122, 52, 144]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3147
  Bounding Box: [133.60, 678.00, 235.20, 797.60]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [15, 57, 22, 66]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3142
  Bounding Box: [1956.80, 1120.80, 2036.80, 1277.60]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [157, 92, 163, 103]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3142
  Bounding Box: [472.00, 155.00, 679.20, 424.80]
  Mask Area: 260 pixels
  Mask Ratio: 0.0092
  Mask BBox: [41, 17, 57, 37]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3132
  Bounding Box: [1630.40, 1934.40, 1800.00, 2046.40]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [132, 156, 144, 163]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3079
  Bounding Box: [1852.80, 638.40, 2012.80, 889.60]
  Mask Area: 171 pixels
  Mask Ratio: 0.0061
  Mask BBox: [149, 54, 161, 73]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [1031.20, 275.00, 1165.60, 380.80]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [85, 26, 95, 33]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3044
  Bounding Box: [1156.00, 988.80, 1312.80, 1211.20]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [95, 82, 106, 98]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3040
  Bounding Box: [1924.80, 1881.60, 2048.00, 2044.80]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [155, 151, 164, 163]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3027
  Bounding Box: [1400.80, 708.80, 1565.60, 1097.60]
  Mask Area: 270 pixels
  Mask Ratio: 0.0096
  Mask BBox: [114, 60, 126, 89]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3025
  Bounding Box: [642.80, 1555.20, 754.80, 1683.20]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [55, 126, 59, 133]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [1105.60, 85.00, 1225.60, 254.20]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [91, 11, 99, 23]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3013
  Bounding Box: [839.20, 429.60, 927.20, 624.80]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [70, 38, 76, 52]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3013
  Bounding Box: [864.80, 429.60, 952.80, 624.80]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [72, 39, 78, 52]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3013
  Bounding Box: [1414.40, 541.60, 1656.00, 951.20]
  Mask Area: 376 pixels
  Mask Ratio: 0.0133
  Mask BBox: [115, 47, 133, 78]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.2986
  Bounding Box: [1078.40, 1952.00, 1193.60, 2044.80]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [89, 157, 97, 162]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.2986
  Bounding Box: [1374.40, 608.00, 1569.60, 992.00]
  Mask Area: 259 pixels
  Mask Ratio: 0.0092
  Mask BBox: [112, 52, 126, 81]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.2981
  Bounding Box: [1459.20, 1576.80, 1568.00, 1740.80]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [118, 128, 126, 139]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.2979
  Bounding Box: [1183.20, 1087.20, 1327.20, 1260.00]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [97, 89, 107, 102]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.2959
  Bounding Box: [513.60, 1646.40, 654.40, 1889.60]
  Mask Area: 192 pixels
  Mask Ratio: 0.0068
  Mask BBox: [45, 133, 55, 151]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.2952
  Bounding Box: [320.80, 0.00, 428.00, 71.40]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [30, 3, 37, 9]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.2935
  Bounding Box: [1068.80, 1947.20, 1155.20, 2040.00]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [88, 157, 94, 162]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.2913
  Bounding Box: [1656.00, 870.40, 1832.00, 1089.60]
  Mask Area: 188 pixels
  Mask Ratio: 0.0067
  Mask BBox: [134, 72, 147, 89]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.2908
  Bounding Box: [1079.20, 1963.20, 1144.80, 2033.60]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [89, 158, 93, 162]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.2898
  Bounding Box: [621.60, 1560.00, 718.40, 1684.80]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [53, 126, 59, 135]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.2881
  Bounding Box: [1859.20, 968.80, 1968.00, 1106.40]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [150, 80, 157, 88]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.2878
  Bounding Box: [228.80, 79.80, 315.20, 203.40]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [22, 11, 28, 19]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.2878
  Bounding Box: [254.40, 79.80, 340.80, 203.40]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [24, 11, 30, 19]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.2876
  Bounding Box: [1096.00, 1891.20, 1211.20, 1958.40]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [90, 152, 98, 156]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.2869
  Bounding Box: [1432.80, 1525.60, 1567.20, 1748.80]
  Mask Area: 171 pixels
  Mask Ratio: 0.0061
  Mask BBox: [116, 124, 126, 140]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.2861
  Bounding Box: [1114.40, 263.80, 1213.60, 374.80]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [92, 25, 98, 33]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.2859
  Bounding Box: [1379.20, 4.90, 1539.20, 100.90]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [112, 5, 124, 11]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.2837
  Bounding Box: [2.70, 1884.80, 189.60, 2038.40]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [5, 152, 18, 163]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.2834
  Bounding Box: [653.60, 179.40, 840.80, 378.40]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [56, 19, 68, 33]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [1780.80, 1624.00, 1864.00, 1707.20]
  Mask Area: 19 pixels
  Mask Ratio: 0.0007
  Mask BBox: [144, 131, 148, 135]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [1142.40, 1240.00, 1321.60, 1489.60]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [94, 101, 106, 119]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.2812
  Bounding Box: [1132.80, 1172.80, 1304.00, 1345.60]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [93, 96, 105, 109]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.2810
  Bounding Box: [1492.00, 3.20, 1620.80, 97.10]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [121, 5, 130, 9]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.2805
  Bounding Box: [1969.60, 1100.80, 2036.80, 1209.60]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [158, 90, 163, 98]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.2805
  Bounding Box: [644.00, 1418.40, 730.40, 1565.60]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [55, 115, 60, 126]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.2800
  Bounding Box: [1636.80, 1346.40, 1739.20, 1452.00]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [132, 110, 139, 117]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.2798
  Bounding Box: [930.40, 1897.60, 1061.60, 2038.40]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [77, 153, 86, 163]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.2795
  Bounding Box: [894.40, 707.60, 1033.60, 834.40]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [74, 60, 84, 69]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.2795
  Bounding Box: [1763.20, 22.40, 1984.00, 226.40]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [142, 6, 158, 21]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.2776
  Bounding Box: [1796.80, 449.60, 2017.60, 745.60]
  Mask Area: 329 pixels
  Mask Ratio: 0.0117
  Mask BBox: [145, 40, 161, 62]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.2773
  Bounding Box: [1437.60, 1300.80, 1516.00, 1409.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [117, 106, 122, 113]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.2751
  Bounding Box: [1752.00, 876.80, 1896.00, 1014.40]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [141, 73, 152, 83]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.2751
  Bounding Box: [809.60, 486.80, 1003.20, 691.60]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [68, 43, 82, 58]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.2744
  Bounding Box: [1123.20, 1258.40, 1294.40, 1364.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [92, 103, 105, 110]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.2739
  Bounding Box: [1168.80, 1955.20, 1309.60, 2048.00]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [96, 157, 104, 163]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.2737
  Bounding Box: [988.00, 249.80, 1085.60, 380.80]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [83, 24, 88, 33]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.2717
  Bounding Box: [1410.40, 1686.40, 1504.80, 1766.40]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [115, 136, 121, 141]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.2712
  Bounding Box: [576.40, 0.00, 725.20, 63.00]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [50, 3, 60, 8]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.2698
  Bounding Box: [1950.40, 1411.20, 2048.00, 1704.00]
  Mask Area: 172 pixels
  Mask Ratio: 0.0061
  Mask BBox: [157, 115, 164, 137]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.2695
  Bounding Box: [536.80, 1644.80, 726.40, 1862.40]
  Mask Area: 215 pixels
  Mask Ratio: 0.0076
  Mask BBox: [46, 133, 60, 149]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.2686
  Bounding Box: [10.10, 181.60, 79.70, 328.40]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [5, 19, 9, 29]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2681
  Bounding Box: [1115.20, 293.60, 1214.40, 397.60]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [92, 27, 98, 33]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2676
  Bounding Box: [1564.00, 1.65, 1633.60, 78.20]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [127, 5, 131, 9]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2664
  Bounding Box: [185.40, 1298.40, 269.40, 1396.00]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [19, 106, 25, 113]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2659
  Bounding Box: [836.00, 1979.20, 928.80, 2036.80]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [70, 159, 76, 163]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2659
  Bounding Box: [861.60, 1979.20, 954.40, 2036.80]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [72, 159, 78, 163]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2659
  Bounding Box: [836.00, 2004.80, 928.80, 2048.00]
  Mask Area: 19 pixels
  Mask Ratio: 0.0007
  Mask BBox: [70, 161, 76, 163]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2659
  Bounding Box: [861.60, 2004.80, 954.40, 2048.00]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [72, 161, 78, 163]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2659
  Bounding Box: [1996.80, 1363.20, 2041.60, 1452.80]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [160, 111, 163, 117]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2654
  Bounding Box: [1907.20, 174.00, 2041.60, 486.00]
  Mask Area: 209 pixels
  Mask Ratio: 0.0074
  Mask BBox: [153, 18, 163, 41]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2651
  Bounding Box: [777.60, 1860.80, 1036.80, 2033.60]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [65, 150, 84, 162]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2642
  Bounding Box: [1312.80, 1336.80, 1413.60, 1440.80]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [107, 109, 114, 116]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2632
  Bounding Box: [77.80, 0.00, 267.80, 136.80]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [11, 3, 24, 14]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2629
  Bounding Box: [1215.20, 245.20, 1306.40, 383.20]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [99, 25, 103, 33]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2627
  Bounding Box: [1969.60, 1643.20, 2043.20, 1784.00]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [158, 133, 163, 143]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2603
  Bounding Box: [1828.80, 0.00, 1992.00, 157.60]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [147, 3, 159, 16]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2598
  Bounding Box: [486.40, 1350.40, 649.60, 1488.00]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [42, 110, 54, 120]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2595
  Bounding Box: [1795.20, 1496.00, 1936.00, 1752.00]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [145, 121, 155, 140]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2588
  Bounding Box: [200.40, 269.60, 296.00, 373.60]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [20, 26, 26, 33]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2583
  Bounding Box: [921.60, 1048.00, 992.00, 1124.80]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [76, 86, 81, 91]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2583
  Bounding Box: [896.00, 1073.60, 966.40, 1150.40]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [74, 88, 79, 93]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2583
  Bounding Box: [921.60, 1073.60, 992.00, 1150.40]
  Mask Area: 21 pixels
  Mask Ratio: 0.0007
  Mask BBox: [76, 88, 81, 93]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2571
  Bounding Box: [1689.60, 350.40, 1779.20, 428.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [136, 32, 142, 37]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2563
  Bounding Box: [1208.00, 279.40, 1312.00, 415.20]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [99, 26, 103, 35]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2559
  Bounding Box: [0.00, 1896.00, 113.30, 2014.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [4, 153, 12, 161]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2549
  Bounding Box: [556.00, 0.00, 699.20, 50.70]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [48, 3, 58, 7]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2549
  Bounding Box: [595.60, 1784.00, 788.00, 1902.40]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [51, 144, 65, 152]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2546
  Bounding Box: [1034.40, 218.40, 1205.60, 369.60]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [85, 22, 98, 32]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2534
  Bounding Box: [1720.00, 224.40, 1793.60, 316.40]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [139, 22, 144, 28]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2534
  Bounding Box: [1745.60, 224.40, 1819.20, 316.40]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [141, 22, 146, 28]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2520
  Bounding Box: [1857.60, 1475.20, 1995.20, 1718.40]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [150, 120, 159, 137]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2515
  Bounding Box: [1979.20, 1392.80, 2040.00, 1541.60]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [159, 113, 163, 124]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2512
  Bounding Box: [1110.40, 840.80, 1209.60, 1029.60]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [91, 70, 98, 84]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2502
  Bounding Box: [572.00, 1552.00, 729.60, 1686.40]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [49, 126, 59, 135]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2494
  Bounding Box: [1979.20, 1824.00, 2048.00, 1955.20]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [159, 147, 164, 156]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2490
  Bounding Box: [1969.60, 6.05, 2048.00, 168.80]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [158, 5, 164, 17]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2487
  Bounding Box: [1729.60, 240.00, 1844.80, 336.40]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [140, 23, 148, 30]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2487
  Bounding Box: [1963.20, 1424.00, 2048.00, 1588.80]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [158, 116, 164, 128]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2483
  Bounding Box: [1822.40, 322.40, 1908.80, 442.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [147, 30, 152, 38]

