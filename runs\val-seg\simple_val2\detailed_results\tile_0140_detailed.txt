Image: tile_0140.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8652
  Bounding Box: [857.60, 1252.00, 1123.20, 1540.00]
  Mask Area: 379 pixels
  Mask Ratio: 0.0134
  Mask BBox: [71, 102, 91, 124]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8555
  Bounding Box: [416.80, 77.20, 669.60, 365.20]
  Mask Area: 296 pixels
  Mask Ratio: 0.0105
  Mask BBox: [37, 11, 56, 32]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8467
  Bounding Box: [306.20, 1179.20, 458.40, 1356.80]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [28, 97, 39, 109]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8403
  Bounding Box: [397.20, 1414.40, 631.60, 1694.40]
  Mask Area: 287 pixels
  Mask Ratio: 0.0102
  Mask BBox: [36, 115, 53, 136]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8276
  Bounding Box: [17.20, 249.20, 229.60, 674.00]
  Mask Area: 396 pixels
  Mask Ratio: 0.0140
  Mask BBox: [6, 24, 21, 56]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8267
  Bounding Box: [303.20, 544.00, 572.00, 843.20]
  Mask Area: 306 pixels
  Mask Ratio: 0.0108
  Mask BBox: [28, 47, 46, 69]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8247
  Bounding Box: [1654.40, 992.00, 1868.80, 1212.80]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [134, 82, 149, 98]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8149
  Bounding Box: [1104.00, 864.00, 1312.00, 1104.00]
  Mask Area: 184 pixels
  Mask Ratio: 0.0065
  Mask BBox: [91, 72, 106, 90]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8130
  Bounding Box: [203.40, 1744.00, 551.20, 2048.00]
  Mask Area: 469 pixels
  Mask Ratio: 0.0166
  Mask BBox: [20, 141, 47, 164]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8066
  Bounding Box: [1210.40, 1825.60, 1378.40, 2040.00]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [99, 147, 110, 163]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8047
  Bounding Box: [1496.00, 1556.00, 1723.20, 1846.40]
  Mask Area: 267 pixels
  Mask Ratio: 0.0095
  Mask BBox: [121, 126, 138, 148]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8042
  Bounding Box: [1638.40, 1931.20, 1779.20, 2036.80]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [132, 155, 142, 163]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.8018
  Bounding Box: [1257.60, 74.90, 1428.80, 309.20]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [103, 10, 115, 28]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.7993
  Bounding Box: [1413.60, 1212.80, 1551.20, 1393.60]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [115, 99, 125, 112]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7979
  Bounding Box: [909.60, 1063.20, 1095.20, 1250.40]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [76, 89, 88, 101]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7959
  Bounding Box: [532.40, 842.40, 643.60, 962.40]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [46, 70, 54, 79]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7939
  Bounding Box: [3.15, 922.40, 152.80, 1141.60]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [5, 77, 14, 93]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7915
  Bounding Box: [1560.00, 741.20, 1819.20, 996.80]
  Mask Area: 301 pixels
  Mask Ratio: 0.0107
  Mask BBox: [126, 62, 146, 81]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7910
  Bounding Box: [1175.20, 305.60, 1364.00, 548.80]
  Mask Area: 205 pixels
  Mask Ratio: 0.0073
  Mask BBox: [96, 28, 110, 46]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7881
  Bounding Box: [105.30, 1360.80, 274.80, 1651.20]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [13, 111, 25, 132]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7871
  Bounding Box: [0.00, 770.40, 100.00, 924.00]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [4, 65, 11, 76]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7861
  Bounding Box: [1448.80, 486.80, 1708.80, 719.60]
  Mask Area: 249 pixels
  Mask Ratio: 0.0088
  Mask BBox: [118, 43, 137, 59]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7803
  Bounding Box: [836.80, 1784.00, 1078.40, 2048.00]
  Mask Area: 340 pixels
  Mask Ratio: 0.0120
  Mask BBox: [70, 144, 88, 164]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7803
  Bounding Box: [1275.20, 600.80, 1414.40, 812.00]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [104, 51, 114, 67]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7803
  Bounding Box: [113.10, 1916.80, 244.80, 2016.00]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [13, 154, 23, 161]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7788
  Bounding Box: [960.80, 627.60, 1232.80, 954.40]
  Mask Area: 388 pixels
  Mask Ratio: 0.0137
  Mask BBox: [80, 54, 100, 78]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7778
  Bounding Box: [1784.00, 6.10, 2020.80, 231.60]
  Mask Area: 226 pixels
  Mask Ratio: 0.0080
  Mask BBox: [144, 5, 160, 22]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7749
  Bounding Box: [1635.20, 134.90, 1801.60, 292.00]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [132, 15, 144, 26]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7700
  Bounding Box: [1092.80, 1419.20, 1360.00, 1641.60]
  Mask Area: 256 pixels
  Mask Ratio: 0.0091
  Mask BBox: [90, 115, 110, 132]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7686
  Bounding Box: [1793.60, 776.80, 1931.20, 924.00]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [145, 65, 154, 76]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7676
  Bounding Box: [258.80, 1507.20, 404.40, 1763.20]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [25, 122, 35, 140]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7676
  Bounding Box: [91.30, 0.00, 383.20, 267.40]
  Mask Area: 324 pixels
  Mask Ratio: 0.0115
  Mask BBox: [12, 4, 33, 24]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7651
  Bounding Box: [905.60, 875.20, 1070.40, 1049.60]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [75, 74, 86, 85]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7646
  Bounding Box: [1457.60, 772.80, 1704.00, 1105.60]
  Mask Area: 378 pixels
  Mask Ratio: 0.0134
  Mask BBox: [118, 65, 137, 90]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7637
  Bounding Box: [1392.80, 1752.00, 1557.60, 1908.80]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [113, 141, 125, 152]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7622
  Bounding Box: [1232.00, 747.20, 1387.20, 867.20]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [101, 63, 112, 71]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7603
  Bounding Box: [0.00, 1387.20, 115.30, 1556.80]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [4, 113, 12, 124]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7578
  Bounding Box: [1478.40, 1915.20, 1644.80, 2017.60]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [120, 154, 132, 161]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7563
  Bounding Box: [270.40, 1343.20, 488.00, 1548.00]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [26, 109, 42, 124]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7559
  Bounding Box: [50.90, 1694.40, 214.40, 1953.60]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [8, 137, 20, 155]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7510
  Bounding Box: [557.60, 1672.00, 767.20, 1976.00]
  Mask Area: 237 pixels
  Mask Ratio: 0.0084
  Mask BBox: [48, 135, 63, 157]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7485
  Bounding Box: [614.00, 303.00, 771.20, 456.80]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [52, 28, 64, 39]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7456
  Bounding Box: [1878.40, 962.40, 1977.60, 1060.00]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [151, 80, 158, 86]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7456
  Bounding Box: [1761.60, 1936.00, 1889.60, 2035.20]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [142, 156, 149, 162]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7432
  Bounding Box: [1179.20, 0.00, 1304.00, 104.40]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [97, 4, 105, 12]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7358
  Bounding Box: [124.70, 1904.00, 260.00, 2044.80]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [14, 153, 23, 163]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7358
  Bounding Box: [1907.20, 864.00, 2048.00, 1019.20]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [153, 72, 164, 83]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7354
  Bounding Box: [864.80, 1635.20, 986.40, 1820.80]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [72, 132, 80, 145]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7354
  Bounding Box: [1328.80, 1348.80, 1528.80, 1532.80]
  Mask Area: 173 pixels
  Mask Ratio: 0.0061
  Mask BBox: [108, 110, 123, 123]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7305
  Bounding Box: [877.60, 4.40, 1090.40, 175.80]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [73, 5, 89, 17]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.7285
  Bounding Box: [1030.40, 489.20, 1278.40, 720.40]
  Mask Area: 261 pixels
  Mask Ratio: 0.0092
  Mask BBox: [85, 43, 103, 60]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.7275
  Bounding Box: [1692.80, 1752.00, 1884.80, 1931.20]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [137, 141, 151, 154]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.7261
  Bounding Box: [1289.60, 854.40, 1404.80, 971.20]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [105, 71, 113, 79]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.7261
  Bounding Box: [1841.60, 1302.40, 2048.00, 1531.20]
  Mask Area: 220 pixels
  Mask Ratio: 0.0078
  Mask BBox: [148, 106, 164, 123]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.7236
  Bounding Box: [1027.20, 1641.60, 1316.80, 1779.20]
  Mask Area: 171 pixels
  Mask Ratio: 0.0061
  Mask BBox: [87, 133, 106, 142]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.7231
  Bounding Box: [706.40, 1257.60, 860.00, 1446.40]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [60, 103, 70, 116]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.7188
  Bounding Box: [1616.00, 1449.60, 1856.00, 1678.40]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [131, 118, 147, 135]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.7183
  Bounding Box: [708.40, 565.60, 990.40, 981.60]
  Mask Area: 420 pixels
  Mask Ratio: 0.0149
  Mask BBox: [60, 49, 81, 80]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.7173
  Bounding Box: [257.00, 281.80, 440.00, 512.00]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [25, 27, 37, 43]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.7173
  Bounding Box: [1745.60, 600.80, 1908.80, 797.60]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [141, 51, 153, 66]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.7104
  Bounding Box: [207.40, 534.40, 333.40, 664.80]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [21, 46, 30, 55]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.7070
  Bounding Box: [1455.20, 2.60, 1734.40, 145.80]
  Mask Area: 199 pixels
  Mask Ratio: 0.0071
  Mask BBox: [118, 5, 139, 15]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6953
  Bounding Box: [136.40, 990.40, 220.80, 1112.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [15, 82, 21, 90]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6929
  Bounding Box: [1042.40, 999.20, 1242.40, 1234.40]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [86, 83, 101, 100]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6899
  Bounding Box: [1268.80, 1640.00, 1472.00, 1841.60]
  Mask Area: 159 pixels
  Mask Ratio: 0.0056
  Mask BBox: [104, 133, 118, 147]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6880
  Bounding Box: [0.00, 1156.80, 125.40, 1368.00]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [4, 95, 13, 110]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6875
  Bounding Box: [1432.80, 985.60, 1564.00, 1115.20]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [116, 81, 124, 90]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6870
  Bounding Box: [1923.20, 1700.80, 2048.00, 1864.00]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [155, 137, 163, 149]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6855
  Bounding Box: [1716.80, 190.00, 1944.00, 392.00]
  Mask Area: 208 pixels
  Mask Ratio: 0.0074
  Mask BBox: [139, 19, 155, 34]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6841
  Bounding Box: [1557.60, 1192.80, 1731.20, 1426.40]
  Mask Area: 200 pixels
  Mask Ratio: 0.0071
  Mask BBox: [126, 98, 139, 115]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6826
  Bounding Box: [1648.00, 363.20, 1763.20, 492.00]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [133, 33, 141, 41]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6821
  Bounding Box: [632.00, 916.80, 756.00, 1048.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [54, 76, 62, 85]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6821
  Bounding Box: [670.00, 1042.40, 888.00, 1271.20]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [57, 86, 73, 103]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6807
  Bounding Box: [1390.40, 900.00, 1467.20, 994.40]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [113, 75, 118, 81]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6807
  Bounding Box: [1785.60, 1532.80, 1894.40, 1702.40]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [144, 124, 151, 136]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6758
  Bounding Box: [0.00, 91.50, 83.40, 238.80]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [4, 12, 9, 22]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6753
  Bounding Box: [1540.80, 268.00, 1713.60, 488.80]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [125, 25, 137, 42]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6748
  Bounding Box: [1883.20, 623.20, 2043.20, 812.00]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [152, 53, 163, 65]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6680
  Bounding Box: [1931.20, 1052.00, 2048.00, 1298.40]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [155, 87, 164, 105]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6665
  Bounding Box: [1904.00, 1480.00, 2019.20, 1625.60]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [153, 120, 161, 130]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6650
  Bounding Box: [308.80, 858.40, 488.80, 1088.80]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [29, 72, 42, 89]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6626
  Bounding Box: [1444.80, 397.60, 1601.60, 550.40]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [117, 36, 129, 46]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6611
  Bounding Box: [294.60, 1049.60, 404.80, 1137.60]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [28, 86, 34, 92]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6597
  Bounding Box: [211.60, 1091.20, 347.20, 1305.60]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [21, 90, 31, 105]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6577
  Bounding Box: [420.40, 19.00, 558.00, 122.60]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [37, 6, 47, 12]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6567
  Bounding Box: [110.50, 740.80, 201.20, 876.80]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [13, 62, 19, 72]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.6567
  Bounding Box: [1080.00, 1867.20, 1212.80, 2017.60]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [89, 150, 98, 161]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.6538
  Bounding Box: [1779.20, 1107.20, 1945.60, 1272.00]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [143, 91, 155, 103]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.6528
  Bounding Box: [618.80, 1464.00, 757.20, 1676.80]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [53, 119, 62, 134]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.6523
  Bounding Box: [11.30, 1574.40, 97.70, 1740.80]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [5, 127, 11, 139]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.6494
  Bounding Box: [1824.00, 930.40, 1910.40, 1015.20]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [147, 77, 153, 83]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.6475
  Bounding Box: [1293.60, 6.70, 1444.00, 84.30]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [106, 5, 116, 10]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.6475
  Bounding Box: [522.00, 1792.00, 644.40, 1936.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [45, 144, 53, 155]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.6445
  Bounding Box: [1020.00, 1538.40, 1168.80, 1662.40]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [84, 125, 95, 132]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.6445
  Bounding Box: [207.20, 302.60, 412.00, 478.40]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [21, 28, 36, 41]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.6426
  Bounding Box: [1116.80, 1304.00, 1268.80, 1424.00]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [92, 106, 103, 115]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.6396
  Bounding Box: [440.80, 1216.80, 624.00, 1407.20]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [39, 100, 52, 113]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.6367
  Bounding Box: [10.15, 210.40, 84.00, 311.60]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 21, 10, 28]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.6323
  Bounding Box: [1416.00, 152.00, 1552.00, 391.60]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [115, 17, 125, 34]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.6309
  Bounding Box: [743.20, 1452.80, 879.20, 2003.20]
  Mask Area: 360 pixels
  Mask Ratio: 0.0128
  Mask BBox: [63, 119, 72, 160]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.6299
  Bounding Box: [726.80, 910.40, 933.60, 1153.60]
  Mask Area: 212 pixels
  Mask Ratio: 0.0075
  Mask BBox: [61, 76, 76, 94]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.6289
  Bounding Box: [1304.00, 582.80, 1448.00, 792.80]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [106, 50, 117, 65]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.6260
  Bounding Box: [1547.20, 1344.00, 1774.40, 1539.20]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [125, 109, 142, 124]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.6230
  Bounding Box: [1707.20, 368.00, 1912.00, 604.00]
  Mask Area: 205 pixels
  Mask Ratio: 0.0073
  Mask BBox: [138, 33, 152, 51]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.6211
  Bounding Box: [1374.40, 560.00, 1488.00, 686.40]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [112, 48, 119, 57]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.6187
  Bounding Box: [754.80, 0.00, 912.00, 106.20]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [63, 4, 75, 12]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.6167
  Bounding Box: [1868.80, 1747.20, 2048.00, 2048.00]
  Mask Area: 265 pixels
  Mask Ratio: 0.0094
  Mask BBox: [150, 141, 164, 164]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.6152
  Bounding Box: [485.20, 457.20, 694.80, 600.40]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [42, 40, 58, 50]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.6147
  Bounding Box: [1926.40, 772.00, 2032.00, 880.80]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [155, 65, 162, 71]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.6113
  Bounding Box: [1764.80, 92.40, 1851.20, 171.40]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [142, 12, 147, 17]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.6113
  Bounding Box: [302.40, 0.00, 432.80, 241.60]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [28, 4, 37, 22]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.6113
  Bounding Box: [473.60, 968.80, 688.00, 1216.80]
  Mask Area: 213 pixels
  Mask Ratio: 0.0075
  Mask BBox: [42, 80, 57, 99]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.6060
  Bounding Box: [1670.40, 612.00, 1788.80, 754.40]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [135, 52, 143, 62]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.6016
  Bounding Box: [860.00, 78.70, 932.00, 167.60]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [72, 11, 76, 16]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5996
  Bounding Box: [9.40, 1760.00, 84.80, 1929.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [5, 142, 10, 154]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5962
  Bounding Box: [195.60, 290.60, 340.00, 460.80]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [20, 27, 30, 39]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5962
  Bounding Box: [1571.20, 1368.00, 1843.20, 1633.60]
  Mask Area: 284 pixels
  Mask Ratio: 0.0101
  Mask BBox: [127, 111, 147, 131]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5898
  Bounding Box: [1961.60, 1074.40, 2044.80, 1314.40]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [158, 88, 163, 106]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5889
  Bounding Box: [1100.80, 1195.20, 1244.80, 1348.80]
  Mask Area: 112 pixels
  Mask Ratio: 0.0040
  Mask BBox: [90, 98, 101, 109]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.5859
  Bounding Box: [1096.00, 0.00, 1184.00, 79.30]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [90, 4, 96, 10]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.5850
  Bounding Box: [504.80, 1924.80, 640.00, 2046.40]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [44, 155, 52, 163]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.5830
  Bounding Box: [1547.20, 1806.40, 1704.00, 1937.60]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [125, 146, 137, 155]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.5781
  Bounding Box: [1260.80, 1299.20, 1353.60, 1420.80]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [103, 106, 109, 114]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.5771
  Bounding Box: [189.40, 643.20, 276.60, 786.40]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [19, 55, 25, 65]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.5742
  Bounding Box: [788.80, 1402.40, 892.80, 1479.20]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [66, 114, 72, 119]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.5742
  Bounding Box: [611.60, 1373.60, 736.40, 1538.40]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [52, 112, 61, 124]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.5679
  Bounding Box: [1924.80, 403.60, 2048.00, 572.40]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [155, 36, 164, 48]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.5654
  Bounding Box: [96.80, 1130.40, 176.40, 1204.00]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [12, 93, 17, 98]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.5645
  Bounding Box: [626.80, 1202.40, 706.00, 1405.60]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [53, 98, 59, 113]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.5640
  Bounding Box: [1492.80, 1159.20, 1587.20, 1284.00]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [121, 95, 127, 103]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.5581
  Bounding Box: [670.80, 4.70, 742.80, 79.10]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [57, 5, 62, 10]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.5562
  Bounding Box: [1357.60, 1904.00, 1480.80, 2038.40]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [111, 153, 117, 163]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.5493
  Bounding Box: [66.70, 175.60, 163.00, 279.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [10, 18, 16, 24]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.5459
  Bounding Box: [418.80, 1683.20, 575.60, 1801.60]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [37, 136, 48, 144]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.5449
  Bounding Box: [1075.20, 1796.80, 1220.80, 1880.00]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [88, 145, 99, 150]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.5425
  Bounding Box: [1057.60, 911.20, 1292.80, 1208.80]
  Mask Area: 231 pixels
  Mask Ratio: 0.0082
  Mask BBox: [87, 76, 104, 98]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.5420
  Bounding Box: [663.20, 456.40, 877.60, 582.80]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [56, 40, 72, 49]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.5400
  Bounding Box: [132.50, 784.00, 224.40, 900.80]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [15, 66, 19, 73]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.5396
  Bounding Box: [97.40, 1636.80, 189.60, 1713.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [12, 132, 18, 137]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.5337
  Bounding Box: [650.40, 1964.80, 779.20, 2035.20]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [55, 158, 64, 162]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.5312
  Bounding Box: [1848.00, 1048.00, 2017.60, 1273.60]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [149, 86, 161, 103]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.5308
  Bounding Box: [1676.80, 1028.00, 1926.40, 1229.60]
  Mask Area: 223 pixels
  Mask Ratio: 0.0079
  Mask BBox: [135, 85, 154, 100]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.5269
  Bounding Box: [777.60, 23.15, 896.00, 120.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [65, 6, 73, 13]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.5220
  Bounding Box: [1022.40, 439.60, 1094.40, 518.80]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [84, 39, 89, 44]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.5176
  Bounding Box: [1409.60, 107.80, 1524.80, 191.60]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [115, 13, 123, 18]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.5171
  Bounding Box: [1864.00, 427.20, 2043.20, 636.80]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [150, 38, 163, 53]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.5171
  Bounding Box: [1412.00, 1587.20, 1546.40, 1747.20]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [115, 128, 123, 140]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.5107
  Bounding Box: [1553.60, 165.20, 1646.40, 291.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [126, 17, 132, 25]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.5103
  Bounding Box: [1489.60, 1924.80, 1667.20, 2048.00]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [121, 155, 134, 164]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.5068
  Bounding Box: [492.00, 1172.80, 641.60, 1268.80]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [43, 96, 54, 103]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.5034
  Bounding Box: [410.40, 319.00, 544.00, 524.00]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [37, 29, 46, 44]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.5029
  Bounding Box: [426.40, 784.80, 571.20, 930.40]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [38, 66, 48, 76]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.5015
  Bounding Box: [1341.60, 326.80, 1496.80, 561.20]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [109, 30, 120, 47]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4980
  Bounding Box: [1272.80, 92.00, 1485.60, 340.80]
  Mask Area: 227 pixels
  Mask Ratio: 0.0080
  Mask BBox: [104, 12, 120, 30]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4976
  Bounding Box: [160.40, 1704.00, 280.40, 1870.40]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [17, 138, 25, 150]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4966
  Bounding Box: [1408.00, 689.20, 1529.60, 783.20]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [114, 58, 123, 65]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4961
  Bounding Box: [10.30, 668.40, 82.30, 761.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 57, 10, 63]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4944
  Bounding Box: [1899.20, 1038.40, 2036.80, 1252.80]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [153, 86, 163, 101]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4885
  Bounding Box: [652.80, 87.90, 840.80, 285.20]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [55, 11, 69, 26]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4878
  Bounding Box: [1548.00, 1782.40, 1632.00, 1856.00]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [125, 144, 131, 148]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4844
  Bounding Box: [1776.00, 109.60, 1843.20, 192.80]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [143, 13, 147, 19]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.4810
  Bounding Box: [1763.20, 1272.00, 1875.20, 1464.00]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [142, 104, 150, 118]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.4756
  Bounding Box: [1207.20, 218.80, 1308.00, 311.60]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [99, 22, 106, 28]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.4712
  Bounding Box: [1116.80, 1210.40, 1259.20, 1404.00]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [92, 99, 102, 113]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.4697
  Bounding Box: [1529.60, 1776.00, 1635.20, 1875.20]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [124, 143, 131, 150]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.4692
  Bounding Box: [1764.80, 1696.00, 1848.00, 1792.00]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [142, 137, 148, 142]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.4644
  Bounding Box: [1912.00, 194.40, 2033.60, 432.80]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [154, 20, 162, 37]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.4585
  Bounding Box: [534.40, 614.40, 761.60, 917.60]
  Mask Area: 209 pixels
  Mask Ratio: 0.0074
  Mask BBox: [46, 52, 63, 75]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.4575
  Bounding Box: [716.40, 1672.00, 772.00, 1777.60]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [60, 135, 64, 142]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.4561
  Bounding Box: [4.15, 658.00, 62.40, 749.20]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [5, 56, 8, 62]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.4519
  Bounding Box: [1936.00, 192.80, 2048.00, 401.20]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [156, 20, 164, 35]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.4514
  Bounding Box: [1244.00, 1056.80, 1341.60, 1204.00]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [102, 87, 108, 98]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.4514
  Bounding Box: [1431.20, 925.60, 1612.80, 1120.80]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [116, 77, 129, 91]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.4507
  Bounding Box: [1873.60, 873.60, 2040.00, 1052.80]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [151, 73, 163, 86]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.4500
  Bounding Box: [53.40, 1380.00, 244.40, 1611.20]
  Mask Area: 206 pixels
  Mask Ratio: 0.0073
  Mask BBox: [9, 112, 23, 129]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.4495
  Bounding Box: [1070.40, 1817.60, 1220.80, 1945.60]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [88, 146, 99, 155]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.4490
  Bounding Box: [290.00, 1344.80, 573.20, 1644.80]
  Mask Area: 414 pixels
  Mask Ratio: 0.0147
  Mask BBox: [27, 110, 48, 132]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.4485
  Bounding Box: [0.95, 0.00, 91.00, 64.40]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [5, 4, 10, 9]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.4426
  Bounding Box: [127.40, 1211.20, 220.20, 1294.40]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [14, 99, 21, 105]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.4421
  Bounding Box: [26.55, 684.80, 109.40, 787.20]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [7, 58, 12, 65]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.4392
  Bounding Box: [171.00, 632.00, 257.80, 755.20]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [18, 54, 24, 62]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.4382
  Bounding Box: [1704.00, 1472.80, 1899.20, 1715.20]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [138, 120, 152, 137]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.4373
  Bounding Box: [660.80, 0.00, 760.00, 90.30]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [56, 4, 63, 11]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.4373
  Bounding Box: [888.80, 348.00, 1016.80, 536.80]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [74, 32, 83, 45]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.4358
  Bounding Box: [6.10, 160.40, 70.90, 312.80]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [5, 17, 9, 28]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.4358
  Bounding Box: [816.80, 1486.40, 927.20, 1588.80]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [70, 121, 76, 128]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.4351
  Bounding Box: [194.80, 1627.20, 300.80, 1723.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [20, 132, 27, 138]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.4346
  Bounding Box: [2.10, 1513.60, 82.60, 1692.80]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [5, 123, 10, 136]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.4302
  Bounding Box: [1402.40, 93.20, 1552.80, 176.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [114, 12, 125, 17]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.4268
  Bounding Box: [99.60, 1931.20, 248.40, 2048.00]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [12, 155, 23, 164]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.4211
  Bounding Box: [121.20, 970.40, 209.60, 1101.60]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [15, 80, 20, 89]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.4182
  Bounding Box: [840.80, 93.60, 925.60, 188.40]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [70, 12, 76, 18]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.4182
  Bounding Box: [866.40, 93.60, 951.20, 188.40]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [72, 12, 78, 18]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.4182
  Bounding Box: [729.60, 1595.20, 859.20, 2017.60]
  Mask Area: 323 pixels
  Mask Ratio: 0.0114
  Mask BBox: [61, 129, 71, 161]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.4167
  Bounding Box: [239.40, 1457.60, 430.00, 1732.80]
  Mask Area: 186 pixels
  Mask Ratio: 0.0066
  Mask BBox: [23, 118, 37, 139]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.4136
  Bounding Box: [1576.80, 1179.20, 1763.20, 1382.40]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [128, 97, 141, 111]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.4131
  Bounding Box: [548.80, 816.80, 662.40, 954.40]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [47, 68, 55, 78]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.4131
  Bounding Box: [1888.00, 1603.20, 2016.00, 1699.20]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [152, 130, 161, 136]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.4041
  Bounding Box: [1964.80, 741.60, 2044.80, 871.20]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [158, 62, 163, 72]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3989
  Bounding Box: [838.40, 66.10, 926.40, 162.40]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [70, 10, 76, 16]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3984
  Bounding Box: [148.00, 967.20, 237.60, 1106.40]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [16, 80, 22, 90]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3970
  Bounding Box: [1333.60, 1299.20, 1424.80, 1368.00]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [109, 106, 114, 110]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3938
  Bounding Box: [60.90, 278.00, 325.60, 554.00]
  Mask Area: 340 pixels
  Mask Ratio: 0.0120
  Mask BBox: [9, 26, 29, 47]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3938
  Bounding Box: [1432.80, 411.20, 1640.00, 636.80]
  Mask Area: 225 pixels
  Mask Ratio: 0.0080
  Mask BBox: [116, 37, 132, 53]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3923
  Bounding Box: [5.65, 1952.00, 121.60, 2041.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [5, 157, 13, 163]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3918
  Bounding Box: [1643.20, 1001.60, 1723.20, 1104.00]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [133, 83, 138, 90]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3857
  Bounding Box: [450.40, 926.40, 558.40, 1030.40]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [40, 77, 47, 83]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3853
  Bounding Box: [8.60, 736.40, 134.80, 915.20]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [5, 62, 14, 75]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3850
  Bounding Box: [1688.00, 595.60, 1806.40, 691.60]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [136, 51, 145, 58]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3804
  Bounding Box: [246.40, 478.80, 393.60, 598.80]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [24, 42, 34, 50]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3772
  Bounding Box: [748.00, 938.40, 996.00, 1141.60]
  Mask Area: 204 pixels
  Mask Ratio: 0.0072
  Mask BBox: [63, 78, 81, 93]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3755
  Bounding Box: [1865.60, 463.60, 2038.40, 732.40]
  Mask Area: 274 pixels
  Mask Ratio: 0.0097
  Mask BBox: [150, 41, 163, 61]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3748
  Bounding Box: [664.80, 529.60, 874.40, 674.40]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [56, 46, 71, 56]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3699
  Bounding Box: [1235.20, 1207.20, 1352.00, 1306.40]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [101, 99, 109, 106]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3689
  Bounding Box: [1315.20, 529.20, 1392.00, 617.20]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [107, 46, 112, 51]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3684
  Bounding Box: [1030.40, 1467.20, 1283.20, 1660.80]
  Mask Area: 228 pixels
  Mask Ratio: 0.0081
  Mask BBox: [85, 119, 104, 133]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3679
  Bounding Box: [807.20, 1179.20, 925.60, 1264.00]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [68, 97, 76, 102]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3657
  Bounding Box: [1564.80, 1098.40, 1667.20, 1175.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [127, 90, 133, 95]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3652
  Bounding Box: [841.60, 1487.20, 940.80, 1620.80]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [70, 121, 77, 130]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3606
  Bounding Box: [436.80, 329.00, 653.60, 519.20]
  Mask Area: 209 pixels
  Mask Ratio: 0.0074
  Mask BBox: [39, 30, 55, 44]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3577
  Bounding Box: [944.80, 1545.60, 1037.60, 1680.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [78, 125, 85, 135]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3572
  Bounding Box: [1244.00, 1284.80, 1341.60, 1406.40]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [102, 105, 108, 113]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [1089.60, 0.00, 1187.20, 54.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [90, 3, 96, 8]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [1115.20, 0.00, 1212.80, 54.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [92, 3, 98, 8]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [1115.20, 5.65, 1212.80, 80.20]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [92, 5, 98, 10]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [960.00, 160.80, 1056.00, 234.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [79, 17, 86, 22]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3552
  Bounding Box: [208.40, 1715.20, 332.80, 1833.60]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [21, 138, 29, 147]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3540
  Bounding Box: [1664.00, 1908.80, 1795.20, 2024.00]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [134, 154, 144, 162]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3533
  Bounding Box: [442.40, 794.40, 634.40, 962.40]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [39, 67, 53, 79]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3530
  Bounding Box: [885.60, 1549.60, 994.40, 1659.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [74, 126, 81, 133]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3530
  Bounding Box: [562.00, 9.00, 657.20, 77.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [48, 5, 55, 8]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3523
  Bounding Box: [1795.20, 1276.00, 2003.20, 1516.00]
  Mask Area: 250 pixels
  Mask Ratio: 0.0089
  Mask BBox: [145, 104, 160, 122]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3521
  Bounding Box: [1905.60, 290.80, 2043.20, 558.80]
  Mask Area: 210 pixels
  Mask Ratio: 0.0074
  Mask BBox: [153, 27, 163, 47]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3518
  Bounding Box: [99.00, 1294.40, 226.20, 1393.60]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [12, 106, 21, 112]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3477
  Bounding Box: [52.20, 937.60, 205.20, 1134.40]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [9, 78, 20, 92]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3469
  Bounding Box: [1225.60, 1737.60, 1433.60, 2038.40]
  Mask Area: 267 pixels
  Mask Ratio: 0.0095
  Mask BBox: [100, 140, 115, 163]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3464
  Bounding Box: [1212.80, 844.80, 1307.20, 936.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [99, 70, 106, 77]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3452
  Bounding Box: [696.00, 272.20, 795.20, 351.00]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [59, 26, 66, 31]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.3442
  Bounding Box: [1734.40, 107.40, 1958.40, 380.00]
  Mask Area: 260 pixels
  Mask Ratio: 0.0092
  Mask BBox: [140, 13, 156, 33]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.3440
  Bounding Box: [832.00, 296.00, 1011.20, 510.40]
  Mask Area: 208 pixels
  Mask Ratio: 0.0074
  Mask BBox: [69, 28, 82, 43]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.3433
  Bounding Box: [73.60, 1096.00, 160.00, 1185.60]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [10, 90, 16, 96]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.3433
  Bounding Box: [99.20, 1096.00, 185.60, 1185.60]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [12, 90, 18, 96]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.3433
  Bounding Box: [73.60, 1121.60, 160.00, 1211.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [10, 92, 16, 98]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.3433
  Bounding Box: [1312.00, 1294.40, 1419.20, 1382.40]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [107, 106, 114, 111]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.3430
  Bounding Box: [1392.00, 784.80, 1459.20, 879.20]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [113, 66, 117, 72]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.3403
  Bounding Box: [75.50, 1307.20, 202.00, 1428.80]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [10, 107, 19, 115]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.3398
  Bounding Box: [1446.40, 1001.60, 1590.40, 1131.20]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [117, 83, 128, 92]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.3386
  Bounding Box: [620.80, 947.20, 737.60, 1065.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [53, 78, 61, 85]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.3381
  Bounding Box: [1449.60, 974.40, 1592.00, 1096.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [118, 81, 124, 89]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.3354
  Bounding Box: [1056.80, 1987.20, 1168.80, 2038.40]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [87, 160, 95, 163]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.3350
  Bounding Box: [147.70, 1196.80, 239.20, 1281.60]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [16, 98, 20, 104]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.3350
  Bounding Box: [147.70, 1222.40, 239.20, 1307.20]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [16, 100, 20, 106]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.3325
  Bounding Box: [924.80, 848.80, 1096.00, 1026.40]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [77, 74, 86, 84]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.3306
  Bounding Box: [1025.60, 390.40, 1088.00, 513.60]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [85, 35, 88, 44]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.3306
  Bounding Box: [12.50, 1732.80, 132.50, 1934.40]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [5, 140, 14, 154]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.3289
  Bounding Box: [1992.00, 1486.40, 2048.00, 1640.00]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [160, 121, 164, 132]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.3281
  Bounding Box: [289.40, 778.40, 367.20, 863.20]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [27, 65, 32, 71]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.3250
  Bounding Box: [798.40, 178.40, 1006.40, 464.80]
  Mask Area: 347 pixels
  Mask Ratio: 0.0123
  Mask BBox: [67, 18, 82, 40]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.3247
  Bounding Box: [1976.00, 3.25, 2048.00, 155.20]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [159, 5, 164, 16]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.3242
  Bounding Box: [1382.40, 1222.40, 1526.40, 1416.00]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [112, 100, 123, 114]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.3237
  Bounding Box: [175.00, 1049.60, 284.20, 1171.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [18, 86, 26, 95]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.3235
  Bounding Box: [234.40, 690.40, 305.20, 792.80]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [23, 58, 27, 65]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.3235
  Bounding Box: [704.00, 1672.00, 784.80, 1806.40]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [59, 135, 65, 145]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.3230
  Bounding Box: [682.00, 557.60, 852.80, 706.40]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [59, 48, 70, 59]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.3230
  Bounding Box: [432.80, 390.00, 669.60, 595.60]
  Mask Area: 263 pixels
  Mask Ratio: 0.0093
  Mask BBox: [38, 35, 56, 50]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.3230
  Bounding Box: [1408.00, 1555.20, 1644.80, 1776.00]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [114, 126, 132, 142]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.3230
  Bounding Box: [1456.00, 316.60, 1667.20, 534.40]
  Mask Area: 209 pixels
  Mask Ratio: 0.0074
  Mask BBox: [118, 29, 134, 45]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.3201
  Bounding Box: [1342.40, 1828.80, 1465.60, 2014.40]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [109, 147, 118, 161]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.3201
  Bounding Box: [502.00, 1859.20, 659.60, 2044.80]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [44, 150, 55, 163]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.3174
  Bounding Box: [235.20, 482.40, 356.00, 618.40]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [23, 42, 31, 52]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.3174
  Bounding Box: [1218.40, 1064.80, 1314.40, 1207.20]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [100, 88, 106, 98]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.3169
  Bounding Box: [134.00, 814.40, 209.20, 928.00]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [15, 68, 20, 76]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.3167
  Bounding Box: [1464.80, 1172.00, 1572.00, 1311.20]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [119, 96, 126, 106]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.3145
  Bounding Box: [1921.60, 1500.80, 2040.00, 1640.00]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [155, 122, 160, 132]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.3140
  Bounding Box: [1891.20, 1646.40, 2048.00, 1860.80]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [152, 133, 163, 149]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.3132
  Bounding Box: [11.00, 52.60, 95.00, 199.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [5, 9, 11, 19]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.3120
  Bounding Box: [225.80, 518.80, 353.80, 644.40]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [22, 45, 31, 54]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.3108
  Bounding Box: [1864.00, 1514.40, 2020.80, 1692.80]
  Mask Area: 112 pixels
  Mask Ratio: 0.0040
  Mask BBox: [150, 123, 160, 135]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [1338.40, 1916.80, 1460.00, 2048.00]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [109, 154, 118, 164]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.3086
  Bounding Box: [1747.20, 1912.00, 1872.00, 2020.80]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [141, 154, 149, 161]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [1006.40, 433.60, 1078.40, 511.20]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [83, 38, 88, 43]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [1006.40, 459.20, 1078.40, 536.80]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [83, 40, 88, 45]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [1032.00, 459.20, 1104.00, 536.80]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [85, 40, 88, 45]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.3069
  Bounding Box: [1167.20, 1572.80, 1287.20, 1643.20]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [96, 127, 104, 132]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [2009.60, 1533.60, 2048.00, 1656.00]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [161, 124, 163, 133]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.3062
  Bounding Box: [1069.60, 1969.60, 1204.00, 2043.20]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [88, 158, 98, 163]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.3059
  Bounding Box: [1681.60, 596.40, 1883.20, 780.80]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [136, 51, 151, 64]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.3057
  Bounding Box: [4.90, 1523.20, 74.10, 1648.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [5, 123, 9, 132]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.3047
  Bounding Box: [12.30, 673.20, 127.10, 773.60]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [5, 57, 13, 64]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.3040
  Bounding Box: [742.40, 638.80, 830.40, 746.80]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [62, 54, 68, 62]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.3040
  Bounding Box: [502.80, 1419.20, 761.20, 1697.60]
  Mask Area: 274 pixels
  Mask Ratio: 0.0097
  Mask BBox: [44, 115, 63, 136]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.3037
  Bounding Box: [1622.40, 167.20, 1785.60, 315.20]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [131, 18, 143, 28]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.3032
  Bounding Box: [145.40, 804.80, 239.80, 908.80]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [16, 67, 20, 73]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [1763.20, 72.30, 1836.80, 161.40]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [142, 10, 147, 16]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [1788.80, 72.30, 1862.40, 161.40]
  Mask Area: 18 pixels
  Mask Ratio: 0.0006
  Mask BBox: [144, 11, 147, 16]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [1788.80, 97.90, 1862.40, 187.00]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [144, 12, 147, 18]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [261.20, 1037.60, 388.80, 1133.60]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [25, 86, 34, 92]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [261.20, 1063.20, 388.80, 1159.20]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [25, 88, 34, 94]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.3008
  Bounding Box: [1572.00, 156.60, 1672.00, 275.00]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [127, 17, 134, 24]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.3008
  Bounding Box: [499.60, 583.60, 601.20, 673.20]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [44, 50, 50, 56]

