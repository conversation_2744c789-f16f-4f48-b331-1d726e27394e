# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license
# YOLOv5 分割模型损失函数模块
# 包含边界框IoU计算、焦点损失、分割损失等核心组件

import torch
import math
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

def bbox_ioa(box1, box2, eps=1e-7):
    """
    计算 box1 与 box2 的交并比（这里是交集面积 / box2 面积，不是常见的 IoU）。
    
    参数:
        box1: np.array，形状 (4)，单个框 [x1, y1, x2, y2]
        box2: np.array，形状 (n, 4)，多个框，每一行为 [x1, y1, x2, y2]
        eps:  防止除零的小常数
    返回:
        np.array，形状 (n)，表示 box1 与每个 box2 的交并比 (Intersection over Area of box2)
    """
    # 分别获取 box1 的坐标
    b1_x1, b1_y1, b1_x2, b1_y2 = box1 
    # 将 box2 转置，便于批量取出坐标
    # 例如 b2_x1 = box2[:,0], b2_y1 = box2[:,1], b2_x2 = box2[:,2], b2_y2 = box2[:,3]
    b2_x1, b2_y1, b2_x2, b2_y2 = box2.T
    # 计算交集区域的宽和高（取 min/max 以保证在交集范围内）
    inter_w = (np.minimum(b1_x2, b2_x2) - np.maximum(b1_x1, b2_x1)).clip(0)  # 交集宽度
    inter_h = (np.minimum(b1_y2, b2_y2) - np.maximum(b1_y1, b2_y1)).clip(0)  # 交集高度
    # 交集面积 = 宽 × 高
    inter_area = inter_w * inter_h
    # box2 的面积（加上 eps 避免除零错误）
    box2_area = (b2_x2 - b2_x1) * (b2_y2 - b2_y1) + eps
    # 交并比（Intersection over Area of box2）
    return inter_area / box2_area

def box_iou(box1, box2, eps=1e-7):
    # https://github.com/pytorch/vision/blob/master/torchvision/ops/boxes.py
    """
    计算边界框的交并比(Intersection over Union, IoU)
    
    两组边界框都应该是 (x1, y1, x2, y2) 格式
    
    Args:
        box1 (Tensor[N, 4]): 第一组边界框
        box2 (Tensor[M, 4]): 第二组边界框
        eps (float): 防止除零的极小值，默认1e-7
    
    Returns:
        iou (Tensor[N, M]): NxM矩阵，包含每对边界框的IoU值
    """
    # inter(N,M) = (rb(N,M,2) - lt(N,M,2)).clamp(0).prod(2)
    (a1, a2), (b1, b2) = box1.unsqueeze(1).chunk(2, 2), box2.unsqueeze(0).chunk(2, 2)
    inter = (torch.min(a2, b2) - torch.max(a1, b1)).clamp(0).prod(2)

    # IoU = inter / (area1 + area2 - inter)
    return inter / ((a2 - a1).prod(2) + (b2 - b1).prod(2) - inter + eps)

def crop_mask(masks, boxes):
    """
    通过将预测边界框外的所有内容置零来"裁剪"预测掩码。
    由Chong进行向量化（感谢Chong）。
    
    Args:
        masks: [n, h, w] tensor，n个掩码
        boxes: [n, 4] tensor，相对点形式的边界框坐标
    """
    n, h, w = masks.shape  # 掩码形状
    x1, y1, x2, y2 = torch.chunk(boxes[:, :, None], 4, 1)  # x1形状(n,1,1)
    r = torch.arange(w, device=masks.device, dtype=x1.dtype)[None, None, :]  # 行形状(1,1,w)
    c = torch.arange(h, device=masks.device, dtype=x1.dtype)[None, :, None]  # 列形状(1,h,1)
    
    return masks * ((r >= x1) & (r < x2) & (c >= y1) & (c < y2))  # 应用边界框掩码

def is_parallel(model):
    """检查模型是否使用数据并行(DP)或分布式数据并行(DDP)。"""
    return type(model) in (nn.parallel.DataParallel, nn.parallel.DistributedDataParallel)

def de_parallel(model):
    """通过移除数据并行(DP)或分布式数据并行(DDP)包装，返回单GPU模型。"""
    return model.module if is_parallel(model) else model

def xywh2xyxy(x):
    """将nx4边界框从[x, y, w, h]格式转换为[x1, y1, x2, y2]格式，其中xy1=左上角，xy2=右下角。"""
    y = x.clone() if isinstance(x, torch.Tensor) else np.copy(x)
    y[..., 0] = x[..., 0] - x[..., 2] / 2  # 左上角x坐标
    y[..., 1] = x[..., 1] - x[..., 3] / 2  # 左上角y坐标
    y[..., 2] = x[..., 0] + x[..., 2] / 2  # 右下角x坐标
    y[..., 3] = x[..., 1] + x[..., 3] / 2  # 右下角y坐标
    return y

def smooth_BCE(eps=0.1):
    """返回标签平滑的BCE目标值以减少过拟合；正样本: `1.0 - 0.5*eps`, 负样本: `0.5*eps`。
    详情参见 https://github.com/ultralytics/yolov3/issues/238#issuecomment-598028441
    """
    return 1.0 - 0.5 * eps, 0.5 * eps

class FocalLoss(nn.Module):
    """焦点损失类，通过gamma和alpha参数修改BCEWithLogitsLoss来解决类别不平衡问题。"""

    def __init__(self, loss_fcn, gamma=1.5, alpha=0.25):
        """初始化焦点损失函数。
        
        Args:
            loss_fcn: 基础损失函数，必须是nn.BCEWithLogitsLoss()
            gamma: 调节因子，控制难易样本的权重
            alpha: 平衡因子，控制正负样本的权重
        """
        super().__init__()
        self.loss_fcn = loss_fcn  # 必须是nn.BCEWithLogitsLoss()
        self.gamma = gamma  # 难易样本调节参数
        self.alpha = alpha  # 正负样本平衡参数
        self.reduction = loss_fcn.reduction
        self.loss_fcn.reduction = "none"  # 需要对每个元素单独应用焦点损失

    def forward(self, pred, true):
        """计算预测值和真实标签之间的焦点损失。"""
        loss = self.loss_fcn(pred, true)  # 计算基础BCE损失

        pred_prob = torch.sigmoid(pred)  # 从logits获取概率
        p_t = true * pred_prob + (1 - true) * (1 - pred_prob)  # 计算pt
        alpha_factor = true * self.alpha + (1 - true) * (1 - self.alpha)  # alpha权重
        modulating_factor = (1.0 - p_t) ** self.gamma  # 调节因子
        loss *= alpha_factor * modulating_factor  # 应用焦点损失权重

        if self.reduction == "mean":
            return loss.mean()
        elif self.reduction == "sum":
            return loss.sum()
        else:  # 'none'
            return loss


def bbox_iou(box1, box2, xywh=True, GIoU=False, DIoU=False, CIoU=False, eps=1e-7):
    """
    计算两个边界框之间的IoU、GIoU、DIoU或CIoU，支持xywh/xyxy格式。
    
    Args:
        box1: 边界框1，形状为(1,4)
        box2: 边界框2，形状为(n,4)
        xywh: 是否为xywh格式，否则为xyxy格式
        GIoU: 是否计算GIoU
        DIoU: 是否计算DIoU
        CIoU: 是否计算CIoU
        eps: 防止除零的小值
    """
    # 获取边界框坐标
    if xywh:  # 从xywh转换为xyxy格式
        (x1, y1, w1, h1), (x2, y2, w2, h2) = box1.chunk(4, -1), box2.chunk(4, -1)
        w1_, h1_, w2_, h2_ = w1 / 2, h1 / 2, w2 / 2, h2 / 2  # 半宽半高
        b1_x1, b1_x2, b1_y1, b1_y2 = x1 - w1_, x1 + w1_, y1 - h1_, y1 + h1_
        b2_x1, b2_x2, b2_y1, b2_y2 = x2 - w2_, x2 + w2_, y2 - h2_, y2 + h2_
    else:  # 已经是xyxy格式
        b1_x1, b1_y1, b1_x2, b1_y2 = box1.chunk(4, -1)
        b2_x1, b2_y1, b2_x2, b2_y2 = box2.chunk(4, -1)
        w1, h1 = b1_x2 - b1_x1, (b1_y2 - b1_y1).clamp(eps)  # 计算宽高
        w2, h2 = b2_x2 - b2_x1, (b2_y2 - b2_y1).clamp(eps)

    # 计算交集面积
    inter = (b1_x2.minimum(b2_x2) - b1_x1.maximum(b2_x1)).clamp(0) * (
        b1_y2.minimum(b2_y2) - b1_y1.maximum(b2_y1)
    ).clamp(0)

    # 计算并集面积
    union = w1 * h1 + w2 * h2 - inter + eps

    # 计算IoU
    iou = inter / union
    if CIoU or DIoU or GIoU:
        cw = b1_x2.maximum(b2_x2) - b1_x1.minimum(b2_x1)  # 最小外接矩形宽度
        ch = b1_y2.maximum(b2_y2) - b1_y1.minimum(b2_y1)  # 最小外接矩形高度
        if CIoU or DIoU:  # 距离IoU或完整IoU https://arxiv.org/abs/1911.08287v1
            c2 = cw**2 + ch**2 + eps  # 最小外接矩形对角线的平方
            rho2 = ((b2_x1 + b2_x2 - b1_x1 - b1_x2) ** 2 + (b2_y1 + b2_y2 - b1_y1 - b1_y2) ** 2) / 4  # 中心点距离的平方
            if CIoU:  # 完整IoU https://github.com/Zzh-tju/DIoU-SSD-pytorch/blob/master/utils/box/box_utils.py#L47
                v = (4 / math.pi**2) * (torch.atan(w2 / h2) - torch.atan(w1 / h1)).pow(2)  # 长宽比一致性
                with torch.no_grad():
                    alpha = v / (v - iou + (1 + eps))  # 权重参数
                return iou - (rho2 / c2 + v * alpha)  # CIoU
            return iou - rho2 / c2  # DIoU
        c_area = cw * ch + eps  # 最小外接矩形面积
        return iou - (c_area - union) / c_area  # GIoU https://arxiv.org/pdf/1902.09630.pdf
    return iou  # 标准IoU

class ComputeLoss:
    """计算YOLOv5模型的损失组件，包括分类损失、目标性损失、边界框损失和掩码损失。"""

    def __init__(self, model, autobalance=False, overlap=False):
        """初始化YOLOv5模型的损失计算函数。
        
        Args:
            model: YOLOv5模型
            autobalance: 是否启用自动平衡
            overlap: 是否处理重叠
        """
        self.sort_obj_iou = False  # 是否按IoU排序目标
        self.overlap = overlap  # 是否处理重叠
        device = next(model.parameters()).device  # 获取模型设备
        h = model.hyp  # 超参数

        # 定义损失函数
        BCEcls = nn.BCEWithLogitsLoss(pos_weight=torch.tensor([h["cls_pw"]], device=device))  # 分类损失
        BCEobj = nn.BCEWithLogitsLoss(pos_weight=torch.tensor([h["obj_pw"]], device=device))  # 目标性损失

        # 类别标签平滑 https://arxiv.org/pdf/1902.04103.pdf eqn 3
        self.cp, self.cn = smooth_BCE(eps=h.get("label_smoothing", 0.0))  # 正样本、负样本BCE目标

        # 焦点损失
        g = h["fl_gamma"]  # 焦点损失gamma参数
        if g > 0:
            BCEcls, BCEobj = FocalLoss(BCEcls, g), FocalLoss(BCEobj, g)  # 应用焦点损失

        m = de_parallel(model).model[-1]  # 检测模块
        self.balance = {3: [4.0, 1.0, 0.4]}.get(m.nl, [4.0, 1.0, 0.25, 0.06, 0.02])  # P3-P7层平衡权重
        self.ssi = list(m.stride).index(16) if autobalance else 0  # stride 16索引
        self.BCEcls, self.BCEobj, self.gr, self.hyp, self.autobalance = BCEcls, BCEobj, 1.0, h, autobalance
        self.na = m.na  # 锚框数量
        self.nc = m.nc  # 类别数量
        self.nl = m.nl  # 层数
        self.nm = m.nm  # 掩码数量
        self.anchors = m.anchors  # 锚框
        self.device = device  # 设备

    def __call__(self, preds, targets, masks):  # 预测值、目标值、掩码
        """计算给定预测值、目标值和掩码的YOLOv5模型损失；返回总损失组件。"""
        p, proto = preds  # 预测值和原型
        bs, nm, mask_h, mask_w = proto.shape  # 批次大小、掩码数量、掩码高度、掩码宽度
        lcls = torch.zeros(1, device=self.device)  # 分类损失
        lbox = torch.zeros(1, device=self.device)  # 边界框损失
        lobj = torch.zeros(1, device=self.device)  # 目标性损失
        lseg = torch.zeros(1, device=self.device)  # 分割损失
        tcls, tbox, indices, anchors, tidxs, xywhn = self.build_targets(p, targets)  # 构建目标

        # 损失计算
        for i, pi in enumerate(p):  # 层索引，层预测值
            b, a, gj, gi = indices[i]  # 图像索引、锚框索引、网格y、网格x
            tobj = torch.zeros(pi.shape[:4], dtype=pi.dtype, device=self.device)  # 目标对象

            if n := b.shape[0]:  # 如果有目标
                pxy, pwh, _, pcls, pmask = pi[b, a, gj, gi].split((2, 2, 1, self.nc, nm), 1)  # 预测值子集

                # 边界框回归
                pxy = pxy.sigmoid() * 2 - 0.5  # 中心点坐标
                pwh = (pwh.sigmoid() * 2) ** 2 * anchors[i]  # 宽高
                pbox = torch.cat((pxy, pwh), 1)  # 预测边界框
                iou = bbox_iou(pbox, tbox[i], CIoU=True).squeeze()  # IoU(预测值, 目标值)
                lbox += (1.0 - iou).mean()  # IoU损失

                # 目标性
                iou = iou.detach().clamp(0).type(tobj.dtype)  # 分离梯度并限制范围
                if self.sort_obj_iou:
                    j = iou.argsort()  # 按IoU排序
                    b, a, gj, gi, iou = b[j], a[j], gj[j], gi[j], iou[j]
                if self.gr < 1:
                    iou = (1.0 - self.gr) + self.gr * iou  # IoU比率调整
                tobj[b, a, gj, gi] = iou  # IoU比率

                # 分类
                if self.nc > 1:  # 分类损失（仅当有多个类别时）
                    t = torch.full_like(pcls, self.cn, device=self.device)  # 目标
                    t[range(n), tcls[i]] = self.cp  # 设置正样本目标
                    lcls += self.BCEcls(pcls, t)  # BCE损失

                # 掩码回归
                if tuple(masks.shape[-2:]) != (mask_h, mask_w):  # 下采样
                    masks = F.interpolate(masks[None], (mask_h, mask_w), mode="nearest")[0]
                marea = xywhn[i][:, 2:].prod(1)  # 掩码宽度、高度归一化
                mxyxy = xywh2xyxy(xywhn[i] * torch.tensor([mask_w, mask_h, mask_w, mask_h], device=self.device))
                for bi in b.unique():  # 遍历每个批次图像
                    j = b == bi  # 匹配索引
                    if self.overlap:  # 如果处理重叠
                        mask_gti = torch.where(masks[bi][None] == tidxs[i][j].view(-1, 1, 1), 1.0, 0.0)
                    else:
                        mask_gti = masks[tidxs[i]][j]  # 获取真实掩码
                    lseg += self.single_mask_loss(mask_gti, pmask[j], proto[bi], mxyxy[j], marea[j])  # 计算掩码损失

            obji = self.BCEobj(pi[..., 4], tobj)  # 目标性损失
            lobj += obji * self.balance[i]  # 加权目标性损失
            if self.autobalance:  # 如果启用自动平衡
                self.balance[i] = self.balance[i] * 0.9999 + 0.0001 / obji.detach().item()

        if self.autobalance:  # 归一化平衡权重
            self.balance = [x / self.balance[self.ssi] for x in self.balance]
        lbox *= self.hyp["box"]  # 边界框损失权重
        lobj *= self.hyp["obj"]  # 目标性损失权重
        lcls *= self.hyp["cls"]  # 分类损失权重
        lseg *= self.hyp["box"] / bs  # 分割损失权重

        loss = lbox + lobj + lcls + lseg  # 总损失
        return loss * bs, torch.cat((lbox, lseg, lobj, lcls)).detach()  # 返回总损失和各组件损失

    def single_mask_loss(self, gt_mask, pred, proto, xyxy, area):
        """计算并归一化YOLOv5预测掩码和真实掩码之间的单个掩码损失。"""
        pred_mask = (pred @ proto.view(self.nm, -1)).view(-1, *proto.shape[1:])  # (n,32) @ (32,80,80) -> (n,80,80)
        loss = F.binary_cross_entropy_with_logits(pred_mask, gt_mask, reduction="none")  # 二元交叉熵损失
        return (crop_mask(loss, xyxy).mean(dim=(1, 2)) / area).mean()  # 裁剪并归一化损失

    def build_targets(self, p, targets):
        """为损失计算准备YOLOv5目标；输入目标(图像, 类别, x, y, w, h)，输出目标类别/边界框。"""
        na, nt = self.na, targets.shape[0]  # 锚框数量，目标数量
        tcls, tbox, indices, anch, tidxs, xywhn = [], [], [], [], [], []  # 初始化输出列表
        gain = torch.ones(8, device=self.device)  # 归一化到网格空间的增益
        ai = torch.arange(na, device=self.device).float().view(na, 1).repeat(1, nt)  # 锚框索引，与.repeat_interleave(nt)相同
        if self.overlap:  # 如果处理重叠
            batch = p[0].shape[0]  # 批次大小
            ti = []  # 目标索引列表
            for i in range(batch):  # 遍历每个批次
                num = (targets[:, 0] == i).sum()  # 找到每个图像的目标数量
                ti.append(torch.arange(num, device=self.device).float().view(1, num).repeat(na, 1) + 1)  # (na, num)
            ti = torch.cat(ti, 1)  # (na, nt) 连接所有目标索引
        else:
            ti = torch.arange(nt, device=self.device).float().view(1, nt).repeat(na, 1)  # 目标索引
        targets = torch.cat((targets.repeat(na, 1, 1), ai[..., None], ti[..., None]), 2)  # 添加锚框索引和目标索引

        g = 0.5  # 偏置
        off = (
            torch.tensor(
                [
                    [0, 0],
                    [1, 0],
                    [0, 1],
                    [-1, 0],
                    [0, -1],  # j,k,l,m
                    # [1, 1], [1, -1], [-1, 1], [-1, -1],  # jk,jm,lk,lm
                ],
                device=self.device,
            ).float()
            * g
        )  # 偏移量

        for i in range(self.nl):  # 遍历每个检测层
            anchors, shape = self.anchors[i], p[i].shape  # 当前层的锚框和预测形状
            gain[2:6] = torch.tensor(shape)[[3, 2, 3, 2]]  # xyxy增益

            # 将目标匹配到锚框
            t = targets * gain  # 形状(3,n,7)
            if nt:  # 如果有目标
                # 匹配
                r = t[..., 4:6] / anchors[:, None]  # 宽高比
                j = torch.max(r, 1 / r).max(2)[0] < self.hyp["anchor_t"]  # 比较
                # j = wh_iou(anchors, t[:, 4:6]) > model.hyp['iou_t']  # iou(3,n)=wh_iou(anchors(3,2), gwh(n,2))
                t = t[j]  # 过滤

                # 偏移量
                gxy = t[:, 2:4]  # 网格xy
                gxi = gain[[2, 3]] - gxy  # 反向
                j, k = ((gxy % 1 < g) & (gxy > 1)).T  # 左上偏移
                l, m = ((gxi % 1 < g) & (gxi > 1)).T  # 右下偏移
                j = torch.stack((torch.ones_like(j), j, k, l, m))  # 偏移掩码
                t = t.repeat((5, 1, 1))[j]  # 重复目标
                offsets = (torch.zeros_like(gxy)[None] + off[:, None])[j]  # 应用偏移
            else:
                t = targets[0]  # 无目标
                offsets = 0

            # 定义
            bc, gxy, gwh, at = t.chunk(4, 1)  # (图像, 类别), 网格xy, 网格wh, 锚框
            (a, tidx), (b, c) = at.long().T, bc.long().T  # 锚框, 图像, 类别
            gij = (gxy - offsets).long()  # 网格索引
            gi, gj = gij.T  # 网格索引

            # 添加
            indices.append((b, a, gj.clamp_(0, shape[2] - 1), gi.clamp_(0, shape[3] - 1)))  # 图像, 锚框, 网格
            tbox.append(torch.cat((gxy - gij, gwh), 1))  # 边界框
            anch.append(anchors[a])  # 锚框
            tcls.append(c)  # 类别
            tidxs.append(tidx)  # 目标索引
            xywhn.append(torch.cat((gxy, gwh), 1) / gain[2:6])  # xywh归一化

        return tcls, tbox, indices, anch, tidxs, xywhn  # 返回目标类别、边界框、索引、锚框、目标索引、归一化坐标
