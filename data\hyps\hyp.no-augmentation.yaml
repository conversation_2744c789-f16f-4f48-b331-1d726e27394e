# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

# Albumentations框架下使用的超参数
# python train.py --hyp hyp.no-augmentation.yaml
# YOLOv5 + Albumentations 使用示例请参考 https://github.com/ultralytics/yolov5/pull/3882

lr0: 0.01 # 初始学习率 (SGD=1E-2, Adam=1E-3)
lrf: 0.1 # 最终OneCycleLR学习率 (lr0 * lrf)
momentum: 0.937 # SGD动量/Adam beta1
weight_decay: 0.0005 # 优化器权重衰减 5e-4
warmup_epochs: 3.0 # 预热周期 (可以为小数)
warmup_momentum: 0.8 # 预热初始动量
warmup_bias_lr: 0.1 # 预热初始偏置学习率
box: 0.05 # 边界框损失增益
cls: 0.3 # 分类损失增益
cls_pw: 1.0 # 分类BCELoss正样本权重
obj: 0.7 # 目标损失增益 (随像素缩放)
obj_pw: 1.0 # 目标BCELoss正样本权重
iou_t: 0.20 # IoU训练阈值
anchor_t: 4.0 # 锚点倍数阈值
# anchors: 3  # 每个输出层的锚点数 (0表示忽略)
# 由于我们希望使用albumentation框架，因此这些参数均为零
fl_gamma: 0.0 # focal loss gamma (efficientDet默认gamma=1.5)
hsv_h: 0 # 图像HSV-Hue增强 (小数)
hsv_s: 0 # 图像HSV-Saturation增强 (小数)
hsv_v: 0 # 图像HSV-Value增强 (小数)
degrees: 0.0 # 图像旋转 (+/- 度)
translate: 0 # 图像平移 (+/- 小数)
scale: 0 # 图像缩放 (+/- 增益)
shear: 0 # 图像剪切 (+/- 度)
perspective: 0.0 # 图像透视变换 (+/- 小数), 范围 0-0.001
flipud: 0.0 # 图像垂直翻转 (概率)
fliplr: 0.0 # 图像水平翻转 (概率)
mosaic: 0.0 # 图像马赛克 (概率)
mixup: 0.0 # 图像混合 (概率)
copy_paste: 0.0 # 分割复制粘贴 (概率)
