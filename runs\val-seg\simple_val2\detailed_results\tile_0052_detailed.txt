Image: tile_0052.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8691
  Bounding Box: [1670.40, 274.80, 1865.60, 518.00]
  Mask Area: 197 pixels
  Mask Ratio: 0.0070
  Mask BBox: [135, 26, 149, 44]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8652
  Bounding Box: [1140.80, 142.40, 1467.20, 414.00]
  Mask Area: 379 pixels
  Mask Ratio: 0.0134
  Mask BBox: [94, 17, 118, 36]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8384
  Bounding Box: [100.30, 0.00, 276.00, 183.20]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [12, 4, 24, 18]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8369
  Bounding Box: [646.40, 1740.80, 899.20, 2032.00]
  Mask Area: 329 pixels
  Mask Ratio: 0.0117
  Mask BBox: [55, 140, 74, 162]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8345
  Bounding Box: [1386.40, 621.20, 1657.60, 972.80]
  Mask Area: 493 pixels
  Mask Ratio: 0.0175
  Mask BBox: [113, 53, 133, 79]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8330
  Bounding Box: [7.60, 1875.20, 205.60, 2048.00]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [5, 151, 20, 164]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8149
  Bounding Box: [894.40, 856.00, 1121.60, 1115.20]
  Mask Area: 225 pixels
  Mask Ratio: 0.0080
  Mask BBox: [74, 71, 90, 89]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8052
  Bounding Box: [853.60, 260.80, 962.40, 383.20]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [71, 25, 79, 33]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8027
  Bounding Box: [400.40, 1481.60, 593.20, 1731.20]
  Mask Area: 201 pixels
  Mask Ratio: 0.0071
  Mask BBox: [36, 120, 50, 139]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8027
  Bounding Box: [845.60, 615.60, 1034.40, 955.20]
  Mask Area: 297 pixels
  Mask Ratio: 0.0105
  Mask BBox: [71, 53, 84, 78]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8018
  Bounding Box: [1477.60, 1256.80, 1700.80, 1644.80]
  Mask Area: 352 pixels
  Mask Ratio: 0.0125
  Mask BBox: [120, 103, 136, 132]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8003
  Bounding Box: [542.80, 365.60, 730.80, 682.40]
  Mask Area: 247 pixels
  Mask Ratio: 0.0088
  Mask BBox: [47, 33, 61, 57]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.7959
  Bounding Box: [1090.40, 1755.20, 1255.20, 1979.20]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [90, 142, 101, 158]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.7954
  Bounding Box: [1867.20, 338.80, 2033.60, 620.40]
  Mask Area: 213 pixels
  Mask Ratio: 0.0075
  Mask BBox: [150, 31, 162, 52]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7920
  Bounding Box: [878.40, 122.80, 1046.40, 264.80]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [73, 14, 84, 24]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7910
  Bounding Box: [565.60, 53.80, 737.60, 174.60]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [49, 9, 60, 17]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7910
  Bounding Box: [113.40, 200.00, 311.80, 529.60]
  Mask Area: 294 pixels
  Mask Ratio: 0.0104
  Mask BBox: [13, 21, 28, 45]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7842
  Bounding Box: [1264.00, 1584.00, 1420.80, 1756.80]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [103, 128, 114, 141]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7822
  Bounding Box: [872.00, 1867.20, 1044.80, 2048.00]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [73, 150, 85, 164]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7798
  Bounding Box: [446.00, 1624.00, 691.60, 1899.20]
  Mask Area: 286 pixels
  Mask Ratio: 0.0101
  Mask BBox: [39, 131, 58, 152]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7720
  Bounding Box: [1115.20, 1448.00, 1256.00, 1755.20]
  Mask Area: 186 pixels
  Mask Ratio: 0.0066
  Mask BBox: [92, 118, 101, 141]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7710
  Bounding Box: [0.00, 1476.80, 156.60, 1825.60]
  Mask Area: 255 pixels
  Mask Ratio: 0.0090
  Mask BBox: [4, 120, 16, 146]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7686
  Bounding Box: [1593.60, 1012.00, 1801.60, 1234.40]
  Mask Area: 190 pixels
  Mask Ratio: 0.0067
  Mask BBox: [129, 84, 144, 100]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7686
  Bounding Box: [88.40, 658.40, 364.00, 920.80]
  Mask Area: 349 pixels
  Mask Ratio: 0.0124
  Mask BBox: [11, 56, 32, 75]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7666
  Bounding Box: [1227.20, 1025.60, 1411.20, 1150.40]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [100, 85, 114, 93]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7661
  Bounding Box: [1238.40, 17.80, 1401.60, 200.60]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [101, 6, 113, 19]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7661
  Bounding Box: [575.60, 686.00, 730.00, 892.00]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [49, 58, 61, 72]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7661
  Bounding Box: [1737.60, 1170.40, 2044.80, 1514.40]
  Mask Area: 477 pixels
  Mask Ratio: 0.0169
  Mask BBox: [140, 96, 163, 122]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7646
  Bounding Box: [1428.00, 1090.40, 1511.20, 1220.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [116, 90, 122, 99]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7632
  Bounding Box: [350.00, 1099.20, 533.20, 1347.20]
  Mask Area: 173 pixels
  Mask Ratio: 0.0061
  Mask BBox: [32, 90, 45, 109]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7568
  Bounding Box: [282.40, 1510.40, 398.40, 1696.00]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [27, 122, 34, 136]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7495
  Bounding Box: [651.20, 1173.60, 830.40, 1348.00]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [55, 96, 68, 109]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7480
  Bounding Box: [1181.60, 536.00, 1383.20, 815.20]
  Mask Area: 291 pixels
  Mask Ratio: 0.0103
  Mask BBox: [97, 46, 112, 67]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7471
  Bounding Box: [574.80, 1905.60, 709.20, 2048.00]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [49, 153, 58, 164]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7437
  Bounding Box: [298.80, 1713.60, 504.40, 2017.60]
  Mask Area: 278 pixels
  Mask Ratio: 0.0098
  Mask BBox: [28, 138, 43, 159]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7432
  Bounding Box: [396.80, 568.00, 593.60, 1025.60]
  Mask Area: 377 pixels
  Mask Ratio: 0.0134
  Mask BBox: [35, 49, 50, 84]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7422
  Bounding Box: [1287.20, 403.20, 1471.20, 620.80]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [105, 36, 118, 52]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7373
  Bounding Box: [304.20, 980.80, 464.00, 1094.40]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [28, 81, 40, 88]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7373
  Bounding Box: [1643.20, 1889.60, 1784.00, 2046.40]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [133, 152, 143, 163]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7349
  Bounding Box: [1654.40, 78.20, 1891.20, 278.20]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [134, 11, 151, 25]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7329
  Bounding Box: [1245.60, 1753.60, 1380.00, 1891.20]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [102, 141, 111, 151]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7324
  Bounding Box: [508.00, 1078.40, 687.20, 1216.00]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [44, 89, 56, 98]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7280
  Bounding Box: [164.00, 1801.60, 356.80, 2003.20]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [17, 145, 31, 160]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7192
  Bounding Box: [1333.60, 1139.20, 1447.20, 1248.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [109, 93, 117, 100]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7173
  Bounding Box: [1111.20, 1001.60, 1253.60, 1139.20]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [91, 83, 101, 92]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7070
  Bounding Box: [947.20, 1098.40, 1059.20, 1181.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [78, 90, 85, 96]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7056
  Bounding Box: [1002.40, 1902.40, 1116.00, 2040.00]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [83, 153, 91, 163]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7051
  Bounding Box: [1838.40, 574.00, 1950.40, 868.80]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [148, 49, 156, 71]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7036
  Bounding Box: [717.20, 849.60, 918.40, 1208.00]
  Mask Area: 300 pixels
  Mask Ratio: 0.0106
  Mask BBox: [61, 71, 75, 98]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.6958
  Bounding Box: [1934.40, 155.00, 2046.40, 341.00]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [156, 17, 163, 30]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.6929
  Bounding Box: [1740.80, 476.40, 1907.20, 714.00]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [140, 42, 152, 59]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.6914
  Bounding Box: [1000.80, 1686.40, 1077.60, 1843.20]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [83, 137, 88, 147]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.6904
  Bounding Box: [581.20, 889.60, 759.60, 1123.20]
  Mask Area: 159 pixels
  Mask Ratio: 0.0056
  Mask BBox: [50, 74, 63, 91]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.6855
  Bounding Box: [243.60, 543.60, 396.80, 742.80]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [24, 47, 34, 59]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.6836
  Bounding Box: [1350.40, 1830.40, 1540.80, 2048.00]
  Mask Area: 176 pixels
  Mask Ratio: 0.0062
  Mask BBox: [110, 147, 124, 164]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.6831
  Bounding Box: [1172.80, 402.00, 1286.40, 504.40]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [96, 36, 104, 43]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6807
  Bounding Box: [1264.00, 1221.60, 1523.20, 1554.40]
  Mask Area: 302 pixels
  Mask Ratio: 0.0107
  Mask BBox: [103, 100, 122, 125]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6787
  Bounding Box: [1192.00, 1282.40, 1305.60, 1431.20]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [98, 105, 105, 115]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6763
  Bounding Box: [729.20, 684.00, 864.80, 882.40]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [61, 58, 71, 72]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6680
  Bounding Box: [897.60, 2.45, 985.60, 83.20]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [75, 5, 80, 10]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6675
  Bounding Box: [1171.20, 426.40, 1289.60, 529.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [96, 38, 104, 44]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6665
  Bounding Box: [453.20, 303.00, 574.00, 430.40]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [40, 28, 48, 37]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6660
  Bounding Box: [377.60, 1358.40, 540.80, 1476.80]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [34, 111, 44, 119]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6660
  Bounding Box: [1483.20, 951.20, 1601.60, 1106.40]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [120, 79, 128, 90]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6646
  Bounding Box: [1968.00, 647.20, 2038.40, 858.40]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [158, 55, 163, 71]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6646
  Bounding Box: [1894.40, 1470.40, 2035.20, 1641.60]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [152, 119, 162, 132]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6641
  Bounding Box: [958.40, 266.60, 1192.00, 482.40]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [79, 25, 97, 41]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6636
  Bounding Box: [528.40, 177.00, 754.00, 381.20]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [46, 18, 62, 33]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6636
  Bounding Box: [1152.00, 1139.20, 1283.20, 1286.40]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [94, 93, 103, 104]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6602
  Bounding Box: [1912.00, 1004.00, 2027.20, 1196.00]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [154, 83, 162, 96]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6489
  Bounding Box: [732.40, 2.20, 894.40, 198.20]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [62, 5, 73, 19]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6484
  Bounding Box: [1478.40, 1.90, 1656.00, 184.40]
  Mask Area: 138 pixels
  Mask Ratio: 0.0049
  Mask BBox: [120, 5, 132, 18]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6479
  Bounding Box: [1526.40, 180.20, 1660.80, 301.80]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [124, 19, 133, 26]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6431
  Bounding Box: [283.60, 1051.20, 361.20, 1156.80]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [27, 87, 31, 94]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6421
  Bounding Box: [1804.80, 1688.00, 2032.00, 1828.80]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [145, 136, 161, 146]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6416
  Bounding Box: [1510.40, 1075.20, 1680.00, 1276.80]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [122, 88, 135, 103]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6372
  Bounding Box: [0.75, 548.40, 144.80, 710.80]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [5, 47, 15, 59]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6353
  Bounding Box: [1734.40, 1504.80, 1952.00, 1726.40]
  Mask Area: 192 pixels
  Mask Ratio: 0.0068
  Mask BBox: [140, 122, 156, 138]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6328
  Bounding Box: [1468.00, 310.60, 1766.40, 768.00]
  Mask Area: 579 pixels
  Mask Ratio: 0.0205
  Mask BBox: [119, 29, 141, 63]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6284
  Bounding Box: [1897.60, 1814.40, 2038.40, 2019.20]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [153, 146, 163, 161]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6245
  Bounding Box: [1672.00, 920.80, 1803.20, 1087.20]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [135, 76, 144, 87]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6167
  Bounding Box: [303.60, 427.20, 422.80, 527.20]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [28, 38, 37, 45]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6089
  Bounding Box: [334.00, 355.60, 548.40, 494.00]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [31, 32, 46, 42]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6089
  Bounding Box: [983.20, 1616.00, 1050.40, 1689.60]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [81, 131, 86, 135]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6074
  Bounding Box: [604.40, 1390.40, 783.20, 1622.40]
  Mask Area: 172 pixels
  Mask Ratio: 0.0061
  Mask BBox: [52, 113, 65, 130]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6060
  Bounding Box: [257.60, 6.90, 354.00, 80.80]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [25, 5, 30, 10]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.6055
  Bounding Box: [1053.60, 1440.00, 1148.00, 1542.40]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [87, 117, 93, 124]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.6025
  Bounding Box: [1091.20, 767.60, 1204.80, 900.80]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [90, 64, 97, 74]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.5991
  Bounding Box: [20.90, 155.20, 133.50, 318.40]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [6, 17, 14, 27]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.5962
  Bounding Box: [963.20, 1604.80, 1062.40, 1694.40]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [80, 130, 86, 135]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.5879
  Bounding Box: [0.00, 704.40, 111.80, 806.40]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [4, 60, 12, 66]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.5879
  Bounding Box: [364.80, 816.80, 445.60, 967.20]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [33, 68, 38, 78]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5854
  Bounding Box: [1202.40, 1672.00, 1271.20, 1774.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [98, 135, 103, 142]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5850
  Bounding Box: [1678.40, 1494.40, 1793.60, 1627.20]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [136, 121, 142, 131]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5776
  Bounding Box: [1021.60, 479.20, 1196.00, 687.20]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [84, 42, 96, 57]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5713
  Bounding Box: [1092.00, 17.65, 1247.20, 161.60]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [90, 6, 101, 16]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5708
  Bounding Box: [1391.20, 997.60, 1511.20, 1095.20]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [113, 82, 122, 89]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5708
  Bounding Box: [1440.00, 542.80, 1550.40, 622.80]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [117, 47, 124, 52]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5693
  Bounding Box: [461.60, 469.20, 579.20, 670.80]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [41, 41, 48, 54]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5669
  Bounding Box: [684.00, 1590.40, 819.20, 1721.60]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [58, 129, 67, 138]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5640
  Bounding Box: [321.00, 147.60, 532.00, 313.20]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [30, 16, 45, 28]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5620
  Bounding Box: [557.60, 0.00, 718.40, 85.60]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [48, 4, 60, 10]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5562
  Bounding Box: [352.60, 4.80, 528.00, 113.60]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [32, 5, 45, 12]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5542
  Bounding Box: [887.20, 1673.60, 1024.80, 1868.80]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [74, 135, 84, 149]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5527
  Bounding Box: [1368.80, 113.10, 1540.00, 267.60]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [111, 13, 124, 24]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5518
  Bounding Box: [1668.80, 1241.60, 1784.00, 1384.00]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [135, 101, 143, 112]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5518
  Bounding Box: [1881.60, 1820.80, 2003.20, 1971.20]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [151, 147, 160, 157]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5425
  Bounding Box: [1158.40, 810.40, 1406.40, 1026.40]
  Mask Area: 247 pixels
  Mask Ratio: 0.0088
  Mask BBox: [95, 68, 112, 84]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5396
  Bounding Box: [982.40, 0.00, 1108.80, 108.70]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [81, 4, 90, 12]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5332
  Bounding Box: [1684.80, 1798.40, 1892.80, 2000.00]
  Mask Area: 182 pixels
  Mask Ratio: 0.0064
  Mask BBox: [136, 145, 151, 160]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5298
  Bounding Box: [820.80, 1966.40, 926.40, 2033.60]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [69, 158, 75, 162]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5278
  Bounding Box: [540.80, 654.40, 605.60, 729.60]
  Mask Area: 21 pixels
  Mask Ratio: 0.0007
  Mask BBox: [47, 56, 51, 60]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5264
  Bounding Box: [0.00, 0.00, 116.80, 89.60]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [3, 4, 11, 10]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5220
  Bounding Box: [692.40, 361.60, 936.00, 586.40]
  Mask Area: 244 pixels
  Mask Ratio: 0.0086
  Mask BBox: [59, 33, 77, 49]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5195
  Bounding Box: [754.40, 1308.80, 1058.40, 1712.00]
  Mask Area: 551 pixels
  Mask Ratio: 0.0195
  Mask BBox: [63, 107, 86, 137]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5186
  Bounding Box: [71.60, 890.40, 304.80, 1327.20]
  Mask Area: 455 pixels
  Mask Ratio: 0.0161
  Mask BBox: [10, 74, 27, 107]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5166
  Bounding Box: [41.75, 422.80, 179.00, 582.00]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [8, 38, 16, 49]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5088
  Bounding Box: [963.20, 1688.00, 1088.00, 1857.60]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [80, 136, 88, 149]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5024
  Bounding Box: [559.60, 19.05, 741.20, 128.60]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [48, 6, 61, 14]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.5010
  Bounding Box: [1076.00, 681.60, 1194.40, 801.60]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [89, 58, 96, 66]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.4990
  Bounding Box: [1188.00, 1670.40, 1295.20, 1766.40]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [97, 135, 105, 141]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.4958
  Bounding Box: [1568.00, 1649.60, 1728.00, 1832.00]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [127, 133, 138, 147]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.4956
  Bounding Box: [288.80, 888.80, 373.20, 968.80]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [27, 74, 33, 79]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.4927
  Bounding Box: [387.20, 492.80, 494.40, 586.40]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [35, 43, 42, 48]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.4795
  Bounding Box: [696.00, 430.40, 990.40, 630.40]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [59, 38, 81, 53]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.4775
  Bounding Box: [208.00, 1476.80, 323.20, 1600.00]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [21, 120, 29, 128]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.4746
  Bounding Box: [463.20, 972.00, 567.20, 1106.40]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [41, 80, 48, 90]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4731
  Bounding Box: [644.80, 1505.60, 756.80, 1622.40]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [55, 122, 63, 130]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.4719
  Bounding Box: [1.60, 580.00, 136.40, 796.00]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [5, 50, 14, 66]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4692
  Bounding Box: [292.00, 76.00, 495.20, 192.40]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [29, 10, 42, 19]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4673
  Bounding Box: [523.20, 647.20, 619.20, 724.00]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [46, 55, 51, 60]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4658
  Bounding Box: [0.00, 1283.20, 96.60, 1435.20]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [4, 105, 11, 116]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4648
  Bounding Box: [932.80, 1115.20, 1043.20, 1204.80]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [77, 92, 85, 98]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4604
  Bounding Box: [1099.20, 1960.00, 1275.20, 2040.00]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [90, 158, 103, 163]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4604
  Bounding Box: [90.00, 1399.20, 189.60, 1525.60]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [12, 114, 18, 123]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4600
  Bounding Box: [959.20, 1113.60, 1068.00, 1209.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [79, 91, 85, 96]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4590
  Bounding Box: [1441.60, 1224.80, 1545.60, 1368.80]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [118, 100, 124, 109]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4561
  Bounding Box: [0.85, 249.80, 66.20, 407.60]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 24, 8, 35]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4551
  Bounding Box: [1707.20, 1339.20, 1812.80, 1459.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [138, 109, 144, 117]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4509
  Bounding Box: [1870.40, 1944.00, 1988.80, 2048.00]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [151, 156, 159, 165]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4446
  Bounding Box: [1.90, 794.40, 98.50, 928.80]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [5, 67, 11, 76]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4441
  Bounding Box: [1526.40, 1037.60, 1769.60, 1252.00]
  Mask Area: 224 pixels
  Mask Ratio: 0.0079
  Mask BBox: [124, 86, 142, 101]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4404
  Bounding Box: [1902.40, 113.00, 2040.00, 333.80]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [153, 13, 163, 30]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4402
  Bounding Box: [533.60, 1.65, 700.80, 113.00]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [46, 5, 58, 12]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4382
  Bounding Box: [1088.00, 701.60, 1206.40, 890.40]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [89, 59, 97, 73]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4353
  Bounding Box: [23.45, 134.40, 173.80, 383.60]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [6, 15, 17, 33]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4351
  Bounding Box: [1952.00, 782.40, 2038.40, 931.20]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [157, 66, 163, 76]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4316
  Bounding Box: [317.40, 408.00, 476.80, 524.00]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [29, 36, 41, 44]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4314
  Bounding Box: [987.20, 1155.20, 1233.60, 1465.60]
  Mask Area: 324 pixels
  Mask Ratio: 0.0115
  Mask BBox: [82, 95, 100, 118]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4302
  Bounding Box: [276.40, 1071.20, 340.40, 1188.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [26, 88, 30, 96]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4302
  Bounding Box: [1843.20, 1971.20, 1974.40, 2041.60]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [148, 158, 157, 163]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4302
  Bounding Box: [301.40, 1500.80, 584.00, 1705.60]
  Mask Area: 279 pixels
  Mask Ratio: 0.0099
  Mask BBox: [28, 122, 49, 137]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4292
  Bounding Box: [971.20, 609.60, 1092.80, 727.20]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [80, 52, 89, 60]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4282
  Bounding Box: [644.40, 1343.20, 754.80, 1410.40]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [55, 109, 62, 113]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4282
  Bounding Box: [482.40, 1228.80, 665.60, 1467.20]
  Mask Area: 199 pixels
  Mask Ratio: 0.0071
  Mask BBox: [42, 100, 55, 118]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4282
  Bounding Box: [581.60, 1854.40, 772.00, 2048.00]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [50, 149, 64, 164]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4253
  Bounding Box: [248.80, 523.60, 387.60, 670.00]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [24, 45, 34, 56]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4238
  Bounding Box: [165.80, 1980.80, 289.80, 2044.80]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [17, 159, 26, 163]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4221
  Bounding Box: [575.20, 696.00, 826.40, 892.80]
  Mask Area: 218 pixels
  Mask Ratio: 0.0077
  Mask BBox: [49, 59, 68, 73]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4207
  Bounding Box: [726.00, 226.20, 859.20, 352.60]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [61, 22, 71, 31]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4182
  Bounding Box: [1852.80, 278.60, 1958.40, 340.60]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [149, 26, 155, 30]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.4163
  Bounding Box: [1931.20, 847.20, 2024.00, 952.80]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [155, 71, 162, 78]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.4121
  Bounding Box: [278.00, 1182.40, 408.40, 1339.20]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [26, 97, 33, 108]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.4116
  Bounding Box: [268.20, 1396.80, 392.80, 1488.00]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [25, 114, 34, 120]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.4102
  Bounding Box: [213.60, 1726.40, 300.80, 1828.80]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [21, 139, 27, 146]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.4021
  Bounding Box: [1534.40, 290.40, 1678.40, 406.40]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [124, 27, 135, 34]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.3989
  Bounding Box: [160.00, 572.80, 263.60, 660.00]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [17, 49, 24, 55]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.3984
  Bounding Box: [675.60, 547.60, 833.60, 690.00]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [57, 47, 69, 57]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.3970
  Bounding Box: [85.10, 1817.60, 350.00, 2025.60]
  Mask Area: 228 pixels
  Mask Ratio: 0.0081
  Mask BBox: [11, 146, 31, 162]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.3960
  Bounding Box: [500.80, 24.80, 582.40, 120.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [44, 6, 49, 13]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.3960
  Bounding Box: [1139.20, 24.40, 1379.20, 186.20]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [93, 6, 111, 18]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3948
  Bounding Box: [1249.60, 1587.20, 1422.40, 1875.20]
  Mask Area: 238 pixels
  Mask Ratio: 0.0084
  Mask BBox: [102, 128, 115, 150]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3943
  Bounding Box: [1247.20, 979.20, 1418.40, 1171.20]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [102, 81, 114, 95]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3918
  Bounding Box: [832.80, 1955.20, 900.00, 2048.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [70, 157, 74, 163]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3918
  Bounding Box: [5.55, 1132.80, 155.60, 1348.80]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [5, 95, 16, 109]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3904
  Bounding Box: [784.80, 1186.40, 984.80, 1322.40]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [66, 97, 79, 107]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3901
  Bounding Box: [0.00, 696.40, 118.40, 894.40]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [4, 59, 12, 73]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3892
  Bounding Box: [281.40, 1151.20, 344.20, 1231.20]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [26, 94, 30, 99]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3877
  Bounding Box: [1649.60, 1840.00, 1851.20, 2028.80]
  Mask Area: 181 pixels
  Mask Ratio: 0.0064
  Mask BBox: [133, 148, 148, 162]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3853
  Bounding Box: [789.60, 1656.00, 895.20, 1726.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [66, 134, 73, 138]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3833
  Bounding Box: [1803.20, 1478.40, 2046.40, 1696.00]
  Mask Area: 250 pixels
  Mask Ratio: 0.0089
  Mask BBox: [145, 120, 163, 136]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3831
  Bounding Box: [996.00, 487.20, 1138.40, 663.20]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [82, 43, 92, 55]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3809
  Bounding Box: [0.00, 2.50, 101.00, 116.50]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [3, 5, 11, 13]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3794
  Bounding Box: [1433.60, 1680.00, 1648.00, 2048.00]
  Mask Area: 412 pixels
  Mask Ratio: 0.0146
  Mask BBox: [116, 136, 132, 163]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3789
  Bounding Box: [540.80, 1918.40, 688.00, 2048.00]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [47, 154, 57, 164]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3777
  Bounding Box: [292.80, 435.20, 421.60, 599.20]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [27, 38, 36, 50]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3774
  Bounding Box: [807.20, 877.60, 1104.80, 1136.80]
  Mask Area: 344 pixels
  Mask Ratio: 0.0122
  Mask BBox: [68, 73, 90, 92]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3774
  Bounding Box: [173.60, 1760.00, 457.20, 2003.20]
  Mask Area: 297 pixels
  Mask Ratio: 0.0105
  Mask BBox: [18, 142, 39, 160]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3767
  Bounding Box: [1381.60, 4.80, 1479.20, 74.50]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [112, 5, 119, 9]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3762
  Bounding Box: [502.40, 1956.80, 601.60, 2040.00]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [44, 157, 50, 162]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3757
  Bounding Box: [1616.00, 14.15, 1718.40, 111.00]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [131, 6, 138, 11]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3743
  Bounding Box: [1087.20, 1974.40, 1234.40, 2048.00]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [89, 159, 100, 163]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3738
  Bounding Box: [14.00, 1814.40, 108.00, 1888.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [6, 146, 12, 151]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3713
  Bounding Box: [1652.80, 870.40, 1819.20, 1100.80]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [134, 72, 146, 89]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3699
  Bounding Box: [1961.60, 827.20, 2041.60, 937.60]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [158, 69, 163, 77]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3699
  Bounding Box: [1772.80, 924.00, 1910.40, 1125.60]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [143, 77, 153, 91]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3689
  Bounding Box: [272.80, 1526.40, 372.80, 1724.80]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [26, 124, 33, 138]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3689
  Bounding Box: [1852.80, 812.00, 1942.40, 916.00]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [149, 68, 155, 75]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3689
  Bounding Box: [1867.20, 837.60, 1947.20, 922.40]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [150, 70, 156, 76]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3689
  Bounding Box: [269.60, 1152.00, 362.40, 1246.40]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [26, 94, 32, 101]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3682
  Bounding Box: [823.20, 274.80, 991.20, 432.40]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [69, 26, 81, 37]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3679
  Bounding Box: [674.80, 476.00, 929.60, 664.80]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [57, 42, 76, 55]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3674
  Bounding Box: [1366.40, 1814.40, 1625.60, 2048.00]
  Mask Area: 282 pixels
  Mask Ratio: 0.0100
  Mask BBox: [111, 146, 130, 164]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3652
  Bounding Box: [515.20, 5.10, 647.20, 107.10]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [45, 5, 54, 12]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3638
  Bounding Box: [1402.40, 1077.60, 1503.20, 1207.20]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [114, 89, 121, 98]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3625
  Bounding Box: [1017.60, 1668.80, 1096.00, 1838.40]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [84, 135, 89, 147]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3625
  Bounding Box: [302.40, 0.95, 506.40, 134.60]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [28, 5, 43, 14]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3599
  Bounding Box: [266.00, 1176.80, 426.80, 1405.60]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [25, 96, 37, 113]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3594
  Bounding Box: [1875.20, 265.40, 1945.60, 346.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [151, 25, 155, 31]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3579
  Bounding Box: [1696.00, 743.60, 1824.00, 864.00]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [137, 63, 146, 71]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3550
  Bounding Box: [1968.00, 1013.60, 2038.40, 1196.00]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [158, 84, 163, 97]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3535
  Bounding Box: [832.00, 278.80, 945.60, 403.20]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [69, 26, 77, 35]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3523
  Bounding Box: [1920.00, 874.40, 2019.20, 986.40]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [154, 73, 161, 81]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3484
  Bounding Box: [1515.20, 1323.20, 1752.00, 1625.60]
  Mask Area: 299 pixels
  Mask Ratio: 0.0106
  Mask BBox: [123, 108, 140, 130]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3452
  Bounding Box: [1529.60, 193.20, 1664.00, 382.80]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [124, 20, 133, 33]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3430
  Bounding Box: [860.00, 287.40, 973.60, 404.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [72, 27, 79, 33]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3423
  Bounding Box: [960.00, 0.00, 1091.20, 138.00]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [79, 3, 89, 14]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3369
  Bounding Box: [1729.60, 2.30, 2001.60, 236.40]
  Mask Area: 331 pixels
  Mask Ratio: 0.0117
  Mask BBox: [140, 5, 160, 22]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3367
  Bounding Box: [651.60, 1533.60, 743.60, 1644.80]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [55, 124, 62, 132]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3359
  Bounding Box: [1766.40, 1972.80, 1872.00, 2033.60]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [142, 159, 150, 162]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3328
  Bounding Box: [626.40, 664.40, 752.80, 828.80]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [53, 56, 62, 68]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3315
  Bounding Box: [852.80, 103.60, 1030.40, 240.40]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [71, 13, 84, 22]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3254
  Bounding Box: [1028.00, 1151.20, 1277.60, 1373.60]
  Mask Area: 290 pixels
  Mask Ratio: 0.0103
  Mask BBox: [85, 94, 103, 111]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3235
  Bounding Box: [581.60, 1412.80, 712.80, 1619.20]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [50, 115, 59, 128]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [1987.20, 0.00, 2041.60, 83.40]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [160, 4, 163, 10]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [1201.60, 447.20, 1419.20, 788.80]
  Mask Area: 379 pixels
  Mask Ratio: 0.0134
  Mask BBox: [98, 39, 114, 65]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3218
  Bounding Box: [408.40, 474.40, 555.60, 630.40]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [36, 42, 47, 53]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3218
  Bounding Box: [955.20, 567.20, 1105.60, 744.00]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [79, 49, 90, 62]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3213
  Bounding Box: [1223.20, 1232.00, 1471.20, 1500.80]
  Mask Area: 317 pixels
  Mask Ratio: 0.0112
  Mask BBox: [100, 101, 118, 121]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3208
  Bounding Box: [28.30, 83.60, 106.10, 161.60]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [7, 11, 11, 16]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3201
  Bounding Box: [369.60, 300.80, 584.80, 476.80]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [33, 28, 48, 41]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3201
  Bounding Box: [0.00, 1173.60, 157.80, 1423.20]
  Mask Area: 232 pixels
  Mask Ratio: 0.0082
  Mask BBox: [4, 96, 16, 115]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3188
  Bounding Box: [1814.40, 0.00, 2041.60, 198.80]
  Mask Area: 242 pixels
  Mask Ratio: 0.0086
  Mask BBox: [146, 4, 163, 19]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3179
  Bounding Box: [552.80, 76.30, 721.60, 203.20]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [50, 10, 60, 17]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3171
  Bounding Box: [976.00, 496.80, 1164.80, 729.60]
  Mask Area: 201 pixels
  Mask Ratio: 0.0071
  Mask BBox: [81, 43, 94, 60]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3154
  Bounding Box: [472.00, 1048.00, 662.40, 1209.60]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [41, 86, 55, 98]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3145
  Bounding Box: [315.60, 100.60, 556.40, 281.00]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [29, 12, 47, 25]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3135
  Bounding Box: [132.10, 259.00, 365.20, 545.60]
  Mask Area: 329 pixels
  Mask Ratio: 0.0117
  Mask BBox: [15, 25, 32, 46]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.3113
  Bounding Box: [328.80, 161.60, 544.80, 390.40]
  Mask Area: 251 pixels
  Mask Ratio: 0.0089
  Mask BBox: [30, 17, 46, 34]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [597.60, 1200.80, 668.00, 1296.80]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [51, 98, 56, 105]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.3083
  Bounding Box: [1976.00, 628.40, 2043.20, 804.80]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [159, 54, 163, 66]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.3081
  Bounding Box: [223.60, 1183.20, 443.20, 1471.20]
  Mask Area: 284 pixels
  Mask Ratio: 0.0101
  Mask BBox: [22, 97, 38, 118]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.3079
  Bounding Box: [1761.60, 920.00, 1876.80, 1091.20]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [142, 76, 150, 89]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [1808.00, 1976.00, 1945.60, 2040.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [146, 159, 155, 163]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.3062
  Bounding Box: [886.40, 0.00, 987.20, 63.50]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [74, 3, 81, 8]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.3062
  Bounding Box: [912.00, 0.00, 1012.80, 63.50]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [76, 3, 83, 8]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.3062
  Bounding Box: [912.00, 1.30, 1012.80, 89.10]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [76, 5, 83, 10]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.3054
  Bounding Box: [1619.20, 1195.20, 1689.60, 1273.60]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [131, 98, 135, 103]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.3054
  Bounding Box: [1284.80, 1144.00, 1460.80, 1262.40]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [105, 94, 118, 100]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.3052
  Bounding Box: [1660.80, 738.40, 1820.80, 898.40]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [134, 62, 146, 74]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.3044
  Bounding Box: [478.40, 964.80, 580.00, 1080.00]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [42, 80, 49, 88]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.3037
  Bounding Box: [237.80, 1478.40, 391.20, 1667.20]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [23, 120, 34, 134]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.3032
  Bounding Box: [740.80, 170.20, 868.80, 282.60]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [62, 18, 71, 26]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.3020
  Bounding Box: [562.80, 641.20, 634.00, 714.80]
  Mask Area: 16 pixels
  Mask Ratio: 0.0006
  Mask BBox: [48, 55, 51, 59]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.3020
  Bounding Box: [562.80, 666.80, 634.00, 740.40]
  Mask Area: 14 pixels
  Mask Ratio: 0.0005
  Mask BBox: [48, 57, 51, 61]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.3020
  Bounding Box: [578.00, 0.00, 758.00, 77.80]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [50, 4, 63, 10]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.3013
  Bounding Box: [1977.60, 4.85, 2044.80, 128.40]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [159, 5, 163, 14]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.3013
  Bounding Box: [1391.20, 1573.60, 1620.80, 1892.80]
  Mask Area: 410 pixels
  Mask Ratio: 0.0145
  Mask BBox: [113, 127, 130, 151]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.3010
  Bounding Box: [1952.00, 675.20, 2041.60, 913.60]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [157, 57, 163, 75]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.2991
  Bounding Box: [596.40, 20.15, 868.80, 190.00]
  Mask Area: 215 pixels
  Mask Ratio: 0.0076
  Mask BBox: [51, 6, 71, 18]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.2976
  Bounding Box: [1034.40, 1803.20, 1228.00, 2043.20]
  Mask Area: 227 pixels
  Mask Ratio: 0.0080
  Mask BBox: [85, 145, 99, 163]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.2957
  Bounding Box: [192.60, 1702.40, 296.20, 1820.80]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [20, 137, 27, 146]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2942
  Bounding Box: [1388.00, 95.50, 1552.80, 234.80]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [113, 12, 125, 22]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2937
  Bounding Box: [1224.80, 1646.40, 1301.60, 1761.60]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [100, 133, 103, 141]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2935
  Bounding Box: [1359.20, 1120.00, 1476.00, 1246.40]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [111, 92, 119, 101]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2930
  Bounding Box: [1622.40, 1049.60, 1804.80, 1320.00]
  Mask Area: 214 pixels
  Mask Ratio: 0.0076
  Mask BBox: [131, 86, 144, 107]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2920
  Bounding Box: [1082.40, 766.40, 1288.80, 956.80]
  Mask Area: 197 pixels
  Mask Ratio: 0.0070
  Mask BBox: [89, 64, 104, 78]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2917
  Bounding Box: [1344.00, 1756.80, 1539.20, 2019.20]
  Mask Area: 264 pixels
  Mask Ratio: 0.0094
  Mask BBox: [109, 142, 124, 161]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2905
  Bounding Box: [1125.60, 1119.20, 1264.80, 1272.80]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [93, 92, 102, 103]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2903
  Bounding Box: [1689.60, 1318.40, 1808.00, 1497.60]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [136, 107, 144, 120]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2896
  Bounding Box: [1058.40, 1678.40, 1135.20, 1838.40]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [87, 136, 92, 147]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2883
  Bounding Box: [1859.20, 953.60, 2022.40, 1193.60]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [150, 79, 161, 97]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2859
  Bounding Box: [787.20, 1216.00, 934.40, 1316.80]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [66, 99, 76, 106]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2859
  Bounding Box: [1370.40, 1461.60, 1517.60, 1560.80]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [112, 119, 122, 125]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2856
  Bounding Box: [1328.00, 1446.40, 1492.80, 1563.20]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [108, 117, 120, 126]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2847
  Bounding Box: [1376.80, 1876.80, 1567.20, 2040.00]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [112, 151, 126, 163]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2847
  Bounding Box: [1884.80, 7.85, 2048.00, 159.40]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [152, 5, 165, 16]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2844
  Bounding Box: [30.90, 320.40, 212.00, 574.00]
  Mask Area: 182 pixels
  Mask Ratio: 0.0064
  Mask BBox: [7, 30, 20, 48]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2839
  Bounding Box: [737.20, 66.90, 879.20, 304.80]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [62, 10, 72, 27]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2830
  Bounding Box: [1360.80, 1148.80, 1463.20, 1267.20]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [111, 94, 118, 100]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2830
  Bounding Box: [671.20, 1600.00, 792.80, 1750.40]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [57, 129, 65, 138]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2830
  Bounding Box: [922.40, 590.40, 1096.80, 783.20]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [77, 51, 89, 65]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2820
  Bounding Box: [1410.40, 1511.20, 1532.00, 1575.20]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [115, 123, 123, 127]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2815
  Bounding Box: [373.20, 476.00, 473.20, 577.60]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [34, 42, 40, 49]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2815
  Bounding Box: [373.20, 501.60, 473.20, 603.20]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [34, 44, 40, 49]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2805
  Bounding Box: [832.80, 1928.00, 968.80, 2048.00]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [70, 155, 79, 164]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2803
  Bounding Box: [1654.40, 1214.40, 1753.60, 1371.20]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [134, 99, 140, 111]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2798
  Bounding Box: [1688.00, 1478.40, 1873.60, 1705.60]
  Mask Area: 217 pixels
  Mask Ratio: 0.0077
  Mask BBox: [136, 120, 150, 137]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2795
  Bounding Box: [7.35, 676.40, 118.20, 787.20]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [5, 57, 12, 65]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2795
  Bounding Box: [32.95, 702.00, 143.80, 812.80]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [7, 59, 12, 67]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2786
  Bounding Box: [1590.40, 924.80, 1673.60, 1008.00]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [129, 77, 133, 82]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2776
  Bounding Box: [1033.60, 1469.60, 1137.60, 1564.00]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [85, 119, 92, 126]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2771
  Bounding Box: [1064.00, 1466.40, 1161.60, 1559.20]
  Mask Area: 21 pixels
  Mask Ratio: 0.0007
  Mask BBox: [88, 119, 92, 123]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2769
  Bounding Box: [1700.80, 1616.00, 1774.40, 1721.60]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [137, 131, 142, 138]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2766
  Bounding Box: [1225.60, 46.15, 1372.80, 222.00]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [100, 8, 111, 18]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2759
  Bounding Box: [730.40, 351.60, 924.00, 537.20]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [62, 32, 76, 45]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2756
  Bounding Box: [948.00, 1603.20, 1029.60, 1696.00]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [79, 130, 84, 136]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2756
  Bounding Box: [948.00, 1628.80, 1029.60, 1721.60]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [79, 132, 84, 138]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2756
  Bounding Box: [973.60, 1628.80, 1055.20, 1721.60]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [81, 132, 86, 138]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2744
  Bounding Box: [300.40, 1031.20, 385.20, 1144.80]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [28, 85, 31, 93]

