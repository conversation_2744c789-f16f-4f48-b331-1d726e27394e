Image: tile_0132.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8828
  Bounding Box: [993.60, 897.60, 1312.00, 1176.00]
  Mask Area: 344 pixels
  Mask Ratio: 0.0122
  Mask BBox: [82, 75, 106, 95]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8608
  Bounding Box: [780.80, 0.00, 1113.60, 445.60]
  Mask Area: 634 pixels
  Mask Ratio: 0.0225
  Mask BBox: [65, 4, 90, 38]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8501
  Bounding Box: [1740.80, 33.40, 1910.40, 219.20]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [141, 7, 153, 21]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8491
  Bounding Box: [299.20, 1494.40, 525.60, 1756.80]
  Mask Area: 273 pixels
  Mask Ratio: 0.0097
  Mask BBox: [28, 121, 45, 141]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8457
  Bounding Box: [374.40, 31.50, 644.80, 245.20]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [34, 7, 54, 23]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8330
  Bounding Box: [78.30, 1357.60, 256.40, 1593.60]
  Mask Area: 199 pixels
  Mask Ratio: 0.0071
  Mask BBox: [11, 111, 24, 128]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8311
  Bounding Box: [1399.20, 95.80, 1606.40, 288.20]
  Mask Area: 171 pixels
  Mask Ratio: 0.0061
  Mask BBox: [114, 12, 129, 26]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8149
  Bounding Box: [1880.00, 1395.20, 2048.00, 1662.40]
  Mask Area: 220 pixels
  Mask Ratio: 0.0078
  Mask BBox: [151, 113, 164, 133]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8120
  Bounding Box: [72.60, 1595.20, 310.20, 1787.20]
  Mask Area: 218 pixels
  Mask Ratio: 0.0077
  Mask BBox: [10, 129, 28, 143]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8105
  Bounding Box: [1121.60, 371.20, 1308.80, 730.40]
  Mask Area: 284 pixels
  Mask Ratio: 0.0101
  Mask BBox: [92, 33, 106, 60]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8101
  Bounding Box: [502.40, 1596.80, 668.80, 1859.20]
  Mask Area: 177 pixels
  Mask Ratio: 0.0063
  Mask BBox: [44, 129, 56, 149]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8066
  Bounding Box: [1702.40, 1167.20, 1881.60, 1360.80]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [137, 96, 150, 110]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.8066
  Bounding Box: [1411.20, 1123.20, 1640.00, 1353.60]
  Mask Area: 207 pixels
  Mask Ratio: 0.0073
  Mask BBox: [115, 92, 131, 107]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.8022
  Bounding Box: [302.80, 627.20, 466.00, 884.80]
  Mask Area: 188 pixels
  Mask Ratio: 0.0067
  Mask BBox: [28, 53, 40, 73]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.8022
  Bounding Box: [767.20, 1002.40, 956.00, 1239.20]
  Mask Area: 217 pixels
  Mask Ratio: 0.0077
  Mask BBox: [64, 83, 78, 100]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7998
  Bounding Box: [1200.80, 1445.60, 1460.00, 1707.20]
  Mask Area: 328 pixels
  Mask Ratio: 0.0116
  Mask BBox: [98, 117, 118, 137]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7993
  Bounding Box: [132.50, 1007.20, 351.60, 1396.00]
  Mask Area: 384 pixels
  Mask Ratio: 0.0136
  Mask BBox: [15, 83, 31, 113]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7964
  Bounding Box: [748.80, 1438.40, 934.40, 1689.60]
  Mask Area: 197 pixels
  Mask Ratio: 0.0070
  Mask BBox: [63, 117, 76, 135]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7939
  Bounding Box: [1785.60, 1705.60, 2028.80, 1971.20]
  Mask Area: 235 pixels
  Mask Ratio: 0.0083
  Mask BBox: [144, 138, 162, 157]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7915
  Bounding Box: [1530.40, 504.40, 1774.40, 855.20]
  Mask Area: 406 pixels
  Mask Ratio: 0.0144
  Mask BBox: [124, 44, 142, 70]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7910
  Bounding Box: [142.40, 320.40, 316.00, 465.20]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [16, 30, 28, 40]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7900
  Bounding Box: [197.20, 1763.20, 482.40, 1964.80]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [20, 142, 41, 157]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7896
  Bounding Box: [20.50, 0.00, 314.00, 275.20]
  Mask Area: 415 pixels
  Mask Ratio: 0.0147
  Mask BBox: [6, 3, 28, 25]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7891
  Bounding Box: [1921.60, 1900.80, 2046.40, 2044.80]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [155, 153, 163, 163]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7881
  Bounding Box: [1137.60, 1182.40, 1412.80, 1315.20]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [93, 97, 114, 106]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7788
  Bounding Box: [1019.20, 1809.60, 1276.80, 2048.00]
  Mask Area: 272 pixels
  Mask Ratio: 0.0096
  Mask BBox: [84, 146, 103, 164]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7705
  Bounding Box: [737.60, 1857.60, 1019.20, 2048.00]
  Mask Area: 279 pixels
  Mask Ratio: 0.0099
  Mask BBox: [62, 150, 83, 165]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7700
  Bounding Box: [1365.60, 1843.20, 1583.20, 2038.40]
  Mask Area: 159 pixels
  Mask Ratio: 0.0056
  Mask BBox: [111, 148, 127, 163]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7651
  Bounding Box: [24.40, 1785.60, 238.20, 2048.00]
  Mask Area: 235 pixels
  Mask Ratio: 0.0083
  Mask BBox: [6, 144, 22, 164]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7646
  Bounding Box: [1235.20, 1926.40, 1360.00, 2032.00]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [101, 155, 108, 162]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7637
  Bounding Box: [577.20, 347.20, 745.20, 543.20]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [50, 32, 61, 46]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7622
  Bounding Box: [609.60, 591.60, 822.40, 830.40]
  Mask Area: 208 pixels
  Mask Ratio: 0.0074
  Mask BBox: [52, 51, 68, 68]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7573
  Bounding Box: [935.20, 433.20, 1055.20, 620.40]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [78, 38, 86, 52]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7524
  Bounding Box: [1836.80, 236.00, 2025.60, 464.00]
  Mask Area: 173 pixels
  Mask Ratio: 0.0061
  Mask BBox: [148, 23, 162, 40]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7466
  Bounding Box: [586.40, 1472.80, 716.00, 1676.80]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [50, 120, 59, 133]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7456
  Bounding Box: [489.20, 1401.60, 605.20, 1601.60]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [43, 114, 51, 129]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7451
  Bounding Box: [842.40, 1720.00, 1052.00, 1892.80]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [70, 139, 86, 151]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7427
  Bounding Box: [733.60, 519.20, 821.60, 692.80]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [62, 45, 68, 58]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7393
  Bounding Box: [0.00, 1368.00, 113.10, 1588.80]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [4, 111, 12, 128]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7378
  Bounding Box: [1319.20, 304.80, 1572.00, 549.60]
  Mask Area: 253 pixels
  Mask Ratio: 0.0090
  Mask BBox: [108, 28, 126, 46]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7329
  Bounding Box: [1002.40, 550.40, 1122.40, 723.20]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [83, 47, 91, 60]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7324
  Bounding Box: [306.60, 153.10, 488.80, 385.20]
  Mask Area: 197 pixels
  Mask Ratio: 0.0070
  Mask BBox: [28, 16, 42, 34]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7251
  Bounding Box: [1881.60, 100.00, 2048.00, 349.60]
  Mask Area: 233 pixels
  Mask Ratio: 0.0083
  Mask BBox: [151, 12, 164, 31]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7231
  Bounding Box: [1313.60, 515.20, 1507.20, 647.20]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [107, 45, 120, 54]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7231
  Bounding Box: [1646.40, 1362.40, 1800.00, 1587.20]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [133, 111, 144, 127]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7212
  Bounding Box: [668.80, 438.40, 808.00, 560.00]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [57, 39, 67, 47]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.6895
  Bounding Box: [731.60, 1704.00, 852.00, 1915.20]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [62, 138, 70, 152]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.6880
  Bounding Box: [326.00, 1306.40, 514.80, 1514.40]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [30, 107, 44, 122]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.6865
  Bounding Box: [1969.60, 1628.80, 2036.80, 1740.80]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [158, 132, 163, 138]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.6841
  Bounding Box: [1404.00, 1652.80, 1532.00, 1832.00]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [114, 134, 123, 147]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.6787
  Bounding Box: [9.30, 707.60, 175.20, 811.20]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [5, 60, 17, 67]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.6772
  Bounding Box: [1283.20, 1371.20, 1598.40, 1571.20]
  Mask Area: 224 pixels
  Mask Ratio: 0.0079
  Mask BBox: [105, 112, 128, 126]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.6772
  Bounding Box: [1771.20, 1499.20, 1892.80, 1678.40]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [143, 122, 151, 135]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.6738
  Bounding Box: [1037.60, 423.20, 1167.20, 541.60]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [86, 38, 94, 46]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.6694
  Bounding Box: [305.20, 316.80, 441.20, 483.20]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [28, 29, 38, 41]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.6650
  Bounding Box: [730.80, 1269.60, 843.20, 1412.00]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [62, 104, 68, 112]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6650
  Bounding Box: [1397.60, 1283.20, 1536.80, 1411.20]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [114, 105, 124, 114]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6621
  Bounding Box: [218.40, 881.60, 346.40, 1008.00]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [22, 74, 31, 82]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6602
  Bounding Box: [1172.80, 1551.20, 1449.60, 1884.80]
  Mask Area: 420 pixels
  Mask Ratio: 0.0149
  Mask BBox: [96, 126, 117, 151]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6484
  Bounding Box: [1585.60, 1498.40, 1710.40, 1782.40]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [128, 122, 137, 143]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6460
  Bounding Box: [52.60, 234.60, 163.00, 374.00]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [9, 23, 16, 33]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6440
  Bounding Box: [560.40, 1881.60, 761.20, 2035.20]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [48, 151, 63, 162]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6436
  Bounding Box: [674.40, 1156.80, 782.40, 1243.20]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [57, 95, 65, 101]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6387
  Bounding Box: [656.00, 1716.80, 746.40, 1828.80]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [56, 139, 62, 146]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6382
  Bounding Box: [732.40, 911.20, 898.40, 1055.20]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [62, 76, 72, 84]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6357
  Bounding Box: [1600.00, 37.70, 1753.60, 303.60]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [129, 7, 140, 27]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6348
  Bounding Box: [364.80, 1175.20, 468.00, 1290.40]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [33, 96, 40, 104]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6333
  Bounding Box: [811.20, 465.60, 974.40, 916.80]
  Mask Area: 307 pixels
  Mask Ratio: 0.0109
  Mask BBox: [68, 41, 80, 74]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6328
  Bounding Box: [1676.80, 1852.80, 1801.60, 2016.00]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [135, 149, 144, 161]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6328
  Bounding Box: [548.40, 579.60, 630.80, 718.80]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [47, 50, 53, 59]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6313
  Bounding Box: [897.60, 1242.40, 1003.20, 1380.00]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [75, 102, 82, 111]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6309
  Bounding Box: [1700.80, 1668.80, 1851.20, 1873.60]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [137, 135, 148, 150]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6304
  Bounding Box: [1766.40, 437.20, 1888.00, 561.20]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [142, 39, 151, 47]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6304
  Bounding Box: [405.60, 1800.00, 585.60, 2001.60]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [36, 145, 49, 160]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6299
  Bounding Box: [552.40, 954.40, 752.40, 1116.00]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [48, 79, 62, 91]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6299
  Bounding Box: [1867.20, 450.40, 2048.00, 702.40]
  Mask Area: 202 pixels
  Mask Ratio: 0.0072
  Mask BBox: [150, 40, 164, 58]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6206
  Bounding Box: [337.60, 489.60, 552.00, 621.60]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [31, 43, 47, 52]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6201
  Bounding Box: [1047.20, 1504.00, 1212.00, 1811.20]
  Mask Area: 214 pixels
  Mask Ratio: 0.0076
  Mask BBox: [86, 122, 98, 145]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6108
  Bounding Box: [1507.20, 1729.60, 1699.20, 1979.20]
  Mask Area: 176 pixels
  Mask Ratio: 0.0062
  Mask BBox: [122, 140, 136, 158]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6074
  Bounding Box: [502.00, 374.80, 593.20, 508.40]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [44, 34, 49, 42]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6050
  Bounding Box: [1833.60, 1300.00, 1971.20, 1392.80]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [148, 106, 156, 112]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6050
  Bounding Box: [1897.60, 944.00, 2022.40, 1084.80]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [153, 78, 161, 88]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6021
  Bounding Box: [1076.80, 750.00, 1225.60, 928.00]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [89, 63, 99, 76]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.5996
  Bounding Box: [1225.60, 146.20, 1419.20, 327.40]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [100, 16, 114, 29]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.5972
  Bounding Box: [92.00, 454.40, 353.20, 674.40]
  Mask Area: 207 pixels
  Mask Ratio: 0.0073
  Mask BBox: [12, 40, 31, 56]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.5942
  Bounding Box: [1251.20, 704.40, 1457.60, 1015.20]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [102, 60, 117, 83]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.5933
  Bounding Box: [1525.60, 277.00, 1608.00, 347.80]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [124, 26, 129, 31]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.5918
  Bounding Box: [1254.40, 246.20, 1436.80, 381.60]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [102, 24, 114, 33]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.5918
  Bounding Box: [1601.60, 249.20, 1844.80, 478.80]
  Mask Area: 229 pixels
  Mask Ratio: 0.0081
  Mask BBox: [130, 24, 148, 41]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.5903
  Bounding Box: [1368.00, 19.60, 1632.00, 114.20]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [111, 6, 130, 12]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.5864
  Bounding Box: [881.60, 1556.80, 1102.40, 1838.40]
  Mask Area: 279 pixels
  Mask Ratio: 0.0099
  Mask BBox: [73, 126, 90, 147]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.5854
  Bounding Box: [1990.40, 1771.20, 2048.00, 1905.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [160, 143, 164, 152]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5850
  Bounding Box: [374.00, 396.00, 511.60, 492.00]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [34, 35, 43, 42]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5845
  Bounding Box: [1011.20, 262.80, 1230.40, 452.80]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [83, 25, 100, 39]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5796
  Bounding Box: [1080.80, 7.65, 1240.80, 164.40]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [89, 5, 100, 16]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5762
  Bounding Box: [1966.40, 650.80, 2040.00, 848.00]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [158, 55, 163, 70]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5742
  Bounding Box: [1809.60, 1937.60, 1944.00, 2036.80]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [146, 156, 155, 163]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5742
  Bounding Box: [1139.20, 1433.60, 1219.20, 1556.80]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [93, 116, 98, 125]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5737
  Bounding Box: [1000.00, 1242.40, 1080.00, 1376.80]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [83, 102, 88, 111]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5718
  Bounding Box: [800.80, 829.60, 888.80, 946.40]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [67, 69, 73, 77]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5674
  Bounding Box: [1453.60, 735.60, 1559.20, 1020.80]
  Mask Area: 138 pixels
  Mask Ratio: 0.0049
  Mask BBox: [118, 62, 125, 82]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5625
  Bounding Box: [1181.60, 16.20, 1397.60, 234.00]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [97, 6, 113, 22]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5615
  Bounding Box: [9.50, 784.80, 232.00, 1119.20]
  Mask Area: 335 pixels
  Mask Ratio: 0.0119
  Mask BBox: [5, 66, 22, 91]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5605
  Bounding Box: [274.40, 1916.80, 449.60, 2038.40]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [26, 154, 39, 163]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5571
  Bounding Box: [815.20, 1335.20, 912.80, 1418.40]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [68, 109, 74, 114]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5542
  Bounding Box: [7.50, 588.00, 208.40, 719.20]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [5, 50, 20, 60]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5518
  Bounding Box: [548.80, 1248.00, 624.80, 1332.80]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [47, 102, 52, 108]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5483
  Bounding Box: [1985.60, 408.00, 2043.20, 531.20]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [160, 36, 163, 44]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5469
  Bounding Box: [311.80, 3.70, 412.40, 149.50]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [29, 5, 34, 15]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5400
  Bounding Box: [1214.40, 174.20, 1438.40, 375.20]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [99, 18, 116, 33]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5371
  Bounding Box: [1109.60, 150.20, 1236.00, 258.60]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [91, 16, 99, 24]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5356
  Bounding Box: [666.40, 1408.00, 765.60, 1515.20]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [57, 114, 63, 122]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5356
  Bounding Box: [242.60, 1377.60, 349.80, 1609.60]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [23, 112, 31, 129]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5332
  Bounding Box: [1462.40, 1560.80, 1532.80, 1649.60]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [119, 126, 122, 132]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5303
  Bounding Box: [166.40, 670.80, 270.40, 836.00]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [17, 57, 25, 69]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5273
  Bounding Box: [1032.00, 1114.40, 1153.60, 1255.20]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [85, 92, 94, 102]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5205
  Bounding Box: [0.00, 1384.80, 186.80, 1617.60]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [4, 113, 18, 130]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5186
  Bounding Box: [1712.00, 1536.80, 1782.40, 1627.20]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [138, 125, 143, 131]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5171
  Bounding Box: [237.80, 656.80, 309.40, 735.20]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [23, 56, 28, 60]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.5132
  Bounding Box: [1200.80, 1056.80, 1356.00, 1212.00]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [98, 87, 109, 98]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.5122
  Bounding Box: [1972.80, 385.60, 2036.80, 523.20]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [159, 35, 163, 44]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.5107
  Bounding Box: [10.80, 1064.80, 72.40, 1181.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [5, 88, 9, 96]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.5103
  Bounding Box: [410.80, 1937.60, 528.40, 2036.80]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [37, 156, 45, 162]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.5054
  Bounding Box: [414.40, 689.20, 676.00, 1010.40]
  Mask Area: 396 pixels
  Mask Ratio: 0.0140
  Mask BBox: [37, 58, 56, 82]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.5029
  Bounding Box: [1316.80, 1255.20, 1419.20, 1360.80]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [107, 103, 114, 110]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.5020
  Bounding Box: [0.00, 1047.20, 78.70, 1167.20]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [4, 86, 9, 95]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.5005
  Bounding Box: [0.00, 434.00, 60.30, 603.60]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [4, 38, 8, 51]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4985
  Bounding Box: [1059.20, 1280.80, 1232.00, 1474.40]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [87, 105, 100, 119]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.4976
  Bounding Box: [360.00, 491.60, 641.60, 680.40]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [33, 43, 54, 57]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4961
  Bounding Box: [184.40, 726.40, 323.60, 918.40]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [19, 61, 28, 75]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4937
  Bounding Box: [714.00, 816.00, 797.60, 900.80]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [60, 68, 66, 74]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4932
  Bounding Box: [424.40, 512.00, 624.40, 702.40]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [38, 44, 52, 58]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4902
  Bounding Box: [1459.20, 1032.00, 1611.20, 1156.80]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [118, 85, 129, 92]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4873
  Bounding Box: [720.80, 1592.00, 848.80, 1726.40]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [61, 129, 70, 138]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4868
  Bounding Box: [693.20, 337.20, 774.40, 426.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [59, 31, 64, 37]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4819
  Bounding Box: [170.00, 699.60, 290.80, 876.80]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [18, 59, 26, 72]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4805
  Bounding Box: [1102.40, 1149.60, 1195.20, 1226.40]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [91, 94, 97, 99]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4775
  Bounding Box: [0.00, 1676.80, 90.80, 1859.20]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [4, 135, 11, 149]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4775
  Bounding Box: [1836.80, 147.30, 2048.00, 449.60]
  Mask Area: 300 pixels
  Mask Ratio: 0.0106
  Mask BBox: [148, 16, 164, 39]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4746
  Bounding Box: [1523.20, 1697.60, 1595.20, 1803.20]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [123, 137, 128, 143]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4668
  Bounding Box: [1426.40, 657.60, 1500.00, 801.60]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [116, 56, 121, 64]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4653
  Bounding Box: [911.20, 1319.20, 1040.80, 1492.00]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [76, 108, 85, 120]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4648
  Bounding Box: [1673.60, 640.00, 1955.20, 1008.00]
  Mask Area: 534 pixels
  Mask Ratio: 0.0189
  Mask BBox: [135, 54, 156, 82]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4624
  Bounding Box: [518.80, 1509.60, 694.00, 1816.00]
  Mask Area: 265 pixels
  Mask Ratio: 0.0094
  Mask BBox: [45, 122, 58, 145]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4600
  Bounding Box: [1305.60, 384.00, 1553.60, 639.20]
  Mask Area: 318 pixels
  Mask Ratio: 0.0113
  Mask BBox: [106, 34, 125, 53]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4570
  Bounding Box: [1700.80, 1616.00, 1860.80, 1798.40]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [137, 131, 149, 144]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4565
  Bounding Box: [1575.20, 343.60, 1644.80, 462.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [128, 31, 131, 40]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4548
  Bounding Box: [1359.20, 1047.20, 1460.00, 1168.80]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [111, 86, 118, 95]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4529
  Bounding Box: [536.80, 1353.60, 641.60, 1456.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [46, 110, 54, 117]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4519
  Bounding Box: [1737.60, 1078.40, 1836.80, 1182.40]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [140, 89, 147, 95]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4480
  Bounding Box: [638.80, 402.40, 796.80, 552.80]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [54, 36, 66, 47]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4451
  Bounding Box: [552.00, 125.90, 776.00, 341.20]
  Mask Area: 201 pixels
  Mask Ratio: 0.0071
  Mask BBox: [48, 14, 64, 30]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4436
  Bounding Box: [1902.40, 17.75, 2024.00, 123.80]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [153, 6, 162, 11]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4377
  Bounding Box: [855.20, 1635.20, 1079.20, 1884.80]
  Mask Area: 252 pixels
  Mask Ratio: 0.0089
  Mask BBox: [71, 132, 88, 151]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4368
  Bounding Box: [2003.20, 1002.40, 2044.80, 1108.00]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [161, 83, 163, 89]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4321
  Bounding Box: [487.20, 205.60, 559.20, 325.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [43, 21, 47, 29]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4312
  Bounding Box: [654.80, 2.60, 805.60, 157.00]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [56, 5, 66, 16]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4302
  Bounding Box: [1880.00, 1078.40, 2048.00, 1323.20]
  Mask Area: 229 pixels
  Mask Ratio: 0.0081
  Mask BBox: [151, 89, 164, 107]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4258
  Bounding Box: [977.60, 463.60, 1105.60, 710.00]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [81, 41, 90, 59]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4138
  Bounding Box: [1061.60, 409.60, 1282.40, 632.00]
  Mask Area: 238 pixels
  Mask Ratio: 0.0084
  Mask BBox: [87, 36, 104, 53]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4136
  Bounding Box: [574.00, 0.00, 672.40, 79.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [49, 4, 56, 10]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.4136
  Bounding Box: [1854.40, 1253.60, 1998.40, 1389.60]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [149, 102, 160, 112]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.4131
  Bounding Box: [629.60, 118.80, 795.20, 272.80]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [54, 14, 65, 25]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.4121
  Bounding Box: [1129.60, 0.00, 1251.20, 130.40]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [93, 4, 101, 14]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.4102
  Bounding Box: [638.40, 1700.80, 731.20, 1812.80]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [55, 137, 61, 145]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.4065
  Bounding Box: [0.00, 1078.40, 162.80, 1372.80]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [4, 89, 15, 111]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.4041
  Bounding Box: [888.00, 912.80, 979.20, 1037.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [74, 76, 80, 84]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.4016
  Bounding Box: [1248.80, 1908.80, 1381.60, 2020.80]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [102, 154, 111, 161]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.3984
  Bounding Box: [526.00, 1439.20, 688.40, 1649.60]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [46, 117, 57, 132]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.3967
  Bounding Box: [1061.60, 1495.20, 1314.40, 1816.00]
  Mask Area: 310 pixels
  Mask Ratio: 0.0110
  Mask BBox: [87, 121, 106, 145]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.3950
  Bounding Box: [933.60, 1021.60, 1088.80, 1266.40]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [77, 84, 89, 102]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3928
  Bounding Box: [495.20, 356.20, 618.40, 522.00]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [43, 32, 52, 44]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3909
  Bounding Box: [1707.20, 425.20, 1905.60, 549.20]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [138, 38, 151, 46]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3899
  Bounding Box: [253.80, 1392.00, 372.00, 1568.00]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [24, 113, 33, 126]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3896
  Bounding Box: [1388.00, 1980.80, 1508.00, 2041.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [113, 159, 121, 163]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3862
  Bounding Box: [599.60, 101.90, 789.60, 314.00]
  Mask Area: 208 pixels
  Mask Ratio: 0.0074
  Mask BBox: [51, 12, 65, 28]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3850
  Bounding Box: [1167.20, 0.00, 1261.60, 100.30]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [96, 4, 102, 11]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3845
  Bounding Box: [1155.20, 88.30, 1411.20, 322.40]
  Mask Area: 221 pixels
  Mask Ratio: 0.0078
  Mask BBox: [95, 11, 114, 29]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3840
  Bounding Box: [60.10, 379.60, 181.20, 550.00]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [9, 34, 18, 44]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3787
  Bounding Box: [146.40, 314.40, 427.20, 471.20]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [16, 29, 37, 40]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3777
  Bounding Box: [1529.60, 1769.60, 1756.80, 2009.60]
  Mask Area: 210 pixels
  Mask Ratio: 0.0074
  Mask BBox: [124, 143, 141, 160]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3772
  Bounding Box: [25.10, 1280.80, 113.30, 1392.80]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [6, 105, 12, 111]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3772
  Bounding Box: [1710.40, 1625.60, 1851.20, 1747.20]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [138, 131, 148, 140]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3757
  Bounding Box: [880.80, 887.20, 970.40, 1016.80]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [73, 74, 79, 83]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3750
  Bounding Box: [896.00, 1260.80, 1051.20, 1411.20]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [74, 103, 86, 114]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3748
  Bounding Box: [1047.20, 0.00, 1172.00, 126.60]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [86, 4, 95, 13]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3723
  Bounding Box: [1256.80, 240.40, 1517.60, 430.00]
  Mask Area: 157 pixels
  Mask Ratio: 0.0056
  Mask BBox: [103, 23, 122, 37]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3711
  Bounding Box: [337.80, 892.00, 425.60, 1018.40]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [31, 74, 37, 83]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3711
  Bounding Box: [758.40, 875.20, 902.40, 1030.40]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [64, 73, 74, 84]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3694
  Bounding Box: [642.00, 1729.60, 730.00, 1841.60]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [55, 140, 61, 147]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3694
  Bounding Box: [1974.40, 1224.80, 2035.20, 1383.20]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [159, 100, 162, 112]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3630
  Bounding Box: [737.60, 926.40, 929.60, 1172.80]
  Mask Area: 224 pixels
  Mask Ratio: 0.0079
  Mask BBox: [62, 77, 76, 95]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3616
  Bounding Box: [1942.40, 658.00, 2032.00, 827.20]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [156, 56, 162, 68]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3594
  Bounding Box: [1396.80, 3.00, 1625.60, 210.80]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [114, 5, 130, 20]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3574
  Bounding Box: [0.00, 1624.00, 105.10, 1896.00]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [3, 131, 10, 152]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3552
  Bounding Box: [1736.00, 1050.40, 1854.40, 1167.20]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [140, 87, 148, 95]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3540
  Bounding Box: [1403.20, 1191.20, 1620.80, 1424.80]
  Mask Area: 218 pixels
  Mask Ratio: 0.0077
  Mask BBox: [114, 98, 130, 115]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3535
  Bounding Box: [206.60, 647.60, 320.60, 750.80]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [21, 55, 28, 62]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3525
  Bounding Box: [748.80, 1745.60, 873.60, 1912.00]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [63, 141, 72, 153]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3508
  Bounding Box: [1974.40, 20.40, 2044.80, 118.60]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [159, 6, 163, 11]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3496
  Bounding Box: [783.20, 851.20, 884.00, 961.60]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [66, 71, 73, 79]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3496
  Bounding Box: [808.80, 851.20, 909.60, 961.60]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [68, 71, 75, 79]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3430
  Bounding Box: [761.20, 493.60, 909.60, 780.00]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [64, 43, 75, 64]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3425
  Bounding Box: [1359.20, 44.90, 1665.60, 263.60]
  Mask Area: 284 pixels
  Mask Ratio: 0.0101
  Mask BBox: [111, 8, 134, 24]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3416
  Bounding Box: [1503.20, 704.80, 1606.40, 992.80]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [122, 60, 129, 81]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3413
  Bounding Box: [239.60, 879.20, 377.20, 1045.60]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [23, 73, 33, 85]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3398
  Bounding Box: [819.20, 1830.40, 894.40, 1888.00]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [68, 147, 73, 151]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3372
  Bounding Box: [690.80, 426.00, 828.00, 535.60]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [58, 38, 65, 45]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3333
  Bounding Box: [0.80, 1979.20, 61.20, 2048.00]
  Mask Area: 14 pixels
  Mask Ratio: 0.0005
  Mask BBox: [5, 159, 7, 164]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3333
  Bounding Box: [468.80, 971.20, 605.60, 1235.20]
  Mask Area: 212 pixels
  Mask Ratio: 0.0075
  Mask BBox: [41, 80, 51, 100]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1747.20, 420.80, 1875.20, 538.40]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [141, 38, 150, 46]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3301
  Bounding Box: [1888.00, 793.60, 2016.00, 1038.40]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [152, 66, 161, 85]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3289
  Bounding Box: [1878.40, 725.60, 2025.60, 973.60]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [151, 61, 162, 80]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3286
  Bounding Box: [335.20, 1280.00, 521.60, 1456.00]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [31, 104, 44, 117]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3286
  Bounding Box: [10.10, 1606.40, 262.00, 1830.40]
  Mask Area: 262 pixels
  Mask Ratio: 0.0093
  Mask BBox: [5, 130, 24, 146]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3279
  Bounding Box: [398.40, 381.60, 561.60, 508.00]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [36, 34, 47, 43]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3242
  Bounding Box: [367.60, 1324.00, 582.80, 1562.40]
  Mask Area: 200 pixels
  Mask Ratio: 0.0071
  Mask BBox: [33, 108, 49, 126]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3218
  Bounding Box: [1087.20, 1203.20, 1356.00, 1404.80]
  Mask Area: 271 pixels
  Mask Ratio: 0.0096
  Mask BBox: [89, 98, 109, 113]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3215
  Bounding Box: [1040.80, 750.00, 1268.00, 1016.00]
  Mask Area: 197 pixels
  Mask Ratio: 0.0070
  Mask BBox: [86, 63, 103, 83]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3213
  Bounding Box: [1367.20, 1302.40, 1517.60, 1424.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [111, 106, 122, 112]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3210
  Bounding Box: [493.60, 960.00, 717.60, 1193.60]
  Mask Area: 311 pixels
  Mask Ratio: 0.0110
  Mask BBox: [43, 79, 60, 97]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3201
  Bounding Box: [692.80, 1139.20, 809.60, 1236.80]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [59, 93, 67, 100]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3201
  Bounding Box: [692.80, 1164.80, 809.60, 1262.40]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [59, 95, 67, 102]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3201
  Bounding Box: [0.00, 673.60, 160.80, 803.20]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [4, 57, 16, 66]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3191
  Bounding Box: [480.80, 356.40, 576.00, 498.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [42, 33, 48, 42]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3191
  Bounding Box: [666.00, 559.20, 934.40, 844.00]
  Mask Area: 327 pixels
  Mask Ratio: 0.0116
  Mask BBox: [57, 48, 76, 69]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3174
  Bounding Box: [1404.00, 910.40, 1468.00, 996.80]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [114, 76, 118, 81]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3171
  Bounding Box: [663.60, 1139.20, 792.00, 1280.00]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [56, 93, 65, 103]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3169
  Bounding Box: [5.85, 813.60, 169.80, 1082.40]
  Mask Area: 257 pixels
  Mask Ratio: 0.0091
  Mask BBox: [5, 68, 17, 88]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3130
  Bounding Box: [298.80, 1876.80, 555.60, 2046.40]
  Mask Area: 196 pixels
  Mask Ratio: 0.0069
  Mask BBox: [28, 151, 47, 163]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3123
  Bounding Box: [657.60, 1118.40, 784.00, 1211.20]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [56, 92, 65, 98]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3123
  Bounding Box: [547.20, 181.00, 724.80, 345.80]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [47, 19, 60, 31]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [504.80, 920.80, 645.60, 1064.80]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [44, 76, 54, 87]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [1155.20, 1131.20, 1379.20, 1310.40]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [95, 93, 111, 106]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3088
  Bounding Box: [1364.00, 695.20, 1436.00, 798.40]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [111, 59, 116, 66]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3086
  Bounding Box: [229.60, 642.00, 303.60, 714.80]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [22, 55, 27, 59]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3086
  Bounding Box: [255.20, 642.00, 329.20, 714.80]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [24, 55, 28, 59]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3086
  Bounding Box: [255.20, 667.60, 329.20, 740.40]
  Mask Area: 15 pixels
  Mask Ratio: 0.0005
  Mask BBox: [24, 57, 27, 60]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.3086
  Bounding Box: [1635.20, 1985.60, 1772.80, 2040.00]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [132, 160, 142, 163]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.3079
  Bounding Box: [0.65, 606.00, 204.40, 810.40]
  Mask Area: 215 pixels
  Mask Ratio: 0.0076
  Mask BBox: [5, 52, 19, 67]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [566.00, 565.20, 642.80, 702.00]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [49, 49, 53, 58]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.3064
  Bounding Box: [761.20, 316.00, 852.00, 429.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [64, 29, 69, 37]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.3059
  Bounding Box: [1686.40, 1469.60, 1792.00, 1640.00]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [136, 119, 143, 132]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.3049
  Bounding Box: [1832.00, 1908.80, 1985.60, 2048.00]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [148, 154, 159, 164]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.3040
  Bounding Box: [572.40, 1833.60, 772.00, 2009.60]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [49, 148, 64, 160]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.3037
  Bounding Box: [369.20, 380.00, 492.40, 474.40]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [33, 34, 42, 41]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.3025
  Bounding Box: [1418.40, 1676.80, 1556.00, 1811.20]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [115, 135, 125, 145]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.3008
  Bounding Box: [540.40, 562.40, 614.80, 701.60]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [47, 48, 52, 58]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.2998
  Bounding Box: [331.60, 885.60, 444.40, 1077.60]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [30, 74, 38, 88]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.2996
  Bounding Box: [569.60, 1347.20, 664.00, 1460.80]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [49, 110, 55, 118]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.2993
  Bounding Box: [1360.80, 1977.60, 1488.80, 2032.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [111, 159, 120, 162]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.2983
  Bounding Box: [1792.00, 1910.40, 1929.60, 2022.40]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [144, 155, 154, 161]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.2964
  Bounding Box: [815.20, 1832.00, 928.80, 1899.20]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [68, 148, 74, 152]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.2954
  Bounding Box: [1875.20, 18.45, 2032.00, 166.00]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [151, 6, 162, 16]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.2947
  Bounding Box: [1011.20, 1224.80, 1102.40, 1359.20]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [83, 100, 90, 110]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.2937
  Bounding Box: [730.80, 431.60, 825.60, 525.20]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [62, 38, 68, 45]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.2927
  Bounding Box: [1595.20, 238.00, 1691.20, 318.00]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [129, 23, 136, 28]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.2917
  Bounding Box: [1740.80, 1152.00, 1910.40, 1334.40]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [140, 94, 153, 108]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.2913
  Bounding Box: [468.80, 963.20, 536.80, 1059.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [41, 80, 45, 86]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.2908
  Bounding Box: [1515.20, 1544.80, 1603.20, 1664.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [123, 125, 129, 132]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.2908
  Bounding Box: [455.60, 924.00, 637.20, 1172.00]
  Mask Area: 224 pixels
  Mask Ratio: 0.0079
  Mask BBox: [40, 77, 53, 95]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.2903
  Bounding Box: [348.80, 1144.80, 459.20, 1272.80]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [32, 94, 39, 103]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2893
  Bounding Box: [1277.60, 269.00, 1458.40, 401.60]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [104, 26, 117, 33]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2893
  Bounding Box: [1433.60, 1027.20, 1628.80, 1217.60]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [116, 85, 131, 99]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2883
  Bounding Box: [0.00, 1170.40, 103.80, 1335.20]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [4, 96, 12, 108]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2874
  Bounding Box: [963.20, 1059.20, 1124.80, 1294.40]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [80, 87, 91, 105]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2864
  Bounding Box: [627.20, 1097.60, 759.20, 1180.80]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [53, 90, 63, 96]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2859
  Bounding Box: [756.80, 484.00, 846.40, 669.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [64, 42, 70, 55]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2859
  Bounding Box: [1220.00, 1217.60, 1432.80, 1355.20]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [100, 100, 115, 109]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2854
  Bounding Box: [1736.00, 1891.20, 1940.80, 2044.80]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [140, 152, 155, 163]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2852
  Bounding Box: [1947.20, 356.60, 2048.00, 494.00]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [157, 32, 164, 42]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2852
  Bounding Box: [1937.60, 1211.20, 2048.00, 1368.00]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [156, 99, 164, 110]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2847
  Bounding Box: [538.40, 1273.60, 612.80, 1358.40]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [47, 104, 51, 110]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2847
  Bounding Box: [564.00, 1273.60, 638.40, 1358.40]
  Mask Area: 18 pixels
  Mask Ratio: 0.0006
  Mask BBox: [49, 104, 53, 110]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2847
  Bounding Box: [848.00, 1251.20, 956.80, 1363.20]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [71, 102, 78, 110]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2832
  Bounding Box: [727.20, 809.60, 832.80, 907.20]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [61, 68, 69, 74]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [1715.20, 1604.80, 1875.20, 1726.40]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [138, 130, 150, 138]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2805
  Bounding Box: [32.80, 786.40, 313.60, 1079.20]
  Mask Area: 365 pixels
  Mask Ratio: 0.0129
  Mask BBox: [7, 66, 28, 88]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2803
  Bounding Box: [1045.60, 1313.60, 1180.00, 1462.40]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [86, 107, 96, 118]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2795
  Bounding Box: [709.60, 324.00, 798.40, 416.00]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [60, 30, 66, 36]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2795
  Bounding Box: [709.60, 349.60, 798.40, 441.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [60, 32, 66, 38]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2791
  Bounding Box: [1208.80, 5.00, 1378.40, 183.80]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [99, 5, 111, 18]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2773
  Bounding Box: [479.60, 1299.20, 577.20, 1406.40]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [42, 106, 49, 113]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2773
  Bounding Box: [1280.00, 262.40, 1507.20, 495.20]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [104, 25, 121, 42]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2764
  Bounding Box: [620.00, 1828.80, 748.00, 1928.00]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [53, 147, 62, 153]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2749
  Bounding Box: [9.00, 1230.40, 96.20, 1363.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [5, 101, 11, 110]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2747
  Bounding Box: [1215.20, 1343.20, 1293.60, 1442.40]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [99, 109, 105, 116]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2737
  Bounding Box: [1798.40, 1368.80, 1878.40, 1516.00]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [145, 111, 150, 122]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2732
  Bounding Box: [1577.60, 240.00, 1660.80, 320.40]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [128, 23, 133, 29]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2732
  Bounding Box: [416.80, 1273.60, 583.20, 1400.00]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [37, 104, 49, 113]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2717
  Bounding Box: [1511.20, 300.60, 1588.80, 379.20]
  Mask Area: 17 pixels
  Mask Ratio: 0.0006
  Mask BBox: [123, 28, 128, 33]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2717
  Bounding Box: [1536.80, 300.60, 1614.40, 379.20]
  Mask Area: 17 pixels
  Mask Ratio: 0.0006
  Mask BBox: [125, 28, 130, 33]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2717
  Bounding Box: [750.40, 1292.00, 856.00, 1431.20]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [63, 105, 70, 112]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2717
  Bounding Box: [1540.80, 282.60, 1643.20, 353.80]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [125, 27, 132, 31]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2712
  Bounding Box: [307.20, 1716.80, 451.20, 1793.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [28, 139, 39, 143]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2678
  Bounding Box: [526.00, 1367.20, 626.80, 1472.80]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [46, 111, 52, 119]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2678
  Bounding Box: [551.60, 1367.20, 652.40, 1472.80]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [48, 111, 54, 119]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2676
  Bounding Box: [232.00, 451.20, 314.40, 522.40]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [23, 40, 28, 44]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2676
  Bounding Box: [45.75, 378.40, 217.40, 602.40]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [8, 34, 20, 51]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2673
  Bounding Box: [2001.60, 411.60, 2048.00, 534.00]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [161, 37, 164, 44]

