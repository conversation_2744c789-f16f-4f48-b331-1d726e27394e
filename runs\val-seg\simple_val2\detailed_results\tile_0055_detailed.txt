Image: tile_0055.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8394
  Bounding Box: [1433.60, 1595.20, 1672.00, 1803.20]
  Mask Area: 202 pixels
  Mask Ratio: 0.0072
  Mask BBox: [116, 129, 133, 143]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8364
  Bounding Box: [1652.80, 663.60, 1928.00, 907.20]
  Mask Area: 313 pixels
  Mask Ratio: 0.0111
  Mask BBox: [134, 56, 153, 74]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8335
  Bounding Box: [1574.40, 1715.20, 1907.20, 2038.40]
  Mask Area: 487 pixels
  Mask Ratio: 0.0173
  Mask BBox: [127, 138, 152, 163]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8320
  Bounding Box: [1338.40, 763.20, 1519.20, 936.00]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [109, 64, 122, 77]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8276
  Bounding Box: [25.40, 574.00, 234.60, 796.80]
  Mask Area: 213 pixels
  Mask Ratio: 0.0075
  Mask BBox: [6, 49, 22, 65]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8218
  Bounding Box: [426.00, 1681.60, 724.40, 2024.00]
  Mask Area: 417 pixels
  Mask Ratio: 0.0148
  Mask BBox: [38, 136, 60, 162]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8203
  Bounding Box: [1095.20, 684.00, 1308.00, 925.60]
  Mask Area: 225 pixels
  Mask Ratio: 0.0080
  Mask BBox: [90, 58, 106, 76]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8184
  Bounding Box: [686.00, 1080.80, 875.20, 1378.40]
  Mask Area: 258 pixels
  Mask Ratio: 0.0091
  Mask BBox: [58, 89, 72, 111]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8120
  Bounding Box: [406.40, 488.80, 496.80, 606.40]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [36, 43, 42, 51]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8096
  Bounding Box: [546.80, 30.90, 888.00, 310.00]
  Mask Area: 475 pixels
  Mask Ratio: 0.0168
  Mask BBox: [47, 7, 73, 28]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8091
  Bounding Box: [606.00, 656.00, 833.60, 1116.80]
  Mask Area: 483 pixels
  Mask Ratio: 0.0171
  Mask BBox: [52, 56, 69, 91]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8047
  Bounding Box: [1534.40, 486.00, 1672.00, 728.40]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [124, 42, 134, 60]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.8027
  Bounding Box: [46.10, 1377.60, 199.20, 1603.20]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [8, 112, 19, 129]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.8018
  Bounding Box: [248.00, 967.20, 440.00, 1212.00]
  Mask Area: 177 pixels
  Mask Ratio: 0.0063
  Mask BBox: [24, 80, 38, 98]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7998
  Bounding Box: [167.80, 0.00, 323.80, 182.60]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [18, 4, 29, 17]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7915
  Bounding Box: [1713.60, 148.00, 1828.80, 321.60]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [138, 16, 146, 29]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7881
  Bounding Box: [1614.40, 12.00, 1768.00, 169.00]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [131, 5, 142, 17]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7876
  Bounding Box: [1264.80, 1374.40, 1400.80, 1540.80]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [103, 112, 113, 124]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7861
  Bounding Box: [356.80, 155.40, 502.40, 327.80]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [32, 17, 43, 29]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7861
  Bounding Box: [99.80, 404.00, 358.40, 604.00]
  Mask Area: 210 pixels
  Mask Ratio: 0.0074
  Mask BBox: [12, 36, 31, 51]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7856
  Bounding Box: [1352.00, 1244.80, 1500.80, 1372.80]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [110, 102, 121, 111]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7803
  Bounding Box: [765.20, 914.40, 918.40, 1180.00]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [64, 76, 75, 96]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7769
  Bounding Box: [1480.00, 156.80, 1622.40, 294.40]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [120, 17, 130, 25]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7729
  Bounding Box: [8.85, 17.00, 202.40, 325.00]
  Mask Area: 267 pixels
  Mask Ratio: 0.0095
  Mask BBox: [5, 6, 19, 29]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7720
  Bounding Box: [220.20, 1384.00, 369.20, 1619.20]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [22, 113, 31, 130]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7686
  Bounding Box: [471.60, 427.20, 656.40, 561.60]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [41, 38, 54, 47]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7676
  Bounding Box: [1349.60, 260.60, 1580.00, 648.00]
  Mask Area: 424 pixels
  Mask Ratio: 0.0150
  Mask BBox: [110, 25, 127, 54]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7627
  Bounding Box: [1051.20, 430.40, 1332.80, 647.20]
  Mask Area: 229 pixels
  Mask Ratio: 0.0081
  Mask BBox: [87, 38, 108, 54]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7598
  Bounding Box: [1249.60, 982.40, 1457.60, 1142.40]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [102, 81, 116, 93]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7588
  Bounding Box: [1857.60, 66.10, 1992.00, 297.20]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [150, 10, 158, 26]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7563
  Bounding Box: [1764.80, 0.00, 1892.80, 192.60]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [142, 4, 151, 19]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7559
  Bounding Box: [1155.20, 1095.20, 1332.80, 1392.80]
  Mask Area: 226 pixels
  Mask Ratio: 0.0080
  Mask BBox: [95, 90, 108, 111]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7554
  Bounding Box: [788.80, 500.00, 971.20, 779.20]
  Mask Area: 204 pixels
  Mask Ratio: 0.0072
  Mask BBox: [66, 45, 79, 62]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7539
  Bounding Box: [300.40, 4.70, 445.20, 172.00]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [28, 5, 38, 17]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7466
  Bounding Box: [1713.60, 332.20, 1819.20, 601.60]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [138, 31, 146, 50]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7466
  Bounding Box: [230.00, 568.40, 415.60, 741.20]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [22, 49, 36, 61]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7402
  Bounding Box: [59.70, 836.80, 247.60, 1276.80]
  Mask Area: 389 pixels
  Mask Ratio: 0.0138
  Mask BBox: [9, 70, 23, 103]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7368
  Bounding Box: [1812.80, 395.20, 1992.00, 620.00]
  Mask Area: 182 pixels
  Mask Ratio: 0.0064
  Mask BBox: [146, 35, 159, 51]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7349
  Bounding Box: [180.80, 190.80, 417.20, 405.20]
  Mask Area: 204 pixels
  Mask Ratio: 0.0072
  Mask BBox: [20, 19, 36, 34]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7314
  Bounding Box: [1018.40, 83.40, 1328.80, 245.80]
  Mask Area: 192 pixels
  Mask Ratio: 0.0068
  Mask BBox: [84, 11, 106, 23]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7197
  Bounding Box: [1538.40, 1258.40, 1676.80, 1373.60]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [125, 103, 134, 110]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7192
  Bounding Box: [1632.00, 322.20, 1718.40, 422.80]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [132, 30, 138, 37]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7129
  Bounding Box: [1286.40, 619.20, 1427.20, 744.80]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [105, 53, 115, 62]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7124
  Bounding Box: [798.40, 1721.60, 1115.20, 2009.60]
  Mask Area: 407 pixels
  Mask Ratio: 0.0144
  Mask BBox: [67, 139, 91, 160]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7080
  Bounding Box: [964.80, 497.60, 1121.60, 727.20]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [80, 43, 91, 60]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7026
  Bounding Box: [876.80, 1500.80, 1073.60, 1619.20]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [73, 122, 86, 130]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7021
  Bounding Box: [518.80, 1422.40, 657.20, 1564.80]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [46, 116, 55, 124]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.6973
  Bounding Box: [1138.40, 0.00, 1351.20, 120.20]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [93, 4, 109, 13]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.6953
  Bounding Box: [1179.20, 1686.40, 1353.60, 1849.60]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [97, 136, 109, 148]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.6953
  Bounding Box: [1039.20, 1380.00, 1284.00, 1616.00]
  Mask Area: 260 pixels
  Mask Ratio: 0.0092
  Mask BBox: [86, 112, 104, 130]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.6924
  Bounding Box: [1755.20, 0.65, 1864.00, 159.60]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [142, 5, 149, 16]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.6914
  Bounding Box: [1760.00, 1460.00, 1878.40, 1683.20]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [142, 119, 150, 133]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.6890
  Bounding Box: [330.00, 390.80, 481.20, 541.20]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [30, 35, 41, 46]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.6841
  Bounding Box: [218.40, 1325.60, 322.80, 1448.80]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [22, 108, 29, 117]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.6816
  Bounding Box: [384.00, 1397.60, 505.60, 1552.80]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [34, 114, 43, 125]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.6807
  Bounding Box: [804.00, 222.40, 930.40, 371.60]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [67, 22, 76, 33]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6797
  Bounding Box: [1528.00, 1055.20, 1665.60, 1210.40]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [124, 87, 133, 98]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6792
  Bounding Box: [440.40, 850.40, 570.80, 1079.20]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [39, 71, 48, 85]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6743
  Bounding Box: [896.00, 207.40, 1057.60, 475.20]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [74, 21, 86, 41]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6680
  Bounding Box: [700.80, 1921.60, 905.60, 2030.40]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [59, 155, 74, 162]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6636
  Bounding Box: [948.00, 768.00, 1114.40, 881.60]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [79, 64, 90, 72]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6636
  Bounding Box: [152.10, 1536.80, 233.60, 1617.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [16, 125, 22, 130]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6631
  Bounding Box: [1740.80, 908.00, 1913.60, 1018.40]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [140, 75, 153, 83]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6582
  Bounding Box: [425.20, 274.80, 570.00, 414.00]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [38, 26, 47, 36]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6543
  Bounding Box: [1180.80, 28.00, 1323.20, 127.00]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [97, 7, 107, 13]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6519
  Bounding Box: [1153.60, 932.00, 1304.00, 1037.60]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [95, 77, 105, 84]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6470
  Bounding Box: [843.20, 0.00, 984.00, 211.60]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [70, 4, 80, 20]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6460
  Bounding Box: [563.20, 1027.20, 681.60, 1262.40]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [48, 85, 57, 102]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6426
  Bounding Box: [177.20, 778.40, 327.20, 911.20]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [18, 65, 29, 75]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6406
  Bounding Box: [1448.80, 628.00, 1540.00, 704.00]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [118, 54, 123, 58]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6406
  Bounding Box: [1024.00, 176.80, 1324.80, 437.60]
  Mask Area: 323 pixels
  Mask Ratio: 0.0114
  Mask BBox: [84, 18, 106, 38]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6401
  Bounding Box: [672.00, 1436.80, 857.60, 1708.80]
  Mask Area: 225 pixels
  Mask Ratio: 0.0080
  Mask BBox: [57, 117, 70, 137]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6382
  Bounding Box: [1872.00, 1286.40, 1993.60, 1392.00]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [151, 105, 159, 111]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6338
  Bounding Box: [0.90, 324.00, 172.80, 552.80]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [5, 30, 17, 47]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6328
  Bounding Box: [1526.40, 288.40, 1680.00, 518.80]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [126, 30, 133, 44]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6318
  Bounding Box: [309.60, 1000.80, 573.60, 1364.00]
  Mask Area: 460 pixels
  Mask Ratio: 0.0163
  Mask BBox: [29, 83, 48, 110]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6294
  Bounding Box: [69.30, 1819.20, 343.20, 2046.40]
  Mask Area: 250 pixels
  Mask Ratio: 0.0089
  Mask BBox: [10, 147, 29, 163]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6294
  Bounding Box: [17.00, 1696.00, 111.20, 1824.00]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [6, 137, 12, 146]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6270
  Bounding Box: [1812.80, 261.60, 1924.80, 385.60]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [146, 25, 152, 32]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6265
  Bounding Box: [1531.20, 1336.00, 1736.00, 1595.20]
  Mask Area: 239 pixels
  Mask Ratio: 0.0085
  Mask BBox: [124, 109, 139, 128]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6235
  Bounding Box: [1659.20, 1052.00, 1867.20, 1316.00]
  Mask Area: 253 pixels
  Mask Ratio: 0.0090
  Mask BBox: [134, 87, 149, 106]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6230
  Bounding Box: [14.60, 754.40, 105.40, 901.60]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [6, 63, 12, 74]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6191
  Bounding Box: [1720.00, 546.00, 1851.20, 665.20]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [139, 47, 148, 55]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6133
  Bounding Box: [1341.60, 1128.00, 1444.00, 1272.00]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [109, 93, 116, 103]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6113
  Bounding Box: [448.80, 1347.20, 546.40, 1473.60]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [40, 110, 46, 119]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6084
  Bounding Box: [32.50, 1232.80, 244.00, 1447.20]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [7, 101, 23, 117]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.6069
  Bounding Box: [1907.20, 339.20, 2048.00, 544.80]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [153, 31, 164, 46]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.6035
  Bounding Box: [1390.40, 8.75, 1534.40, 151.00]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [113, 5, 123, 15]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.6035
  Bounding Box: [1312.80, 165.20, 1445.60, 367.60]
  Mask Area: 112 pixels
  Mask Ratio: 0.0040
  Mask BBox: [107, 17, 116, 32]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.6016
  Bounding Box: [1644.80, 1564.80, 1772.80, 1670.40]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [133, 127, 142, 134]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.5942
  Bounding Box: [28.80, 504.00, 122.40, 602.40]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [7, 44, 13, 50]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.5898
  Bounding Box: [1114.40, 551.60, 1276.00, 715.60]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [92, 48, 101, 58]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5898
  Bounding Box: [656.00, 1232.80, 840.80, 1512.80]
  Mask Area: 239 pixels
  Mask Ratio: 0.0085
  Mask BBox: [56, 101, 69, 122]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5869
  Bounding Box: [920.00, 869.60, 1121.60, 1122.40]
  Mask Area: 244 pixels
  Mask Ratio: 0.0086
  Mask BBox: [76, 72, 91, 91]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5835
  Bounding Box: [1900.80, 577.60, 2035.20, 852.80]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [153, 50, 162, 70]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5830
  Bounding Box: [175.60, 1756.80, 338.80, 1936.00]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [18, 142, 30, 155]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5776
  Bounding Box: [502.40, 0.00, 917.60, 362.80]
  Mask Area: 626 pixels
  Mask Ratio: 0.0222
  Mask BBox: [44, 2, 73, 32]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5752
  Bounding Box: [144.70, 1609.60, 238.40, 1753.60]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [16, 130, 22, 140]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5723
  Bounding Box: [1883.20, 1932.80, 2033.60, 2041.60]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [152, 155, 162, 163]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5718
  Bounding Box: [1696.00, 1325.60, 1958.40, 1554.40]
  Mask Area: 238 pixels
  Mask Ratio: 0.0084
  Mask BBox: [137, 108, 156, 125]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5693
  Bounding Box: [240.60, 970.40, 502.40, 1287.20]
  Mask Area: 407 pixels
  Mask Ratio: 0.0144
  Mask BBox: [23, 80, 43, 104]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5659
  Bounding Box: [8.40, 1127.20, 90.50, 1215.20]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [5, 93, 9, 98]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5645
  Bounding Box: [1513.60, 906.40, 1625.60, 1023.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [123, 75, 130, 83]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5610
  Bounding Box: [863.20, 1608.00, 1055.20, 1726.40]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [72, 130, 84, 138]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5601
  Bounding Box: [842.40, 1197.60, 1037.60, 1477.60]
  Mask Area: 265 pixels
  Mask Ratio: 0.0094
  Mask BBox: [70, 98, 85, 119]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5591
  Bounding Box: [1819.20, 1469.60, 2027.20, 1801.60]
  Mask Area: 304 pixels
  Mask Ratio: 0.0108
  Mask BBox: [147, 119, 162, 144]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5586
  Bounding Box: [1385.60, 1926.40, 1614.40, 2048.00]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [113, 155, 130, 164]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5581
  Bounding Box: [96.30, 791.20, 169.80, 856.80]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [12, 66, 17, 70]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5562
  Bounding Box: [364.00, 700.80, 491.20, 905.60]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [33, 59, 42, 74]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5537
  Bounding Box: [490.40, 628.80, 638.40, 995.20]
  Mask Area: 225 pixels
  Mask Ratio: 0.0080
  Mask BBox: [43, 54, 53, 81]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5532
  Bounding Box: [427.20, 1360.80, 540.80, 1512.80]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [38, 111, 46, 122]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5513
  Bounding Box: [1386.40, 686.80, 1519.20, 802.40]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [113, 58, 121, 65]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5508
  Bounding Box: [1169.60, 1851.20, 1355.20, 2030.40]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [96, 149, 109, 162]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5503
  Bounding Box: [1084.80, 463.20, 1297.60, 711.20]
  Mask Area: 198 pixels
  Mask Ratio: 0.0070
  Mask BBox: [89, 41, 105, 58]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5449
  Bounding Box: [0.00, 864.00, 74.10, 1022.40]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [4, 72, 9, 83]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5435
  Bounding Box: [1513.60, 756.40, 1596.80, 836.80]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [123, 64, 127, 69]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5342
  Bounding Box: [1516.80, 37.75, 1641.60, 158.60]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [123, 7, 132, 15]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5312
  Bounding Box: [585.20, 1204.00, 704.40, 1404.00]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [50, 99, 58, 113]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5215
  Bounding Box: [1200.80, 356.00, 1352.80, 469.60]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [98, 32, 109, 40]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.5205
  Bounding Box: [1974.40, 1902.40, 2048.00, 2020.80]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [159, 153, 164, 161]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.5181
  Bounding Box: [1282.40, 122.40, 1344.80, 212.00]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [105, 14, 109, 19]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.5132
  Bounding Box: [838.40, 1586.40, 915.20, 1657.60]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [70, 128, 75, 133]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.5127
  Bounding Box: [1902.40, 824.80, 2030.40, 1039.20]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [153, 69, 162, 85]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.5107
  Bounding Box: [446.40, 612.00, 511.20, 717.60]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [39, 52, 43, 59]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.5073
  Bounding Box: [948.80, 33.00, 1028.80, 182.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [79, 7, 84, 18]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.5015
  Bounding Box: [442.80, 1568.00, 615.60, 1699.20]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [39, 127, 52, 135]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.4971
  Bounding Box: [619.60, 69.70, 912.80, 344.40]
  Mask Area: 376 pixels
  Mask Ratio: 0.0133
  Mask BBox: [53, 10, 75, 30]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4922
  Bounding Box: [356.60, 1395.20, 462.40, 1548.80]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [32, 113, 40, 124]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.4915
  Bounding Box: [730.40, 903.20, 912.80, 1277.60]
  Mask Area: 278 pixels
  Mask Ratio: 0.0098
  Mask BBox: [62, 75, 75, 103]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4907
  Bounding Box: [226.80, 1619.20, 457.20, 1920.00]
  Mask Area: 258 pixels
  Mask Ratio: 0.0091
  Mask BBox: [22, 131, 39, 153]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4885
  Bounding Box: [1003.20, 1619.20, 1222.40, 1971.20]
  Mask Area: 314 pixels
  Mask Ratio: 0.0111
  Mask BBox: [83, 131, 99, 157]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4873
  Bounding Box: [1328.00, 1592.00, 1436.80, 1736.00]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [108, 129, 115, 139]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4858
  Bounding Box: [1090.40, 1168.80, 1167.20, 1288.80]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [90, 96, 95, 103]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4854
  Bounding Box: [13.30, 301.20, 136.70, 382.80]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [6, 28, 14, 33]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4810
  Bounding Box: [79.60, 1586.40, 177.00, 1742.40]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [11, 128, 17, 140]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4771
  Bounding Box: [5.70, 982.40, 69.80, 1113.60]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [5, 81, 9, 90]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4741
  Bounding Box: [1977.60, 66.90, 2044.80, 212.40]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [159, 10, 163, 20]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4741
  Bounding Box: [335.80, 173.40, 536.80, 365.60]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [31, 18, 45, 32]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4673
  Bounding Box: [1072.80, 1987.20, 1223.20, 2041.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [88, 160, 99, 163]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4624
  Bounding Box: [1627.20, 1578.40, 1771.20, 1702.40]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [132, 128, 142, 136]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4609
  Bounding Box: [7.90, 1013.60, 79.60, 1136.80]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 84, 8, 92]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4595
  Bounding Box: [13.30, 1578.40, 90.30, 1696.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [6, 128, 9, 136]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4585
  Bounding Box: [95.50, 1402.40, 347.60, 1588.80]
  Mask Area: 235 pixels
  Mask Ratio: 0.0083
  Mask BBox: [12, 114, 31, 128]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4575
  Bounding Box: [1916.80, 1.75, 2044.80, 107.40]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [154, 5, 162, 12]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4575
  Bounding Box: [725.20, 1942.40, 926.40, 2048.00]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [61, 156, 76, 164]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4565
  Bounding Box: [946.40, 692.40, 1048.80, 754.00]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [78, 59, 85, 62]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4561
  Bounding Box: [1668.80, 390.40, 1736.00, 514.40]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [135, 35, 138, 44]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4561
  Bounding Box: [1276.80, 1117.60, 1363.20, 1226.40]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [104, 92, 110, 99]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4519
  Bounding Box: [1492.00, 1801.60, 1620.80, 1987.20]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [121, 145, 129, 159]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4504
  Bounding Box: [120.50, 0.00, 205.20, 86.90]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [14, 4, 20, 10]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4438
  Bounding Box: [168.00, 0.95, 431.20, 185.40]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [18, 5, 37, 18]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4392
  Bounding Box: [590.40, 1544.00, 712.00, 1697.60]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [51, 126, 59, 136]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4377
  Bounding Box: [115.50, 1764.80, 346.40, 1992.00]
  Mask Area: 177 pixels
  Mask Ratio: 0.0063
  Mask BBox: [14, 142, 31, 159]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4363
  Bounding Box: [10.20, 374.40, 130.20, 571.20]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [5, 34, 14, 48]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4307
  Bounding Box: [7.70, 886.40, 71.30, 1078.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [5, 74, 9, 88]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4216
  Bounding Box: [1953.60, 204.80, 2048.00, 360.40]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [157, 20, 164, 32]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4192
  Bounding Box: [1740.80, 1405.60, 1900.80, 1657.60]
  Mask Area: 202 pixels
  Mask Ratio: 0.0072
  Mask BBox: [140, 114, 152, 133]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4036
  Bounding Box: [556.40, 1339.20, 642.00, 1457.60]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [48, 109, 54, 117]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.3965
  Bounding Box: [1894.40, 0.00, 2025.60, 86.60]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [152, 4, 162, 9]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.3928
  Bounding Box: [1392.00, 274.00, 1668.80, 581.20]
  Mask Area: 417 pixels
  Mask Ratio: 0.0148
  Mask BBox: [113, 26, 134, 49]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.3899
  Bounding Box: [382.80, 1628.80, 502.80, 1721.60]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [34, 132, 43, 138]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.3896
  Bounding Box: [832.00, 1308.80, 1032.00, 1489.60]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [69, 107, 84, 120]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.3896
  Bounding Box: [1368.80, 1945.60, 1580.00, 2035.20]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [112, 156, 127, 162]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.3877
  Bounding Box: [891.20, 21.05, 1017.60, 184.40]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [74, 6, 83, 18]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.3862
  Bounding Box: [1112.00, 571.60, 1289.60, 921.60]
  Mask Area: 284 pixels
  Mask Ratio: 0.0101
  Mask BBox: [91, 49, 104, 75]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.3845
  Bounding Box: [206.00, 1144.00, 296.80, 1265.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [21, 94, 27, 102]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.3796
  Bounding Box: [851.20, 1170.40, 1104.00, 1448.80]
  Mask Area: 405 pixels
  Mask Ratio: 0.0143
  Mask BBox: [71, 96, 90, 117]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.3772
  Bounding Box: [1624.00, 357.60, 1745.60, 510.40]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [131, 32, 140, 43]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.3745
  Bounding Box: [626.80, 1486.40, 836.80, 1716.80]
  Mask Area: 252 pixels
  Mask Ratio: 0.0089
  Mask BBox: [53, 121, 69, 138]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.3738
  Bounding Box: [317.40, 0.00, 468.40, 139.30]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [29, 4, 40, 14]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.3701
  Bounding Box: [1627.20, 1633.60, 1745.60, 1758.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [132, 132, 140, 140]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3701
  Bounding Box: [1108.80, 1385.60, 1377.60, 1576.00]
  Mask Area: 219 pixels
  Mask Ratio: 0.0078
  Mask BBox: [91, 113, 111, 127]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3699
  Bounding Box: [1328.80, 26.40, 1402.40, 126.60]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [108, 7, 112, 13]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3684
  Bounding Box: [1406.40, 1798.40, 1532.80, 1897.60]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [114, 145, 123, 152]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3669
  Bounding Box: [1955.20, 855.20, 2044.80, 1060.00]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [157, 71, 163, 86]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3667
  Bounding Box: [1648.00, 0.00, 1862.40, 178.20]
  Mask Area: 186 pixels
  Mask Ratio: 0.0066
  Mask BBox: [133, 4, 149, 17]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3657
  Bounding Box: [0.00, 1822.40, 79.10, 2040.00]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [4, 147, 9, 162]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3652
  Bounding Box: [944.00, 12.20, 1049.60, 154.20]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [78, 5, 85, 16]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3647
  Bounding Box: [1048.80, 0.00, 1156.00, 65.80]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [86, 4, 93, 8]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3630
  Bounding Box: [426.40, 1335.20, 530.40, 1461.60]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [38, 109, 45, 118]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3625
  Bounding Box: [1507.20, 761.60, 1612.80, 872.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [122, 64, 129, 72]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3616
  Bounding Box: [557.20, 1036.00, 750.00, 1272.80]
  Mask Area: 192 pixels
  Mask Ratio: 0.0068
  Mask BBox: [48, 85, 62, 103]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3604
  Bounding Box: [408.80, 268.80, 603.20, 483.20]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [36, 25, 51, 41]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3601
  Bounding Box: [968.80, 522.40, 1245.60, 707.20]
  Mask Area: 248 pixels
  Mask Ratio: 0.0088
  Mask BBox: [80, 45, 101, 59]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3599
  Bounding Box: [1550.40, 1240.80, 1697.60, 1354.40]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [126, 101, 136, 109]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3567
  Bounding Box: [341.20, 1570.40, 514.00, 1676.80]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [31, 127, 44, 134]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3567
  Bounding Box: [1776.00, 1452.00, 1974.40, 1750.40]
  Mask Area: 286 pixels
  Mask Ratio: 0.0101
  Mask BBox: [143, 118, 158, 140]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [1595.20, 150.80, 1729.60, 339.60]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [129, 16, 139, 28]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [1303.20, 1755.20, 1509.60, 1928.00]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [106, 142, 121, 154]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3550
  Bounding Box: [1636.80, 0.00, 1809.60, 150.60]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [132, 3, 145, 15]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3525
  Bounding Box: [1033.60, 1995.20, 1195.20, 2048.00]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [85, 160, 97, 163]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3513
  Bounding Box: [695.20, 1924.80, 847.20, 2048.00]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [59, 155, 70, 164]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3501
  Bounding Box: [1342.40, 1173.60, 1492.80, 1376.80]
  Mask Area: 157 pixels
  Mask Ratio: 0.0056
  Mask BBox: [109, 96, 120, 111]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3486
  Bounding Box: [1665.60, 1557.60, 1793.60, 1654.40]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [135, 126, 144, 132]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3486
  Bounding Box: [1665.60, 1583.20, 1793.60, 1680.00]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [135, 128, 144, 135]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3481
  Bounding Box: [204.80, 1550.40, 256.00, 1640.00]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [20, 126, 23, 132]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3481
  Bounding Box: [1410.40, 7.20, 1612.80, 149.60]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [115, 5, 129, 15]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3467
  Bounding Box: [216.40, 1275.20, 345.60, 1444.80]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [21, 104, 30, 116]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3462
  Bounding Box: [1872.00, 986.40, 2025.60, 1280.80]
  Mask Area: 246 pixels
  Mask Ratio: 0.0087
  Mask BBox: [151, 82, 162, 104]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3459
  Bounding Box: [1508.80, 1544.80, 1609.60, 1638.40]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [122, 125, 129, 131]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3438
  Bounding Box: [1479.20, 1247.20, 1686.40, 1367.20]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [120, 102, 135, 110]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3433
  Bounding Box: [1402.40, 679.60, 1543.20, 778.40]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [114, 58, 124, 64]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3433
  Bounding Box: [1707.20, 899.20, 1816.00, 971.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [138, 75, 145, 79]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3394
  Bounding Box: [178.80, 700.80, 310.40, 897.60]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [18, 59, 28, 74]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3394
  Bounding Box: [1942.40, 1371.20, 2048.00, 1568.00]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [156, 112, 164, 126]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3389
  Bounding Box: [1918.40, 1018.40, 2027.20, 1192.80]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [154, 84, 162, 97]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3381
  Bounding Box: [528.40, 248.00, 827.20, 624.00]
  Mask Area: 476 pixels
  Mask Ratio: 0.0169
  Mask BBox: [46, 24, 68, 52]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3379
  Bounding Box: [1173.60, 1694.40, 1348.00, 1963.20]
  Mask Area: 204 pixels
  Mask Ratio: 0.0072
  Mask BBox: [96, 137, 109, 157]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3372
  Bounding Box: [1370.40, 700.80, 1520.80, 838.40]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [112, 59, 122, 69]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3372
  Bounding Box: [1566.40, 180.40, 1713.60, 443.60]
  Mask Area: 159 pixels
  Mask Ratio: 0.0056
  Mask BBox: [127, 19, 137, 38]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3367
  Bounding Box: [14.90, 1578.40, 127.70, 1720.00]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [6, 128, 13, 138]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3364
  Bounding Box: [1414.40, 632.40, 1536.00, 735.60]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [115, 54, 123, 61]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3364
  Bounding Box: [225.80, 897.60, 322.20, 995.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [23, 75, 29, 81]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3354
  Bounding Box: [577.60, 1979.20, 676.00, 2046.40]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [50, 159, 56, 163]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3333
  Bounding Box: [1676.80, 383.60, 1766.40, 542.00]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [135, 34, 141, 46]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3328
  Bounding Box: [1130.40, 301.20, 1352.80, 490.00]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [93, 28, 109, 42]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3325
  Bounding Box: [1824.00, 1556.80, 2048.00, 1870.40]
  Mask Area: 361 pixels
  Mask Ratio: 0.0128
  Mask BBox: [147, 126, 164, 150]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3320
  Bounding Box: [592.00, 1185.60, 736.00, 1443.20]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [51, 97, 61, 116]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3315
  Bounding Box: [1296.80, 1758.40, 1429.60, 1883.20]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [106, 142, 115, 151]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1726.40, 1077.60, 1867.20, 1300.00]
  Mask Area: 172 pixels
  Mask Ratio: 0.0061
  Mask BBox: [139, 89, 149, 105]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3281
  Bounding Box: [1155.20, 1480.80, 1344.00, 1718.40]
  Mask Area: 217 pixels
  Mask Ratio: 0.0077
  Mask BBox: [95, 120, 108, 138]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3279
  Bounding Box: [524.80, 1560.00, 685.60, 1694.40]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [45, 126, 57, 136]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3274
  Bounding Box: [1883.20, 1881.60, 2033.60, 2048.00]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [152, 151, 162, 165]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3271
  Bounding Box: [362.80, 1929.60, 482.80, 2048.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [33, 155, 41, 163]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [1528.80, 742.00, 1616.00, 824.00]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [124, 62, 130, 68]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [1528.80, 767.60, 1616.00, 849.60]
  Mask Area: 19 pixels
  Mask Ratio: 0.0007
  Mask BBox: [124, 64, 127, 70]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3252
  Bounding Box: [559.60, 1985.60, 648.40, 2043.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [48, 160, 54, 163]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3237
  Bounding Box: [1508.00, 738.40, 1584.80, 823.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [122, 62, 127, 68]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3235
  Bounding Box: [682.00, 1697.60, 829.60, 1822.40]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [58, 137, 68, 146]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3230
  Bounding Box: [8.30, 1486.40, 67.80, 1600.00]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [5, 121, 8, 128]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [1160.00, 26.80, 1364.80, 146.00]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [95, 7, 110, 15]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3208
  Bounding Box: [929.60, 1979.20, 1043.20, 2043.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [77, 159, 85, 163]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3201
  Bounding Box: [184.40, 1710.40, 392.00, 1960.00]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [19, 138, 34, 157]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3184
  Bounding Box: [1544.00, 1330.40, 1720.00, 1509.60]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [125, 108, 138, 121]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3179
  Bounding Box: [1052.00, 6.40, 1151.20, 93.80]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [87, 5, 93, 9]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3179
  Bounding Box: [503.20, 302.80, 757.60, 573.20]
  Mask Area: 346 pixels
  Mask Ratio: 0.0123
  Mask BBox: [44, 28, 63, 48]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3169
  Bounding Box: [1491.20, 145.40, 1705.60, 324.20]
  Mask Area: 159 pixels
  Mask Ratio: 0.0056
  Mask BBox: [121, 16, 137, 27]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3169
  Bounding Box: [1512.00, 1750.40, 1828.80, 2019.20]
  Mask Area: 386 pixels
  Mask Ratio: 0.0137
  Mask BBox: [123, 141, 146, 161]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.3152
  Bounding Box: [1359.20, 1851.20, 1578.40, 2027.20]
  Mask Area: 184 pixels
  Mask Ratio: 0.0065
  Mask BBox: [111, 149, 127, 162]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.3145
  Bounding Box: [1964.80, 1385.60, 2041.60, 1563.20]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [158, 113, 163, 126]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.3137
  Bounding Box: [1696.00, 1022.40, 1907.20, 1300.80]
  Mask Area: 290 pixels
  Mask Ratio: 0.0103
  Mask BBox: [137, 84, 152, 105]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.3132
  Bounding Box: [1904.00, 652.40, 2048.00, 938.40]
  Mask Area: 257 pixels
  Mask Ratio: 0.0091
  Mask BBox: [153, 55, 164, 77]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.3130
  Bounding Box: [1558.40, 1564.00, 1788.80, 1732.80]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [126, 127, 143, 139]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.3120
  Bounding Box: [524.40, 1460.00, 678.80, 1575.20]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [45, 119, 57, 127]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.3115
  Bounding Box: [898.40, 1974.40, 1020.00, 2032.00]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [75, 159, 83, 162]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.3108
  Bounding Box: [351.00, 1362.40, 489.20, 1535.20]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [32, 111, 42, 123]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.3103
  Bounding Box: [911.20, 201.00, 1037.60, 399.20]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [76, 20, 85, 35]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.3101
  Bounding Box: [316.80, 661.60, 497.60, 877.60]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [29, 56, 42, 72]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.3091
  Bounding Box: [1499.20, 880.80, 1606.40, 999.20]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [122, 73, 129, 82]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.3079
  Bounding Box: [200.00, 812.80, 324.40, 945.60]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [20, 68, 29, 77]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.3079
  Bounding Box: [1864.00, 285.40, 1969.60, 375.20]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [150, 27, 157, 33]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [527.60, 1323.20, 630.00, 1441.60]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [46, 108, 53, 116]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.3040
  Bounding Box: [1968.00, 18.80, 2048.00, 156.80]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [158, 6, 163, 16]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.3037
  Bounding Box: [1553.60, 466.40, 1697.60, 693.60]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [126, 41, 134, 58]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [1276.00, 714.40, 1384.80, 824.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [104, 60, 112, 68]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.3003
  Bounding Box: [1528.80, 881.60, 1630.40, 1000.00]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [124, 73, 131, 82]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.2998
  Bounding Box: [380.00, 469.20, 479.20, 591.60]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [34, 41, 41, 50]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.2998
  Bounding Box: [1948.80, 49.85, 2048.00, 213.00]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [157, 8, 164, 20]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.2998
  Bounding Box: [1528.00, 383.60, 1688.00, 696.40]
  Mask Area: 197 pixels
  Mask Ratio: 0.0070
  Mask BBox: [124, 34, 135, 58]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.2988
  Bounding Box: [9.40, 789.60, 94.60, 954.40]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [5, 66, 11, 78]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.2983
  Bounding Box: [24.65, 1641.60, 149.40, 1811.20]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [6, 133, 15, 145]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.2981
  Bounding Box: [576.80, 309.20, 844.00, 710.80]
  Mask Area: 515 pixels
  Mask Ratio: 0.0182
  Mask BBox: [50, 29, 69, 59]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2979
  Bounding Box: [802.40, 740.40, 957.60, 884.80]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [67, 62, 78, 73]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2964
  Bounding Box: [1910.40, 1806.40, 2035.20, 1944.00]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [154, 146, 162, 155]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2961
  Bounding Box: [1790.40, 279.00, 1908.80, 401.20]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [144, 26, 153, 35]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2959
  Bounding Box: [1747.20, 85.10, 1968.00, 290.80]
  Mask Area: 186 pixels
  Mask Ratio: 0.0066
  Mask BBox: [141, 11, 157, 26]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2959
  Bounding Box: [554.40, 1050.40, 696.00, 1333.60]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [48, 87, 58, 108]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2954
  Bounding Box: [858.40, 1457.60, 946.40, 1547.20]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [72, 118, 77, 124]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2937
  Bounding Box: [387.60, 1593.60, 538.00, 1705.60]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [35, 129, 46, 137]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2898
  Bounding Box: [1040.80, 0.00, 1135.20, 53.10]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [86, 3, 92, 8]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2898
  Bounding Box: [1066.40, 0.00, 1160.80, 53.10]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [88, 3, 92, 8]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2886
  Bounding Box: [1819.20, 235.00, 1995.20, 380.00]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [147, 23, 159, 33]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2881
  Bounding Box: [422.80, 657.20, 625.20, 961.60]
  Mask Area: 265 pixels
  Mask Ratio: 0.0094
  Mask BBox: [38, 56, 52, 79]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2876
  Bounding Box: [1222.40, 1548.80, 1435.20, 1734.40]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [100, 125, 116, 139]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2869
  Bounding Box: [384.40, 502.40, 479.60, 617.60]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [35, 44, 41, 51]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2854
  Bounding Box: [1396.00, 654.00, 1560.80, 798.40]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [114, 56, 125, 66]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2852
  Bounding Box: [8.00, 1459.20, 61.20, 1572.80]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [5, 118, 7, 126]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2852
  Bounding Box: [625.20, 1980.80, 722.80, 2038.40]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [53, 159, 59, 163]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2842
  Bounding Box: [1321.60, 1590.40, 1420.80, 1696.00]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [108, 129, 114, 136]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2832
  Bounding Box: [964.80, 544.00, 1108.80, 811.20]
  Mask Area: 198 pixels
  Mask Ratio: 0.0070
  Mask BBox: [80, 47, 90, 67]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2832
  Bounding Box: [1282.40, 1350.40, 1525.60, 1569.60]
  Mask Area: 231 pixels
  Mask Ratio: 0.0082
  Mask BBox: [105, 110, 123, 126]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2830
  Bounding Box: [501.60, 413.20, 682.40, 544.40]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [44, 37, 57, 46]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [1444.00, 1796.80, 1659.20, 2017.60]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [117, 145, 129, 161]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2820
  Bounding Box: [837.60, 1606.40, 1061.60, 1833.60]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [70, 130, 86, 147]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2769
  Bounding Box: [1950.40, 162.00, 2046.40, 320.80]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [157, 17, 163, 29]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2759
  Bounding Box: [110.60, 437.60, 394.40, 652.00]
  Mask Area: 262 pixels
  Mask Ratio: 0.0093
  Mask BBox: [13, 39, 34, 54]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2756
  Bounding Box: [1.15, 807.20, 81.60, 1004.00]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [5, 68, 10, 82]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2751
  Bounding Box: [1292.00, 1363.20, 1426.40, 1518.40]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [105, 111, 115, 122]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2751
  Bounding Box: [0.00, 972.80, 91.40, 1134.40]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [4, 80, 8, 92]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2747
  Bounding Box: [840.00, 1592.00, 950.40, 1688.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [70, 129, 78, 135]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2737
  Bounding Box: [1551.20, 1974.40, 1664.00, 2041.60]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [126, 159, 133, 163]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2737
  Bounding Box: [1742.40, 1302.40, 1982.40, 1459.20]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [141, 106, 158, 117]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2727
  Bounding Box: [1309.60, 19.75, 1381.60, 113.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [107, 6, 111, 12]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2727
  Bounding Box: [1309.60, 45.35, 1381.60, 138.80]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [107, 8, 111, 14]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2727
  Bounding Box: [1056.00, 16.35, 1360.00, 211.80]
  Mask Area: 223 pixels
  Mask Ratio: 0.0079
  Mask BBox: [87, 6, 110, 20]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2725
  Bounding Box: [857.60, 1476.00, 1003.20, 1576.80]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [71, 120, 82, 127]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2720
  Bounding Box: [1891.20, 1275.20, 2022.40, 1384.00]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [152, 104, 161, 112]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2717
  Bounding Box: [5.80, 301.00, 176.80, 435.60]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [5, 28, 17, 38]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2710
  Bounding Box: [1993.60, 1555.20, 2041.60, 1683.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [160, 126, 163, 135]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2710
  Bounding Box: [588.80, 510.80, 783.20, 710.80]
  Mask Area: 190 pixels
  Mask Ratio: 0.0067
  Mask BBox: [50, 44, 64, 59]

