#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多模态检测脚本
验证multimodal_detect.py的基本功能
"""

import os
import sys
from pathlib import Path
import torch
import cv2
import numpy as np

# 定义视频格式
VID_FORMATS = "asf", "avi", "gif", "m4v", "mkv", "mov", "mp4", "mpeg", "mpg", "ts", "wmv", "webm"

# 添加项目根目录到路径
FILE = Path(__file__).resolve()
ROOT = FILE.parents[0]
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

def create_test_images():
    """创建测试用的RGB和XPL图像"""
    # 创建测试目录
    test_dir = ROOT / 'test_multimodal_data'
    rgb_dir = test_dir / 'rgb'
    xpl_dir = test_dir / 'xpl'
    
    rgb_dir.mkdir(parents=True, exist_ok=True)
    xpl_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建测试图像
    for i in range(3):
        # RGB图像 - 彩色渐变
        rgb_img = np.zeros((480, 640, 3), dtype=np.uint8)
        rgb_img[:, :, 0] = np.linspace(0, 255, 640).astype(np.uint8)  # 红色渐变
        rgb_img[:, :, 1] = np.linspace(0, 255, 480).reshape(-1, 1).astype(np.uint8)  # 绿色渐变
        rgb_img[:, :, 2] = 128  # 蓝色固定值
        
        # 添加一些简单的几何形状作为目标
        cv2.rectangle(rgb_img, (100 + i*50, 100), (200 + i*50, 200), (255, 255, 255), -1)
        cv2.circle(rgb_img, (320, 240 + i*50), 50, (0, 255, 0), -1)
        
        # XPL图像 - 灰度图转换为3通道
        xpl_img = np.zeros((480, 640, 3), dtype=np.uint8)
        # 创建不同的纹理模式
        x, y = np.meshgrid(np.arange(640), np.arange(480))
        pattern = (np.sin(x/20) * np.cos(y/20) * 127 + 128).astype(np.uint8)
        xpl_img[:, :, 0] = pattern
        xpl_img[:, :, 1] = pattern
        xpl_img[:, :, 2] = pattern
        
        # 添加对应的几何形状
        cv2.rectangle(xpl_img, (100 + i*50, 100), (200 + i*50, 200), (200, 200, 200), -1)
        cv2.circle(xpl_img, (320, 240 + i*50), 50, (150, 150, 150), -1)
        
        # 保存图像
        cv2.imwrite(str(rgb_dir / f'test_{i:03d}.jpg'), rgb_img)
        cv2.imwrite(str(xpl_dir / f'test_{i:03d}.jpg'), xpl_img)
    
    print(f"测试图像已创建在: {test_dir}")
    print(f"RGB图像: {len(list(rgb_dir.glob('*.jpg')))} 张")
    print(f"XPL图像: {len(list(xpl_dir.glob('*.jpg')))} 张")
    
    return rgb_dir, xpl_dir

def test_multimodal_dataloader():
    """测试多模态数据加载器"""
    print("\n=== 测试多模态数据加载器 ===")
    
    # 创建测试图像
    rgb_dir, xpl_dir = create_test_images()
    
    try:
        # 导入多模态检测脚本中的数据加载器
        from multimodal_detect import LoadDualModalImages
        
        # 创建数据加载器
        dataset = LoadDualModalImages(
            rgb_path=str(rgb_dir),
            xpl_path=str(xpl_dir),
            img_size=640,
            stride=32
        )
        
        print(f"数据集大小: {len(dataset)}")
        
        # 测试加载第一个样本
        for i, (path, im, im0_shape, vid_cap) in enumerate(dataset):
            rgb_path, xpl_path = path
            rgb_im, xpl_im = im
            
            print(f"样本 {i+1}:")
            print(f"  RGB路径: {rgb_path}")
            print(f"  XPL路径: {xpl_path}")
            print(f"  RGB形状: {rgb_im.shape}")
            print(f"  XPL形状: {xpl_im.shape}")
            print(f"  原始形状: {im0_shape}")
            
            # 只测试第一个样本
            if i == 0:
                break
        
        print("✓ 数据加载器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 数据加载器测试失败: {e}")
        return False

def test_model_loading():
    """测试模型加载"""
    print("\n=== 测试模型加载 ===")
    
    try:
        from models.common import DetectMultiBackend
        
        # 查找可用的权重文件
        weight_files = [
            'yolov5n-mid-seg.pt',
            'yolov5s.pt',
            'yolov5n.pt'
        ]
        
        weight_path = None
        for weight_file in weight_files:
            if (ROOT / weight_file).exists():
                weight_path = ROOT / weight_file
                break
        
        if weight_path is None:
            print("✗ 未找到可用的权重文件")
            return False
        
        print(f"使用权重文件: {weight_path}")
        
        # 加载模型
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = DetectMultiBackend(weight_path, device=device)
        
        print(f"模型设备: {device}")
        print(f"模型步长: {model.stride}")
        print(f"类别数量: {len(model.names)}")
        print(f"类别名称: {list(model.names.values())[:5]}...")  # 显示前5个类别
        
        print("✓ 模型加载测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 模型加载测试失败: {e}")
        return False

def test_multimodal_inference():
    """测试多模态推理"""
    print("\n=== 测试多模态推理 ===")
    
    try:
        # 创建测试图像
        rgb_dir, xpl_dir = create_test_images()
        
        # 创建简单的测试输入
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建虚拟的多模态输入
        rgb_tensor = torch.randn(1, 3, 640, 640).to(device)
        xpl_tensor = torch.randn(1, 3, 640, 640).to(device)
        multimodal_input = (rgb_tensor, xpl_tensor)
        
        print(f"RGB输入形状: {rgb_tensor.shape}")
        print(f"XPL输入形状: {xpl_tensor.shape}")
        print(f"输入设备: {device}")
        
        # 检查InputRouter是否正常工作
        from models.router import InputRouter
        
        rgb_router = InputRouter('RGB')
        xpl_router = InputRouter('X')
        
        rgb_output = rgb_router(multimodal_input)
        xpl_output = xpl_router(multimodal_input)
        
        print(f"RGB路由输出形状: {rgb_output.shape}")
        print(f"XPL路由输出形状: {xpl_output.shape}")
        
        print("✓ 多模态推理测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 多模态推理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试多模态检测功能...")
    print(f"项目根目录: {ROOT}")
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    # 运行测试
    tests = [
        test_multimodal_dataloader,
        test_model_loading,
        test_multimodal_inference
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        if test_func():
            passed += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！多模态检测功能正常")
    else:
        print("⚠️  部分测试失败，请检查相关功能")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)