# Parameters
nc: 80
depth_multiple: 0.33
width_multiple: 0.25
anchors:
  - [10,13, 16,30, 33,23]
  - [30,61, 62,45, 59,119]
  - [116,90, 156,198, 373,326]

# Backbone
backbone:
  # ====== 1. 输入层 ======
  - [-1, 1, InputRouter, ['RGB']]                # 0: RGB 输入
  - [-1, 1, InputRouter, ['X']]                  # 1: X   输入

  # ====== 2. RGB 独立主干 (从 P1 到 P5+SPPF) ======
  - [0, 1, Conv, [64, 3, 2]]                     # 2: P1/2
  - [-1, 1, Conv, [128, 3, 2]]                   # 3: P2/4
  - [-1, 3, C3,  [128]]
  - [-1, 1, Conv, [256, 3, 2]]                   # 5: P3/8
  - [-1, 6, C3,  [256]]                          # 6: === RGB P3/8 输出 (待融合) ===
  - [-1, 1, Conv, [512, 3, 2]]                   # 7: P4/16
  - [-1, 6, C3,  [512]]                          # 8: === RGB P4/16 输出 (待融合) ===
  - [-1, 1, Conv, [1024, 3, 2]]                  # 9: P5/32
  - [-1, 3, C3,  [1024]]
  - [-1, 1, SPPF, [1024, 5]]                     # 11: === RGB SPPF 输出 (待融合) ===

  # ====== 3. X 独立主干 (从 P1 到 P5+SPPF) ======
  - [1, 1, Conv, [64, 3, 2]]                     # 12: P1/2 (from=1, X 输入)
  - [-1, 1, Conv, [128, 3, 2]]                   # 13: P2/4
  - [-1, 3, C3,  [128]]
  - [-1, 1, Conv, [256, 3, 2]]                   # 15: P3/8
  - [-1, 6, C3,  [256]]                          # 16: === X P3/8 输出 (待融合) ===
  - [-1, 1, Conv, [512, 3, 2]]                   # 17: P4/16
  - [-1, 6, C3,  [512]]                          # 18: === X P4/16 输出 (待融合) ===
  - [-1, 1, Conv, [1024, 3, 2]]                  # 19: P5/32
  - [-1, 3, C3,  [1024]]
  - [-1, 1, SPPF, [1024, 5]]                     # 21: === X SPPF 输出 (待融合) ===

  # ====== 4. 延迟融合区 (Late Fusion Stage) ======
  # --- P3 融合 ---
  - [[6, 16], 1, Concat, [1]]                    # 22: Concat(RGB_P3, X_P3), ch=256+256=512
  - [-1, 3, C3, [256]]                           # 23: === Fused P3/8 输出 (送入 Head) ===

  # --- P4 融合 ---
  - [[8, 18], 1, Concat, [1]]                    # 24: Concat(RGB_P4, X_P4), ch=512+512=1024
  - [-1, 3, C3, [512]]                           # 25: === Fused P4/16 输出 (送入 Head) ===

  # --- P5 (SPPF后) 融合 ---
  - [[11, 21], 1, Concat, [1]]                   # 26: Concat(RGB_SPPF, X_SPPF), ch=1024+1024=2048
  - [-1, 1, Conv, [1024, 1, 1]]                  # 27: === Fused P5/32 输出 (送入 Head), 使用 1x1 Conv 降维和融合 ===

# Head (Neck + Detection/Segmentation Head)
head:
  # ------ Neck (PANet 结构) ------
  # 从 P5 开始上采样，并与 P4 融合
  - [27, 1, Conv, [512, 1, 1]]                   # 28: from Fused P5
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 25], 1, Concat, [1]]                   # 30: Concat with Fused P4 (来自层 25)
  - [-1, 3, C3, [512, False]]

  # 继续上采样，并与 P3 融合
  - [-1, 1, Conv, [256, 1, 1]]
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 23], 1, Concat, [1]]                   # 34: Concat with Fused P3 (来自层 23)
  - [-1, 3, C3, [256, False]]                    # 35 (P3/8 output)

  # 下采样路径
  - [-1, 1, Conv, [256, 3, 2]]
  - [[-1, 31], 1, Concat, [1]]                   # 37: Concat with Neck P4 (来自层 31)
  - [-1, 3, C3, [512, False]]                    # 38 (P4/16 output)

  - [-1, 1, Conv, [512, 3, 2]]
  - [[-1, 28], 1, Concat, [1]]                   # 40: Concat with Head P5 (来自层 28)
  - [-1, 3, C3, [1024, False]]                   # 41 (P5/32 output)

  # ------ Detection/Segmentation Head ------
  - [[35, 38, 41], 1, Segment, [nc, anchors, 32, 256]] # 42: 使用新的P3,P4,P5输出