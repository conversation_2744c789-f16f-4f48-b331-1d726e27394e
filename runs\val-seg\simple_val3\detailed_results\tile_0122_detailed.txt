Image: tile_0122.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8579
  Bounding Box: [731.20, 1147.20, 1036.80, 1556.80]
  Mask Area: 592 pixels
  Mask Ratio: 0.0210
  Mask BBox: [62, 94, 84, 125]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8335
  Bounding Box: [1702.40, 6.60, 1952.00, 270.80]
  Mask Area: 304 pixels
  Mask Ratio: 0.0108
  Mask BBox: [137, 5, 155, 25]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8311
  Bounding Box: [1253.60, 1066.40, 1476.00, 1269.60]
  Mask Area: 181 pixels
  Mask Ratio: 0.0064
  Mask BBox: [102, 88, 118, 103]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8311
  Bounding Box: [1870.40, 517.60, 2040.00, 744.80]
  Mask Area: 190 pixels
  Mask Ratio: 0.0067
  Mask BBox: [151, 45, 163, 61]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8271
  Bounding Box: [856.80, 1755.20, 1023.20, 2048.00]
  Mask Area: 241 pixels
  Mask Ratio: 0.0085
  Mask BBox: [71, 142, 83, 164]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8271
  Bounding Box: [812.80, 620.80, 1097.60, 953.60]
  Mask Area: 376 pixels
  Mask Ratio: 0.0133
  Mask BBox: [68, 53, 89, 78]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8218
  Bounding Box: [916.80, 516.40, 1040.00, 687.60]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [76, 45, 85, 57]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8218
  Bounding Box: [1803.20, 748.80, 1979.20, 950.40]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [145, 63, 157, 77]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8188
  Bounding Box: [626.00, 1745.60, 812.80, 1940.80]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [53, 141, 67, 155]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8101
  Bounding Box: [1320.80, 833.60, 1520.80, 963.20]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [108, 70, 122, 79]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8076
  Bounding Box: [1841.60, 297.60, 2011.20, 470.40]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [148, 28, 160, 40]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.7964
  Bounding Box: [1420.00, 707.60, 1584.80, 875.20]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [115, 60, 126, 72]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.7954
  Bounding Box: [297.60, 504.00, 458.40, 669.60]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [28, 44, 39, 55]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.7949
  Bounding Box: [1076.80, 1636.80, 1198.40, 1822.40]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [89, 132, 96, 146]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7939
  Bounding Box: [1876.80, 1092.00, 2048.00, 1335.20]
  Mask Area: 225 pixels
  Mask Ratio: 0.0080
  Mask BBox: [151, 90, 164, 108]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7935
  Bounding Box: [1836.80, 1286.40, 2035.20, 1529.60]
  Mask Area: 242 pixels
  Mask Ratio: 0.0086
  Mask BBox: [148, 105, 162, 123]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7930
  Bounding Box: [795.20, 1540.00, 1112.00, 1779.20]
  Mask Area: 357 pixels
  Mask Ratio: 0.0126
  Mask BBox: [67, 125, 89, 142]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7900
  Bounding Box: [1724.80, 1627.20, 1923.20, 1790.40]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [139, 132, 154, 143]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7803
  Bounding Box: [308.80, 841.60, 440.80, 1057.60]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [29, 70, 38, 86]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7798
  Bounding Box: [1432.80, 8.50, 1616.00, 222.40]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [116, 5, 130, 21]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7793
  Bounding Box: [19.40, 9.20, 327.00, 501.60]
  Mask Area: 654 pixels
  Mask Ratio: 0.0232
  Mask BBox: [6, 5, 29, 43]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7783
  Bounding Box: [556.80, 268.00, 840.00, 659.20]
  Mask Area: 490 pixels
  Mask Ratio: 0.0174
  Mask BBox: [48, 25, 69, 55]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7754
  Bounding Box: [168.00, 1005.60, 290.40, 1189.60]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [18, 83, 26, 96]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7754
  Bounding Box: [1016.00, 1915.20, 1252.80, 2014.40]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [84, 154, 101, 161]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7661
  Bounding Box: [1096.80, 382.40, 1303.20, 659.20]
  Mask Area: 213 pixels
  Mask Ratio: 0.0075
  Mask BBox: [90, 34, 105, 55]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7651
  Bounding Box: [1194.40, 1617.60, 1423.20, 1915.20]
  Mask Area: 304 pixels
  Mask Ratio: 0.0108
  Mask BBox: [98, 131, 115, 153]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7603
  Bounding Box: [674.40, 1550.40, 829.60, 1761.60]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [57, 126, 68, 141]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7603
  Bounding Box: [295.60, 5.60, 582.80, 237.40]
  Mask Area: 278 pixels
  Mask Ratio: 0.0098
  Mask BBox: [28, 5, 49, 21]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7588
  Bounding Box: [511.20, 1638.40, 694.40, 1827.20]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [44, 132, 58, 146]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7583
  Bounding Box: [387.60, 243.60, 602.80, 386.40]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [35, 24, 49, 34]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7559
  Bounding Box: [989.60, 1372.80, 1135.20, 1569.60]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [82, 112, 92, 126]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7554
  Bounding Box: [530.40, 0.80, 642.40, 75.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [46, 5, 54, 9]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7544
  Bounding Box: [547.60, 1453.60, 746.00, 1617.60]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [47, 118, 62, 130]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7529
  Bounding Box: [1360.00, 618.80, 1513.60, 796.80]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [111, 53, 122, 66]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7515
  Bounding Box: [1472.00, 996.80, 1622.40, 1313.60]
  Mask Area: 243 pixels
  Mask Ratio: 0.0086
  Mask BBox: [119, 82, 130, 106]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7471
  Bounding Box: [414.00, 750.00, 563.60, 875.20]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [37, 63, 48, 72]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7466
  Bounding Box: [336.80, 360.40, 480.80, 532.40]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [31, 33, 41, 45]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7441
  Bounding Box: [114.80, 784.80, 318.80, 1013.60]
  Mask Area: 212 pixels
  Mask Ratio: 0.0075
  Mask BBox: [13, 66, 28, 83]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7422
  Bounding Box: [1840.00, 1492.80, 2028.80, 1716.80]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [148, 121, 162, 138]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7422
  Bounding Box: [1568.00, 332.20, 1760.00, 530.40]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [127, 30, 139, 45]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7422
  Bounding Box: [13.70, 1257.60, 213.20, 1512.00]
  Mask Area: 212 pixels
  Mask Ratio: 0.0075
  Mask BBox: [6, 103, 20, 122]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7393
  Bounding Box: [1260.00, 1472.80, 1530.40, 1627.20]
  Mask Area: 182 pixels
  Mask Ratio: 0.0064
  Mask BBox: [103, 120, 123, 131]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7358
  Bounding Box: [1244.80, 1218.40, 1358.40, 1434.40]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [102, 100, 109, 114]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7339
  Bounding Box: [656.80, 56.60, 904.80, 424.80]
  Mask Area: 417 pixels
  Mask Ratio: 0.0148
  Mask BBox: [56, 9, 74, 37]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7285
  Bounding Box: [931.20, 29.20, 1137.60, 442.80]
  Mask Area: 406 pixels
  Mask Ratio: 0.0144
  Mask BBox: [77, 7, 92, 37]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7266
  Bounding Box: [1808.00, 460.80, 1948.80, 652.80]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [146, 40, 156, 54]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7261
  Bounding Box: [1080.80, 1473.60, 1264.80, 1654.40]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [89, 120, 102, 133]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7236
  Bounding Box: [6.80, 846.40, 143.20, 1056.00]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [5, 71, 15, 86]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7222
  Bounding Box: [1910.40, 1819.20, 2041.60, 2014.40]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [154, 147, 163, 161]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7212
  Bounding Box: [1889.60, 1694.40, 2048.00, 1825.60]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [152, 137, 164, 146]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.7207
  Bounding Box: [49.70, 1616.00, 208.40, 1798.40]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [8, 131, 20, 143]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.7173
  Bounding Box: [1495.20, 550.40, 1716.80, 794.40]
  Mask Area: 190 pixels
  Mask Ratio: 0.0067
  Mask BBox: [121, 47, 138, 66]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.7144
  Bounding Box: [1363.20, 1617.60, 1657.60, 1832.00]
  Mask Area: 295 pixels
  Mask Ratio: 0.0105
  Mask BBox: [111, 131, 133, 147]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.7129
  Bounding Box: [1350.40, 1891.20, 1646.40, 2035.20]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [110, 152, 132, 162]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.7124
  Bounding Box: [342.40, 1492.00, 592.00, 1707.20]
  Mask Area: 241 pixels
  Mask Ratio: 0.0085
  Mask BBox: [31, 121, 50, 137]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.7109
  Bounding Box: [1720.00, 371.60, 1822.40, 498.00]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [139, 34, 145, 42]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.7100
  Bounding Box: [1266.40, 1960.00, 1373.60, 2033.60]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [103, 158, 110, 162]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.7046
  Bounding Box: [1042.40, 624.40, 1218.40, 801.60]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [86, 53, 99, 66]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.7021
  Bounding Box: [1633.60, 1514.40, 1777.60, 1683.20]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [132, 123, 142, 135]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6992
  Bounding Box: [0.00, 576.80, 130.60, 684.80]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [4, 50, 13, 56]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6992
  Bounding Box: [1370.40, 1257.60, 1551.20, 1532.80]
  Mask Area: 207 pixels
  Mask Ratio: 0.0073
  Mask BBox: [112, 103, 125, 123]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6987
  Bounding Box: [116.80, 1469.60, 305.60, 1678.40]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [14, 119, 26, 135]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6914
  Bounding Box: [1822.40, 1214.40, 1889.60, 1323.20]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [147, 99, 151, 106]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6914
  Bounding Box: [978.40, 1019.20, 1125.60, 1153.60]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [81, 84, 90, 92]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6880
  Bounding Box: [1181.60, 647.20, 1380.00, 864.80]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [97, 55, 111, 71]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6875
  Bounding Box: [498.80, 1012.80, 880.80, 1355.20]
  Mask Area: 529 pixels
  Mask Ratio: 0.0187
  Mask BBox: [45, 84, 72, 109]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6851
  Bounding Box: [1400.80, 400.00, 1522.40, 579.20]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [114, 36, 122, 49]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6836
  Bounding Box: [1523.20, 103.30, 1718.40, 305.60]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [123, 13, 138, 27]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6782
  Bounding Box: [1838.40, 1926.40, 2017.60, 2041.60]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [148, 155, 161, 163]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6763
  Bounding Box: [200.80, 576.80, 424.00, 839.20]
  Mask Area: 220 pixels
  Mask Ratio: 0.0078
  Mask BBox: [20, 50, 37, 69]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6733
  Bounding Box: [493.60, 1336.80, 746.40, 1482.40]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [43, 109, 61, 119]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6733
  Bounding Box: [1202.40, 59.30, 1402.40, 427.60]
  Mask Area: 345 pixels
  Mask Ratio: 0.0122
  Mask BBox: [98, 9, 113, 37]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6709
  Bounding Box: [6.50, 689.60, 170.80, 854.40]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [5, 58, 17, 70]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6675
  Bounding Box: [462.00, 1180.80, 530.00, 1286.40]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [41, 97, 44, 103]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6602
  Bounding Box: [440.40, 1785.60, 654.80, 1987.20]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [39, 144, 55, 157]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6597
  Bounding Box: [4.05, 1777.60, 205.80, 2008.00]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [5, 143, 20, 160]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6572
  Bounding Box: [591.20, 1905.60, 842.40, 2033.60]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [51, 153, 69, 162]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6548
  Bounding Box: [1582.40, 1704.00, 1742.40, 1937.60]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [128, 138, 140, 153]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6523
  Bounding Box: [356.20, 1094.40, 486.40, 1177.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [32, 90, 41, 95]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6372
  Bounding Box: [1945.60, 61.60, 2038.40, 330.80]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [156, 9, 163, 29]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6348
  Bounding Box: [278.40, 1715.20, 452.00, 1996.80]
  Mask Area: 239 pixels
  Mask Ratio: 0.0085
  Mask BBox: [26, 138, 39, 159]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6270
  Bounding Box: [17.90, 1007.20, 181.60, 1213.60]
  Mask Area: 171 pixels
  Mask Ratio: 0.0061
  Mask BBox: [6, 83, 18, 98]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6260
  Bounding Box: [249.60, 1590.40, 319.60, 1728.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [24, 129, 28, 137]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6235
  Bounding Box: [446.40, 898.40, 608.80, 1093.60]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [39, 75, 51, 87]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6206
  Bounding Box: [1184.80, 1814.40, 1280.80, 1942.40]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [97, 146, 103, 155]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6152
  Bounding Box: [1410.40, 256.00, 1554.40, 449.60]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [115, 24, 125, 39]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.6074
  Bounding Box: [1644.80, 544.40, 1849.60, 848.80]
  Mask Area: 226 pixels
  Mask Ratio: 0.0080
  Mask BBox: [133, 47, 148, 70]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.5967
  Bounding Box: [1.45, 1686.40, 74.00, 1817.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 136, 9, 145]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.5967
  Bounding Box: [1940.80, 780.80, 2036.80, 908.80]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [157, 65, 161, 74]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.5908
  Bounding Box: [1540.00, 1457.60, 1608.00, 1555.20]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [125, 118, 129, 125]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.5889
  Bounding Box: [2.30, 358.80, 103.10, 577.20]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [5, 33, 12, 49]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.5864
  Bounding Box: [405.60, 1705.60, 505.60, 1769.60]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [36, 138, 43, 142]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5811
  Bounding Box: [438.40, 703.20, 566.40, 792.80]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [39, 59, 48, 65]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5796
  Bounding Box: [1982.40, 912.00, 2046.40, 1080.00]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [159, 76, 163, 87]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5781
  Bounding Box: [607.20, 1280.80, 719.20, 1351.20]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [52, 105, 60, 108]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5771
  Bounding Box: [574.40, 710.40, 753.60, 947.20]
  Mask Area: 189 pixels
  Mask Ratio: 0.0067
  Mask BBox: [49, 60, 62, 77]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5649
  Bounding Box: [1886.40, 940.00, 2020.80, 1104.80]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [152, 79, 161, 89]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5601
  Bounding Box: [464.80, 414.40, 635.20, 669.60]
  Mask Area: 199 pixels
  Mask Ratio: 0.0071
  Mask BBox: [41, 37, 53, 56]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5576
  Bounding Box: [1017.60, 950.40, 1094.40, 1033.60]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [84, 79, 88, 84]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5571
  Bounding Box: [1015.20, 1790.40, 1109.60, 1912.00]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [84, 144, 90, 153]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5566
  Bounding Box: [424.80, 709.60, 538.40, 840.80]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [38, 60, 46, 69]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5537
  Bounding Box: [913.60, 0.00, 1000.00, 82.00]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [76, 4, 82, 9]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5488
  Bounding Box: [241.80, 1132.80, 339.80, 1251.20]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [23, 93, 30, 101]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5479
  Bounding Box: [1087.20, 794.40, 1183.20, 890.40]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [89, 67, 96, 72]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5435
  Bounding Box: [1454.40, 16.20, 1680.00, 274.00]
  Mask Area: 258 pixels
  Mask Ratio: 0.0091
  Mask BBox: [118, 6, 135, 25]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5430
  Bounding Box: [1084.00, 1136.80, 1261.60, 1335.20]
  Mask Area: 188 pixels
  Mask Ratio: 0.0067
  Mask BBox: [89, 93, 102, 108]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5405
  Bounding Box: [1921.60, 937.60, 2046.40, 1094.40]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [155, 78, 163, 88]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5391
  Bounding Box: [758.40, 485.20, 929.60, 694.00]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [64, 42, 74, 58]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5356
  Bounding Box: [1513.60, 821.60, 1633.60, 999.20]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [123, 69, 131, 82]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5308
  Bounding Box: [968.00, 421.20, 1145.60, 563.60]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [80, 37, 93, 48]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5229
  Bounding Box: [475.20, 1242.40, 627.20, 1357.60]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [42, 102, 52, 110]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5117
  Bounding Box: [1079.20, 864.80, 1308.00, 1160.80]
  Mask Area: 270 pixels
  Mask Ratio: 0.0096
  Mask BBox: [89, 72, 106, 94]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5098
  Bounding Box: [539.20, 702.80, 697.60, 892.00]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [47, 59, 58, 73]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5088
  Bounding Box: [1572.80, 1542.40, 1627.20, 1616.00]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [127, 125, 130, 129]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5068
  Bounding Box: [1044.80, 1180.80, 1252.80, 1464.00]
  Mask Area: 291 pixels
  Mask Ratio: 0.0103
  Mask BBox: [86, 97, 101, 118]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5020
  Bounding Box: [430.80, 879.20, 559.60, 1024.80]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [38, 74, 47, 84]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.4897
  Bounding Box: [764.80, 711.20, 835.20, 820.00]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [64, 60, 69, 68]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.4893
  Bounding Box: [1854.40, 1864.00, 2046.40, 2043.20]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [149, 150, 163, 163]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.4839
  Bounding Box: [1340.00, 518.80, 1455.20, 630.00]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [109, 45, 117, 53]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.4805
  Bounding Box: [1100.00, 1812.80, 1188.00, 1918.40]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [90, 146, 96, 153]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.4727
  Bounding Box: [1732.80, 1776.00, 1806.40, 1881.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [140, 143, 145, 150]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.4724
  Bounding Box: [465.20, 370.40, 726.00, 693.60]
  Mask Area: 426 pixels
  Mask Ratio: 0.0151
  Mask BBox: [41, 33, 60, 58]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.4712
  Bounding Box: [10.20, 1475.20, 95.00, 1721.60]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [5, 120, 11, 138]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.4707
  Bounding Box: [11.80, 865.60, 160.00, 1166.40]
  Mask Area: 233 pixels
  Mask Ratio: 0.0083
  Mask BBox: [5, 72, 16, 95]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.4683
  Bounding Box: [1608.00, 0.00, 1713.60, 61.00]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [130, 4, 137, 8]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.4543
  Bounding Box: [162.80, 821.60, 434.80, 1029.60]
  Mask Area: 288 pixels
  Mask Ratio: 0.0102
  Mask BBox: [17, 69, 37, 84]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.4480
  Bounding Box: [1697.60, 277.20, 1803.20, 368.80]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [137, 26, 144, 32]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4451
  Bounding Box: [130.20, 1236.00, 221.40, 1330.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [15, 101, 21, 107]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.4446
  Bounding Box: [1260.00, 376.00, 1428.00, 596.00]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [103, 34, 115, 50]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4417
  Bounding Box: [466.40, 1320.00, 756.00, 1550.40]
  Mask Area: 281 pixels
  Mask Ratio: 0.0100
  Mask BBox: [41, 108, 63, 125]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4392
  Bounding Box: [1370.40, 5.75, 1492.00, 177.60]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [112, 5, 120, 17]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4363
  Bounding Box: [107.20, 500.80, 302.80, 731.20]
  Mask Area: 217 pixels
  Mask Ratio: 0.0077
  Mask BBox: [13, 44, 27, 61]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4353
  Bounding Box: [1562.40, 446.80, 1676.80, 537.20]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [127, 39, 134, 45]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4346
  Bounding Box: [315.80, 206.60, 452.00, 307.00]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [29, 21, 39, 27]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4336
  Bounding Box: [359.20, 1389.60, 472.80, 1498.40]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [33, 113, 40, 120]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4292
  Bounding Box: [1820.80, 1416.80, 1891.20, 1511.20]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [147, 115, 151, 122]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4258
  Bounding Box: [1525.60, 796.00, 1648.00, 965.60]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [124, 67, 132, 79]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4219
  Bounding Box: [1090.40, 1505.60, 1224.80, 1777.60]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [90, 122, 99, 142]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4216
  Bounding Box: [1381.60, 1824.00, 1635.20, 2028.80]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [112, 147, 131, 162]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4214
  Bounding Box: [506.40, 0.00, 656.80, 100.20]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [44, 4, 55, 11]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4211
  Bounding Box: [3.95, 558.00, 190.00, 698.00]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [5, 48, 18, 58]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4192
  Bounding Box: [1720.00, 1793.60, 1812.80, 1899.20]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [139, 145, 145, 152]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4158
  Bounding Box: [1523.20, 1281.60, 1744.00, 1558.40]
  Mask Area: 277 pixels
  Mask Ratio: 0.0098
  Mask BBox: [123, 105, 140, 125]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4143
  Bounding Box: [727.60, 568.00, 850.40, 706.40]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [61, 49, 70, 59]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4136
  Bounding Box: [1113.60, 1635.20, 1388.80, 1881.60]
  Mask Area: 301 pixels
  Mask Ratio: 0.0107
  Mask BBox: [91, 132, 112, 150]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4121
  Bounding Box: [356.80, 1268.80, 455.20, 1344.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [32, 104, 39, 108]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4082
  Bounding Box: [1617.60, 355.20, 1825.60, 508.80]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [131, 32, 146, 43]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4070
  Bounding Box: [1136.00, 0.00, 1252.80, 69.70]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [93, 3, 101, 8]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4055
  Bounding Box: [790.40, 21.85, 908.80, 140.20]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [66, 6, 74, 14]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4031
  Bounding Box: [212.80, 1329.60, 392.40, 1539.20]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [21, 108, 34, 124]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.3992
  Bounding Box: [310.00, 364.80, 485.20, 641.60]
  Mask Area: 243 pixels
  Mask Ratio: 0.0086
  Mask BBox: [29, 33, 41, 54]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.3955
  Bounding Box: [1277.60, 0.00, 1388.00, 73.20]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [104, 4, 112, 9]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.3943
  Bounding Box: [1003.20, 1926.40, 1284.80, 2044.80]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [83, 155, 101, 163]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.3933
  Bounding Box: [489.60, 1968.00, 592.00, 2035.20]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [43, 158, 50, 162]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.3914
  Bounding Box: [358.00, 651.20, 461.20, 729.60]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [32, 55, 40, 60]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.3909
  Bounding Box: [1552.00, 386.00, 1731.20, 534.00]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [126, 35, 139, 45]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.3906
  Bounding Box: [478.80, 461.20, 713.20, 727.60]
  Mask Area: 228 pixels
  Mask Ratio: 0.0081
  Mask BBox: [42, 41, 59, 60]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.3896
  Bounding Box: [154.20, 396.40, 321.40, 510.00]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [17, 35, 29, 43]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.3882
  Bounding Box: [1710.40, 489.20, 1809.60, 594.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [138, 43, 145, 50]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.3872
  Bounding Box: [732.00, 508.80, 900.00, 714.40]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [62, 44, 74, 58]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.3862
  Bounding Box: [1525.60, 1309.60, 1699.20, 1508.00]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [124, 107, 136, 121]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.3850
  Bounding Box: [267.80, 1585.60, 350.60, 1713.60]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [25, 128, 31, 137]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.3831
  Bounding Box: [1704.00, 356.40, 1803.20, 487.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [138, 32, 144, 42]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.3826
  Bounding Box: [1755.20, 1563.20, 1976.00, 1771.20]
  Mask Area: 214 pixels
  Mask Ratio: 0.0076
  Mask BBox: [142, 127, 158, 142]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.3821
  Bounding Box: [1702.40, 1971.20, 1817.60, 2035.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [137, 158, 145, 162]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.3816
  Bounding Box: [835.20, 1977.60, 932.80, 2038.40]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [70, 159, 75, 163]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.3804
  Bounding Box: [1664.00, 1037.60, 1763.20, 1165.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [134, 86, 140, 95]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.3777
  Bounding Box: [814.40, 507.60, 1070.40, 679.60]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [68, 44, 87, 57]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.3735
  Bounding Box: [790.40, 1792.00, 867.20, 1955.20]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [66, 144, 71, 156]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.3728
  Bounding Box: [1260.80, 909.60, 1459.20, 1100.00]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [103, 76, 117, 89]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.3721
  Bounding Box: [1977.60, 442.00, 2035.20, 538.00]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [159, 39, 162, 45]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3721
  Bounding Box: [1601.60, 549.20, 1707.20, 634.80]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [130, 47, 136, 53]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3679
  Bounding Box: [1947.20, 453.20, 2048.00, 532.40]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [157, 40, 163, 44]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3669
  Bounding Box: [762.40, 3.75, 842.40, 101.00]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [64, 5, 69, 11]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3667
  Bounding Box: [140.80, 1401.60, 342.40, 1638.40]
  Mask Area: 207 pixels
  Mask Ratio: 0.0073
  Mask BBox: [15, 114, 30, 131]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3652
  Bounding Box: [1604.80, 1096.00, 1713.60, 1316.80]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [130, 90, 137, 106]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3647
  Bounding Box: [1948.80, 742.00, 2035.20, 883.20]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [157, 62, 162, 72]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3643
  Bounding Box: [992.00, 1303.20, 1188.80, 1556.00]
  Mask Area: 235 pixels
  Mask Ratio: 0.0083
  Mask BBox: [82, 106, 96, 125]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3604
  Bounding Box: [221.20, 1249.60, 322.80, 1379.20]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [22, 102, 29, 109]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3599
  Bounding Box: [19.80, 1094.40, 197.80, 1252.80]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [6, 90, 19, 101]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3584
  Bounding Box: [1104.80, 1464.00, 1232.80, 1614.40]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [91, 119, 100, 130]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3523
  Bounding Box: [1021.60, 612.80, 1087.20, 738.40]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [84, 52, 88, 60]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3513
  Bounding Box: [352.00, 272.00, 552.00, 513.60]
  Mask Area: 270 pixels
  Mask Ratio: 0.0096
  Mask BBox: [32, 26, 47, 44]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3508
  Bounding Box: [1343.20, 11.70, 1474.40, 217.20]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [109, 5, 119, 20]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3433
  Bounding Box: [646.40, 1574.40, 833.60, 1881.60]
  Mask Area: 236 pixels
  Mask Ratio: 0.0084
  Mask BBox: [55, 127, 69, 150]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3428
  Bounding Box: [427.60, 716.80, 583.60, 889.60]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [38, 60, 49, 72]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3408
  Bounding Box: [1627.20, 0.00, 1726.40, 50.95]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [132, 3, 138, 7]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3408
  Bounding Box: [1601.60, 3.45, 1700.80, 76.60]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [130, 5, 136, 8]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3408
  Bounding Box: [1627.20, 3.45, 1726.40, 76.60]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [132, 5, 138, 8]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3403
  Bounding Box: [1130.40, 0.00, 1224.80, 66.20]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [93, 4, 99, 9]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3403
  Bounding Box: [0.00, 1984.00, 82.30, 2038.40]
  Mask Area: 21 pixels
  Mask Ratio: 0.0007
  Mask BBox: [4, 159, 8, 163]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3398
  Bounding Box: [711.20, 816.00, 795.20, 923.20]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [60, 68, 66, 75]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3394
  Bounding Box: [476.40, 1950.40, 608.40, 2048.00]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [42, 157, 51, 163]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3376
  Bounding Box: [9.40, 280.00, 73.80, 384.40]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 26, 9, 34]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3311
  Bounding Box: [1894.40, 2.15, 2006.40, 120.20]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [152, 5, 160, 13]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3311
  Bounding Box: [450.40, 1206.40, 580.00, 1336.00]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [40, 99, 49, 108]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1604.80, 0.00, 1697.60, 47.60]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [130, 3, 136, 7]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3296
  Bounding Box: [38.70, 1291.20, 316.00, 1532.80]
  Mask Area: 302 pixels
  Mask Ratio: 0.0107
  Mask BBox: [8, 105, 28, 123]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3293
  Bounding Box: [3.40, 1466.40, 111.40, 1657.60]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [5, 119, 12, 133]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3286
  Bounding Box: [228.00, 1582.40, 309.20, 1720.00]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [22, 128, 28, 138]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3286
  Bounding Box: [253.20, 1614.40, 338.80, 1745.60]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [24, 131, 30, 140]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3286
  Bounding Box: [1534.40, 1420.00, 1609.60, 1567.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [124, 115, 129, 126]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3264
  Bounding Box: [17.50, 592.80, 151.70, 705.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [6, 51, 13, 59]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3262
  Bounding Box: [976.00, 1028.00, 1174.40, 1196.00]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [81, 85, 95, 97]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3247
  Bounding Box: [471.20, 1692.80, 677.60, 1964.80]
  Mask Area: 247 pixels
  Mask Ratio: 0.0088
  Mask BBox: [41, 137, 56, 157]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3245
  Bounding Box: [1084.80, 1200.00, 1340.80, 1444.80]
  Mask Area: 327 pixels
  Mask Ratio: 0.0116
  Mask BBox: [89, 98, 108, 116]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3237
  Bounding Box: [1547.20, 800.80, 1656.00, 920.80]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [125, 67, 133, 75]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3196
  Bounding Box: [1343.20, 391.20, 1519.20, 600.00]
  Mask Area: 165 pixels
  Mask Ratio: 0.0058
  Mask BBox: [109, 35, 122, 50]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3191
  Bounding Box: [1686.40, 1884.80, 1782.40, 1968.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [136, 152, 142, 157]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3167
  Bounding Box: [506.40, 0.00, 641.60, 70.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [44, 3, 54, 9]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3154
  Bounding Box: [304.00, 1060.80, 395.20, 1132.80]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [28, 87, 34, 92]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3140
  Bounding Box: [141.40, 1974.40, 298.20, 2038.40]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [16, 159, 27, 163]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3137
  Bounding Box: [221.80, 1154.40, 321.40, 1279.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [22, 95, 29, 103]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3137
  Bounding Box: [247.40, 1154.40, 347.00, 1279.20]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [24, 95, 31, 103]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3132
  Bounding Box: [1720.00, 796.80, 1812.80, 873.60]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [139, 67, 144, 72]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3127
  Bounding Box: [999.20, 936.80, 1076.00, 1032.80]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [83, 78, 88, 84]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3127
  Bounding Box: [999.20, 962.40, 1076.00, 1058.40]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [83, 80, 88, 84]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3127
  Bounding Box: [1024.80, 962.40, 1101.60, 1058.40]
  Mask Area: 18 pixels
  Mask Ratio: 0.0006
  Mask BBox: [85, 80, 88, 84]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3123
  Bounding Box: [311.20, 1070.40, 432.80, 1168.00]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [29, 88, 37, 95]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3123
  Bounding Box: [224.80, 619.60, 508.00, 833.60]
  Mask Area: 254 pixels
  Mask Ratio: 0.0090
  Mask BBox: [22, 53, 43, 69]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3115
  Bounding Box: [956.80, 1041.60, 1110.40, 1169.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [79, 86, 90, 92]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3110
  Bounding Box: [0.00, 1987.20, 111.20, 2048.00]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [4, 160, 12, 163]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3108
  Bounding Box: [1286.40, 851.20, 1497.60, 1035.20]
  Mask Area: 192 pixels
  Mask Ratio: 0.0068
  Mask BBox: [105, 71, 120, 84]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3062
  Bounding Box: [1380.00, 592.40, 1546.40, 762.80]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [112, 51, 124, 63]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3057
  Bounding Box: [1562.40, 1539.20, 1644.80, 1628.80]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [127, 125, 130, 129]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3054
  Bounding Box: [1407.20, 569.60, 1652.80, 827.20]
  Mask Area: 250 pixels
  Mask Ratio: 0.0089
  Mask BBox: [114, 49, 133, 68]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3044
  Bounding Box: [370.80, 324.40, 513.20, 517.20]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [33, 30, 44, 44]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3042
  Bounding Box: [912.00, 281.80, 995.20, 459.20]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [76, 27, 81, 39]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3030
  Bounding Box: [1031.20, 1809.60, 1165.60, 1918.40]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [85, 146, 95, 153]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3027
  Bounding Box: [1160.00, 0.00, 1272.00, 57.30]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [95, 3, 103, 8]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3027
  Bounding Box: [1160.00, 4.10, 1272.00, 82.90]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [95, 5, 103, 8]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3025
  Bounding Box: [326.00, 1182.40, 458.80, 1339.20]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [30, 97, 39, 108]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3005
  Bounding Box: [1054.40, 1872.00, 1294.40, 2041.60]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [87, 151, 105, 163]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.2998
  Bounding Box: [1105.60, 1622.40, 1225.60, 1788.80]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [91, 131, 99, 143]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.2998
  Bounding Box: [1073.60, 714.80, 1198.40, 885.60]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [88, 60, 96, 73]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.2998
  Bounding Box: [1298.40, 1500.80, 1528.80, 1668.80]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [106, 122, 123, 134]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.2988
  Bounding Box: [1436.80, 440.00, 1550.40, 568.00]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [117, 39, 125, 48]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.2976
  Bounding Box: [543.20, 702.00, 671.20, 839.20]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [47, 59, 54, 69]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.2969
  Bounding Box: [1372.80, 0.00, 1584.00, 225.00]
  Mask Area: 210 pixels
  Mask Ratio: 0.0074
  Mask BBox: [112, 4, 127, 21]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.2961
  Bounding Box: [9.70, 1145.60, 179.60, 1257.60]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [5, 94, 18, 102]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.2952
  Bounding Box: [756.00, 883.20, 935.20, 1030.40]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [64, 73, 77, 83]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.2947
  Bounding Box: [1817.60, 1856.00, 1910.40, 1980.80]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [146, 149, 153, 157]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.2947
  Bounding Box: [521.60, 1523.20, 720.80, 1814.40]
  Mask Area: 253 pixels
  Mask Ratio: 0.0090
  Mask BBox: [45, 123, 60, 145]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.2935
  Bounding Box: [1697.60, 489.60, 1793.60, 572.00]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [137, 43, 144, 48]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.2935
  Bounding Box: [1696.00, 1931.20, 1811.20, 2043.20]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [137, 155, 145, 163]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.2927
  Bounding Box: [1459.20, 994.40, 1577.60, 1120.80]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [118, 82, 127, 90]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.2925
  Bounding Box: [1105.60, 775.20, 1211.20, 887.20]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [91, 65, 98, 73]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.2917
  Bounding Box: [534.80, 1488.80, 724.40, 1651.20]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [46, 121, 60, 132]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.2905
  Bounding Box: [124.90, 519.60, 362.00, 824.80]
  Mask Area: 357 pixels
  Mask Ratio: 0.0126
  Mask BBox: [14, 45, 32, 68]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.2893
  Bounding Box: [1241.60, 1940.80, 1376.00, 2043.20]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [101, 156, 110, 163]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.2891
  Bounding Box: [1976.00, 742.40, 2048.00, 843.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [159, 62, 163, 69]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.2891
  Bounding Box: [377.60, 1059.20, 543.20, 1192.00]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [34, 87, 46, 97]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.2886
  Bounding Box: [542.80, 0.00, 664.40, 57.60]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [47, 3, 55, 8]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.2881
  Bounding Box: [945.60, 496.40, 1070.40, 656.40]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [78, 43, 87, 55]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.2881
  Bounding Box: [412.80, 682.00, 543.20, 778.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [37, 58, 46, 64]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.2866
  Bounding Box: [412.80, 862.40, 535.20, 1008.00]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [37, 72, 45, 82]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.2861
  Bounding Box: [1115.20, 1798.40, 1212.80, 1910.40]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [92, 145, 98, 153]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.2847
  Bounding Box: [1268.00, 1195.20, 1368.80, 1398.40]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [104, 98, 110, 113]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.2847
  Bounding Box: [654.00, 1715.20, 840.80, 1913.60]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [56, 138, 68, 153]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.2842
  Bounding Box: [343.60, 221.80, 580.40, 366.80]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [31, 22, 49, 32]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.2839
  Bounding Box: [1056.00, 1277.60, 1244.80, 1488.80]
  Mask Area: 208 pixels
  Mask Ratio: 0.0074
  Mask BBox: [87, 104, 101, 120]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.2837
  Bounding Box: [1142.40, 1.10, 1241.60, 92.30]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [94, 5, 100, 11]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2837
  Bounding Box: [1278.40, 0.00, 1424.00, 130.00]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [104, 4, 115, 14]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2834
  Bounding Box: [1611.20, 796.80, 1755.20, 955.20]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [130, 67, 141, 78]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2805
  Bounding Box: [10.00, 1716.80, 79.40, 1864.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [5, 139, 9, 149]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2786
  Bounding Box: [538.00, 196.00, 671.60, 280.80]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [47, 20, 56, 25]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2786
  Bounding Box: [450.00, 1064.80, 566.00, 1149.60]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [40, 88, 48, 93]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2786
  Bounding Box: [1368.00, 737.20, 1563.20, 944.80]
  Mask Area: 219 pixels
  Mask Ratio: 0.0078
  Mask BBox: [111, 62, 126, 77]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2771
  Bounding Box: [1830.40, 1905.60, 1987.20, 2020.80]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [147, 153, 159, 161]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2766
  Bounding Box: [833.60, 1953.60, 934.40, 2048.00]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [70, 157, 75, 163]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2759
  Bounding Box: [1816.00, 1894.40, 1947.20, 2038.40]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [146, 152, 156, 163]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2742
  Bounding Box: [1822.40, 480.40, 1979.20, 698.80]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [147, 42, 158, 58]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2737
  Bounding Box: [219.40, 1246.40, 347.00, 1428.80]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [22, 102, 31, 115]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2734
  Bounding Box: [308.60, 1378.40, 465.60, 1504.80]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [29, 112, 40, 121]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2720
  Bounding Box: [1676.80, 1961.60, 1772.80, 2032.00]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [135, 158, 142, 162]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2717
  Bounding Box: [988.80, 1787.20, 1084.80, 1915.20]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [83, 144, 88, 153]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2712
  Bounding Box: [200.40, 1804.80, 296.40, 1913.60]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [20, 145, 26, 153]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2703
  Bounding Box: [1968.00, 723.60, 2048.00, 802.40]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [158, 61, 163, 66]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2700
  Bounding Box: [1415.20, 1819.20, 1575.20, 1944.00]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [115, 147, 127, 155]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2690
  Bounding Box: [895.20, 1764.80, 1095.20, 1969.60]
  Mask Area: 212 pixels
  Mask Ratio: 0.0075
  Mask BBox: [74, 142, 89, 157]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2659
  Bounding Box: [1215.20, 1779.20, 1304.80, 1920.00]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [99, 145, 103, 153]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2646
  Bounding Box: [1484.00, 467.60, 1583.20, 566.80]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [120, 41, 127, 47]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2646
  Bounding Box: [213.80, 502.00, 448.80, 732.40]
  Mask Area: 215 pixels
  Mask Ratio: 0.0076
  Mask BBox: [21, 44, 39, 61]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2644
  Bounding Box: [1995.20, 912.80, 2048.00, 1063.20]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [160, 76, 165, 87]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2644
  Bounding Box: [1283.20, 2.85, 1457.60, 183.60]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [105, 5, 117, 18]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2632
  Bounding Box: [1248.00, 1876.80, 1371.20, 2048.00]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [102, 151, 111, 163]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2625
  Bounding Box: [1540.80, 429.20, 1659.20, 518.00]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [125, 38, 133, 44]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2625
  Bounding Box: [1508.00, 1456.00, 1593.60, 1547.20]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [124, 118, 128, 124]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2625
  Bounding Box: [1508.00, 1481.60, 1593.60, 1572.80]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [123, 120, 128, 126]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2625
  Bounding Box: [1533.60, 1481.60, 1619.20, 1572.80]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [124, 120, 130, 126]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2615
  Bounding Box: [1121.60, 333.40, 1185.60, 411.20]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [92, 31, 96, 36]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2612
  Bounding Box: [1720.00, 1760.00, 1793.60, 1852.80]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [139, 142, 144, 148]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2612
  Bounding Box: [1745.60, 1760.00, 1819.20, 1852.80]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [141, 142, 146, 148]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2605
  Bounding Box: [1972.80, 784.80, 2040.00, 884.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [159, 66, 162, 73]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2605
  Bounding Box: [1998.40, 784.80, 2048.00, 884.00]
  Mask Area: 16 pixels
  Mask Ratio: 0.0006
  Mask BBox: [161, 66, 162, 73]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2605
  Bounding Box: [1972.80, 810.40, 2040.00, 909.60]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [159, 68, 162, 75]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2605
  Bounding Box: [1998.40, 810.40, 2048.00, 909.60]
  Mask Area: 15 pixels
  Mask Ratio: 0.0005
  Mask BBox: [161, 68, 162, 75]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2605
  Bounding Box: [1208.80, 1814.40, 1312.80, 1952.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [99, 146, 106, 156]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2598
  Bounding Box: [1624.00, 808.80, 1774.40, 917.60]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [131, 68, 142, 75]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2588
  Bounding Box: [1923.20, 764.00, 2009.60, 903.20]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [156, 64, 160, 74]

