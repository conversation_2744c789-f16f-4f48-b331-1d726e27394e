# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license
"""下载工具模块。"""

import logging
import subprocess
import urllib
from pathlib import Path

import requests
import torch


def is_url(url, check=True):
    """判断字符串是否为URL，可选择检查其在线存在性，返回布尔值。"""
    try:
        url = str(url)
        result = urllib.parse.urlparse(url)
        assert all([result.scheme, result.netloc])  # 检查是否为url
        return (urllib.request.urlopen(url).getcode() == 200) if check else True  # 检查是否在线存在
    except (AssertionError, urllib.request.HTTPError):
        return False


def gsutil_getsize(url=""):
    """
    使用`gsutil du`返回Google Cloud Storage URL上文件的字节大小。

    如果命令失败或输出为空则返回0。
    """
    output = subprocess.check_output(["gsutil", "du", url], shell=True, encoding="utf-8")
    return int(output.split()[0]) if output else 0


def url_getsize(url="https://ultralytics.com/images/bus.jpg"):
    """返回给定URL上可下载文件的字节大小；如果未找到则默认为-1。"""
    response = requests.head(url, allow_redirects=True)
    return int(response.headers.get("content-length", -1))


def curl_download(url, filename, *, silent: bool = False) -> bool:
    """使用curl从url下载文件到指定文件名。"""
    silent_option = "sS" if silent else ""  # 静默模式
    proc = subprocess.run(
        [
            "curl",
            "-#",
            f"-{silent_option}L",
            url,
            "--output",
            filename,
            "--retry",
            "9",
            "-C",
            "-",
        ]
    )
    return proc.returncode == 0


def safe_download(file, url, url2=None, min_bytes=1e0, error_msg=""):
    """
    如果文件大于最小大小，从URL（或备用URL）下载文件到指定路径。

    删除不完整的下载。
    """
    from utils.segment.general import LOGGER

    file = Path(file)
    assert_msg = f"下载的文件 '{file}' 不存在或大小 < min_bytes={min_bytes}"
    try:  # url1
        LOGGER.info(f"正在下载 {url} 到 {file}...")
        torch.hub.download_url_to_file(url, str(file), progress=LOGGER.level <= logging.INFO)
        assert file.exists() and file.stat().st_size > min_bytes, assert_msg  # 检查
    except Exception as e:  # url2
        if file.exists():
            file.unlink()  # 删除部分下载
        LOGGER.info(f"错误: {e}\n重新尝试 {url2 or url} 到 {file}...")
        # curl下载，失败时重试和恢复
        curl_download(url2 or url, file)
    finally:
        if not file.exists() or file.stat().st_size < min_bytes:  # 检查
            if file.exists():
                file.unlink()  # 删除部分下载
            LOGGER.info(f"错误: {assert_msg}\n{error_msg}")
        LOGGER.info("")


def attempt_download(file, repo="ultralytics/yolov5", release="v7.0"):
    """如果本地未找到文件，从GitHub发布资产或通过直接URL下载文件，支持备份版本。
    """
    from utils.segment.general import LOGGER

    def github_assets(repository, version="latest"):
        """使用GitHub API获取GitHub仓库发布标签和资产名称。"""
        if version != "latest":
            version = f"tags/{version}"  # 例如 tags/v7.0
        response = requests.get(f"https://api.github.com/repos/{repository}/releases/{version}").json()  # github api
        return response["tag_name"], [x["name"] for x in response["assets"]]  # 标签, 资产

    file = Path(str(file).strip().replace("'", ""))
    if not file.exists():
        # 指定URL
        name = Path(urllib.parse.unquote(str(file))).name  # 解码 '%2F' 为 '/' 等
        if str(file).startswith(("http:/", "https:/")):  # 下载
            url = str(file).replace(":/", "://")  # Pathlib将 :// 转为 :/
            file = name.split("?")[0]  # 解析认证 https://url.com/file.txt?auth...
            if Path(file).is_file():
                LOGGER.info(f"在 {file} 本地找到 {url}")  # 文件已存在
            else:
                safe_download(file=file, url=url, min_bytes=1e5)
            return file

        # GitHub资产
        assets = [f"yolov5{size}{suffix}.pt" for size in "nsmlx" for suffix in ("", "6", "-cls", "-seg")]  # 默认
        try:
            tag, assets = github_assets(repo, release)
        except Exception:
            try:
                tag, assets = github_assets(repo)  # 最新发布
            except Exception:
                try:
                    tag = subprocess.check_output("git tag", shell=True, stderr=subprocess.STDOUT).decode().split()[-1]
                except Exception:
                    tag = release

        if name in assets:
            file.parent.mkdir(parents=True, exist_ok=True)  # 创建父目录（如果需要）
            safe_download(
                file,
                url=f"https://github.com/{repo}/releases/download/{tag}/{name}",
                min_bytes=1e5,
                error_msg=f"{file} 缺失，尝试从 https://github.com/{repo}/releases/{tag} 下载",
            )

    return str(file)
