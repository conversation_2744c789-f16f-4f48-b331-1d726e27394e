#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的多模态检测测试脚本
用于验证多模态检测功能的基本实现
"""

import os
import sys
from pathlib import Path
import torch
import numpy as np
import cv2

# 添加项目根目录到路径
FILE = Path(__file__).resolve()
ROOT = FILE.parents[0]  # YOLOv5 root directory
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))  # add ROOT to PATH
ROOT = Path(os.path.relpath(ROOT, Path.cwd()))  # relative

def create_test_images(output_dir="test_images"):
    """创建测试用的RGB和XPL图像"""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    rgb_dir = output_path / "rgb"
    xpl_dir = output_path / "xpl"
    rgb_dir.mkdir(exist_ok=True)
    xpl_dir.mkdir(exist_ok=True)
    
    # 创建3张测试图像
    for i in range(3):
        # RGB图像 - 彩色
        rgb_img = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        rgb_img[:, :, 0] = 100 + i * 50  # 不同的红色通道
        cv2.imwrite(str(rgb_dir / f"test_{i:03d}.jpg"), rgb_img)
        
        # XPL图像 - 灰度转RGB
        xpl_img = np.random.randint(50, 200, (640, 640), dtype=np.uint8)
        xpl_img = cv2.cvtColor(xpl_img, cv2.COLOR_GRAY2RGB)
        cv2.imwrite(str(xpl_dir / f"test_{i:03d}.jpg"), xpl_img)
    
    print(f"测试图像已创建在: {output_path.absolute()}")
    print(f"RGB图像: {len(list(rgb_dir.glob('*.jpg')))} 张")
    print(f"XPL图像: {len(list(xpl_dir.glob('*.jpg')))} 张")
    
    return rgb_dir, xpl_dir

def test_basic_functionality():
    """测试基本功能"""
    print("=== 基本功能测试 ===")
    print(f"项目根目录: {ROOT.absolute()}")
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    print()

def test_model_loading():
    """测试模型加载"""
    print("=== 测试模型加载 ===")
    
    # 查找可用的权重文件
    weight_patterns = [
        "yolov5n-mid-seg.pt",
        "yolov5s.pt", 
        "yolov5n.pt",
        "*.pt"
    ]
    
    weights_found = []
    for pattern in weight_patterns:
        weights = list(ROOT.glob(pattern))
        if weights:
            weights_found.extend(weights)
    
    if weights_found:
        print(f"✓ 找到权重文件: {weights_found[0]}")
        return weights_found[0]
    else:
        print("✗ 未找到权重文件")
        return None

def test_multimodal_input():
    """测试多模态输入处理"""
    print("=== 测试多模态输入处理 ===")
    
    # 创建测试图像
    rgb_dir, xpl_dir = create_test_images()
    
    # 模拟多模态输入
    rgb_img = cv2.imread(str(list(rgb_dir.glob('*.jpg'))[0]))
    xpl_img = cv2.imread(str(list(xpl_dir.glob('*.jpg'))[0]))
    
    # 转换为tensor
    rgb_tensor = torch.from_numpy(rgb_img.transpose(2, 0, 1)).float() / 255.0
    xpl_tensor = torch.from_numpy(xpl_img.transpose(2, 0, 1)).float() / 255.0
    
    # 添加batch维度
    rgb_tensor = rgb_tensor.unsqueeze(0)
    xpl_tensor = xpl_tensor.unsqueeze(0)
    
    print(f"RGB输入形状: {rgb_tensor.shape}")
    print(f"XPL输入形状: {xpl_tensor.shape}")
    
    # 测试多模态输入组合
    multimodal_input = (rgb_tensor, xpl_tensor)
    print(f"多模态输入类型: {type(multimodal_input)}")
    print(f"✓ 多模态输入处理测试通过")
    print()
    
    return rgb_dir, xpl_dir

def run_simple_detection_test():
    """运行简单的检测测试"""
    print("=== 运行简单检测测试 ===")
    
    # 创建测试图像
    rgb_dir, xpl_dir = create_test_images()
    
    # 查找权重文件
    weights = test_model_loading()
    if not weights:
        print("跳过检测测试 - 无可用权重")
        return
    
    try:
        # 尝试运行多模态检测
        cmd = f"python multimodal_detect.py --weights {weights} --source {rgb_dir} --xpl-source {xpl_dir} --save-txt --nosave"
        print(f"运行命令: {cmd}")
        
        # 这里只是模拟，实际运行需要完整的环境
        print("✓ 检测命令构建成功")
        
    except Exception as e:
        print(f"✗ 检测测试失败: {e}")
    
    print()

def main():
    """主函数"""
    print("开始简单多模态检测测试...")
    print()
    
    # 运行各项测试
    test_basic_functionality()
    test_model_loading()
    test_multimodal_input()
    run_simple_detection_test()
    
    print("=== 测试完成 ===")
    print("如果要运行实际检测，请使用:")
    print("python multimodal_detect.py --weights <权重文件> --source <RGB图像目录> --xpl-source <XPL图像目录>")

if __name__ == '__main__':
    main()