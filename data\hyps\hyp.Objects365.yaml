# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

# Objects365 训练超参数
# python train.py --weights yolov5m.pt --data Objects365.yaml --evolve
# 有关超参数演变的详细信息，请参阅超参数演变教程 https://github.com/ultralytics/yolov5#tutorials

lr0: 0.00258 # 初始学习率
lrf: 0.17 # 最终学习率 (lr0 * lrf)
momentum: 0.779 # SGD动量/Adam beta1
weight_decay: 0.00058 # 优化器权重衰减
warmup_epochs: 1.33 # 预热周期
warmup_momentum: 0.86 # 预热初始动量
warmup_bias_lr: 0.0711 # 预热初始偏置学习率
box: 0.0539 # 边界框损失增益
cls: 0.299 # 分类损失增益
cls_pw: 0.825 # 分类BCELoss正样本权重
obj: 0.632 # 目标损失增益
obj_pw: 1.0 # 目标BCELoss正样本权重
iou_t: 0.2 # IoU训练阈值
anchor_t: 3.44 # 锚点倍数阈值
anchors: 3.2 # 每个输出层的锚点数（0表示忽略）
fl_gamma: 0.0 # focal loss gamma (efficientDet默认gamma=1.5)
hsv_h: 0.0188 # 图像HSV-Hue增强 (小数)
hsv_s: 0.704 # 图像HSV-Saturation增强 (小数)
hsv_v: 0.36 # 图像HSV-Value增强 (小数)
degrees: 0.0 # 图像旋转 (+/- 度)
translate: 0.0902 # 图像平移 (+/- 小数)
scale: 0.491 # 图像缩放 (+/- 增益)
shear: 0.0 # 图像剪切 (+/- 度)
perspective: 0.0 # 图像透视变换 (+/- 小数), 范围 0-0.001
flipud: 0.0 # 图像垂直翻转 (概率)
fliplr: 0.5 # 图像水平翻转 (概率)
mosaic: 1.0 # 图像马赛克 (概率)
mixup: 0.0 # 图像混合 (概率)
copy_paste: 0.0 # 分割复制粘贴 (概率)
