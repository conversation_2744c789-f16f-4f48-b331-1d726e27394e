#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的YOLOv5分割模型验证脚本
用于测试训练好的权重并生成可视化图像
同时调试标签输出问题

主要功能:
1. 加载训练好的权重
2. 运行验证过程
3. 生成val_batch0_pred等可视化图像
4. 调试标签输出问题
5. 支持单模态和多模态验证
6. 集成详细的调试分析功能
"""

import argparse
import os
import sys
from pathlib import Path
import torch
import numpy as np
from PIL import Image
import json

# 添加项目根目录到Python路径
FILE = Path(__file__).resolve()
ROOT = FILE.parents[0]
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

from models.common import DetectMultiBackend
from utils.segment.dataloaders import create_dataloader
from utils.segment.general import (LOGGER, check_dataset, check_img_size, check_requirements, 
                           check_suffix, check_yaml, colorstr, increment_path, 
                           non_max_suppression, print_args, scale_boxes, strip_optimizer, xyxy2xywh)
from utils.segment.metrics import ConfusionMatrix, ap_per_class, box_iou
from utils.segment.plots import plot_images_and_masks, output_to_target
from utils.segment.general import process_mask, scale_image
from utils.torch_utils import select_device, smart_inference_mode
from debug_plots import ValidationDebugger, create_debug_visualization


@smart_inference_mode()
def val_run(data,
           weights=None,
           batch_size=32,
           imgsz=640,
           conf_thres=0.001,
           iou_thres=0.6,
           max_det=300,
           task='val',
           device='',
           workers=8,
           single_cls=False,
           augment=False,
           verbose=False,
           save_txt=False,
           save_hybrid=False,
           save_conf=False,
           save_json=False,
           project='runs/val-seg',
           name='exp',
           exist_ok=False,
           half=True,
           dnn=False,
           model=None,
           dataloader=None,
           save_dir=Path(''),
           plots=True,
           callbacks=None,
           compute_loss=None,
           multimodal=False,
           xpl_path=''):
    """
    运行YOLOv5分割模型验证
    
    Args:
        data: 数据集配置文件路径
        weights: 模型权重路径
        batch_size: 批次大小
        imgsz: 图像尺寸
        conf_thres: 置信度阈值
        iou_thres: IoU阈值
        max_det: 最大检测数量
        task: 任务类型
        device: 设备
        workers: 数据加载器工作进程数
        single_cls: 单类别模式
        augment: 测试时增强
        verbose: 详细输出
        save_txt: 保存结果到txt
        save_hybrid: 保存混合标签
        save_conf: 保存置信度
        save_json: 保存JSON结果
        project: 项目目录
        name: 实验名称
        exist_ok: 覆盖现有目录
        half: 半精度推理
        dnn: 使用OpenCV DNN
        model: 预加载的模型
        dataloader: 预加载的数据加载器
        save_dir: 保存目录
        plots: 生成图表
        callbacks: 回调函数
        compute_loss: 损失计算函数
        multimodal: 多模态模式
        xpl_path: XPL图像路径
    
    Returns:
        tuple: (metrics, maps, times)
    """
    # 初始化
    if save_dir == Path(''):
        save_dir = increment_path(Path(project) / name, exist_ok=exist_ok)
    save_dir.mkdir(parents=True, exist_ok=True)
    
    # 设备选择
    device = select_device(device, batch_size=batch_size)
    
    # 加载模型
    if model is None:
        model = DetectMultiBackend(weights, device=device, dnn=dnn, data=data, fp16=half)
        stride, names, pt = model.stride, model.names, model.pt
        imgsz = check_img_size(imgsz, s=stride)
    else:
        stride, names, pt = model.stride, model.names, model.pt
    
    # 数据加载器
    if dataloader is None:
        data = check_dataset(data)
        val_path = data['val']
        dataloader = create_dataloader(val_path,
                                     imgsz,
                                     batch_size,
                                     stride,
                                     single_cls,
                                     augment=augment,
                                     cache=None,
                                     rect=True,
                                     workers=workers,
                                     pad=0.5,
                                     prefix=colorstr('val: '))[0]
    
    # 配置模型
    model.eval()
    if isinstance(model, DetectMultiBackend):
        model.warmup(imgsz=(1 if pt else batch_size, 3, imgsz, imgsz))
    
    # 验证循环
    LOGGER.info(f"开始验证，共 {len(dataloader)} 个批次")
    
    for batch_i, (im, targets, paths, shapes, masks) in enumerate(dataloader):
        if batch_i >= 3:  # 只处理前3个批次进行调试
            break
            
        LOGGER.info(f"\n{colorstr('bright_green', 'bold', f'处理批次 {batch_i}')}")
        
        # 准备输入
        im = im.to(device, non_blocking=True)
        targets = targets.to(device)
        masks = masks.to(device)
        im = im.half() if half else im.float()
        im /= 255
        nb, _, height, width = im.shape
        
        # 推理
        preds, proto = model(im)[:2]
        
        # NMS处理
        preds = non_max_suppression(preds,
                                   conf_thres,
                                   iou_thres,
                                   labels=[],
                                   multi_label=True,
                                   agnostic=single_cls,
                                   max_det=max_det,
                                   nm=32)
        
        # 处理掩码
        if proto is not None:
            for i, pred in enumerate(preds):
                if pred.shape[0]:
                    masks_pred = process_mask(proto[i], pred[:, 6:], pred[:, :4], im.shape[2:], upsample=True)
                    # 将3维掩码展平为2维 (n_detections, height*width)
                    if len(masks_pred.shape) == 3:
                        masks_pred = masks_pred.view(masks_pred.shape[0], -1)
                    pred = torch.cat((pred[:, :6], masks_pred), dim=1)
                    preds[i] = pred
        
        # 转换预测结果
        converted_targets = debug_output_to_target(preds, max_det)
        
        # 准备掩码数据
        plot_masks = []
        if proto is not None:
            for i, pred in enumerate(preds):
                if pred.shape[0] and pred.shape[1] > 6:
                    mask_data = pred[:, 6:].cpu()
                    if mask_data.shape[1] >= height * width:
                        mask_reshaped = mask_data[:, :height*width].view(-1, height, width)
                        plot_masks.extend(mask_reshaped[:15])
                    else:
                        plot_masks.extend(mask_data[:15])
        
        if plot_masks:
            plot_masks = torch.stack(plot_masks[:15])
        else:
            plot_masks = torch.zeros(0, height, width)
        
        # 生成可视化
        if plots and batch_i == 0:
            print(f"\n=== 生成可视化图像 ===\n")
            print(f"图像形状: {im.shape}")
            print(f"目标数量: {len(targets)}")
            print(f"掩码形状: {masks.shape if hasattr(masks, 'shape') else 'N/A'}")
            print(f"转换后目标形状: {converted_targets.shape if hasattr(converted_targets, 'shape') else 'N/A'}")
            print(f"绘图掩码形状: {plot_masks.shape if hasattr(plot_masks, 'shape') else 'N/A'}")
            
            # 生成标签图像
            print(f"生成标签图像: val_batch{batch_i}_labels.jpg")
            try:
                plot_images_and_masks(im, targets, masks, paths, 
                                     save_dir / f'val_batch{batch_i}_labels.jpg', names)
                print(f"✓ 标签图像生成成功")
            except Exception as e:
                print(f"✗ 标签图像生成失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 生成预测图像
            print(f"生成预测图像: val_batch{batch_i}_pred.jpg")
            print(f"预测目标示例 (前5个): {converted_targets[:5] if len(converted_targets) > 0 else 'Empty'}")
            try:
                plot_images_and_masks(im, converted_targets, plot_masks, paths, 
                                     save_dir / f'val_batch{batch_i}_pred.jpg', names)
                print(f"✓ 预测图像生成成功")
            except Exception as e:
                print(f"✗ 预测图像生成失败: {e}")
                import traceback
                traceback.print_exc()
    
    LOGGER.info(f"\n{colorstr('bright_green', 'bold', '验证完成!')}")
    return save_dir


def debug_output_to_target(output, max_det=300):
    """
    调试版本的output_to_target函数，添加详细的调试信息
    
    Args:
        output: 模型预测输出
        max_det: 最大检测数量
    
    Returns:
        targets: 转换后的目标格式 [batch_id, class_id, x, y, w, h, conf]
    """
    print(f"\n=== 调试 output_to_target 函数 ===")
    print(f"输入output类型: {type(output)}")
    print(f"输入output长度: {len(output) if hasattr(output, '__len__') else 'N/A'}")
    
    targets = []
    for i, o in enumerate(output):
        print(f"\n批次 {i}:")
        print(f"  原始输出形状: {o.shape if hasattr(o, 'shape') else 'N/A'}")
        print(f"  原始输出类型: {type(o)}")
        
        if hasattr(o, 'shape') and len(o.shape) >= 2:
            print(f"  检测数量: {o.shape[0]}")
            print(f"  特征维度: {o.shape[1]}")
            
            # 限制检测数量
            o_limited = o[:max_det]
            print(f"  限制后检测数量: {o_limited.shape[0]}")
            
            if o_limited.shape[1] >= 6:
                # 分离边界框、置信度和类别
                box, conf, cls = o_limited[:, :4].cpu(), o_limited[:, 4:5].cpu(), o_limited[:, 5:6].cpu()
                print(f"  边界框形状: {box.shape}")
                print(f"  置信度形状: {conf.shape}")
                print(f"  类别形状: {cls.shape}")
                
                # 创建批次索引
                j = torch.full((conf.shape[0], 1), i)
                print(f"  批次索引形状: {j.shape}")
                
                # 转换边界框格式 (xyxy -> xywh)
                # 检查边界框格式
                print(f"  边界框示例 (前3个): {box[:3].tolist() if box.shape[0] > 0 else 'None'}")
                
                # 假设输入是xyxy格式，转换为xywh
                box_xywh = torch.zeros_like(box)
                if box.shape[0] > 0:
                    box_xywh[:, 0] = (box[:, 0] + box[:, 2]) / 2  # x_center
                    box_xywh[:, 1] = (box[:, 1] + box[:, 3]) / 2  # y_center
                    box_xywh[:, 2] = box[:, 2] - box[:, 0]        # width
                    box_xywh[:, 3] = box[:, 3] - box[:, 1]        # height
                
                print(f"  转换后边界框示例 (前3个): {box_xywh[:3].tolist() if box_xywh.shape[0] > 0 else 'None'}")
                
                # 拼接结果
                target = torch.cat((j, cls, box_xywh, conf), 1)
                print(f"  最终目标形状: {target.shape}")
                print(f"  最终目标示例 (前3个): {target[:3].tolist() if target.shape[0] > 0 else 'None'}")
                
                targets.append(target)
            else:
                print(f"  警告: 特征维度不足 ({o_limited.shape[1]} < 6)")
        else:
            print(f"  警告: 输出形状异常")
    
    if targets:
        result = torch.cat(targets, 0).numpy()
        print(f"\n最终结果形状: {result.shape}")
        print(f"最终结果类型: {type(result)}")
        return result
    else:
        print(f"\n警告: 没有有效的目标")
        return np.array([]).reshape(0, 7)  # 返回空数组，形状为 (0, 7)


def debug_plot_masks_collection(plot_masks, batch_i=0):
    """
    调试掩码收集过程
    
    Args:
        plot_masks: 收集的掩码数据
        batch_i: 批次索引
    """
    print(f"\n{colorstr('yellow', 'bold', f'调试掩码收集 - 批次 {batch_i}')}")
    print(f"掩码类型: {type(plot_masks)}")
    print(f"掩码形状: {plot_masks.shape if hasattr(plot_masks, 'shape') else 'N/A'}")
    print(f"掩码数据类型: {plot_masks.dtype if hasattr(plot_masks, 'dtype') else 'N/A'}")
    
    if hasattr(plot_masks, 'shape') and len(plot_masks.shape) >= 3:
        num_masks = plot_masks.shape[0]
        mask_height = plot_masks.shape[1]
        mask_width = plot_masks.shape[2]
        
        print(f"掩码数量: {num_masks}")
        print(f"掩码尺寸: {mask_height} x {mask_width}")
        
        # 检查掩码值分布
        if hasattr(plot_masks, 'cpu'):
            masks_np = plot_masks.cpu().numpy()
        else:
            masks_np = np.array(plot_masks)
        
        print(f"掩码值范围: [{masks_np.min():.3f}, {masks_np.max():.3f}]")
        print(f"唯一值数量: {len(np.unique(masks_np))}")
        
        # 分析前几个掩码
        for i in range(min(3, num_masks)):
            mask = masks_np[i]
            non_zero_pixels = np.count_nonzero(mask)
            total_pixels = mask.size
            coverage = non_zero_pixels / total_pixels
            print(f"  掩码 {i}: 非零像素={non_zero_pixels}, 覆盖率={coverage:.3f}")


def run_validation_with_debug(weights, data, **kwargs):
    """
    运行带调试信息的验证
    
    Args:
        weights: 权重文件路径
        data: 数据配置文件路径
        **kwargs: 其他参数
    """
    print(f"\n{colorstr('bright_blue', 'bold', '开始验证过程...')}")
    print(f"权重文件: {weights}")
    print(f"数据配置: {data}")
    print(f"其他参数: {kwargs}")
    
    # 设置保存目录
    save_dir = Path(kwargs.get('project', 'runs/val-seg')) / kwargs.get('name', 'exp')
    save_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"结果保存目录: {save_dir}")
    
    try:
        # 移除不支持的参数
        filtered_kwargs = {k: v for k, v in kwargs.items() if k not in ['debug_mode', 'debug']}
        
        # 调用原始验证函数
        results = val_run(
            data=data,
            weights=weights,
            **filtered_kwargs
        )
        
        print(f"\n{colorstr('bright_green', 'bold', '验证完成!')}")
        print(f"结果: {results}")
        
        # 检查生成的可视化文件
        val_images = list(save_dir.glob('val_batch*_*.jpg'))
        if val_images:
            print(f"\n生成的可视化文件:")
            for img_path in val_images:
                print(f"  {img_path}")
                
                # 检查图像信息
                try:
                    img = Image.open(img_path)
                    print(f"    尺寸: {img.size}")
                    print(f"    模式: {img.mode}")
                except Exception as e:
                    print(f"    读取图像时出错: {e}")
        else:
            print(f"\n警告: 没有找到可视化文件")
        
        return results
        
    except Exception as e:
        print(f"\n{colorstr('bright_red', 'bold', '验证过程出错:')} {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(prog='simple_val_test.py', description='YOLOv5分割模型验证脚本')
    parser.add_argument('--weights', type=str, default='yolov5s-seg.pt', help='模型权重路径')
    parser.add_argument('--data', type=str, default='data/coco128-seg.yaml', help='数据集配置文件')
    parser.add_argument('--batch-size', type=int, default=32, help='批次大小')
    parser.add_argument('--imgsz', '--img', '--img-size', type=int, default=640, help='图像尺寸')
    parser.add_argument('--conf-thres', type=float, default=0.001, help='置信度阈值')
    parser.add_argument('--iou-thres', type=float, default=0.6, help='NMS IoU阈值')
    parser.add_argument('--max-det', type=int, default=300, help='每张图像的最大检测数量')
    parser.add_argument('--device', default='', help='cuda设备，例如0或0,1,2,3或cpu')
    parser.add_argument('--workers', type=int, default=8, help='数据加载器最大工作进程数')
    parser.add_argument('--single-cls', action='store_true', help='将多类数据视为单类')
    parser.add_argument('--augment', action='store_true', help='增强推理')
    parser.add_argument('--verbose', action='store_true', help='报告mAP按类别')
    parser.add_argument('--save-txt', action='store_true', help='保存结果到*.txt')
    parser.add_argument('--save-hybrid', action='store_true', help='保存标签+预测混合结果到*.txt')
    parser.add_argument('--save-conf', action='store_true', help='在--save-txt标签中保存置信度')
    parser.add_argument('--save-json', action='store_true', help='保存COCO-JSON格式的结果文件')
    parser.add_argument('--project', default='runs/val-seg', help='保存到project/name')
    parser.add_argument('--name', default='exp', help='保存到project/name')
    parser.add_argument('--exist-ok', action='store_true', help='现有project/name正常，不递增')
    parser.add_argument('--half', action='store_true', help='使用FP16半精度推理')
    parser.add_argument('--dnn', action='store_true', help='使用OpenCV DNN进行ONNX推理')
    parser.add_argument('--multimodal', action='store_true', help='多模态验证模式')
    parser.add_argument('--debug', action='store_true', help='启用详细调试模式')
    
    opt = parser.parse_args()
    print_args(vars(opt))
    
    # 检查要求
    check_requirements(exclude=('tensorboard', 'thop'))
    
    # 运行验证
    try:
        if opt.debug:
            print(f"\n{colorstr('bright_blue', 'bold', '启动调试模式验证')}")
            save_dir = run_validation_with_debug(
                weights=opt.weights,
                data=opt.data,
                batch_size=opt.batch_size,
                imgsz=opt.imgsz,
                conf_thres=opt.conf_thres,
                iou_thres=opt.iou_thres,
                max_det=opt.max_det,
                device=opt.device,
                workers=opt.workers,
                single_cls=opt.single_cls,
                augment=opt.augment,
                verbose=opt.verbose,
                save_txt=opt.save_txt,
                save_hybrid=opt.save_hybrid,
                save_conf=opt.save_conf,
                save_json=opt.save_json,
                project=opt.project,
                name=opt.name,
                exist_ok=opt.exist_ok,
                half=opt.half,
                dnn=opt.dnn,
                multimodal=opt.multimodal,
                debug_mode=True
            )
        else:
            print(f"\n{colorstr('bright_green', 'bold', '启动标准验证')}")
            save_dir = val_run(
                data=opt.data,
                weights=opt.weights,
                batch_size=opt.batch_size,
                imgsz=opt.imgsz,
                conf_thres=opt.conf_thres,
                iou_thres=opt.iou_thres,
                max_det=opt.max_det,
                device=opt.device,
                workers=opt.workers,
                single_cls=opt.single_cls,
                augment=opt.augment,
                verbose=opt.verbose,
                save_txt=opt.save_txt,
                save_hybrid=opt.save_hybrid,
                save_conf=opt.save_conf,
                save_json=opt.save_json,
                project=opt.project,
                name=opt.name,
                exist_ok=opt.exist_ok,
                half=opt.half,
                dnn=opt.dnn,
                multimodal=opt.multimodal
            )
        
        print(f"\n{colorstr('bright_green', 'bold', '验证完成!')}")
        print(f"结果保存在: {save_dir}")
        
        if opt.debug:
            print(f"\n{colorstr('bright_blue', 'bold', '调试模式总结:')}")
            print(f"1. 检查 {save_dir}/debug/ 目录查看详细调试信息")
            print(f"2. 查看 val_batch0_pred.jpg 和 val_batch0_labels.jpg 对比预测和真实标签")
            print(f"3. 查看 debug_batch*_img*.jpg 文件查看调试可视化")
            print(f"4. 查看 debug_report_batch_*.json 文件查看详细分析报告")
            
    except Exception as e:
        LOGGER.error(f"验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())