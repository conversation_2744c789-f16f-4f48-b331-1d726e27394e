Image: tile_0120.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8535
  Bounding Box: [1515.20, 0.10, 1732.80, 213.60]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [123, 5, 139, 20]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8491
  Bounding Box: [1380.80, 1158.40, 1651.20, 1376.00]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [113, 95, 132, 111]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8457
  Bounding Box: [1568.80, 195.60, 1870.40, 369.20]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [127, 20, 150, 32]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8433
  Bounding Box: [1002.40, 1193.60, 1213.60, 1332.80]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [83, 98, 98, 108]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8418
  Bounding Box: [623.60, 364.00, 820.00, 651.20]
  Mask Area: 283 pixels
  Mask Ratio: 0.0100
  Mask BBox: [53, 33, 68, 53]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8374
  Bounding Box: [1769.60, 1369.60, 1900.80, 1513.60]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [143, 111, 152, 122]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8340
  Bounding Box: [912.80, 45.20, 1092.00, 326.80]
  Mask Area: 205 pixels
  Mask Ratio: 0.0073
  Mask BBox: [76, 8, 89, 29]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8271
  Bounding Box: [666.00, 667.20, 819.20, 889.60]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [57, 57, 66, 73]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8267
  Bounding Box: [507.20, 659.20, 726.40, 904.00]
  Mask Area: 261 pixels
  Mask Ratio: 0.0092
  Mask BBox: [44, 56, 60, 74]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8218
  Bounding Box: [1270.40, 752.80, 1441.60, 949.60]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [105, 64, 116, 77]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8076
  Bounding Box: [1846.40, 1249.60, 1964.80, 1438.40]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [149, 102, 157, 116]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8047
  Bounding Box: [247.80, 982.40, 388.80, 1102.40]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [24, 81, 34, 90]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.8022
  Bounding Box: [343.00, 1750.40, 518.00, 2000.00]
  Mask Area: 172 pixels
  Mask Ratio: 0.0061
  Mask BBox: [31, 141, 43, 159]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.8003
  Bounding Box: [337.60, 1568.80, 523.20, 1752.00]
  Mask Area: 138 pixels
  Mask Ratio: 0.0049
  Mask BBox: [31, 127, 44, 140]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7954
  Bounding Box: [1564.80, 1699.20, 1817.60, 1923.20]
  Mask Area: 270 pixels
  Mask Ratio: 0.0096
  Mask BBox: [127, 137, 145, 154]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7891
  Bounding Box: [693.60, 195.60, 809.60, 370.80]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [59, 20, 67, 32]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7871
  Bounding Box: [1324.80, 1758.40, 1638.40, 1992.00]
  Mask Area: 333 pixels
  Mask Ratio: 0.0118
  Mask BBox: [108, 142, 131, 159]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7861
  Bounding Box: [1712.00, 789.60, 1910.40, 984.80]
  Mask Area: 165 pixels
  Mask Ratio: 0.0058
  Mask BBox: [138, 66, 153, 80]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7832
  Bounding Box: [1161.60, 1713.60, 1372.80, 2024.00]
  Mask Area: 298 pixels
  Mask Ratio: 0.0106
  Mask BBox: [95, 138, 111, 162]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7754
  Bounding Box: [1817.60, 546.80, 1990.40, 760.40]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [146, 47, 159, 62]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7734
  Bounding Box: [957.60, 679.20, 1132.00, 884.00]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [79, 58, 89, 73]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7720
  Bounding Box: [805.60, 717.20, 901.60, 860.80]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [67, 61, 74, 71]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7710
  Bounding Box: [686.80, 1592.00, 968.00, 1800.00]
  Mask Area: 263 pixels
  Mask Ratio: 0.0093
  Mask BBox: [58, 129, 79, 144]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7681
  Bounding Box: [247.00, 382.40, 507.20, 640.00]
  Mask Area: 276 pixels
  Mask Ratio: 0.0098
  Mask BBox: [24, 34, 43, 51]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7661
  Bounding Box: [418.40, 287.40, 645.60, 429.20]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [38, 27, 53, 36]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7646
  Bounding Box: [81.60, 495.20, 209.60, 668.00]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [11, 43, 20, 56]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7646
  Bounding Box: [1761.60, 25.20, 1912.00, 147.80]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [142, 6, 153, 15]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7646
  Bounding Box: [1902.40, 775.20, 2048.00, 1101.60]
  Mask Area: 262 pixels
  Mask Ratio: 0.0093
  Mask BBox: [153, 65, 164, 90]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7617
  Bounding Box: [480.80, 1364.80, 656.00, 1579.20]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [42, 111, 55, 127]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7568
  Bounding Box: [732.80, 1812.80, 969.60, 2048.00]
  Mask Area: 248 pixels
  Mask Ratio: 0.0088
  Mask BBox: [62, 146, 79, 164]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7559
  Bounding Box: [145.10, 1262.40, 367.20, 1731.20]
  Mask Area: 441 pixels
  Mask Ratio: 0.0156
  Mask BBox: [16, 103, 32, 139]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7554
  Bounding Box: [535.60, 931.20, 666.80, 1070.40]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [46, 77, 56, 86]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7539
  Bounding Box: [1628.80, 349.60, 1801.60, 526.40]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [132, 33, 143, 43]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7495
  Bounding Box: [1052.00, 5.00, 1215.20, 218.60]
  Mask Area: 157 pixels
  Mask Ratio: 0.0056
  Mask BBox: [87, 5, 98, 21]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7495
  Bounding Box: [1752.00, 1559.20, 1864.00, 1689.60]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [141, 126, 149, 135]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7471
  Bounding Box: [1133.60, 445.20, 1260.00, 618.00]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [93, 39, 102, 51]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7456
  Bounding Box: [7.65, 1590.40, 116.40, 1744.00]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [5, 129, 13, 140]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7422
  Bounding Box: [351.80, 856.00, 541.60, 1180.80]
  Mask Area: 259 pixels
  Mask Ratio: 0.0092
  Mask BBox: [32, 71, 45, 96]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7358
  Bounding Box: [337.60, 629.20, 514.40, 840.80]
  Mask Area: 165 pixels
  Mask Ratio: 0.0058
  Mask BBox: [31, 54, 44, 69]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7319
  Bounding Box: [652.40, 965.60, 832.00, 1263.20]
  Mask Area: 217 pixels
  Mask Ratio: 0.0077
  Mask BBox: [55, 80, 68, 102]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7295
  Bounding Box: [886.40, 768.00, 1152.00, 1038.40]
  Mask Area: 297 pixels
  Mask Ratio: 0.0105
  Mask BBox: [74, 64, 93, 85]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7290
  Bounding Box: [39.20, 648.40, 366.40, 971.20]
  Mask Area: 356 pixels
  Mask Ratio: 0.0126
  Mask BBox: [8, 56, 32, 78]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7280
  Bounding Box: [816.00, 1128.80, 963.20, 1264.80]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [68, 93, 78, 101]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7236
  Bounding Box: [0.00, 432.40, 124.20, 599.60]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [4, 38, 13, 50]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7212
  Bounding Box: [211.20, 1155.20, 471.20, 1384.00]
  Mask Area: 268 pixels
  Mask Ratio: 0.0095
  Mask BBox: [21, 95, 40, 112]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7119
  Bounding Box: [584.00, 1318.40, 661.60, 1412.80]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [50, 107, 54, 114]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7090
  Bounding Box: [1565.60, 547.20, 1729.60, 880.80]
  Mask Area: 241 pixels
  Mask Ratio: 0.0085
  Mask BBox: [127, 47, 139, 72]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7085
  Bounding Box: [1214.40, 6.15, 1387.20, 147.00]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [99, 5, 112, 15]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7075
  Bounding Box: [1544.80, 868.00, 1723.20, 1178.40]
  Mask Area: 257 pixels
  Mask Ratio: 0.0091
  Mask BBox: [125, 72, 138, 96]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7065
  Bounding Box: [1912.00, 1425.60, 2046.40, 1508.80]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [154, 116, 162, 121]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.7065
  Bounding Box: [572.40, 1308.80, 639.60, 1416.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [49, 107, 53, 114]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.7026
  Bounding Box: [520.40, 1628.80, 750.80, 1987.20]
  Mask Area: 360 pixels
  Mask Ratio: 0.0128
  Mask BBox: [46, 132, 62, 159]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.7021
  Bounding Box: [1528.80, 734.40, 1611.20, 884.80]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [124, 62, 129, 72]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.7017
  Bounding Box: [64.70, 31.80, 307.20, 330.60]
  Mask Area: 271 pixels
  Mask Ratio: 0.0096
  Mask BBox: [10, 7, 27, 29]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.7007
  Bounding Box: [185.60, 533.60, 304.40, 719.20]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [19, 46, 27, 60]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.6992
  Bounding Box: [362.00, 1388.00, 479.60, 1604.80]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [33, 113, 41, 129]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6963
  Bounding Box: [1808.00, 208.80, 2048.00, 580.00]
  Mask Area: 454 pixels
  Mask Ratio: 0.0161
  Mask BBox: [146, 21, 164, 49]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6948
  Bounding Box: [1470.40, 1055.20, 1576.00, 1186.40]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [119, 87, 126, 95]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6938
  Bounding Box: [1258.40, 245.60, 1480.80, 435.20]
  Mask Area: 138 pixels
  Mask Ratio: 0.0049
  Mask BBox: [103, 24, 119, 37]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6924
  Bounding Box: [820.00, 286.00, 984.80, 517.20]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [69, 27, 80, 43]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6792
  Bounding Box: [35.40, 1400.00, 159.40, 1611.20]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [7, 114, 15, 129]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6763
  Bounding Box: [1259.20, 472.00, 1587.20, 822.40]
  Mask Area: 454 pixels
  Mask Ratio: 0.0161
  Mask BBox: [103, 42, 127, 68]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6704
  Bounding Box: [242.00, 1659.20, 394.80, 1848.00]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [23, 134, 34, 148]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6660
  Bounding Box: [510.00, 1080.80, 654.80, 1204.00]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [44, 89, 55, 98]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6631
  Bounding Box: [957.60, 1649.60, 1149.60, 1864.00]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [79, 133, 91, 149]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6606
  Bounding Box: [324.60, 34.20, 499.60, 249.60]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [30, 7, 43, 23]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6587
  Bounding Box: [419.20, 1183.20, 608.80, 1426.40]
  Mask Area: 214 pixels
  Mask Ratio: 0.0076
  Mask BBox: [37, 97, 51, 115]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6582
  Bounding Box: [1673.60, 1397.60, 1792.00, 1598.40]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [135, 114, 143, 128]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6509
  Bounding Box: [1932.80, 1180.00, 2048.00, 1306.40]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [155, 97, 163, 106]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6494
  Bounding Box: [1522.40, 1481.60, 1659.20, 1675.20]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [123, 120, 133, 134]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6475
  Bounding Box: [1900.80, 11.60, 2035.20, 242.40]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [153, 5, 162, 22]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6431
  Bounding Box: [1424.80, 1537.60, 1568.80, 1758.40]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [116, 125, 126, 141]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6387
  Bounding Box: [637.20, 1243.20, 797.60, 1400.00]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [54, 102, 66, 113]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6372
  Bounding Box: [1068.80, 572.80, 1315.20, 1051.20]
  Mask Area: 482 pixels
  Mask Ratio: 0.0171
  Mask BBox: [88, 49, 106, 85]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6348
  Bounding Box: [800.80, 1250.40, 941.60, 1458.40]
  Mask Area: 112 pixels
  Mask Ratio: 0.0040
  Mask BBox: [67, 103, 77, 117]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6338
  Bounding Box: [111.60, 1708.80, 214.80, 1798.40]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [13, 138, 20, 144]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6323
  Bounding Box: [906.40, 1008.00, 1071.20, 1275.20]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [75, 83, 86, 103]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6313
  Bounding Box: [1556.80, 1964.80, 1739.20, 2028.80]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [126, 158, 139, 162]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6309
  Bounding Box: [1705.60, 972.00, 1840.00, 1272.80]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [138, 82, 147, 103]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6304
  Bounding Box: [1195.20, 133.40, 1345.60, 249.00]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [98, 15, 109, 23]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6250
  Bounding Box: [288.40, 0.00, 385.60, 80.20]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [27, 4, 34, 9]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6221
  Bounding Box: [1335.20, 1354.40, 1519.20, 1593.60]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [109, 110, 122, 128]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6167
  Bounding Box: [193.60, 256.00, 326.00, 469.60]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [20, 24, 29, 38]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6118
  Bounding Box: [1731.20, 1897.60, 1929.60, 2041.60]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [140, 153, 154, 163]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6104
  Bounding Box: [172.00, 1076.00, 320.80, 1188.00]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [18, 89, 27, 96]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6001
  Bounding Box: [1148.80, 945.60, 1238.40, 1128.00]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [94, 78, 100, 92]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.5991
  Bounding Box: [323.00, 1904.00, 484.80, 2048.00]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [30, 154, 41, 163]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.5972
  Bounding Box: [840.00, 211.00, 992.00, 357.00]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [70, 21, 81, 30]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.5898
  Bounding Box: [1665.60, 520.00, 1787.20, 654.40]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [135, 45, 143, 55]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.5894
  Bounding Box: [1176.00, 1132.00, 1299.20, 1260.00]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [96, 93, 105, 102]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.5854
  Bounding Box: [1814.40, 1043.20, 1964.80, 1257.60]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [146, 86, 157, 102]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.5845
  Bounding Box: [1028.00, 258.40, 1346.40, 450.40]
  Mask Area: 217 pixels
  Mask Ratio: 0.0077
  Mask BBox: [85, 25, 109, 39]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5825
  Bounding Box: [1320.80, 1037.60, 1469.60, 1149.60]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [108, 86, 118, 93]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5776
  Bounding Box: [1300.00, 1619.20, 1444.00, 1772.80]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [106, 131, 116, 141]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5767
  Bounding Box: [756.00, 908.00, 930.40, 1167.20]
  Mask Area: 173 pixels
  Mask Ratio: 0.0061
  Mask BBox: [64, 75, 76, 95]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5762
  Bounding Box: [1059.20, 1321.60, 1398.40, 1651.20]
  Mask Area: 456 pixels
  Mask Ratio: 0.0162
  Mask BBox: [87, 108, 113, 132]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5752
  Bounding Box: [928.00, 1840.00, 1056.00, 1900.80]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [77, 148, 86, 152]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5742
  Bounding Box: [10.20, 129.70, 68.90, 297.60]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [5, 15, 9, 27]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5737
  Bounding Box: [1974.40, 1099.20, 2044.80, 1172.80]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [159, 90, 163, 94]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5728
  Bounding Box: [3.20, 604.00, 75.70, 734.40]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 52, 9, 61]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5723
  Bounding Box: [1007.20, 1892.80, 1170.40, 2036.80]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [83, 152, 94, 163]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5693
  Bounding Box: [1365.60, 949.60, 1469.60, 1026.40]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [111, 79, 118, 83]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5688
  Bounding Box: [664.80, 903.20, 896.80, 1232.80]
  Mask Area: 327 pixels
  Mask Ratio: 0.0116
  Mask BBox: [56, 75, 74, 99]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5684
  Bounding Box: [88.10, 1100.80, 181.60, 1260.80]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [11, 90, 18, 102]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5645
  Bounding Box: [1728.00, 124.40, 1859.20, 198.40]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [139, 14, 149, 19]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5610
  Bounding Box: [1939.20, 1928.00, 2048.00, 2048.00]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [156, 155, 164, 164]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5605
  Bounding Box: [839.20, 1766.40, 960.80, 1875.20]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [70, 142, 79, 150]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5605
  Bounding Box: [809.60, 208.60, 998.40, 455.20]
  Mask Area: 201 pixels
  Mask Ratio: 0.0071
  Mask BBox: [68, 21, 81, 39]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5601
  Bounding Box: [278.60, 1515.20, 366.40, 1678.40]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [26, 123, 32, 135]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5527
  Bounding Box: [1216.00, 1267.20, 1313.60, 1345.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [99, 103, 106, 108]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5498
  Bounding Box: [484.00, 413.20, 620.80, 653.20]
  Mask Area: 176 pixels
  Mask Ratio: 0.0062
  Mask BBox: [42, 37, 52, 55]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5493
  Bounding Box: [1654.40, 1243.20, 1814.40, 1422.40]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [134, 102, 145, 115]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5483
  Bounding Box: [1005.60, 1019.20, 1111.20, 1128.00]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [83, 84, 90, 92]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5391
  Bounding Box: [644.80, 1310.40, 811.20, 1558.40]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [55, 107, 67, 125]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5332
  Bounding Box: [89.20, 270.80, 208.80, 426.00]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [11, 26, 20, 35]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5303
  Bounding Box: [141.70, 370.80, 216.40, 462.00]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [16, 33, 20, 40]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5293
  Bounding Box: [9.30, 14.80, 121.10, 133.40]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [5, 6, 13, 14]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5146
  Bounding Box: [1234.40, 633.20, 1316.00, 763.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [101, 54, 106, 62]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5132
  Bounding Box: [920.00, 1296.80, 1001.60, 1423.20]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [76, 106, 82, 115]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.5103
  Bounding Box: [1980.80, 1324.80, 2038.40, 1459.20]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [159, 108, 163, 117]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.5098
  Bounding Box: [1964.80, 1305.60, 2032.00, 1430.40]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [158, 106, 162, 115]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.5098
  Bounding Box: [833.60, 0.00, 961.60, 137.00]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [70, 4, 79, 14]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.5093
  Bounding Box: [817.60, 409.20, 1099.20, 742.00]
  Mask Area: 373 pixels
  Mask Ratio: 0.0132
  Mask BBox: [68, 36, 89, 61]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.5083
  Bounding Box: [724.80, 1625.60, 974.40, 1859.20]
  Mask Area: 238 pixels
  Mask Ratio: 0.0084
  Mask BBox: [61, 131, 80, 149]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.5059
  Bounding Box: [1.50, 1952.00, 144.90, 2044.80]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [5, 157, 15, 163]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.5049
  Bounding Box: [150.40, 1974.40, 326.40, 2041.60]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [16, 159, 29, 163]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.5039
  Bounding Box: [516.00, 1531.20, 667.20, 1636.80]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [45, 124, 56, 131]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.5020
  Bounding Box: [1377.60, 68.00, 1556.80, 341.20]
  Mask Area: 242 pixels
  Mask Ratio: 0.0086
  Mask BBox: [112, 10, 125, 30]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.5020
  Bounding Box: [976.80, 622.40, 1263.20, 961.60]
  Mask Area: 448 pixels
  Mask Ratio: 0.0159
  Mask BBox: [81, 53, 102, 79]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4912
  Bounding Box: [1612.80, 1881.60, 1692.80, 1968.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [130, 151, 136, 157]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4897
  Bounding Box: [923.20, 668.40, 1139.20, 987.20]
  Mask Area: 268 pixels
  Mask Ratio: 0.0095
  Mask BBox: [77, 57, 92, 81]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4824
  Bounding Box: [1788.80, 452.40, 1875.20, 539.60]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [144, 40, 150, 46]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4812
  Bounding Box: [784.00, 705.60, 931.20, 892.80]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [66, 60, 75, 73]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4756
  Bounding Box: [129.20, 1766.40, 322.00, 1980.80]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [15, 142, 29, 157]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4722
  Bounding Box: [1539.20, 1423.20, 1689.60, 1593.60]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [125, 116, 135, 128]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4712
  Bounding Box: [270.40, 1583.20, 502.40, 1795.20]
  Mask Area: 210 pixels
  Mask Ratio: 0.0074
  Mask BBox: [26, 128, 43, 144]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4653
  Bounding Box: [624.80, 1216.80, 805.60, 1456.80]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [53, 100, 66, 117]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4607
  Bounding Box: [1559.20, 961.60, 1708.80, 1204.80]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [126, 80, 137, 96]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4585
  Bounding Box: [1454.40, 1346.40, 1595.20, 1452.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [118, 110, 128, 117]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4570
  Bounding Box: [548.80, 210.20, 702.40, 320.20]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [47, 21, 58, 29]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4534
  Bounding Box: [1439.20, 802.40, 1536.80, 960.80]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [117, 67, 124, 79]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4519
  Bounding Box: [284.20, 192.00, 431.60, 364.80]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [27, 19, 37, 32]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4434
  Bounding Box: [465.60, 8.80, 816.00, 209.80]
  Mask Area: 353 pixels
  Mask Ratio: 0.0125
  Mask BBox: [42, 5, 67, 20]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4431
  Bounding Box: [1915.20, 1407.20, 2036.80, 1541.60]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [154, 114, 162, 124]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4421
  Bounding Box: [1436.80, 161.20, 1641.60, 512.40]
  Mask Area: 342 pixels
  Mask Ratio: 0.0121
  Mask BBox: [117, 17, 132, 44]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4326
  Bounding Box: [1758.40, 1552.00, 1896.00, 1724.80]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [142, 126, 152, 138]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4321
  Bounding Box: [1437.60, 870.40, 1535.20, 1032.00]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [117, 72, 123, 84]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4307
  Bounding Box: [1360.80, 1556.00, 1564.00, 1771.20]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [111, 126, 126, 142]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4302
  Bounding Box: [1582.40, 1152.00, 1659.20, 1243.20]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [128, 94, 133, 101]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4292
  Bounding Box: [204.40, 937.60, 268.40, 1048.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [20, 78, 24, 85]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4287
  Bounding Box: [726.40, 609.60, 833.60, 691.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [61, 52, 68, 57]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4282
  Bounding Box: [103.40, 320.20, 216.60, 464.00]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [13, 30, 19, 40]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4282
  Bounding Box: [1729.60, 616.40, 1851.20, 787.20]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [140, 53, 148, 65]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4277
  Bounding Box: [1.55, 1159.20, 76.60, 1327.20]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [5, 95, 9, 107]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4268
  Bounding Box: [683.20, 199.00, 888.00, 371.60]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [58, 20, 73, 33]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4248
  Bounding Box: [0.00, 816.80, 129.80, 959.20]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [4, 68, 14, 78]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4248
  Bounding Box: [1182.40, 1664.00, 1291.20, 1740.80]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [97, 134, 104, 139]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4216
  Bounding Box: [1948.80, 1282.40, 2044.80, 1428.00]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [157, 105, 163, 115]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4170
  Bounding Box: [176.40, 1015.20, 367.20, 1168.80]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [18, 84, 32, 95]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4163
  Bounding Box: [1113.60, 1984.00, 1222.40, 2032.00]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [91, 159, 98, 162]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4153
  Bounding Box: [1380.00, 944.00, 1501.60, 1035.20]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [112, 78, 121, 84]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.4136
  Bounding Box: [682.40, 678.80, 888.80, 890.40]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [58, 58, 73, 73]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.4126
  Bounding Box: [0.00, 789.60, 106.10, 925.60]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [4, 66, 12, 76]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.4126
  Bounding Box: [77.80, 1258.40, 207.40, 1359.20]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [11, 103, 19, 110]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.4121
  Bounding Box: [1242.40, 426.40, 1316.00, 524.80]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [102, 38, 106, 44]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.4121
  Bounding Box: [1705.60, 942.40, 1795.20, 1091.20]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [138, 78, 144, 89]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.4111
  Bounding Box: [1955.20, 1592.00, 2041.60, 1934.40]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [157, 129, 163, 155]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.4097
  Bounding Box: [179.80, 1673.60, 365.60, 1932.80]
  Mask Area: 192 pixels
  Mask Ratio: 0.0068
  Mask BBox: [19, 135, 32, 154]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.4072
  Bounding Box: [1126.40, 1590.40, 1222.40, 1673.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [92, 129, 99, 134]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.4055
  Bounding Box: [1691.20, 945.60, 1803.20, 1144.00]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [137, 78, 144, 93]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.4036
  Bounding Box: [559.20, 1843.20, 731.20, 1993.60]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [48, 148, 61, 159]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3999
  Bounding Box: [8.65, 1779.20, 145.20, 1945.60]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [5, 143, 15, 155]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3989
  Bounding Box: [257.20, 660.00, 352.80, 824.00]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [25, 56, 31, 68]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3984
  Bounding Box: [1360.80, 1953.60, 1484.00, 2036.80]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [111, 157, 119, 162]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3975
  Bounding Box: [1936.00, 1401.60, 2035.20, 1502.40]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [156, 114, 162, 121]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3975
  Bounding Box: [13.70, 434.80, 163.20, 634.00]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [6, 38, 16, 53]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3960
  Bounding Box: [3.30, 1219.20, 75.40, 1372.80]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [5, 100, 9, 111]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3918
  Bounding Box: [76.20, 1920.00, 170.20, 2012.80]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [10, 154, 16, 161]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3892
  Bounding Box: [1875.20, 1226.40, 1984.00, 1444.00]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [151, 100, 158, 116]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3882
  Bounding Box: [1985.60, 580.80, 2040.00, 723.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [160, 50, 163, 60]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3882
  Bounding Box: [1298.40, 1596.80, 1520.80, 1782.40]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [106, 129, 122, 143]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3872
  Bounding Box: [1724.80, 1011.20, 1932.80, 1257.60]
  Mask Area: 265 pixels
  Mask Ratio: 0.0094
  Mask BBox: [139, 83, 154, 102]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3860
  Bounding Box: [89.10, 506.80, 280.00, 696.40]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [11, 44, 25, 58]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3840
  Bounding Box: [428.00, 114.20, 573.60, 245.80]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [38, 13, 48, 23]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3840
  Bounding Box: [1496.80, 1428.00, 1680.00, 1648.00]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [121, 116, 135, 132]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3816
  Bounding Box: [948.80, 1059.20, 1150.40, 1316.80]
  Mask Area: 177 pixels
  Mask Ratio: 0.0063
  Mask BBox: [79, 87, 93, 106]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3789
  Bounding Box: [418.00, 224.40, 521.20, 309.60]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [37, 22, 44, 28]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3757
  Bounding Box: [1814.40, 1680.00, 1961.60, 1830.40]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [146, 136, 157, 146]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3752
  Bounding Box: [1067.20, 0.00, 1308.80, 210.00]
  Mask Area: 230 pixels
  Mask Ratio: 0.0081
  Mask BBox: [88, 5, 106, 20]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3726
  Bounding Box: [14.50, 379.60, 79.40, 453.20]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [6, 34, 10, 39]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3721
  Bounding Box: [1467.20, 932.80, 1540.80, 1048.00]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [119, 77, 123, 85]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3721
  Bounding Box: [0.00, 1750.40, 139.50, 1916.80]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [3, 141, 14, 153]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3706
  Bounding Box: [1221.60, 1055.20, 1300.00, 1149.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [100, 87, 105, 92]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3689
  Bounding Box: [0.00, 123.60, 89.70, 326.80]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [4, 14, 11, 29]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3684
  Bounding Box: [1444.00, 7.40, 1520.80, 71.60]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [117, 5, 122, 9]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3669
  Bounding Box: [1889.60, 1436.80, 2033.60, 1523.20]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [152, 117, 162, 122]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3667
  Bounding Box: [1579.20, 572.40, 1809.60, 851.20]
  Mask Area: 309 pixels
  Mask Ratio: 0.0109
  Mask BBox: [128, 49, 145, 70]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3635
  Bounding Box: [1740.80, 84.60, 1878.40, 191.40]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [140, 11, 150, 18]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3616
  Bounding Box: [1835.20, 981.60, 1928.00, 1072.80]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [148, 81, 154, 87]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3608
  Bounding Box: [1327.20, 35.10, 1532.00, 297.60]
  Mask Area: 227 pixels
  Mask Ratio: 0.0080
  Mask BBox: [108, 7, 123, 27]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3606
  Bounding Box: [503.20, 1080.00, 647.20, 1275.20]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [44, 89, 54, 103]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3604
  Bounding Box: [1215.20, 1296.80, 1496.80, 1625.60]
  Mask Area: 393 pixels
  Mask Ratio: 0.0139
  Mask BBox: [99, 106, 120, 130]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3599
  Bounding Box: [1820.80, 971.20, 1913.60, 1048.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [147, 80, 153, 85]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3589
  Bounding Box: [1077.60, 211.60, 1226.40, 303.20]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [89, 21, 99, 26]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3584
  Bounding Box: [5.60, 772.80, 85.90, 894.40]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [5, 65, 10, 73]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3562
  Bounding Box: [1.15, 590.40, 83.20, 692.80]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 51, 10, 58]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [662.80, 1408.00, 823.20, 1566.40]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [56, 114, 66, 126]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3550
  Bounding Box: [255.20, 714.40, 354.00, 852.00]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [24, 60, 31, 70]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3530
  Bounding Box: [69.80, 925.60, 242.20, 1135.20]
  Mask Area: 177 pixels
  Mask Ratio: 0.0063
  Mask BBox: [10, 77, 22, 92]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3525
  Bounding Box: [219.60, 218.80, 382.40, 430.80]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [22, 22, 33, 37]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3491
  Bounding Box: [754.00, 618.40, 826.40, 703.20]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [63, 53, 68, 58]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3455
  Bounding Box: [1237.60, 8.40, 1426.40, 178.80]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [101, 5, 115, 17]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3430
  Bounding Box: [1237.60, 1056.00, 1322.40, 1131.20]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [101, 87, 107, 92]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3425
  Bounding Box: [268.80, 967.20, 410.00, 1088.80]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [25, 80, 33, 89]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3425
  Bounding Box: [31.95, 1564.00, 135.00, 1732.80]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [7, 127, 14, 139]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3411
  Bounding Box: [3.65, 14.30, 159.60, 171.60]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [5, 6, 16, 17]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3408
  Bounding Box: [1758.40, 1379.20, 1892.80, 1633.60]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [142, 112, 151, 131]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3406
  Bounding Box: [1697.60, 13.90, 1889.60, 180.40]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [137, 6, 151, 18]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3403
  Bounding Box: [915.20, 1849.60, 1051.20, 1926.40]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [76, 149, 86, 154]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3403
  Bounding Box: [12.40, 1792.00, 79.00, 1907.20]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [5, 144, 8, 152]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3386
  Bounding Box: [788.80, 721.60, 883.20, 886.40]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [66, 61, 72, 73]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3376
  Bounding Box: [864.80, 86.90, 1069.60, 357.20]
  Mask Area: 249 pixels
  Mask Ratio: 0.0088
  Mask BBox: [72, 11, 87, 31]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3376
  Bounding Box: [1567.20, 518.80, 1763.20, 776.00]
  Mask Area: 251 pixels
  Mask Ratio: 0.0089
  Mask BBox: [127, 45, 141, 64]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3369
  Bounding Box: [1611.20, 1245.60, 1790.40, 1444.00]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [130, 102, 143, 116]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3350
  Bounding Box: [1804.80, 1633.60, 1974.40, 1851.20]
  Mask Area: 182 pixels
  Mask Ratio: 0.0064
  Mask BBox: [145, 132, 158, 148]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3345
  Bounding Box: [788.80, 184.40, 862.40, 282.00]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [66, 19, 70, 26]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3323
  Bounding Box: [1820.80, 1606.40, 2019.20, 1875.20]
  Mask Area: 289 pixels
  Mask Ratio: 0.0102
  Mask BBox: [147, 130, 161, 150]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3308
  Bounding Box: [1336.80, 1031.20, 1532.00, 1154.40]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [109, 85, 123, 94]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1729.60, 314.80, 1825.60, 414.80]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [140, 29, 146, 36]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3289
  Bounding Box: [1636.80, 1557.60, 1736.00, 1692.80]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [132, 126, 139, 136]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3286
  Bounding Box: [1392.00, 1641.60, 1524.80, 1766.40]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [113, 133, 123, 141]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3284
  Bounding Box: [116.10, 1683.20, 270.40, 1811.20]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [14, 136, 25, 144]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3276
  Bounding Box: [1475.20, 437.60, 1611.20, 600.00]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [120, 39, 129, 50]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [37.40, 241.40, 198.40, 438.80]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [7, 23, 19, 38]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3259
  Bounding Box: [1593.60, 1175.20, 1680.00, 1242.40]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [129, 96, 135, 101]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3257
  Bounding Box: [1883.20, 1148.80, 2048.00, 1312.00]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [152, 94, 164, 106]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3252
  Bounding Box: [253.20, 5.55, 365.20, 80.40]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [24, 5, 32, 9]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3242
  Bounding Box: [1076.00, 34.10, 1244.00, 237.20]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [89, 7, 101, 20]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [1646.40, 531.60, 1764.80, 671.60]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [133, 46, 141, 56]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [1582.40, 1921.60, 1704.00, 2048.00]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [128, 155, 137, 164]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.3208
  Bounding Box: [931.20, 1532.00, 1033.60, 1660.80]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [77, 124, 84, 133]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.3179
  Bounding Box: [510.40, 896.80, 656.80, 1061.60]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [44, 75, 55, 86]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.3174
  Bounding Box: [1128.80, 940.00, 1188.00, 1034.40]
  Mask Area: 17 pixels
  Mask Ratio: 0.0006
  Mask BBox: [93, 78, 96, 84]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.3174
  Bounding Box: [717.60, 854.40, 834.40, 926.40]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [61, 71, 69, 76]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.3169
  Bounding Box: [920.80, 1816.00, 1050.40, 1892.80]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [76, 146, 86, 151]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.3169
  Bounding Box: [946.40, 1841.60, 1076.00, 1918.40]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [78, 148, 88, 153]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.3162
  Bounding Box: [1578.40, 1057.60, 1702.40, 1201.60]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [128, 87, 136, 97]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.3157
  Bounding Box: [914.40, 1237.60, 1002.40, 1415.20]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [76, 101, 82, 114]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.3154
  Bounding Box: [1640.00, 925.60, 1825.60, 1199.20]
  Mask Area: 256 pixels
  Mask Ratio: 0.0091
  Mask BBox: [133, 77, 146, 97]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.3135
  Bounding Box: [1865.60, 1910.40, 2048.00, 2048.00]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [150, 154, 164, 164]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.3108
  Bounding Box: [1977.60, 1508.00, 2044.80, 1600.00]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [159, 122, 163, 127]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [1137.60, 1980.80, 1214.40, 2041.60]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [93, 159, 98, 163]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.3091
  Bounding Box: [88.80, 1169.60, 209.60, 1332.80]
  Mask Area: 112 pixels
  Mask Ratio: 0.0040
  Mask BBox: [11, 96, 20, 108]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.3081
  Bounding Box: [1431.20, 1352.80, 1616.00, 1498.40]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [116, 110, 130, 121]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.3079
  Bounding Box: [1460.00, 242.40, 1636.80, 481.60]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [119, 23, 131, 41]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.3069
  Bounding Box: [6.90, 731.20, 70.80, 840.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [5, 62, 9, 69]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [1156.00, 1028.00, 1285.60, 1277.60]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [95, 85, 104, 103]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.3052
  Bounding Box: [506.00, 1921.60, 633.20, 2036.80]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [44, 155, 53, 163]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.3052
  Bounding Box: [1143.20, 966.40, 1279.20, 1123.20]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [94, 80, 103, 91]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.3052
  Bounding Box: [524.80, 1862.40, 709.60, 2016.00]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [45, 150, 59, 161]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.3049
  Bounding Box: [530.80, 1097.60, 669.20, 1224.00]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [46, 90, 55, 99]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.3047
  Bounding Box: [675.60, 1286.40, 892.00, 1548.80]
  Mask Area: 276 pixels
  Mask Ratio: 0.0098
  Mask BBox: [57, 105, 73, 124]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.3044
  Bounding Box: [1934.40, 736.00, 2048.00, 865.60]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [156, 62, 164, 71]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.3037
  Bounding Box: [6.25, 1827.20, 146.60, 2048.00]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [5, 147, 15, 163]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.3035
  Bounding Box: [0.00, 388.40, 97.30, 474.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [4, 35, 11, 41]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.3003
  Bounding Box: [1197.60, 1257.60, 1290.40, 1329.60]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [98, 103, 104, 107]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.3003
  Bounding Box: [1197.60, 1283.20, 1290.40, 1355.20]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [98, 105, 104, 108]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.3000
  Bounding Box: [800.00, 3.80, 980.80, 181.80]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [67, 5, 80, 18]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2991
  Bounding Box: [55.70, 510.40, 192.00, 691.20]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [9, 44, 18, 56]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2988
  Bounding Box: [1569.60, 1100.00, 1672.00, 1220.00]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [127, 90, 134, 99]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2986
  Bounding Box: [1705.60, 78.00, 1817.60, 196.00]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [138, 11, 145, 19]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2986
  Bounding Box: [1325.60, 1295.20, 1452.00, 1412.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [108, 106, 117, 114]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2983
  Bounding Box: [1779.20, 1544.80, 1881.60, 1667.20]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [143, 125, 150, 134]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2983
  Bounding Box: [2.90, 1747.20, 78.40, 1884.80]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 141, 8, 151]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2974
  Bounding Box: [1340.00, 1056.80, 1490.40, 1167.20]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [109, 87, 120, 95]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2974
  Bounding Box: [13.70, 678.40, 68.80, 784.80]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [6, 57, 8, 65]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2954
  Bounding Box: [849.60, 1060.00, 1054.40, 1266.40]
  Mask Area: 173 pixels
  Mask Ratio: 0.0061
  Mask BBox: [71, 87, 86, 102]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2949
  Bounding Box: [1841.60, 694.40, 1960.00, 805.60]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [148, 59, 157, 66]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2930
  Bounding Box: [889.60, 0.00, 993.60, 87.90]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [74, 4, 81, 10]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2930
  Bounding Box: [1907.20, 741.20, 2048.00, 950.40]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [153, 62, 164, 78]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2925
  Bounding Box: [1518.40, 1373.60, 1700.80, 1581.60]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [123, 112, 136, 127]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2910
  Bounding Box: [30.65, 5.60, 138.20, 117.60]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [7, 5, 14, 13]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2905
  Bounding Box: [1737.60, 605.20, 1926.40, 792.00]
  Mask Area: 173 pixels
  Mask Ratio: 0.0061
  Mask BBox: [140, 52, 154, 65]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2893
  Bounding Box: [1718.40, 0.00, 1910.40, 136.40]
  Mask Area: 138 pixels
  Mask Ratio: 0.0049
  Mask BBox: [139, 3, 153, 14]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2891
  Bounding Box: [1953.60, 1328.00, 2048.00, 1470.40]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [157, 108, 163, 118]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2891
  Bounding Box: [728.00, 1723.20, 795.20, 1841.60]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [61, 139, 66, 147]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2871
  Bounding Box: [1208.00, 1275.20, 1331.20, 1374.40]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [99, 104, 107, 111]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2869
  Bounding Box: [1504.00, 709.60, 1600.00, 863.20]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [122, 60, 128, 71]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2859
  Bounding Box: [690.00, 1170.40, 781.60, 1261.60]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [58, 96, 65, 102]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2849
  Bounding Box: [1047.20, 1028.80, 1141.60, 1168.00]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [86, 85, 93, 95]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2839
  Bounding Box: [512.40, 1966.40, 647.60, 2043.20]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [45, 158, 54, 163]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2827
  Bounding Box: [360.00, 37.10, 556.80, 253.20]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [33, 7, 47, 23]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [1349.60, 944.00, 1448.80, 1017.60]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [110, 78, 117, 83]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [1349.60, 969.60, 1448.80, 1043.20]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [110, 80, 117, 83]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [1375.20, 969.60, 1474.40, 1043.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [112, 80, 119, 83]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [1969.60, 603.60, 2043.20, 751.60]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [158, 52, 163, 62]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2822
  Bounding Box: [299.60, 1884.80, 446.80, 2035.20]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [28, 152, 38, 162]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2808
  Bounding Box: [1472.00, 756.80, 1606.40, 907.20]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [119, 64, 129, 74]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2798
  Bounding Box: [291.80, 590.40, 400.00, 679.20]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [27, 51, 35, 57]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2795
  Bounding Box: [560.80, 1076.00, 668.80, 1207.20]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [48, 89, 56, 98]

