import subprocess

def main():
    # 基本配置（按需修改）
    data_config = 'dataset-seg/datasets.yaml'
    model_config = 'models/segment/multi-yolov5n-mid-seg.yaml'
    epochs = 3
    batch_size = 8
    img_size = 640
    run_name = 'seg_experiment'

    print("极简YOLOv5分割训练 开始训练...")

    # 训练命令
    cmd = [
        'python', 'segment/train.py',
        '--data', data_config,
        '--cfg', model_config,
        '--epochs', str(epochs),
        '--batch-size', str(batch_size),
        '--img', str(img_size),
        '--name', run_name,
        '--multimodal'  # 若不需要双模态，删除此行
    ]
    print(f"执行命令: {' '.join(cmd)}")

    result = subprocess.run(cmd)

    if result.returncode == 0:
        print("\n训练完成!")
        print(f"训练结果保存在: runs/train/{run_name}/")
    else:
        print(f"\n训练过程中出现错误，退出码: {result.returncode}")


if __name__ == '__main__':
    main()
