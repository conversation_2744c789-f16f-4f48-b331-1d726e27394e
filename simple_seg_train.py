
import os
import sys
import torch
import yaml
from pathlib import Path

# 禁用git检查以避免训练时的错误
os.environ['GIT_PYTHON_REFRESH'] = 'quiet'

# 添加项目根目录到路径
FILE = Path(__file__).resolve()
ROOT = FILE.parents[0]
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

from models.yolo import SegmentationModel
from utils.segment.general import check_dataset, init_seeds, colorstr

from utils.torch_utils import select_device

def main():
    # 基本配置
    data_config = 'dataset-seg/datasets.yaml'  # 数据集配置文件
    model_config = 'models/segment/yolov5n-mid-seg.yaml'  # 模型配置文件
    epochs = 30  # 训练轮数（快速测试）
    batch_size = 8  # 批次大小
    img_size = 640  # 图像尺寸
    device = ''  # 设备 ('' = 自动选择)
    
    print(f"{colorstr('极简YOLOv5分割训练')} 开始训练...")
    
    # 初始化随机种子
    init_seeds(1)
    
    # 选择设备
    device = select_device(device)
    print(f"使用设备: {device}")
    
    # 检查数据集
    with open(data_config, encoding='utf-8') as f:
        data_dict = yaml.safe_load(f)
    
    data_dict = check_dataset(data_dict)
    nc = int(data_dict['nc'])  # 类别数
    names = data_dict['names']  # 类别名称
    print(f"数据集类别数: {nc}, 类别名称: {names}")
    
    # 创建模型
    model = SegmentationModel(model_config, ch=3, nc=nc, anchors=None).to(device)
    print(f"模型创建完成: {model_config}")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 使用原始的segment训练脚本进行训练
    print("\n开始使用官方segment训练脚本进行训练...")
    print("训练命令: python segment/train.py --data dataset-seg/datasets.yaml --cfg models/segment/yolov5n-seg.yaml --epochs 50 --batch-size 8 --img 640")
    
    # 执行训练命令
    import subprocess
    cmd = [
        'python', 'segment/train.py',
        '--data', data_config,
        '--cfg', model_config, 
        '--epochs', str(epochs),
        '--batch-size', str(batch_size),
        '--img', str(img_size),
        '--name', 'seg_experiment',
        '--multimodal'  # 启用双模态数据加载器
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 使用subprocess运行训练
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
                                 universal_newlines=True, bufsize=1)
        
        # 实时输出训练日志
        for line in process.stdout:
            print(line.strip())
            
        process.wait()
        
        if process.returncode == 0:
            print("\n训练完成!")
            print("训练结果保存在: runs/train/seg_experiment/")
        else:
            print(f"\n训练过程中出现错误，退出码: {process.returncode}")
            
    except Exception as e:
        print(f"训练过程中出现异常: {e}")

if __name__ == '__main__':
    main()