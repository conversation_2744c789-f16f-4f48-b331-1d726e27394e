#!/usr/bin/env python3
"""
测试多模态可视化修复效果
验证PPL和XPL图像对是否正确显示相同的标签和掩码
"""

import torch
import numpy as np
import cv2
from pathlib import Path
import sys
import os

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from utils.segment.plots import plot_images_and_masks
from utils.segment.multimodal_plots import plot_multimodal_comparison

def create_test_data():
    """创建测试用的多模态数据"""
    # 创建模拟的PPL和XPL图像
    batch_size = 2
    height, width = 640, 640
    
    # 生成随机图像数据
    ppl_images = torch.rand(batch_size, 3, height, width)
    xpl_images = torch.rand(batch_size, 3, height, width)
    
    # 为了让图像看起来不同，给XPL添加一些变化
    xpl_images = ppl_images * 0.8 + torch.rand_like(ppl_images) * 0.2
    
    # 创建目标标签 [img_idx, cls, cx, cy, w, h]
    targets = torch.tensor([
        [0, 0, 0.3, 0.3, 0.2, 0.2],  # 第一张图像的第一个目标
        [0, 1, 0.7, 0.7, 0.15, 0.15], # 第一张图像的第二个目标
        [1, 0, 0.4, 0.5, 0.25, 0.3],  # 第二张图像的第一个目标
        [1, 2, 0.6, 0.3, 0.18, 0.22], # 第二张图像的第二个目标
    ])
    
    # 创建对应的掩码 (overlap格式: 每个像素值表示实例ID)
    masks = torch.zeros(batch_size, height, width, dtype=torch.long)
    
    # 为第一张图像创建掩码
    # 目标1: 类别0，在(0.3, 0.3)位置
    y1_start, y1_end = int(0.2 * height), int(0.4 * height)
    x1_start, x1_end = int(0.2 * width), int(0.4 * width)
    masks[0, y1_start:y1_end, x1_start:x1_end] = 1
    
    # 目标2: 类别1，在(0.7, 0.7)位置
    y2_start, y2_end = int(0.625 * height), int(0.775 * height)
    x2_start, x2_end = int(0.625 * width), int(0.775 * width)
    masks[0, y2_start:y2_end, x2_start:x2_end] = 2
    
    # 为第二张图像创建掩码
    # 目标1: 类别0，在(0.4, 0.5)位置
    y3_start, y3_end = int(0.35 * height), int(0.65 * height)
    x3_start, x3_end = int(0.275 * width), int(0.525 * width)
    masks[1, y3_start:y3_end, x3_start:x3_end] = 1
    
    # 目标2: 类别2，在(0.6, 0.3)位置
    y4_start, y4_end = int(0.19 * height), int(0.41 * height)
    x4_start, x4_end = int(0.51 * width), int(0.69 * width)
    masks[1, y4_start:y4_end, x4_start:x4_end] = 2
    
    return (ppl_images, xpl_images), targets, masks

def test_multimodal_visualization():
    """测试多模态可视化功能"""
    print("创建测试数据...")
    images, targets, masks = create_test_data()
    
    # 类别名称
    names = ['矿物A', '矿物B', '矿物C']
    
    # 创建输出目录
    output_dir = Path("test_visualization_output")
    output_dir.mkdir(exist_ok=True)
    print(f"输出目录: {output_dir.absolute()}")
    
    print("测试多模态图像网格可视化...")
    
    # 测试1: 多模态图像网格
    try:
        fname1 = output_dir / "test_multimodal_grid.jpg"
        t1 = plot_images_and_masks(
            images=images,
            targets=targets,
            masks=masks,
            fname=str(fname1),
            names=names
        )
        # 等待保存线程完成
        try:
            t1.join()
        except Exception:
            pass
        print(f"✓ 多模态网格图像已保存到: {fname1}")
    except Exception as e:
        print(f"✗ 多模态网格可视化失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("测试多模态对比图...")
    
    # 测试2: 多模态对比图
    try:
        fname2 = output_dir / "test_multimodal_comparison.jpg"
        ppl_img, xpl_img = images[0][0], images[1][0]  # 取第一张图像
        mask = masks[0]  # 对应的掩码
        
        t2 = plot_multimodal_comparison(
            ppl_image=ppl_img,
            xpl_image=xpl_img,
            pred_masks=mask,
            gt_masks=None,
            fname=str(fname2),
            names=names
        )
        # 等待保存线程完成
        try:
            t2.join()
        except Exception:
            pass
        print(f"✓ 多模态对比图像已保存到: {fname2}")
    except Exception as e:
        print(f"✗ 多模态对比可视化失败: {e}")
        import traceback
        traceback.print_exc()

    print("\n测试完成！请检查生成的图像：")
    print(f"- 多模态网格图: {output_dir / 'test_multimodal_grid.jpg'}")
    print(f"- 多模态对比图: {output_dir / 'test_multimodal_comparison.jpg'}")
    print("\n期望结果：")
    print("1. PPL和XPL图像对应显示相同的边界框和类别标签")
    print("2. 掩码以半透明覆盖的形式正确显示在对应位置")
    print("3. 每个目标都有正确的颜色和标签名称")

if __name__ == "__main__":
    test_multimodal_visualization()