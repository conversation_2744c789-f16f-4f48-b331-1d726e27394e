
yolov5/
├── detect.py
├── train.py
├── val.py
│
├── models/
│   ├── __init__.py                   # 注册入口（把 SegFormerHead/SegFormerModel 暴露给外部）
│   ├── common.py
│   ├── router.py                     # 在多模态/多输入模型中，按名称取对应输入。该层本身不做任何变换，只把对应输入张量传下去。
│   ├── experimental.py               # 实验性模块 加载单个或多个 YOLOv5 模型权重，并返回模型/模型集成 (Ensemble)
│   ├── headformer.py                 # 如仍需兼容旧former头，保留；新头放 segformer 下
│   ├── pixel_adapter.py              # P3/P4/P5→ memory 四元组 + mask_features
│   ├── SegFormerHeadhead.py          # ★ SegFormerHead（queries/decoder/pred heads）MSDeformAttn 解码器2D位置编码 + level embedding滑窗融合/跨块合并/阈值化/插值回原图
│   └── segment/
│       ├── multi-yolov5n-mid-seg.yaml
│       ├── multi-yolov5n-mid2-seg.yaml
│       └── multi-yolov5n-formerhead-seg.yaml   # 旧配置可留存（不强依赖）
│
├── segformer/                            # ★ 新：SegFormer 体系（自带 train/val/predict）
│   ├── train.py                          # ★ SegFormer 训练闭环（使用 segformer 自己的数据/损失/头）
│   ├── val.py                            # ★ 评估 Dice/IoU/mAP（按 segformer 的输出规范）
│   ├── predict.py                        # ★ 推理（支持 tile/overlap/halo & 拼接融合）
│   ├── __init__.py
│
├── utils/
│   ├── downloads.py
│   ├── torch_utils.py
│   ├── autoanchor.py
│   ├── autobatch.py
│   ├── callbacks.py
│   ├── loggers/
│   ├── aws/
│   └── segment/                          # 旧 segment 体系（保留互不影响）
│       ├── dataloaders.py                # 数据加载
│       ├── general.py                    # YOLOv5 的通用工具库，涵盖了环境检测（如是否在 Colab/Kaggle/Docker、是否联网）、路径与文件检查、随机数种子初始化、颜色/日志输出、以及与分割相关的通用函数（如坐标变换、mask 处理、绘图工具），它的作用是为 训练、验证、推理全过程 提供底层支持，让主流程代码更简洁、稳定、可移植。
│       ├── augmentation.py               # 图像增强函数
│       ├── metrics.py                    # YOLOv5 分割模型性能评估指标模块
│       ├── plots.py                      # 分割可视化工具（合并精简版
│   └── segformer/   
│       ├── dataloaders.py                # 数据加载
│       ├── loss.py                       # ★ 匈牙利匹配（分类 + Dice/BCE 代价）
│       ├── metrics.py                    # 分割指标（Dice/IoU/mAP）与统计
│       ├── plots.py                      # 可视化（mask/轮廓/叠加）
│       └── __init__.py
│
├── data-seg/
│   ├── datasets.yaml           # 数据集声明（nc/names/train/val/test）
│   └──images/                  #  单偏光图像
│   │   └──train/               # 训练集图像
│   │   └──val/                 # 验证集图像
│   └──images_xpl/              #  正交光图像
│   │   └──train/               # 训练集图像
│   │   └──val/                 # 验证集图像
│   └──labels/                  #  YOLOv5-seg 多边形 txt
│   │   └──train/               # 训练集标签
│   │   └──val/                 # 验证集标签

“former 检测头”（SegFormerHead）设计思路
    这套 head 是查询式、集合预测的：我们不再像 YOLO 那样在每个网格点回归框/掩码，
    而是初始化 Nq 个“查询”，每个查询代表一条潜在实例。每层解码器用 自注意力建模查询间关系，
    再用 Deformable Cross-Attention 在多尺度特征（P3/P4/P5 展平后的 memory）上做稀疏采样；
    采样位置由每个查询的 reference points 指引，因此能以极少的点覆盖关键区域，既快又稳。
    同时在多尺度 memory 里，我们给每一层特征注入 2D 位置编码与 level embedding，
    让模型知道“这是 20×20 还是 80×80 的特征”，从而有效融合尺度信息。

    解码器输出的每个查询向量同时经过两条轻头：一条输出类别（含 no-object），
    一条输出 mask embedding。掩码不是从 memory 直接“画”出来，而是把 
    mask embedding 与一张上采样到高分辨率的 mask_features（例如从 P3 上采样到 160×160）
    做点积生成（本质是一个每像素线性分类器）。这种“查询向量 × 高分辨率特征”的方式，
    把定位/语义交给查询，把细节/边界交给高分辨率特征，兼顾精度与速度。

    训练时采用 Hungarian 匹配（集合预测思想）：预测与 GT 一一对应，
    代价由分类 + Dice + BCE 组成，并在每层解码器输出上加 aux loss 
    稳定收敛。由于是固定 Nq 的集合预测，当图像目标极多时，可通过提升 Nq设计为100
    Nq 或在推理时启用滑窗解决；拼接阶段对重叠区域做 logits 融合，
    并在全图维度进行跨块实例合并，即可避免“切块缺角”的问题。最终若需要 bbox，只需对掩码取外接框即可无缝用于检测指标。