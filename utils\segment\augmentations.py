
"""图像增强函数 (Image augmentation functions)."""

import math
import random
import cv2
import numpy as np
import torch
import torchvision.transforms as T
import torchvision.transforms.functional as TF

from utils.segment.general import LOGGER, check_version, colorstr, resample_segments, segment2box, xywhn2xyxy
from utils.segment.metrics import bbox_ioa

IMAGENET_MEAN = 0.485, 0.456, 0.406  # RGB mean
IMAGENET_STD = 0.229, 0.224, 0.225  # RGB standard deviation


class Albumentations:
    """Provides optional data augmentation for YOLOv5 using Albumentations library if installed."""

    def __init__(self, size=640):
        """Initializes Albumentations class for optional data augmentation in YOLOv5 with specified input size."""
        self.transform = None
        prefix = colorstr("albumentations: ")
        try:
            import albumentations as A

            check_version(A.__version__, "1.0.3", hard=True)  # version requirement

            T = [
                A.RandomResizedCrop(height=size, width=size, scale=(0.8, 1.0), ratio=(0.9, 1.11), p=0.0),
                <PERSON><PERSON>(p=0.01),
                <PERSON><PERSON>(p=0.01),
                <PERSON><PERSON>(p=0.01),
                <PERSON><PERSON><PERSON><PERSON><PERSON>E(p=0.01),
                A.RandomBrightnessContrast(p=0.0),
                A.RandomGamma(p=0.0),
                A.ImageCompression(quality_lower=75, p=0.0),
            ]  # transforms
            self.transform = A.Compose(T, bbox_params=A.BboxParams(format="yolo", label_fields=["class_labels"]))

            LOGGER.info(prefix + ", ".join(f"{x}".replace("always_apply=False, ", "") for x in T if x.p))
        except ImportError:  # package not installed, skip
            pass
        except Exception as e:
            LOGGER.info(f"{prefix}{e}")

    def __call__(self, im, labels, p=1.0, im_xpl=None):
        """
        Applies transformations to an image and labels with probability `p`, returning updated image and labels.
        
        Args:
            im: PPL图像 (RGB模态)
            labels: 标签数组
            p: 应用变换的概率
            im_xpl: XPL图像 (X模态)，可选参数用于双模态同步变换
            
        Returns:
            如果是单模态: (im, labels)
            如果是双模态: (im, labels, im_xpl)
        """
        if self.transform and random.random() < p:
            if im_xpl is not None:
                # 双模态同步变换：使用相同的随机种子确保变换一致性
                seed = random.randint(0, 2**32 - 1)
                
                # 变换PPL图像
                random.seed(seed)
                np.random.seed(seed)
                new_ppl = self.transform(image=im, bboxes=labels[:, 1:], class_labels=labels[:, 0])
                
                # 使用相同种子变换XPL图像（不包含标签，因为标签已经在PPL中处理）
                random.seed(seed)
                np.random.seed(seed)
                new_xpl = self.transform(image=im_xpl, bboxes=labels[:, 1:], class_labels=labels[:, 0])
                
                im = new_ppl["image"]
                im_xpl = new_xpl["image"]
                labels = np.array([[c, *b] for c, b in zip(new_ppl["class_labels"], new_ppl["bboxes"])])
                
                return im, labels, im_xpl
            else:
                # 单模态变换（保持原有逻辑）
                new = self.transform(image=im, bboxes=labels[:, 1:], class_labels=labels[:, 0])  # transformed
                im, labels = new["image"], np.array([[c, *b] for c, b in zip(new["class_labels"], new["bboxes"])])
        
        # 返回结果
        if im_xpl is not None:
            return im, labels, im_xpl
        else:
            return im, labels


def augment_hsv(im, hgain=0.5, sgain=0.5, vgain=0.5, im_xpl=None):
    """
    Applies HSV color-space augmentation to an image with random gains for hue, saturation, and value.
    
    Args:
        im: PPL图像 (RGB模态)
        hgain: 色调增益
        sgain: 饱和度增益  
        vgain: 亮度增益
        im_xpl: XPL图像 (X模态)，可选参数用于双模态同步增强
        
    Returns:
        None (原地修改图像) 或 双模态时返回修改后的XPL图像
    """
    if hgain or sgain or vgain:
        r = np.random.uniform(-1, 1, 3) * [hgain, sgain, vgain] + 1  # random gains
        
        # 处理PPL图像（RGB模态）
        hue, sat, val = cv2.split(cv2.cvtColor(im, cv2.COLOR_BGR2HSV))
        dtype = im.dtype  # uint8

        x = np.arange(0, 256, dtype=r.dtype)
        lut_hue = ((x * r[0]) % 180).astype(dtype)
        lut_sat = np.clip(x * r[1], 0, 255).astype(dtype)
        lut_val = np.clip(x * r[2], 0, 255).astype(dtype)

        im_hsv = cv2.merge((cv2.LUT(hue, lut_hue), cv2.LUT(sat, lut_sat), cv2.LUT(val, lut_val)))
        cv2.cvtColor(im_hsv, cv2.COLOR_HSV2BGR, dst=im)  # no return needed
        
        # 如果提供了XPL图像，应用相同的HSV变换
        if im_xpl is not None:
            hue_xpl, sat_xpl, val_xpl = cv2.split(cv2.cvtColor(im_xpl, cv2.COLOR_BGR2HSV))
            
            # 使用相同的查找表确保变换一致性
            im_hsv_xpl = cv2.merge((cv2.LUT(hue_xpl, lut_hue), cv2.LUT(sat_xpl, lut_sat), cv2.LUT(val_xpl, lut_val)))
            cv2.cvtColor(im_hsv_xpl, cv2.COLOR_HSV2BGR, dst=im_xpl)  # no return needed
            return im, im_xpl  # 返回双模态结果
    
    # 如果没有XPL图像，只返回PPL图像
    return im


def copy_paste(im, labels, segments, p=0.5, im_xpl=None):
    """
    Applies Copy-Paste augmentation by flipping and merging segments and labels on an image.

    Args:
        im: PPL图像 (RGB模态)
        labels: 标签数组
        segments: 分割段
        p: 应用增强的概率
        im_xpl: XPL图像 (X模态)，可选参数用于双模态同步增强
        
    Returns:
        如果是单模态: (im, labels, segments)
        如果是双模态: (im, labels, segments, im_xpl)
        
    Details at https://arxiv.org/abs/2012.07177.
    """
    n = len(segments)
    if p and n:
        h, w, c = im.shape  # height, width, channels
        im_new = np.zeros(im.shape, np.uint8)
        
        # 如果是双模态，为XPL图像创建相同的掩码
        if im_xpl is not None:
            im_new_xpl = np.zeros(im_xpl.shape, np.uint8)
        
        for j in random.sample(range(n), k=round(p * n)):
            l, s = labels[j], segments[j]
            box = w - l[3], l[2], w - l[1], l[4]
            ioa = bbox_ioa(box, labels[:, 1:5])  # intersection over area
            if (ioa < 0.30).all():  # allow 30% obscuration of existing labels
                labels = np.concatenate((labels, [[l[0], *box]]), 0)
                segments.append(np.concatenate((w - s[:, 0:1], s[:, 1:2]), 1))
                cv2.drawContours(im_new, [segments[j].astype(np.int32)], -1, (1, 1, 1), cv2.FILLED)
                
                # 为XPL图像绘制相同的轮廓掩码
                if im_xpl is not None:
                    cv2.drawContours(im_new_xpl, [segments[j].astype(np.int32)], -1, (1, 1, 1), cv2.FILLED)

        # 对PPL图像应用翻转和复制粘贴
        result = cv2.flip(im, 1)  # augment segments (flip left-right)
        i = cv2.flip(im_new, 1).astype(bool)
        im[i] = result[i]  # cv2.imwrite('debug.jpg', im)  # debug
        
        # 对XPL图像应用相同的翻转和复制粘贴
        if im_xpl is not None:
            result_xpl = cv2.flip(im_xpl, 1)  # augment segments (flip left-right)
            i_xpl = cv2.flip(im_new_xpl, 1).astype(bool)
            im_xpl[i_xpl] = result_xpl[i_xpl]

    # 返回结果
    if im_xpl is not None:
        return im, labels, segments, im_xpl
    else:
        return im, labels, segments


def letterbox(im, new_shape=(640, 640), color=(114, 114, 114), auto=True, scaleFill=False, scaleup=True, stride=32):
    """Resizes and pads image to new_shape with stride-multiple constraints, returns resized image, ratio, padding."""
    shape = im.shape[:2]  # current shape [height, width]
    if isinstance(new_shape, int):
        new_shape = (new_shape, new_shape)

    # Scale ratio (new / old)
    r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
    if not scaleup:  # only scale down, do not scale up (for better val mAP)
        r = min(r, 1.0)

    # Compute padding
    ratio = r, r  # width, height ratios
    new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
    dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
    if auto:  # minimum rectangle
        dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
    elif scaleFill:  # stretch
        dw, dh = 0.0, 0.0
        new_unpad = (new_shape[1], new_shape[0])
        ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # width, height ratios

    dw /= 2  # divide padding into 2 sides
    dh /= 2

    if shape[::-1] != new_unpad:  # resize
        im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)
    top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
    left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
    im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # add border
    return im, ratio, (dw, dh)



# segment2box 函数已移动到 utils.segment.general 模块中

# resample_segments 函数已移动到 utils.segment.general 模块中

def box_candidates(box1, box2, wh_thr=2, ar_thr=100, area_thr=0.1, eps=1e-16):
    """
    Filters bounding box candidates by minimum width-height threshold `wh_thr` (pixels), aspect ratio threshold
    `ar_thr`, and area ratio threshold `area_thr`.

    box1(4,n) is before augmentation, box2(4,n) is after augmentation.
    """
    w1, h1 = box1[2] - box1[0], box1[3] - box1[1]
    w2, h2 = box2[2] - box2[0], box2[3] - box2[1]
    ar = np.maximum(w2 / (h2 + eps), h2 / (w2 + eps))  # aspect ratio
    return (w2 > wh_thr) & (h2 > wh_thr) & (w2 * h2 / (w1 * h1 + eps) > area_thr) & (ar < ar_thr)  # candidates

def mixup(im, labels, segments, im2, labels2, segments2, im_xpl=None, im2_xpl=None):
    """
    双模态mixup增强函数
    
    Args:
        im: PPL图像1
        labels: 图像1的标签
        segments: 图像1的分割信息
        im2: PPL图像2
        labels2: 图像2的标签
        segments2: 图像2的分割信息
        im_xpl: XPL图像1 (可选)
        im2_xpl: XPL图像2 (可选)
    
    Returns:
        如果提供XPL图像: (im, labels, segments, im_xpl)
        否则: (im, labels, segments)
    """
    r = np.random.beta(32.0, 32.0)  # beta分布采样，决定mixup比例
    im = (im * r + im2 * (1 - r)).astype(np.uint8)  # 图像加权融合
    
    # 如果提供了XPL图像，应用相同的mixup比例
    if im_xpl is not None and im2_xpl is not None:
        im_xpl = (im_xpl * r + im2_xpl * (1 - r)).astype(np.uint8)
    
    labels = np.concatenate((labels, labels2), 0)  # 合并两张图的标签
    segments = np.concatenate((segments, segments2), 0)  # 合并分割信息
    
    if im_xpl is not None:
        return im, labels, segments, im_xpl
    return im, labels, segments


def random_perspective(
    im, targets=(), segments=(), degrees=10, translate=0.1, scale=0.1, shear=10, perspective=0.0, border=(0, 0), im_xpl=None
):
    """
    双模态随机透视变换（Random Perspective）
    - 作用：对图像和标签应用随机旋转、平移、缩放、错切、透视等增强操作。
    - 参数：
        im: PPL输入图像
        targets: 目标框 [cls, x1, y1, x2, y2]
        segments: 多边形分割点
        degrees: 最大旋转角度
        translate: 平移比例
        scale: 缩放比例
        shear: 错切角度
        perspective: 透视比例
        border: 图像边框扩展
        im_xpl: XPL输入图像 (可选)
    
    Returns:
        如果提供XPL图像: (im, targets, new_segments, im_xpl)
        否则: (im, targets, new_segments)
    """
    height = im.shape[0] + border[0] * 2  # 新图像高度
    width = im.shape[1] + border[1] * 2   # 新图像宽度

    # ----------------- 构建变换矩阵 -----------------
    # 1) 平移到图像中心
    C = np.eye(3)
    C[0, 2] = -im.shape[1] / 2
    C[1, 2] = -im.shape[0] / 2

    # 2) 透视变换
    P = np.eye(3)
    P[2, 0] = random.uniform(-perspective, perspective)  # x方向透视
    P[2, 1] = random.uniform(-perspective, perspective)  # y方向透视

    # 3) 旋转+缩放
    R = np.eye(3)
    a = random.uniform(-degrees, degrees)  # 随机旋转角度
    s = random.uniform(1 - scale, 1 + scale)  # 随机缩放比例
    R[:2] = cv2.getRotationMatrix2D(angle=a, center=(0, 0), scale=s)

    # 4) 错切（shear）
    S = np.eye(3)
    S[0, 1] = math.tan(random.uniform(-shear, shear) * math.pi / 180)  # x方向错切
    S[1, 0] = math.tan(random.uniform(-shear, shear) * math.pi / 180)  # y方向错切

    # 5) 平移（translation）
    T = np.eye(3)
    T[0, 2] = random.uniform(0.5 - translate, 0.5 + translate) * width
    T[1, 2] = random.uniform(0.5 - translate, 0.5 + translate) * height

    # 6) 合并所有变换矩阵
    M = T @ S @ R @ P @ C  # 注意顺序（右到左）

    # ----------------- 图像变换 -----------------
    if (border[0] != 0) or (border[1] != 0) or (M != np.eye(3)).any():
        if perspective:  # 透视变换
            im = cv2.warpPerspective(im, M, dsize=(width, height), borderValue=(114, 114, 114))
            # 如果提供了XPL图像，应用相同的透视变换
            if im_xpl is not None:
                im_xpl = cv2.warpPerspective(im_xpl, M, dsize=(width, height), borderValue=(114, 114, 114))
        else:  # 仿射变换
            im = cv2.warpAffine(im, M[:2], dsize=(width, height), borderValue=(114, 114, 114))
            # 如果提供了XPL图像，应用相同的仿射变换
            if im_xpl is not None:
                im_xpl = cv2.warpAffine(im_xpl, M[:2], dsize=(width, height), borderValue=(114, 114, 114))

    # ----------------- 标签变换 -----------------
    new_segments = []
    if n := len(targets):  # 如果有目标
        new = np.zeros((n, 4))
        segments = resample_segments(segments)  # 上采样分割点，提高精度
        for i, segment in enumerate(segments):
            xy = np.ones((len(segment), 3))  # 构建齐次坐标
            xy[:, :2] = segment
            xy = xy @ M.T  # 应用变换矩阵
            xy = xy[:, :2] / xy[:, 2:3] if perspective else xy[:, :2]  # 透视归一化或仿射

            # 转换成 bbox
            new[i] = segment2box(xy, width, height)
            new_segments.append(xy)

        # 过滤掉过小或无效的目标框
        i = box_candidates(box1=targets[:, 1:5].T * s, box2=new.T, area_thr=0.01)
        targets = targets[i]
        targets[:, 1:5] = new[i]
        new_segments = np.array(new_segments)[i]

    if im_xpl is not None:
        return im, targets, new_segments, im_xpl
    return im, targets, new_segments
