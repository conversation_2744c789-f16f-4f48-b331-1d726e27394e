Image: tile_0115.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8843
  Bounding Box: [732.00, 1649.60, 1058.40, 2036.80]
  Mask Area: 510 pixels
  Mask Ratio: 0.0181
  Mask BBox: [62, 134, 86, 163]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8804
  Bounding Box: [1409.60, 292.80, 1643.20, 514.40]
  Mask Area: 233 pixels
  Mask Ratio: 0.0083
  Mask BBox: [115, 27, 131, 44]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8506
  Bounding Box: [1678.40, 889.60, 1864.00, 1134.40]
  Mask Area: 205 pixels
  Mask Ratio: 0.0073
  Mask BBox: [136, 74, 149, 92]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8418
  Bounding Box: [561.20, 387.20, 932.80, 877.60]
  Mask Area: 826 pixels
  Mask Ratio: 0.0293
  Mask BBox: [48, 35, 76, 72]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8418
  Bounding Box: [1522.40, 774.40, 1745.60, 980.80]
  Mask Area: 240 pixels
  Mask Ratio: 0.0085
  Mask BBox: [123, 65, 140, 80]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8389
  Bounding Box: [1340.80, 521.20, 1496.00, 671.60]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [109, 45, 119, 56]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8301
  Bounding Box: [1836.80, 5.25, 2016.00, 164.00]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [148, 5, 161, 16]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8203
  Bounding Box: [1364.80, 1269.60, 1635.20, 1627.20]
  Mask Area: 372 pixels
  Mask Ratio: 0.0132
  Mask BBox: [111, 104, 131, 131]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8193
  Bounding Box: [1536.00, 1464.00, 1856.00, 1828.80]
  Mask Area: 502 pixels
  Mask Ratio: 0.0178
  Mask BBox: [124, 119, 148, 145]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8154
  Bounding Box: [8.50, 1456.80, 233.60, 1849.60]
  Mask Area: 406 pixels
  Mask Ratio: 0.0144
  Mask BBox: [5, 118, 22, 148]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8140
  Bounding Box: [1228.00, 14.60, 1426.40, 307.00]
  Mask Area: 236 pixels
  Mask Ratio: 0.0084
  Mask BBox: [100, 6, 114, 26]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8130
  Bounding Box: [1907.20, 1563.20, 2022.40, 1774.40]
  Mask Area: 112 pixels
  Mask Ratio: 0.0040
  Mask BBox: [153, 127, 161, 142]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.8101
  Bounding Box: [667.20, 739.60, 926.40, 966.40]
  Mask Area: 199 pixels
  Mask Ratio: 0.0071
  Mask BBox: [57, 62, 76, 79]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.8096
  Bounding Box: [1095.20, 342.80, 1332.00, 546.80]
  Mask Area: 176 pixels
  Mask Ratio: 0.0062
  Mask BBox: [90, 31, 106, 44]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.8076
  Bounding Box: [539.20, 1207.20, 732.80, 1444.00]
  Mask Area: 190 pixels
  Mask Ratio: 0.0067
  Mask BBox: [47, 99, 61, 116]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.8047
  Bounding Box: [154.40, 1274.40, 281.20, 1452.00]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [17, 104, 25, 116]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.8042
  Bounding Box: [1212.80, 904.00, 1430.40, 1172.80]
  Mask Area: 271 pixels
  Mask Ratio: 0.0096
  Mask BBox: [99, 75, 115, 94]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.8037
  Bounding Box: [1407.20, 902.40, 1596.80, 1172.80]
  Mask Area: 206 pixels
  Mask Ratio: 0.0073
  Mask BBox: [114, 75, 128, 94]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.8018
  Bounding Box: [1027.20, 1489.60, 1315.20, 1777.60]
  Mask Area: 357 pixels
  Mask Ratio: 0.0126
  Mask BBox: [85, 121, 106, 142]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.8008
  Bounding Box: [496.00, 1361.60, 684.80, 1660.80]
  Mask Area: 255 pixels
  Mask Ratio: 0.0090
  Mask BBox: [43, 111, 57, 133]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7935
  Bounding Box: [668.80, 1116.00, 855.20, 1420.00]
  Mask Area: 278 pixels
  Mask Ratio: 0.0098
  Mask BBox: [57, 92, 70, 114]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7930
  Bounding Box: [510.40, 1811.20, 726.40, 2048.00]
  Mask Area: 226 pixels
  Mask Ratio: 0.0080
  Mask BBox: [44, 146, 60, 164]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7915
  Bounding Box: [64.60, 409.20, 201.60, 570.00]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [10, 37, 19, 48]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7900
  Bounding Box: [1702.40, 176.40, 1955.20, 379.20]
  Mask Area: 226 pixels
  Mask Ratio: 0.0080
  Mask BBox: [137, 18, 156, 33]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7871
  Bounding Box: [68.40, 632.80, 331.60, 968.80]
  Mask Area: 379 pixels
  Mask Ratio: 0.0134
  Mask BBox: [10, 54, 29, 78]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7861
  Bounding Box: [1680.00, 1852.80, 1849.60, 2006.40]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [136, 149, 148, 160]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7783
  Bounding Box: [1665.60, 2.80, 1844.80, 228.80]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [135, 5, 148, 21]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7783
  Bounding Box: [1008.80, 762.40, 1143.20, 914.40]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [83, 64, 93, 74]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7769
  Bounding Box: [328.40, 8.80, 526.00, 414.40]
  Mask Area: 284 pixels
  Mask Ratio: 0.0101
  Mask BBox: [30, 7, 44, 36]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7720
  Bounding Box: [867.20, 0.00, 1009.60, 251.20]
  Mask Area: 159 pixels
  Mask Ratio: 0.0056
  Mask BBox: [72, 4, 82, 20]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7710
  Bounding Box: [1449.60, 1681.60, 1600.00, 1934.40]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [118, 136, 127, 153]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7676
  Bounding Box: [1308.80, 678.80, 1500.80, 904.00]
  Mask Area: 205 pixels
  Mask Ratio: 0.0073
  Mask BBox: [107, 58, 121, 74]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7637
  Bounding Box: [1176.00, 1892.80, 1310.40, 2048.00]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [96, 152, 106, 164]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7622
  Bounding Box: [326.40, 1431.20, 497.60, 1614.40]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [30, 116, 42, 130]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7598
  Bounding Box: [117.10, 7.25, 223.20, 144.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [14, 5, 21, 14]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7568
  Bounding Box: [1872.00, 296.00, 2028.80, 469.60]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [151, 29, 161, 40]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7539
  Bounding Box: [1828.80, 702.80, 1985.60, 981.60]
  Mask Area: 208 pixels
  Mask Ratio: 0.0074
  Mask BBox: [147, 59, 158, 80]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7529
  Bounding Box: [379.60, 1024.80, 586.80, 1400.80]
  Mask Area: 338 pixels
  Mask Ratio: 0.0120
  Mask BBox: [34, 85, 49, 113]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7485
  Bounding Box: [1004.80, 1148.00, 1240.00, 1333.60]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [83, 94, 100, 104]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7471
  Bounding Box: [833.60, 1434.40, 969.60, 1595.20]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [70, 117, 79, 127]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7446
  Bounding Box: [1060.00, 1284.80, 1184.80, 1470.40]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [87, 105, 96, 118]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7422
  Bounding Box: [256.80, 1833.60, 453.20, 1984.00]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [25, 148, 38, 157]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7373
  Bounding Box: [1279.20, 1672.00, 1436.00, 1931.20]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [104, 135, 116, 153]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7339
  Bounding Box: [117.50, 967.20, 278.40, 1181.60]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [14, 80, 25, 96]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7310
  Bounding Box: [19.30, 1803.20, 225.20, 2046.40]
  Mask Area: 238 pixels
  Mask Ratio: 0.0084
  Mask BBox: [6, 145, 21, 163]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7285
  Bounding Box: [4.55, 1224.00, 169.20, 1436.80]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [5, 100, 17, 115]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7197
  Bounding Box: [277.40, 26.30, 357.80, 212.80]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [26, 7, 31, 20]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7188
  Bounding Box: [504.80, 1577.60, 738.40, 1804.80]
  Mask Area: 173 pixels
  Mask Ratio: 0.0061
  Mask BBox: [44, 128, 61, 144]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7173
  Bounding Box: [161.20, 534.00, 342.00, 693.20]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [17, 46, 29, 57]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7168
  Bounding Box: [908.80, 424.00, 1009.60, 576.00]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [75, 38, 82, 47]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.7153
  Bounding Box: [1840.00, 1675.20, 2035.20, 1947.20]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [148, 135, 162, 156]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.7080
  Bounding Box: [1335.20, 1960.00, 1487.20, 2033.60]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [109, 158, 120, 162]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.7075
  Bounding Box: [599.20, 0.00, 738.40, 114.00]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [51, 4, 61, 12]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.7031
  Bounding Box: [440.00, 1963.20, 553.60, 2027.20]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [39, 158, 47, 162]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.7012
  Bounding Box: [1859.20, 966.40, 1948.80, 1088.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [150, 80, 155, 88]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.7007
  Bounding Box: [497.20, 544.00, 583.60, 662.40]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [43, 47, 49, 55]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6987
  Bounding Box: [8.50, 1013.60, 84.70, 1127.20]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [5, 84, 8, 92]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6987
  Bounding Box: [454.80, 370.80, 556.40, 524.40]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [40, 33, 47, 44]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6982
  Bounding Box: [1480.80, 508.40, 1627.20, 696.40]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [120, 44, 131, 58]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6963
  Bounding Box: [1087.20, 62.40, 1263.20, 304.80]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [89, 9, 102, 27]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6924
  Bounding Box: [1460.80, 109.20, 1632.00, 316.00]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [119, 13, 130, 26]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6904
  Bounding Box: [236.40, 1442.40, 331.60, 1696.00]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [23, 117, 29, 136]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6899
  Bounding Box: [1913.60, 1920.00, 2048.00, 2035.20]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [154, 154, 164, 162]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6895
  Bounding Box: [1592.00, 1923.20, 1723.20, 2038.40]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [129, 155, 138, 163]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6890
  Bounding Box: [994.40, 507.20, 1116.00, 780.00]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [82, 44, 91, 64]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6870
  Bounding Box: [976.80, 31.60, 1130.40, 266.60]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [81, 7, 92, 24]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6826
  Bounding Box: [445.60, 709.60, 596.80, 957.60]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [39, 60, 49, 78]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6821
  Bounding Box: [1652.80, 1226.40, 1809.60, 1351.20]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [134, 100, 145, 108]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6689
  Bounding Box: [1307.20, 323.20, 1443.20, 520.00]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [107, 30, 115, 44]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6680
  Bounding Box: [705.60, 1420.80, 832.00, 1659.20]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [60, 115, 68, 132]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6680
  Bounding Box: [1508.80, 532.00, 1704.00, 776.80]
  Mask Area: 226 pixels
  Mask Ratio: 0.0080
  Mask BBox: [122, 46, 137, 64]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6670
  Bounding Box: [1588.80, 1771.20, 1697.60, 1908.80]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [129, 143, 136, 153]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6660
  Bounding Box: [815.20, 1100.80, 972.00, 1422.40]
  Mask Area: 218 pixels
  Mask Ratio: 0.0077
  Mask BBox: [68, 90, 79, 114]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6621
  Bounding Box: [1038.40, 1672.00, 1128.00, 1809.60]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [86, 135, 91, 144]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6587
  Bounding Box: [1090.40, 567.60, 1306.40, 808.00]
  Mask Area: 225 pixels
  Mask Ratio: 0.0080
  Mask BBox: [91, 49, 106, 67]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6543
  Bounding Box: [1924.80, 1009.60, 2048.00, 1188.80]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [155, 83, 164, 96]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6519
  Bounding Box: [1264.00, 1156.00, 1467.20, 1292.00]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [103, 95, 118, 104]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6509
  Bounding Box: [1471.20, 1963.20, 1606.40, 2030.40]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [119, 158, 129, 162]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6509
  Bounding Box: [323.60, 625.20, 425.20, 862.40]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [30, 53, 37, 71]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6484
  Bounding Box: [377.20, 895.20, 549.20, 1074.40]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [35, 74, 46, 86]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6460
  Bounding Box: [1062.40, 1036.00, 1184.00, 1162.40]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [87, 85, 96, 94]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6440
  Bounding Box: [1931.20, 1576.00, 2048.00, 1780.80]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [155, 128, 164, 143]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6348
  Bounding Box: [1840.00, 1846.40, 1980.80, 2016.00]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [148, 149, 158, 161]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6338
  Bounding Box: [248.80, 971.20, 397.20, 1126.40]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [24, 80, 35, 91]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6323
  Bounding Box: [1603.20, 431.60, 1696.00, 526.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [130, 38, 136, 45]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6318
  Bounding Box: [1144.00, 972.00, 1232.00, 1096.80]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [94, 80, 99, 89]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.6299
  Bounding Box: [241.80, 852.00, 376.80, 989.60]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [23, 71, 33, 81]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.6284
  Bounding Box: [20.95, 905.60, 155.40, 1022.40]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [6, 75, 15, 83]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.6284
  Bounding Box: [530.00, 145.30, 745.20, 397.60]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [46, 16, 62, 35]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.6274
  Bounding Box: [532.80, 1014.40, 732.80, 1156.80]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [46, 84, 61, 94]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.6255
  Bounding Box: [1825.60, 581.60, 1912.00, 692.80]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [147, 50, 153, 58]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.6255
  Bounding Box: [1747.20, 372.00, 1920.00, 521.60]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [141, 34, 153, 44]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.6196
  Bounding Box: [288.20, 1630.40, 451.60, 1848.00]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [27, 132, 38, 148]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.6187
  Bounding Box: [1913.60, 172.00, 2048.00, 302.40]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [154, 18, 163, 26]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.6177
  Bounding Box: [867.20, 590.00, 1016.00, 847.20]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [72, 51, 83, 69]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.6152
  Bounding Box: [1032.80, 0.00, 1186.40, 84.20]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [85, 4, 96, 9]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.6147
  Bounding Box: [1161.60, 1380.80, 1288.00, 1512.00]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [95, 112, 104, 121]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.6133
  Bounding Box: [1160.00, 1908.80, 1368.00, 2048.00]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [95, 154, 110, 164]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.6064
  Bounding Box: [1148.00, 716.80, 1327.20, 985.60]
  Mask Area: 200 pixels
  Mask Ratio: 0.0071
  Mask BBox: [94, 60, 107, 80]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.6025
  Bounding Box: [924.00, 1423.20, 1039.20, 1595.20]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [77, 116, 85, 128]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.6001
  Bounding Box: [1963.20, 896.80, 2043.20, 1077.60]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [158, 75, 163, 88]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5947
  Bounding Box: [1266.40, 488.00, 1370.40, 578.40]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [103, 43, 111, 49]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5942
  Bounding Box: [1753.60, 1120.00, 1929.60, 1296.00]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [141, 92, 154, 105]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5908
  Bounding Box: [1026.40, 1883.20, 1103.20, 1992.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [85, 152, 90, 159]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5898
  Bounding Box: [762.40, 172.80, 1029.60, 478.80]
  Mask Area: 287 pixels
  Mask Ratio: 0.0102
  Mask BBox: [64, 18, 84, 41]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5854
  Bounding Box: [1063.20, 1944.00, 1181.60, 2048.00]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [88, 156, 96, 163]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5776
  Bounding Box: [1764.80, 1334.40, 1953.60, 1640.00]
  Mask Area: 221 pixels
  Mask Ratio: 0.0078
  Mask BBox: [142, 109, 156, 132]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5767
  Bounding Box: [1616.00, 64.00, 1689.60, 252.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [131, 9, 135, 23]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5742
  Bounding Box: [501.60, 8.20, 595.20, 84.90]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [44, 5, 49, 8]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5703
  Bounding Box: [1620.80, 864.00, 1844.80, 1120.00]
  Mask Area: 263 pixels
  Mask Ratio: 0.0093
  Mask BBox: [131, 72, 148, 91]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5640
  Bounding Box: [1229.60, 1275.20, 1421.60, 1508.80]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [101, 104, 112, 121]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5625
  Bounding Box: [700.80, 13.60, 870.40, 137.40]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [59, 6, 71, 14]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5591
  Bounding Box: [338.40, 504.40, 424.00, 653.20]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [31, 44, 37, 55]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5527
  Bounding Box: [1624.00, 1144.00, 1704.00, 1249.60]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [131, 94, 137, 101]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5527
  Bounding Box: [1902.40, 1316.00, 2027.20, 1493.60]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [153, 107, 162, 120]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5508
  Bounding Box: [177.80, 1147.20, 353.00, 1310.40]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [18, 94, 31, 106]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5454
  Bounding Box: [189.20, 1720.00, 301.60, 1867.20]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [19, 139, 27, 149]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5430
  Bounding Box: [57.60, 580.00, 162.00, 669.60]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [9, 50, 16, 56]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5366
  Bounding Box: [1582.40, 244.40, 1681.60, 349.20]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [128, 24, 134, 31]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.5317
  Bounding Box: [1636.80, 1373.60, 1787.20, 1496.80]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [132, 112, 143, 119]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.5312
  Bounding Box: [0.00, 32.40, 169.60, 238.20]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [4, 7, 16, 22]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.5166
  Bounding Box: [987.20, 338.40, 1147.20, 609.60]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [82, 31, 93, 51]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.5073
  Bounding Box: [711.20, 1432.80, 904.80, 1630.40]
  Mask Area: 186 pixels
  Mask Ratio: 0.0066
  Mask BBox: [60, 116, 74, 131]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.5063
  Bounding Box: [1364.00, 1876.80, 1458.40, 1950.40]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [111, 151, 117, 156]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.5049
  Bounding Box: [384.80, 1670.40, 497.60, 1795.20]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [36, 135, 42, 143]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.5049
  Bounding Box: [1328.80, 742.00, 1527.20, 918.40]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [108, 62, 123, 74]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.5044
  Bounding Box: [1444.80, 0.00, 1606.40, 206.60]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [117, 4, 129, 20]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4995
  Bounding Box: [29.45, 1145.60, 147.40, 1260.80]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [7, 94, 15, 102]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.4922
  Bounding Box: [956.00, 1223.20, 1101.60, 1492.00]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [79, 100, 90, 120]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4922
  Bounding Box: [1800.00, 550.00, 1902.40, 689.20]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [145, 47, 152, 57]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4907
  Bounding Box: [626.00, 108.40, 752.40, 160.00]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [53, 13, 62, 16]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4883
  Bounding Box: [1441.60, 644.80, 1523.20, 757.60]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [117, 55, 122, 63]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4875
  Bounding Box: [162.80, 116.60, 394.00, 551.20]
  Mask Area: 471 pixels
  Mask Ratio: 0.0167
  Mask BBox: [17, 14, 34, 47]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4863
  Bounding Box: [847.20, 1361.60, 920.80, 1449.60]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [71, 111, 75, 116]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4849
  Bounding Box: [1579.20, 974.40, 1656.00, 1115.20]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [128, 81, 133, 91]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4844
  Bounding Box: [22.45, 255.60, 153.20, 408.00]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [6, 24, 15, 35]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4819
  Bounding Box: [1276.80, 1350.40, 1420.80, 1544.00]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [104, 110, 113, 123]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4790
  Bounding Box: [935.20, 1072.00, 1077.60, 1219.20]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [78, 88, 88, 99]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4751
  Bounding Box: [1340.00, 1939.20, 1469.60, 2048.00]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [109, 156, 118, 163]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4727
  Bounding Box: [1075.20, 5.20, 1236.80, 91.70]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [88, 5, 100, 9]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4727
  Bounding Box: [890.40, 15.20, 1103.20, 258.80]
  Mask Area: 220 pixels
  Mask Ratio: 0.0078
  Mask BBox: [74, 6, 90, 24]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4722
  Bounding Box: [8.50, 737.20, 93.20, 923.20]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [5, 62, 11, 76]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4717
  Bounding Box: [1383.20, 3.35, 1495.20, 133.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [113, 5, 120, 14]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4707
  Bounding Box: [627.20, 904.80, 716.80, 1015.20]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [53, 75, 59, 81]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4688
  Bounding Box: [1691.20, 545.60, 1819.20, 752.80]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [137, 47, 146, 62]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4653
  Bounding Box: [422.00, 1932.80, 548.40, 2048.00]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [37, 155, 46, 163]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4575
  Bounding Box: [182.20, 1696.00, 282.60, 1843.20]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [19, 137, 26, 147]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4561
  Bounding Box: [1292.80, 560.00, 1361.60, 666.40]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [105, 48, 109, 56]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4561
  Bounding Box: [1368.00, 796.80, 1518.40, 916.80]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [111, 67, 122, 75]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4490
  Bounding Box: [450.80, 47.95, 586.00, 160.80]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [40, 8, 49, 16]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4460
  Bounding Box: [1697.60, 5.15, 1979.20, 207.00]
  Mask Area: 272 pixels
  Mask Ratio: 0.0096
  Mask BBox: [137, 5, 158, 20]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4451
  Bounding Box: [959.20, 1982.40, 1077.60, 2048.00]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [79, 159, 88, 163]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4426
  Bounding Box: [1110.40, 283.00, 1299.20, 384.00]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [91, 27, 105, 33]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4426
  Bounding Box: [1328.80, 290.80, 1581.60, 506.00]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [108, 27, 127, 43]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4412
  Bounding Box: [758.80, 1960.00, 848.80, 2040.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [64, 158, 70, 163]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4387
  Bounding Box: [1532.80, 1154.40, 1611.20, 1252.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [124, 95, 129, 101]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4382
  Bounding Box: [560.00, 1157.60, 810.40, 1429.60]
  Mask Area: 304 pixels
  Mask Ratio: 0.0108
  Mask BBox: [48, 95, 67, 115]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4377
  Bounding Box: [1915.20, 1352.00, 2046.40, 1540.80]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [154, 110, 162, 124]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4321
  Bounding Box: [1114.40, 977.60, 1232.80, 1132.80]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [92, 81, 99, 92]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4321
  Bounding Box: [1130.40, 1734.40, 1288.80, 1897.60]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [93, 140, 104, 152]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4297
  Bounding Box: [1859.20, 1590.40, 2041.60, 1897.60]
  Mask Area: 275 pixels
  Mask Ratio: 0.0097
  Mask BBox: [150, 129, 163, 152]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.4277
  Bounding Box: [0.45, 702.00, 77.00, 870.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [5, 59, 10, 71]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.4243
  Bounding Box: [397.60, 645.20, 474.40, 854.40]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [36, 55, 41, 68]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.4233
  Bounding Box: [1976.00, 1445.60, 2040.00, 1572.00]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [159, 117, 163, 126]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.4177
  Bounding Box: [560.00, 1164.80, 650.40, 1256.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [48, 95, 54, 102]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.4158
  Bounding Box: [1832.00, 593.60, 1934.40, 712.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [148, 51, 153, 59]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.4102
  Bounding Box: [1150.40, 4.60, 1240.00, 84.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [94, 5, 100, 9]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.4099
  Bounding Box: [1091.20, 982.40, 1203.20, 1172.80]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [90, 81, 97, 93]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.4097
  Bounding Box: [460.80, 4.15, 588.80, 109.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [40, 5, 49, 12]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.4082
  Bounding Box: [1289.60, 1689.60, 1558.40, 1916.80]
  Mask Area: 323 pixels
  Mask Ratio: 0.0114
  Mask BBox: [105, 136, 125, 153]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.4050
  Bounding Box: [1335.20, 451.60, 1477.60, 675.60]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [109, 40, 119, 56]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.4026
  Bounding Box: [181.40, 1261.60, 299.40, 1424.80]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [19, 103, 27, 115]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3989
  Bounding Box: [1974.40, 1212.80, 2041.60, 1374.40]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [159, 99, 163, 111]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3970
  Bounding Box: [611.20, 892.80, 756.80, 1030.40]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [52, 74, 63, 84]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3960
  Bounding Box: [1157.60, 1257.60, 1239.20, 1368.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [95, 103, 100, 110]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3943
  Bounding Box: [1806.40, 594.80, 1902.40, 713.20]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [146, 51, 152, 59]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3892
  Bounding Box: [1614.40, 1104.00, 1720.00, 1252.80]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [131, 91, 138, 101]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3850
  Bounding Box: [1415.20, 1198.40, 1528.80, 1268.80]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [115, 98, 123, 103]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3840
  Bounding Box: [739.60, 72.00, 851.20, 153.40]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [62, 10, 70, 15]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3811
  Bounding Box: [785.60, 840.00, 1067.20, 1155.20]
  Mask Area: 339 pixels
  Mask Ratio: 0.0120
  Mask BBox: [66, 71, 87, 94]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3809
  Bounding Box: [365.20, 430.80, 506.00, 666.80]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [33, 38, 43, 56]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3809
  Bounding Box: [1222.40, 670.40, 1508.80, 945.60]
  Mask Area: 361 pixels
  Mask Ratio: 0.0128
  Mask BBox: [100, 57, 121, 77]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3806
  Bounding Box: [227.00, 962.40, 396.80, 1191.20]
  Mask Area: 173 pixels
  Mask Ratio: 0.0061
  Mask BBox: [22, 80, 34, 97]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3794
  Bounding Box: [1683.20, 1851.20, 1968.00, 2008.00]
  Mask Area: 197 pixels
  Mask Ratio: 0.0070
  Mask BBox: [136, 149, 157, 160]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3777
  Bounding Box: [412.00, 355.80, 547.20, 604.00]
  Mask Area: 171 pixels
  Mask Ratio: 0.0061
  Mask BBox: [37, 32, 46, 51]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3774
  Bounding Box: [758.00, 137.80, 854.40, 254.20]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [64, 15, 69, 23]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3767
  Bounding Box: [0.00, 452.00, 77.60, 616.00]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [4, 40, 10, 52]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3760
  Bounding Box: [1163.20, 1349.60, 1344.00, 1525.60]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [95, 110, 108, 123]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3748
  Bounding Box: [755.20, 154.60, 843.20, 285.80]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [63, 17, 69, 26]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3721
  Bounding Box: [398.00, 1654.40, 514.00, 1776.00]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [36, 134, 44, 142]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3716
  Bounding Box: [664.80, 925.60, 770.40, 999.20]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [56, 77, 64, 82]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3711
  Bounding Box: [1361.60, 2.55, 1459.20, 114.60]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [111, 5, 117, 12]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3689
  Bounding Box: [409.20, 751.20, 597.20, 1050.40]
  Mask Area: 240 pixels
  Mask Ratio: 0.0085
  Mask BBox: [36, 63, 50, 86]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3682
  Bounding Box: [396.00, 1395.20, 694.40, 1646.40]
  Mask Area: 328 pixels
  Mask Ratio: 0.0116
  Mask BBox: [35, 113, 58, 132]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3679
  Bounding Box: [1279.20, 524.00, 1356.00, 650.40]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [104, 45, 109, 54]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3674
  Bounding Box: [1774.40, 1158.40, 1928.00, 1348.80]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [143, 95, 154, 107]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3674
  Bounding Box: [1352.00, 408.40, 1467.20, 531.60]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [110, 36, 116, 45]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3669
  Bounding Box: [6.00, 1098.40, 70.90, 1215.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [5, 90, 8, 98]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3669
  Bounding Box: [1022.40, 1172.00, 1209.60, 1444.00]
  Mask Area: 240 pixels
  Mask Ratio: 0.0085
  Mask BBox: [84, 96, 98, 116]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3662
  Bounding Box: [1955.20, 863.20, 2048.00, 1039.20]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [157, 72, 164, 85]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3625
  Bounding Box: [357.60, 1614.40, 442.40, 1688.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [32, 131, 38, 135]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3611
  Bounding Box: [730.40, 1939.20, 836.00, 2048.00]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [62, 156, 69, 163]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3604
  Bounding Box: [1104.00, 867.20, 1192.00, 993.60]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [91, 72, 97, 81]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3582
  Bounding Box: [292.80, 1149.60, 475.20, 1455.20]
  Mask Area: 262 pixels
  Mask Ratio: 0.0093
  Mask BBox: [27, 94, 41, 117]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3577
  Bounding Box: [3.05, 1028.00, 100.60, 1151.20]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [5, 85, 11, 93]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3562
  Bounding Box: [1070.40, 1001.60, 1166.40, 1152.00]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [88, 83, 95, 93]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [130.00, 0.00, 240.00, 122.60]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [15, 4, 22, 13]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [8.80, 875.20, 77.80, 984.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [5, 73, 10, 80]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3525
  Bounding Box: [0.30, 493.60, 72.10, 654.40]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [5, 43, 9, 55]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3503
  Bounding Box: [503.60, 267.20, 578.80, 386.00]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [44, 25, 49, 34]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3503
  Bounding Box: [0.00, 1320.00, 148.80, 1459.20]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [4, 108, 15, 117]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3501
  Bounding Box: [1897.60, 1942.40, 2035.20, 2044.80]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [153, 156, 162, 162]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3481
  Bounding Box: [292.00, 7.30, 383.60, 202.80]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [27, 5, 33, 19]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3442
  Bounding Box: [35.90, 0.00, 130.50, 62.00]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [7, 3, 14, 7]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3438
  Bounding Box: [1969.60, 1418.40, 2048.00, 1540.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [158, 115, 163, 124]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3438
  Bounding Box: [878.40, 530.40, 1078.40, 828.00]
  Mask Area: 236 pixels
  Mask Ratio: 0.0084
  Mask BBox: [73, 46, 88, 68]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3430
  Bounding Box: [1278.40, 356.40, 1424.00, 562.00]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [104, 32, 115, 47]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3423
  Bounding Box: [1844.80, 1870.40, 2033.60, 2043.20]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [149, 151, 162, 163]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3408
  Bounding Box: [202.80, 1688.00, 268.40, 1793.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [20, 136, 24, 144]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3403
  Bounding Box: [484.40, 508.40, 570.80, 652.40]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [42, 44, 48, 54]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3403
  Bounding Box: [510.00, 508.40, 596.40, 652.40]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [44, 44, 49, 54]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3394
  Bounding Box: [606.00, 160.00, 772.80, 375.20]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [52, 17, 64, 33]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3389
  Bounding Box: [16.60, 1243.20, 187.20, 1382.40]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [6, 102, 18, 111]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3386
  Bounding Box: [946.40, 1448.00, 1031.20, 1609.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [78, 118, 84, 129]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3372
  Bounding Box: [284.40, 1761.60, 462.80, 1976.00]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [27, 142, 40, 158]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3359
  Bounding Box: [1012.00, 48.20, 1228.00, 270.20]
  Mask Area: 224 pixels
  Mask Ratio: 0.0079
  Mask BBox: [84, 9, 99, 25]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3345
  Bounding Box: [1916.80, 279.60, 2048.00, 428.40]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [154, 26, 164, 37]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3345
  Bounding Box: [1523.20, 1377.60, 1820.80, 1768.00]
  Mask Area: 569 pixels
  Mask Ratio: 0.0202
  Mask BBox: [123, 112, 146, 142]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3333
  Bounding Box: [1998.40, 1145.60, 2043.20, 1256.00]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [161, 94, 163, 102]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1987.20, 281.60, 2041.60, 390.40]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [160, 26, 163, 34]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3301
  Bounding Box: [856.00, 1577.60, 1019.20, 1712.00]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [71, 128, 83, 137]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3291
  Bounding Box: [12.65, 1144.80, 171.80, 1338.40]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [5, 94, 17, 108]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3289
  Bounding Box: [7.55, 844.80, 141.40, 1008.00]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [5, 70, 15, 82]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3262
  Bounding Box: [270.00, 1434.40, 440.00, 1665.60]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [26, 117, 38, 134]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3254
  Bounding Box: [546.00, 1160.00, 666.80, 1289.60]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [47, 95, 56, 103]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3254
  Bounding Box: [646.40, 114.00, 748.80, 198.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [55, 13, 62, 19]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3252
  Bounding Box: [1915.20, 1306.40, 2024.00, 1442.40]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [154, 107, 162, 116]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3247
  Bounding Box: [1843.20, 612.80, 1916.80, 749.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [148, 52, 153, 62]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.3215
  Bounding Box: [1025.60, 1630.40, 1198.40, 1806.40]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [85, 134, 97, 145]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.3215
  Bounding Box: [1045.60, 1900.80, 1199.20, 2041.60]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [86, 153, 97, 163]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.3186
  Bounding Box: [8.90, 628.80, 86.00, 740.00]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [5, 54, 10, 61]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.3169
  Bounding Box: [1995.20, 4.70, 2048.00, 170.80]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [160, 5, 164, 17]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.3169
  Bounding Box: [166.00, 1173.60, 386.40, 1428.00]
  Mask Area: 261 pixels
  Mask Ratio: 0.0092
  Mask BBox: [17, 96, 34, 115]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.3167
  Bounding Box: [1116.00, 1648.00, 1295.20, 1913.60]
  Mask Area: 200 pixels
  Mask Ratio: 0.0071
  Mask BBox: [92, 133, 105, 153]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.3162
  Bounding Box: [1352.00, 1894.40, 1440.00, 1971.20]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [110, 152, 116, 157]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.3162
  Bounding Box: [1377.60, 1894.40, 1465.60, 1971.20]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [112, 152, 118, 157]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.3149
  Bounding Box: [140.70, 1416.80, 223.40, 1500.00]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [15, 115, 21, 121]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.3149
  Bounding Box: [1221.60, 1937.60, 1354.40, 2048.00]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [100, 156, 109, 164]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.3142
  Bounding Box: [537.60, 18.95, 740.80, 131.80]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [46, 6, 61, 14]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.3140
  Bounding Box: [1486.40, 147.80, 1659.20, 333.80]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [121, 16, 130, 30]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.3123
  Bounding Box: [955.20, 765.20, 1134.40, 960.80]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [79, 64, 92, 79]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.3115
  Bounding Box: [1363.20, 177.20, 1481.60, 322.40]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [111, 18, 119, 29]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.3110
  Bounding Box: [436.40, 1856.00, 640.40, 2048.00]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [39, 149, 54, 164]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.3103
  Bounding Box: [0.00, 1212.00, 147.20, 1381.60]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [3, 99, 15, 111]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.3103
  Bounding Box: [646.80, 158.80, 766.80, 319.60]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [55, 17, 63, 28]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [1603.20, 1162.40, 1696.00, 1264.80]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [130, 95, 136, 102]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [1628.80, 1162.40, 1721.60, 1264.80]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [132, 95, 138, 102]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.3047
  Bounding Box: [768.00, 1977.60, 868.80, 2041.60]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [64, 159, 71, 163]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.3040
  Bounding Box: [885.60, 408.80, 996.00, 551.20]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [74, 36, 81, 47]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.3040
  Bounding Box: [680.00, 1558.40, 1134.40, 2016.00]
  Mask Area: 666 pixels
  Mask Ratio: 0.0236
  Mask BBox: [58, 126, 92, 161]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.3035
  Bounding Box: [923.20, 1160.80, 1086.40, 1476.00]
  Mask Area: 165 pixels
  Mask Ratio: 0.0058
  Mask BBox: [77, 95, 88, 119]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.3032
  Bounding Box: [1641.60, 1124.80, 1756.80, 1238.40]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [133, 92, 141, 100]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.3032
  Bounding Box: [248.60, 1892.80, 438.40, 2043.20]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [24, 152, 38, 163]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [1256.00, 1974.40, 1363.20, 2035.20]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [103, 159, 110, 162]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2996
  Bounding Box: [977.60, 756.40, 1112.00, 904.00]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [81, 64, 90, 74]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2996
  Bounding Box: [1939.20, 1192.00, 2032.00, 1326.40]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [156, 98, 162, 107]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2981
  Bounding Box: [432.00, 1875.20, 516.80, 1964.80]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [38, 151, 44, 157]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2981
  Bounding Box: [606.00, 8.90, 843.20, 128.70]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [52, 5, 69, 14]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2981
  Bounding Box: [570.40, 906.40, 751.20, 1096.80]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [49, 75, 62, 89]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2976
  Bounding Box: [1415.20, 174.00, 1638.40, 493.60]
  Mask Area: 303 pixels
  Mask Ratio: 0.0107
  Mask BBox: [115, 18, 131, 42]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2966
  Bounding Box: [687.20, 160.60, 764.80, 284.20]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [58, 17, 63, 26]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2966
  Bounding Box: [561.60, 1491.20, 779.20, 1753.60]
  Mask Area: 220 pixels
  Mask Ratio: 0.0078
  Mask BBox: [48, 121, 64, 140]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2961
  Bounding Box: [1660.80, 389.20, 1760.00, 505.20]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [134, 35, 141, 43]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2961
  Bounding Box: [314.40, 914.40, 551.20, 1106.40]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [29, 76, 47, 89]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2959
  Bounding Box: [143.40, 717.60, 402.40, 1005.60]
  Mask Area: 305 pixels
  Mask Ratio: 0.0108
  Mask BBox: [16, 61, 35, 82]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2957
  Bounding Box: [277.00, 1387.20, 357.40, 1481.60]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [26, 113, 31, 118]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2952
  Bounding Box: [364.80, 1612.80, 485.60, 1705.60]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [33, 130, 41, 137]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2930
  Bounding Box: [1840.00, 676.40, 1916.80, 766.80]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [148, 57, 152, 62]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2917
  Bounding Box: [1934.40, 1434.40, 2048.00, 1573.60]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [156, 117, 164, 126]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2915
  Bounding Box: [968.80, 87.20, 1104.80, 290.40]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [80, 11, 90, 26]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2913
  Bounding Box: [1321.60, 1527.20, 1406.40, 1704.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [108, 124, 113, 134]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2913
  Bounding Box: [1811.20, 1235.20, 1913.60, 1344.00]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [146, 101, 153, 108]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2913
  Bounding Box: [940.80, 378.00, 1123.20, 604.40]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [78, 34, 91, 51]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2908
  Bounding Box: [1860.80, 1319.20, 2040.00, 1528.80]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [150, 108, 163, 123]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2891
  Bounding Box: [0.00, 1111.20, 114.00, 1234.40]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [4, 91, 12, 100]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2886
  Bounding Box: [1293.60, 1956.80, 1456.80, 2036.80]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [106, 157, 117, 163]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2869
  Bounding Box: [31.90, 556.00, 131.50, 664.80]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [7, 48, 14, 55]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2859
  Bounding Box: [986.40, 516.00, 1178.40, 760.80]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [82, 45, 96, 63]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2849
  Bounding Box: [1372.00, 2.30, 1572.00, 184.80]
  Mask Area: 187 pixels
  Mask Ratio: 0.0066
  Mask BBox: [112, 5, 126, 18]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2847
  Bounding Box: [1998.40, 42.30, 2046.40, 186.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [161, 8, 163, 18]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2844
  Bounding Box: [499.20, 312.00, 586.40, 465.60]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [43, 29, 49, 40]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2837
  Bounding Box: [993.60, 0.00, 1124.80, 79.00]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [82, 4, 91, 10]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [5.95, 825.60, 80.80, 963.20]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [5, 69, 10, 79]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2822
  Bounding Box: [1899.20, 487.20, 2040.00, 759.20]
  Mask Area: 186 pixels
  Mask Ratio: 0.0066
  Mask BBox: [153, 43, 163, 63]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2815
  Bounding Box: [324.80, 528.80, 412.80, 671.20]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [30, 46, 36, 56]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2815
  Bounding Box: [350.40, 528.80, 438.40, 671.20]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [32, 46, 38, 56]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2810
  Bounding Box: [1897.60, 1026.40, 2028.80, 1205.60]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [153, 85, 162, 98]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2805
  Bounding Box: [1032.00, 1640.00, 1132.80, 1771.20]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [85, 133, 92, 142]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2805
  Bounding Box: [1057.60, 1640.00, 1158.40, 1771.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [87, 134, 92, 142]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2803
  Bounding Box: [24.70, 0.00, 110.10, 48.05]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [6, 3, 12, 7]

