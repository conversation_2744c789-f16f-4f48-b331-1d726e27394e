#!/usr/bin/env python3
# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license
"""
SegFormer-style YOLOv5 Training Script (anchor-free, multi-modal friendly)

Example:
    python -m segformer.train \
        --cfg models/segment/multi-yolov5n-formerhead-seg.yaml \
        --data data-seg/datasets.yaml \
        --epochs 150 --batch 16 --imgsz 640 --device 0 \
        --workers 8 --name segformer-exp1
"""

import argparse
import math
import os
import sys
from pathlib import Path
from copy import deepcopy
from contextlib import suppress
from collections import defaultdict

import torch
import torch.nn as nn
import torch.optim as optim
from torch.amp import autocast, GradScaler

FILE = Path(__file__).resolve()
ROOT = FILE.parents[1]  # yolov5/
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

from models.experimental import attempt_load
from models.yolo import SegmentationModel
from models.router import InputRouter
from utils.torch_utils import select_device, smart_optimizer, time_sync, ModelEMA, de_parallel
from utils.segment.general import LOGGER, check_yaml, yaml_load, colorstr, increment_path, check_img_size, init_seeds
from utils.segment.dataloaders import create_multimodal_dataloader
from utils.segformer.loss import SetCriterion   # 统一损失（你前面合并好的版本）
from utils.segformer.metrics import SegmentationEvaluator
from ultralytics.utils.patches import torch_load


def build_model(cfg, nc, device, weights=None):
    # 优先使用 YOLOv5 原生 YAML 解析器加载（你已注册 PixelAdapter / SegFormerHead / InputRouter）
    if weights and weights.endswith('.pt'):
        # 从预训练权重加载
        ckpt = torch_load(weights, map_location='cpu')
        model = SegmentationModel(cfg or ckpt['model'].yaml, ch=3, nc=nc)
        csd = ckpt['model'].float().state_dict()
        model.load_state_dict(csd, strict=False)
        LOGGER.info(f'Loaded pretrained weights from {weights}')
    else:
        # 从头创建模型
        model = SegmentationModel(cfg, ch=3, nc=nc)
    
    model.to(device)
    return model


def build_targets(labels_out, masks, batch_size, device):
    """
    将 dataloader 输出的 (labels_out [N,6], masks[B or N,...]) 整理成 DETR/SegFormer 风格 targets：
    返回长度为 B 的 list，每个元素是：{'labels': Long[Ni], 'masks': Bool/Float[Ni,H,W]}
    其中 labels_out 的第 0 列是图像索引（collate 时已写入）
    """
    targets = []
    # masks 形状：
    # - overlap=False: [sum(Ni), H, W]
    # - overlap=True:  [1, H, W]（语义叠加），这里默认 overlap=False
    # 因为我们需要 instance masks，所以建议 training 时 overlap=False
    lb = labels_out.to(device)
    mk = masks.to(device)

    # 将 mk 拆回每张图像的实例掩码
    idx_per_img = defaultdict(list)
    for i in range(lb.shape[0]):
        b = int(lb[i, 0].item())
        idx_per_img[b].append(i)

    # 游标式拆分 masks（overlap=False 情况：mk 顺序与 labels_out 对齐）
    for b in range(batch_size):
        if len(idx_per_img[b]) == 0:
            # 确保空目标的掩码形状正确
            if mk.numel() > 0:
                h, w = mk.shape[-2], mk.shape[-1]
            else:
                h, w = 640, 640  # 默认尺寸，避免空张量问题
            targets.append({"labels": torch.zeros((0,), dtype=torch.long, device=device),
                            "masks": torch.zeros((0, h, w), dtype=mk.dtype, device=device)})
            continue
        inds = idx_per_img[b]
        labels_b = lb[inds, 1].long()  # (cls_id)
        masks_b  = mk[inds]            # [Ni,H,W]
        targets.append({"labels": labels_b, "masks": masks_b})
    return targets


def save_checkpoint(ckpt, path):
    path = Path(path)
    path.parent.mkdir(parents=True, exist_ok=True)
    torch.save(ckpt, path)
    LOGGER.info(f"Saved checkpoint to {str(path)}")


def train(opt):
    save_dir = increment_path(Path("runs") / "segformer" / "train" / (opt.name or "exp"), exist_ok=opt.exist_ok)
    (save_dir / "weights").mkdir(parents=True, exist_ok=True)

    device = select_device(opt.device)
    data = yaml_load(check_yaml(opt.data))
    cfg  = check_yaml(opt.cfg)

    nc = int(data["nc"])
    names = data.get("names", {i: f"class_{i}" for i in range(nc)})
    multimodal = bool(data.get("multimodal", False))

    imgsz = check_img_size(opt.imgsz, s=32)
    LOGGER.info(colorstr("hyper-params: ") + str({
        "epochs": opt.epochs, "batch": opt.batch, "imgsz": imgsz, "lr0": opt.lr0,
        "weight_decay": opt.weight_decay, "warmup_epochs": opt.warmup_epochs
    }))

    # 初始化随机种子
    init_seeds(opt.seed)
    
    # ---------------- Model / Loss / EMA ----------------
    weights = getattr(opt, 'weights', None)
    model = build_model(cfg, nc, device, weights)
    
    # 检测模型是否包含 InputRouter（多输入支持）
    has_input_router = any(isinstance(m, InputRouter) for m in model.modules())
    LOGGER.info(f"Model has InputRouter: {has_input_router}")
    
    # 统一损失（你前面实现的 SetCriterion，内部含匹配与 Dice/BCE/CE 组合）
    criterion = SetCriterion(
        num_classes=nc, 
        eos_coef=0.1,
        cost_class=2.0, 
        cost_dice=1.0, 
        cost_bce=1.0,
        lambda_ce=1.0, 
        lambda_bce=1.0, 
        lambda_dice=1.0,
        aux_loss=True,
        pos_weight_bce=1.0
    ).to(device)

    # 优化器和EMA
    optimizer = smart_optimizer(model, name="AdamW", lr=opt.lr0, momentum=0.9, decay=opt.weight_decay)
    scaler = GradScaler('cuda', enabled=not opt.no_amp)
    ema = ModelEMA(model)

    # ---------------- Data ----------------
    # 训练集
    train_loader, train_set = create_multimodal_dataloader(
        path=str(Path(data["path"]) / data["train"]),
        imgsz=imgsz,
        batch_size=opt.batch,
        stride=32,
        single_cls=False,
        hyp={},  # 你可加载 hyp.yaml
        augment=True,
        cache=opt.cache,
        pad=0.0,
        rect=False,
        rank=-1,
        workers=opt.workers,
        image_weights=False,
        quad=False,
        prefix="train: ",
        shuffle=True,
        mask_downsample_ratio=1,
        overlap_mask=False,
        seed=opt.seed,
        xpl_path=(str(Path(data["path"]) / data["train_xpl"]) if multimodal else None),
        multimodal=multimodal,
    )
    # 验证集
    val_loader, val_set = create_multimodal_dataloader(
        path=str(Path(data["path"]) / data["val"]),
        imgsz=imgsz,
        batch_size=1,  # 验证时固定使用 batch_size=1，避免小批次问题
        stride=32,
        single_cls=False,
        hyp={},
        augment=False,
        cache=False,
        pad=0.0,
        rect=True,
        rank=-1,
        workers=opt.workers,
        image_weights=False,
        quad=False,
        prefix="val: ",
        shuffle=False,
        mask_downsample_ratio=1,
        overlap_mask=False,
        seed=opt.seed,
        xpl_path=(str(Path(data["path"]) / data["val_xpl"]) if multimodal else None),
        multimodal=multimodal,
    )

    # ---------------- Scheduler（cosine + warmup） ----------------
    lf = lambda x: (  # cosine
        ((1 + math.cos(math.pi * x / opt.epochs)) / 2) * (1 - opt.lrf) + opt.lrf
    )
    scheduler = optim.lr_scheduler.LambdaLR(optimizer, lr_lambda=lf)
    warmup_iters = int(opt.warmup_epochs * len(train_loader))

    # ---------------- Train loop ----------------
    best_metric = 0.0
    global_step = 0
    for epoch in range(opt.epochs):
        model.train()
        mloss = 0.0
        nb = len(train_loader)

        pbar = enumerate(train_loader)
        LOGGER.info(("\n" if epoch else "") + f"{'Epoch':>6} {'GPU_mem':>8} {'lr':>8} "
                    f"{'loss_ce':>10} {'loss_mask':>10} {'loss_dice':>10} {'loss_total':>12}")
        for i, batch in pbar:
            t0 = time_sync()
            if multimodal:
                ppl_img, xpl_img, labels_out, paths, shapes, masks = batch
                ppl_img, xpl_img = ppl_img.to(device, non_blocking=True).float() / 255.0, \
                                   xpl_img.to(device, non_blocking=True).float() / 255.0
                images = {"RGB": ppl_img, "X": xpl_img}
                B = ppl_img.shape[0]
            else:
                img, labels_out, paths, shapes, masks = batch
                img = img.to(device, non_blocking=True).float() / 255.0
                images = img
                B = img.shape[0]

            targets = build_targets(labels_out, masks, B, device)

            # Warmup
            if global_step < warmup_iters:
                # 线性 warmup 到 base lr
                for pg in optimizer.param_groups:
                    pg["lr"] = opt.lr0 * (global_step + 1) / warmup_iters

            optimizer.zero_grad(set_to_none=True)
            with autocast('cuda', enabled=not opt.no_amp):
                outputs = model(images)  # {'pred_logits','pred_masks'}
                losses = criterion(outputs, targets)  # dict: loss, loss_ce, loss_mask, loss_dice
                loss = losses["loss"]

            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
            
            # EMA 更新
            ema.update(model)

            mloss += loss.item()
            global_step += 1

            # log line (mini)
            mem = torch.cuda.memory_reserved() / 1E9 if torch.cuda.is_available() else 0
            lr_now = optimizer.param_groups[0]["lr"]
            if (i % max(1, nb // 20) == 0) or (i == nb - 1):
                LOGGER.info(f"{epoch+1:6d} {mem:8.3f} {lr_now:8.5f} "
                            f"{losses['loss_ce'].item():10.4f} {losses['loss_mask'].item():10.4f} "
                            f"{losses['loss_dice'].item():10.4f} {loss.item():12.4f}")

        scheduler.step()

        # ---------------- Validation ----------------
        metrics = validate_one_epoch(model, val_loader, device, nc=nc, multimodal=multimodal)
        map50_95 = metrics["mAP"]
        is_best = map50_95 >= best_metric
        if is_best:
            best_metric = map50_95

        # Save
        ckpt = {
            "epoch": epoch,
            "best_metric": best_metric,
            "model": deepcopy(model).half().state_dict() if not opt.no_half_ckpt else model.state_dict(),
            "optimizer": optimizer.state_dict(),
            "metrics": metrics,
            "names": names,
            "nc": nc,
            "cfg": cfg,
        }
        save_checkpoint(ckpt, save_dir / "weights" / "last.pt")
        if is_best:
            save_checkpoint(ckpt, save_dir / "weights" / "best.pt")

        LOGGER.info(colorstr("val: ") + f"{SegmentationEvaluator.pretty(metrics)}")

    LOGGER.info(colorstr("Training done. ") + f"Best mAP@.50:.95={best_metric:.4f}  "
                f"weights: {save_dir / 'weights' / 'best.pt'}")


@torch.no_grad()
def validate_one_epoch(model, loader, device, nc, multimodal=False):
    model.eval()
    evaluator = SegmentationEvaluator(num_classes=nc, mask_thr=0.5, conf_thr=0.05)

    for batch in loader:
        if multimodal:
            ppl_img, xpl_img, labels_out, paths, shapes, masks = batch
            ppl_img, xpl_img = ppl_img.to(device).float() / 255.0, xpl_img.to(device).float() / 255.0
            images = {"RGB": ppl_img, "X": xpl_img}
            B = ppl_img.shape[0]
        else:
            img, labels_out, paths, shapes, masks = batch
            img = img.to(device).float() / 255.0
            images = img
            B = img.shape[0]

        outputs = model(images)
        targets = build_targets(labels_out, masks, B, device)
        evaluator.update(outputs, targets)

    return evaluator.compute()


def parse_opt():
    parser = argparse.ArgumentParser()
    parser.add_argument("--cfg", type=str, required=True, help="model yaml path")
    parser.add_argument("--data", type=str, required=True, help="dataset yaml path")
    parser.add_argument("--weights", type=str, default="", help="pretrained weights path")
    parser.add_argument("--epochs", type=int, default=150)
    parser.add_argument("--batch", type=int, default=16)
    parser.add_argument("--imgsz", type=int, default=640)
    parser.add_argument("--device", type=str, default="")
    parser.add_argument("--workers", type=int, default=8)
    parser.add_argument("--lr0", type=float, default=2e-4, help="initial lr")
    parser.add_argument("--lrf", type=float, default=0.01, help="final lr fraction")
    parser.add_argument("--weight_decay", type=float, default=1e-4)
    parser.add_argument("--warmup_epochs", type=float, default=3.0)
    parser.add_argument("--cache", type=str, default="")  # "ram"/"disk"/False
    parser.add_argument("--seed", type=int, default=0)
    parser.add_argument("--name", type=str, default="")
    parser.add_argument("--exist_ok", action="store_true")
    parser.add_argument("--no-amp", dest="no_amp", action="store_true", help="disable mixed precision")
    parser.add_argument("--no-half-ckpt", dest="no_half_ckpt", action="store_true",
                        help="save fp32 ckpt instead of half")
    return parser.parse_args()


if __name__ == "__main__":
    opt = parse_opt()
    train(opt)
