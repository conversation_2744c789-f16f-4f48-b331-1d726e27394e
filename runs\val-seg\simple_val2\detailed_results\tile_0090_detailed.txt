Image: tile_0090.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8633
  Bounding Box: [505.60, 872.00, 815.20, 1228.80]
  Mask Area: 476 pixels
  Mask Ratio: 0.0169
  Mask BBox: [44, 73, 67, 99]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8525
  Bounding Box: [1449.60, 233.60, 1820.80, 508.00]
  Mask Area: 515 pixels
  Mask Ratio: 0.0182
  Mask BBox: [118, 23, 146, 43]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8384
  Bounding Box: [1200.80, 602.80, 1344.80, 803.20]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [98, 52, 109, 66]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8335
  Bounding Box: [1.60, 609.60, 228.80, 884.00]
  Mask Area: 226 pixels
  Mask Ratio: 0.0080
  Mask BBox: [5, 52, 21, 71]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8296
  Bounding Box: [1521.60, 1009.60, 1710.40, 1217.60]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [124, 85, 137, 98]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8271
  Bounding Box: [774.40, 1620.80, 995.20, 1790.40]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [65, 131, 81, 143]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8242
  Bounding Box: [403.20, 823.20, 548.80, 986.40]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [36, 69, 46, 81]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8232
  Bounding Box: [1004.80, 1585.60, 1192.00, 1755.20]
  Mask Area: 138 pixels
  Mask Ratio: 0.0049
  Mask BBox: [83, 128, 97, 141]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8101
  Bounding Box: [1164.00, 1520.80, 1381.60, 1782.40]
  Mask Area: 242 pixels
  Mask Ratio: 0.0086
  Mask BBox: [95, 123, 111, 143]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8066
  Bounding Box: [90.20, 1889.60, 250.60, 2046.40]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [12, 152, 23, 163]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8042
  Bounding Box: [1320.00, 1304.80, 1611.20, 1476.00]
  Mask Area: 217 pixels
  Mask Ratio: 0.0077
  Mask BBox: [108, 106, 129, 119]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8008
  Bounding Box: [8.80, 1700.80, 251.40, 1838.40]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [5, 137, 23, 147]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.8003
  Bounding Box: [832.00, 1203.20, 990.40, 1425.60]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [69, 98, 81, 115]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.7983
  Bounding Box: [1029.60, 1385.60, 1215.20, 1641.60]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [85, 113, 97, 132]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7983
  Bounding Box: [1779.20, 321.20, 2038.40, 650.80]
  Mask Area: 341 pixels
  Mask Ratio: 0.0121
  Mask BBox: [143, 30, 163, 54]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7979
  Bounding Box: [398.40, 1716.80, 512.80, 1835.20]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [36, 139, 44, 147]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7979
  Bounding Box: [180.80, 656.80, 319.20, 832.00]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [19, 56, 28, 68]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7930
  Bounding Box: [1235.20, 1651.20, 1516.80, 1916.80]
  Mask Area: 318 pixels
  Mask Ratio: 0.0113
  Mask BBox: [101, 133, 122, 153]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7778
  Bounding Box: [546.00, 1179.20, 730.80, 1460.80]
  Mask Area: 184 pixels
  Mask Ratio: 0.0065
  Mask BBox: [47, 97, 61, 118]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7769
  Bounding Box: [1264.80, 11.70, 1512.80, 309.60]
  Mask Area: 410 pixels
  Mask Ratio: 0.0145
  Mask BBox: [103, 5, 122, 28]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7749
  Bounding Box: [800.00, 12.30, 985.60, 221.60]
  Mask Area: 187 pixels
  Mask Ratio: 0.0066
  Mask BBox: [67, 5, 80, 20]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7734
  Bounding Box: [154.80, 1435.20, 528.80, 1724.80]
  Mask Area: 474 pixels
  Mask Ratio: 0.0168
  Mask BBox: [17, 117, 45, 138]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7720
  Bounding Box: [461.20, 546.80, 707.60, 737.20]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [41, 47, 59, 61]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7710
  Bounding Box: [1654.40, 581.60, 1990.40, 1028.00]
  Mask Area: 658 pixels
  Mask Ratio: 0.0233
  Mask BBox: [134, 50, 159, 84]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7700
  Bounding Box: [400.80, 1235.20, 613.60, 1499.20]
  Mask Area: 231 pixels
  Mask Ratio: 0.0082
  Mask BBox: [36, 101, 51, 121]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7661
  Bounding Box: [1381.60, 1494.40, 1519.20, 1627.20]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [112, 121, 122, 131]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7612
  Bounding Box: [1897.60, 1575.20, 2044.80, 1816.00]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [153, 128, 163, 145]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7612
  Bounding Box: [1616.00, 1713.60, 1731.20, 1860.80]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [131, 138, 139, 149]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7578
  Bounding Box: [936.80, 652.40, 1120.80, 843.20]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [78, 56, 91, 68]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7573
  Bounding Box: [1348.00, 496.80, 1592.00, 783.20]
  Mask Area: 291 pixels
  Mask Ratio: 0.0103
  Mask BBox: [110, 43, 127, 65]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7568
  Bounding Box: [1163.20, 162.80, 1280.00, 380.40]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [95, 17, 103, 32]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7510
  Bounding Box: [826.40, 1782.40, 1047.20, 2048.00]
  Mask Area: 290 pixels
  Mask Ratio: 0.0103
  Mask BBox: [69, 144, 85, 164]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7485
  Bounding Box: [364.80, 1004.80, 547.20, 1219.20]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [33, 83, 46, 99]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7456
  Bounding Box: [1584.80, 1904.00, 1726.40, 2048.00]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [128, 153, 137, 163]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7451
  Bounding Box: [1067.20, 1892.80, 1334.40, 2048.00]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [88, 154, 108, 165]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7441
  Bounding Box: [1006.40, 1779.20, 1124.80, 1932.80]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [83, 143, 91, 153]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7383
  Bounding Box: [70.80, 880.00, 241.20, 1060.80]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [10, 73, 22, 86]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7383
  Bounding Box: [55.40, 153.00, 320.60, 425.60]
  Mask Area: 328 pixels
  Mask Ratio: 0.0116
  Mask BBox: [9, 16, 29, 37]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7358
  Bounding Box: [1595.20, 1232.80, 1755.20, 1496.80]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [129, 101, 140, 120]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7354
  Bounding Box: [1332.80, 768.80, 1590.40, 1090.40]
  Mask Area: 321 pixels
  Mask Ratio: 0.0114
  Mask BBox: [109, 65, 128, 89]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7324
  Bounding Box: [670.80, 515.60, 851.20, 647.60]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [57, 45, 70, 54]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7212
  Bounding Box: [224.00, 868.00, 360.00, 1002.40]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [22, 72, 32, 81]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7134
  Bounding Box: [54.70, 1037.60, 176.00, 1135.20]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [9, 86, 17, 91]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7124
  Bounding Box: [1758.40, 1556.80, 1908.80, 1684.80]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [142, 126, 153, 135]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7100
  Bounding Box: [560.40, 1467.20, 755.60, 1652.80]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [48, 119, 63, 133]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7080
  Bounding Box: [1806.40, 0.00, 2043.20, 271.60]
  Mask Area: 338 pixels
  Mask Ratio: 0.0120
  Mask BBox: [146, 4, 163, 25]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7061
  Bounding Box: [598.80, 1659.20, 758.00, 1780.80]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [51, 134, 62, 143]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7056
  Bounding Box: [1731.20, 38.10, 1820.80, 172.40]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [140, 7, 146, 17]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7041
  Bounding Box: [35.20, 444.00, 354.00, 669.60]
  Mask Area: 326 pixels
  Mask Ratio: 0.0116
  Mask BBox: [7, 39, 31, 56]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7012
  Bounding Box: [385.60, 1840.00, 583.20, 2048.00]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [35, 148, 49, 164]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.6992
  Bounding Box: [1520.00, 0.70, 1664.00, 212.00]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [123, 5, 132, 19]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.6982
  Bounding Box: [236.60, 1846.40, 410.80, 2048.00]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [23, 149, 36, 164]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.6978
  Bounding Box: [623.60, 1972.80, 742.80, 2036.80]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [53, 159, 62, 163]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.6948
  Bounding Box: [550.00, 251.00, 686.80, 494.40]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [47, 24, 57, 42]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.6938
  Bounding Box: [56.60, 1429.60, 219.00, 1691.20]
  Mask Area: 213 pixels
  Mask Ratio: 0.0075
  Mask BBox: [9, 116, 21, 136]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.6924
  Bounding Box: [1282.40, 1094.40, 1418.40, 1294.40]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [105, 90, 114, 105]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6899
  Bounding Box: [1704.00, 1870.40, 1902.40, 1998.40]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [139, 151, 150, 160]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6870
  Bounding Box: [1385.60, 1123.20, 1526.40, 1296.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [114, 92, 121, 103]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6855
  Bounding Box: [272.00, 84.80, 571.20, 516.00]
  Mask Area: 564 pixels
  Mask Ratio: 0.0200
  Mask BBox: [26, 11, 48, 44]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6816
  Bounding Box: [713.60, 3.20, 860.80, 121.20]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [60, 5, 70, 13]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6782
  Bounding Box: [1921.60, 1076.80, 2048.00, 1299.20]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [155, 89, 164, 105]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6738
  Bounding Box: [893.60, 220.40, 1008.80, 356.40]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [74, 22, 82, 29]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6709
  Bounding Box: [130.40, 1026.40, 346.40, 1405.60]
  Mask Area: 342 pixels
  Mask Ratio: 0.0121
  Mask BBox: [15, 85, 31, 113]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6646
  Bounding Box: [1194.40, 1173.60, 1317.60, 1423.20]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [98, 96, 106, 115]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6587
  Bounding Box: [50.60, 18.25, 283.80, 156.40]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [8, 6, 25, 15]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6562
  Bounding Box: [1510.40, 1184.80, 1622.40, 1319.20]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [122, 97, 129, 107]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6562
  Bounding Box: [1697.60, 1697.60, 1966.40, 1924.80]
  Mask Area: 241 pixels
  Mask Ratio: 0.0085
  Mask BBox: [137, 137, 157, 154]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6548
  Bounding Box: [3.90, 1932.80, 94.10, 2038.40]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [5, 155, 11, 163]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6519
  Bounding Box: [967.20, 514.40, 1146.40, 708.80]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [80, 45, 93, 59]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6509
  Bounding Box: [1056.00, 1296.80, 1171.20, 1408.80]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [87, 106, 95, 114]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6509
  Bounding Box: [1276.80, 376.00, 1489.60, 592.00]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [104, 34, 120, 50]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6499
  Bounding Box: [1905.60, 1848.00, 2020.80, 2036.80]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [153, 150, 161, 163]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6489
  Bounding Box: [723.20, 1340.00, 833.60, 1603.20]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [61, 109, 69, 129]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6455
  Bounding Box: [1635.20, 945.60, 1731.20, 1052.80]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [132, 78, 139, 86]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6440
  Bounding Box: [469.60, 722.40, 580.00, 856.80]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [41, 61, 49, 69]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6406
  Bounding Box: [502.80, 0.00, 686.80, 215.00]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [44, 4, 57, 20]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6333
  Bounding Box: [1964.80, 275.80, 2035.20, 432.00]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [158, 26, 162, 37]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6313
  Bounding Box: [678.40, 1185.60, 776.80, 1296.00]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [57, 97, 64, 105]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6309
  Bounding Box: [568.00, 426.00, 718.40, 551.60]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [49, 38, 60, 47]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6304
  Bounding Box: [990.40, 1156.80, 1084.80, 1299.20]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [82, 95, 88, 105]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6284
  Bounding Box: [1880.00, 1436.80, 1976.00, 1592.00]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [151, 117, 158, 126]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6270
  Bounding Box: [1748.80, 1331.20, 1899.20, 1560.00]
  Mask Area: 157 pixels
  Mask Ratio: 0.0056
  Mask BBox: [141, 108, 152, 125]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6235
  Bounding Box: [331.40, 10.15, 515.60, 105.00]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [30, 5, 44, 11]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6230
  Bounding Box: [626.00, 1854.40, 780.80, 1976.00]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [53, 149, 64, 158]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6167
  Bounding Box: [310.20, 792.80, 445.60, 973.60]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [29, 66, 38, 80]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6147
  Bounding Box: [521.60, 1708.80, 640.00, 1849.60]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [45, 138, 53, 147]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.6133
  Bounding Box: [1503.20, 1451.20, 1616.00, 1547.20]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [122, 118, 130, 124]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.6128
  Bounding Box: [970.40, 0.00, 1085.60, 67.20]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [80, 4, 88, 8]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.6128
  Bounding Box: [1517.60, 1510.40, 1638.40, 1659.20]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [123, 122, 131, 133]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.6108
  Bounding Box: [1168.00, 924.80, 1336.00, 1142.40]
  Mask Area: 187 pixels
  Mask Ratio: 0.0066
  Mask BBox: [96, 77, 108, 93]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.6104
  Bounding Box: [1811.20, 1940.80, 1945.60, 2036.80]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [146, 156, 155, 163]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.6089
  Bounding Box: [28.20, 1101.60, 153.40, 1359.20]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [7, 91, 15, 110]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.6011
  Bounding Box: [90.80, 1352.00, 215.20, 1435.20]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [12, 110, 20, 115]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.6001
  Bounding Box: [808.00, 1392.80, 990.40, 1617.60]
  Mask Area: 182 pixels
  Mask Ratio: 0.0064
  Mask BBox: [68, 113, 81, 130]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.6001
  Bounding Box: [1140.80, 781.60, 1352.00, 1058.40]
  Mask Area: 286 pixels
  Mask Ratio: 0.0101
  Mask BBox: [94, 66, 109, 86]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5972
  Bounding Box: [0.00, 1364.80, 102.80, 1520.00]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [4, 111, 12, 122]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5933
  Bounding Box: [731.20, 1261.60, 828.80, 1368.80]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [62, 103, 68, 109]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5923
  Bounding Box: [1635.20, 1478.40, 1795.20, 1649.60]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [132, 120, 144, 132]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5884
  Bounding Box: [45.30, 138.40, 154.00, 228.00]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [8, 15, 16, 21]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5850
  Bounding Box: [35.15, 1820.80, 198.00, 1939.20]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [7, 147, 19, 155]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5840
  Bounding Box: [1007.20, 394.40, 1108.00, 527.20]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [83, 35, 90, 45]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5840
  Bounding Box: [1096.80, 418.80, 1274.40, 673.20]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [90, 37, 103, 56]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5801
  Bounding Box: [1612.80, 1856.00, 1702.40, 1910.40]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [130, 149, 136, 153]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5801
  Bounding Box: [494.40, 1990.40, 627.20, 2048.00]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [43, 160, 52, 164]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5742
  Bounding Box: [38.70, 643.60, 306.40, 846.40]
  Mask Area: 258 pixels
  Mask Ratio: 0.0091
  Mask BBox: [8, 55, 27, 70]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5737
  Bounding Box: [993.60, 60.20, 1166.40, 233.80]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [82, 9, 95, 22]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5728
  Bounding Box: [1382.40, 970.40, 1576.00, 1165.60]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [112, 80, 127, 95]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5679
  Bounding Box: [841.60, 474.80, 982.40, 711.60]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [70, 43, 80, 59]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5679
  Bounding Box: [703.20, 676.00, 1108.00, 1016.80]
  Mask Area: 604 pixels
  Mask Ratio: 0.0214
  Mask BBox: [59, 57, 90, 83]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5674
  Bounding Box: [1700.80, 1113.60, 1812.80, 1337.60]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [137, 91, 145, 108]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5659
  Bounding Box: [1883.20, 577.20, 2011.20, 742.80]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [152, 50, 161, 62]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5610
  Bounding Box: [1937.60, 902.40, 2048.00, 1073.60]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [156, 75, 164, 87]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5605
  Bounding Box: [1910.40, 1816.00, 2006.40, 1912.00]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [154, 146, 159, 153]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5479
  Bounding Box: [1926.40, 1283.20, 2041.60, 1510.40]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [155, 105, 163, 121]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5464
  Bounding Box: [895.20, 656.00, 1133.60, 900.80]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [74, 56, 92, 74]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5459
  Bounding Box: [190.00, 1835.20, 361.60, 2024.00]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [19, 148, 32, 162]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5430
  Bounding Box: [1.80, 1536.00, 80.10, 1680.00]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [5, 124, 9, 135]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5376
  Bounding Box: [633.60, 642.40, 712.00, 743.20]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [54, 55, 59, 62]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5371
  Bounding Box: [1337.60, 841.60, 1598.40, 1171.20]
  Mask Area: 312 pixels
  Mask Ratio: 0.0111
  Mask BBox: [109, 70, 128, 95]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.5327
  Bounding Box: [1697.60, 724.80, 2001.60, 1132.80]
  Mask Area: 522 pixels
  Mask Ratio: 0.0185
  Mask BBox: [137, 61, 159, 92]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.5220
  Bounding Box: [1174.40, 1468.00, 1296.00, 1573.60]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [96, 119, 105, 126]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.5210
  Bounding Box: [1447.20, 1676.80, 1628.80, 1974.40]
  Mask Area: 198 pixels
  Mask Ratio: 0.0070
  Mask BBox: [118, 135, 130, 158]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.5205
  Bounding Box: [328.80, 490.40, 520.80, 616.80]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [30, 43, 43, 52]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.5190
  Bounding Box: [1383.20, 1980.80, 1562.40, 2048.00]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [113, 159, 126, 163]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.5181
  Bounding Box: [186.60, 1825.60, 296.60, 1985.60]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [19, 147, 27, 159]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.5142
  Bounding Box: [723.60, 237.20, 1007.20, 570.00]
  Mask Area: 415 pixels
  Mask Ratio: 0.0147
  Mask BBox: [61, 23, 81, 48]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.5122
  Bounding Box: [11.75, 1573.60, 87.60, 1699.20]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [5, 127, 9, 136]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.5107
  Bounding Box: [829.60, 1074.40, 884.00, 1173.60]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [69, 88, 73, 95]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.5093
  Bounding Box: [1189.60, 1486.40, 1280.80, 1580.80]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [97, 121, 104, 127]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.5063
  Bounding Box: [1099.20, 952.00, 1161.60, 1046.40]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [90, 79, 94, 85]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4985
  Bounding Box: [1984.00, 666.40, 2038.40, 859.20]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [159, 57, 163, 71]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4927
  Bounding Box: [1168.80, 35.50, 1260.00, 166.00]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [96, 7, 101, 16]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4866
  Bounding Box: [358.00, 17.80, 496.40, 126.60]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [32, 6, 42, 11]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4844
  Bounding Box: [1622.40, 1510.40, 1782.40, 1699.20]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [131, 122, 142, 136]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4834
  Bounding Box: [403.20, 1606.40, 586.40, 1737.60]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [36, 130, 49, 139]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4797
  Bounding Box: [325.80, 1726.40, 494.00, 1848.00]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [30, 139, 42, 148]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4785
  Bounding Box: [1641.60, 1611.20, 1776.00, 1704.00]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [133, 130, 142, 137]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4771
  Bounding Box: [1707.20, 1020.00, 1790.40, 1133.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [138, 84, 143, 91]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4736
  Bounding Box: [932.00, 1391.20, 1192.80, 1630.40]
  Mask Area: 286 pixels
  Mask Ratio: 0.0101
  Mask BBox: [77, 113, 97, 131]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4707
  Bounding Box: [719.20, 1710.40, 836.00, 1832.00]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [61, 138, 67, 147]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4702
  Bounding Box: [1675.20, 4.55, 1764.80, 158.20]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [135, 5, 141, 16]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4697
  Bounding Box: [771.20, 908.80, 881.60, 1057.60]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [65, 75, 72, 86]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4658
  Bounding Box: [1360.80, 1516.80, 1504.80, 1643.20]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [111, 123, 121, 132]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4580
  Bounding Box: [1092.80, 1095.20, 1177.60, 1303.20]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [90, 90, 95, 105]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4495
  Bounding Box: [1106.40, 1083.20, 1173.60, 1248.00]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [91, 89, 95, 101]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4470
  Bounding Box: [9.10, 248.20, 78.80, 372.80]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [5, 24, 8, 33]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4460
  Bounding Box: [1969.60, 1480.80, 2043.20, 1636.80]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [158, 121, 163, 131]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4451
  Bounding Box: [1873.60, 1056.80, 2024.00, 1282.40]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [151, 87, 162, 104]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4446
  Bounding Box: [1680.00, 486.80, 1763.20, 578.80]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [136, 43, 141, 48]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4441
  Bounding Box: [1509.60, 925.60, 1649.60, 1090.40]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [122, 77, 132, 89]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4431
  Bounding Box: [1841.60, 1052.00, 1960.00, 1260.00]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [148, 87, 157, 102]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4431
  Bounding Box: [816.00, 391.20, 1003.20, 725.60]
  Mask Area: 275 pixels
  Mask Ratio: 0.0097
  Mask BBox: [68, 35, 82, 60]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4426
  Bounding Box: [1979.20, 512.80, 2046.40, 665.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [159, 45, 163, 54]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4417
  Bounding Box: [264.80, 1360.00, 384.80, 1462.40]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [25, 111, 34, 117]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4351
  Bounding Box: [1090.40, 425.60, 1176.80, 522.40]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [90, 38, 95, 43]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4316
  Bounding Box: [929.60, 203.00, 1054.40, 347.40]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [77, 20, 86, 31]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4309
  Bounding Box: [1761.60, 831.20, 1985.60, 1143.20]
  Mask Area: 283 pixels
  Mask Ratio: 0.0100
  Mask BBox: [142, 69, 159, 93]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4297
  Bounding Box: [760.40, 1364.00, 953.60, 1590.40]
  Mask Area: 215 pixels
  Mask Ratio: 0.0076
  Mask BBox: [64, 111, 78, 128]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4280
  Bounding Box: [19.00, 1712.00, 233.00, 1913.60]
  Mask Area: 237 pixels
  Mask Ratio: 0.0084
  Mask BBox: [6, 138, 22, 153]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4263
  Bounding Box: [1404.00, 1145.60, 1601.60, 1310.40]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [114, 94, 129, 106]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4224
  Bounding Box: [1390.40, 985.60, 1552.00, 1265.60]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [113, 81, 125, 102]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.4197
  Bounding Box: [1050.40, 272.60, 1165.60, 418.40]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [87, 26, 95, 36]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.4187
  Bounding Box: [1437.60, 986.40, 1699.20, 1197.60]
  Mask Area: 188 pixels
  Mask Ratio: 0.0067
  Mask BBox: [117, 82, 136, 97]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.4148
  Bounding Box: [1332.80, 1100.80, 1516.80, 1291.20]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [109, 90, 121, 104]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.4138
  Bounding Box: [1476.80, 1635.20, 1622.40, 1923.20]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [120, 132, 130, 154]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.4136
  Bounding Box: [1852.80, 596.80, 1987.20, 748.80]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [149, 51, 159, 61]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.4111
  Bounding Box: [336.20, 1258.40, 420.80, 1450.40]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [31, 103, 36, 116]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.4097
  Bounding Box: [1552.00, 504.00, 1625.60, 644.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [126, 44, 130, 54]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.4087
  Bounding Box: [260.40, 1768.00, 423.60, 1864.00]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [25, 143, 37, 149]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.4045
  Bounding Box: [988.80, 1296.80, 1059.20, 1405.60]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [82, 106, 86, 113]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.4045
  Bounding Box: [1793.60, 1216.80, 1912.00, 1394.40]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [145, 100, 153, 112]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.4043
  Bounding Box: [671.20, 504.80, 919.20, 666.40]
  Mask Area: 181 pixels
  Mask Ratio: 0.0064
  Mask BBox: [57, 44, 75, 56]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.4026
  Bounding Box: [638.00, 1921.60, 753.20, 2040.00]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [54, 155, 62, 163]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.4011
  Bounding Box: [1788.80, 1256.80, 1916.80, 1436.00]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [144, 103, 153, 116]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.4004
  Bounding Box: [1160.00, 785.60, 1342.40, 971.20]
  Mask Area: 172 pixels
  Mask Ratio: 0.0061
  Mask BBox: [95, 66, 108, 79]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.4004
  Bounding Box: [61.00, 928.00, 206.80, 1142.40]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [9, 77, 20, 93]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3994
  Bounding Box: [1370.40, 1574.40, 1460.00, 1664.00]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [112, 127, 118, 133]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3992
  Bounding Box: [1878.40, 1808.00, 2028.80, 1968.00]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [151, 146, 162, 157]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3975
  Bounding Box: [2.00, 202.20, 71.00, 364.00]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [5, 20, 9, 32]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3975
  Bounding Box: [524.00, 1120.00, 652.80, 1203.20]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [45, 92, 54, 97]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3970
  Bounding Box: [1127.20, 465.60, 1314.40, 792.00]
  Mask Area: 239 pixels
  Mask Ratio: 0.0085
  Mask BBox: [93, 41, 106, 65]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3955
  Bounding Box: [89.60, 1336.00, 254.40, 1446.40]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [11, 109, 23, 116]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3921
  Bounding Box: [660.00, 1193.60, 805.60, 1320.00]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [56, 98, 66, 107]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3909
  Bounding Box: [1171.20, 1406.40, 1342.40, 1520.00]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [96, 114, 108, 122]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3899
  Bounding Box: [727.20, 1891.20, 839.20, 2032.00]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [61, 152, 69, 162]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3896
  Bounding Box: [322.20, 664.00, 410.00, 792.00]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [30, 56, 36, 65]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3882
  Bounding Box: [1852.80, 278.20, 1964.80, 358.20]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [149, 26, 157, 29]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3877
  Bounding Box: [7.25, 1227.20, 68.00, 1360.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [5, 100, 9, 110]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3872
  Bounding Box: [1320.00, 1968.00, 1406.40, 2035.20]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [108, 158, 112, 162]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3811
  Bounding Box: [880.80, 204.00, 988.00, 340.80]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [73, 20, 81, 29]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3809
  Bounding Box: [268.60, 1848.00, 545.60, 2048.00]
  Mask Area: 291 pixels
  Mask Ratio: 0.0103
  Mask BBox: [25, 149, 46, 164]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3799
  Bounding Box: [612.80, 1676.80, 747.20, 1817.60]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [52, 135, 62, 145]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3789
  Bounding Box: [234.80, 841.60, 398.40, 996.80]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [23, 70, 35, 80]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3760
  Bounding Box: [45.65, 1016.80, 163.00, 1221.60]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [8, 84, 16, 99]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3735
  Bounding Box: [0.00, 1168.00, 72.50, 1320.00]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [4, 96, 8, 107]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3716
  Bounding Box: [1583.20, 583.60, 1672.00, 893.60]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [128, 50, 133, 73]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3701
  Bounding Box: [254.00, 62.20, 338.40, 150.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [24, 9, 30, 15]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3694
  Bounding Box: [891.20, 998.40, 1110.40, 1232.00]
  Mask Area: 254 pixels
  Mask Ratio: 0.0090
  Mask BBox: [74, 82, 90, 100]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3667
  Bounding Box: [1564.80, 1686.40, 1724.80, 1904.00]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [127, 136, 138, 152]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3643
  Bounding Box: [611.20, 1960.00, 734.40, 2048.00]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [52, 158, 61, 163]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3643
  Bounding Box: [611.20, 1985.60, 734.40, 2048.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [52, 160, 61, 166]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3630
  Bounding Box: [1519.20, 913.60, 1704.00, 1190.40]
  Mask Area: 219 pixels
  Mask Ratio: 0.0078
  Mask BBox: [123, 76, 137, 96]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3625
  Bounding Box: [578.40, 720.00, 668.00, 857.60]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [50, 61, 56, 70]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3621
  Bounding Box: [1956.80, 526.00, 2048.00, 675.60]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [157, 46, 164, 56]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3616
  Bounding Box: [1833.60, 616.80, 1932.80, 732.80]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [148, 53, 154, 61]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3584
  Bounding Box: [981.60, 492.40, 1212.00, 700.40]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [81, 43, 98, 58]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [1798.40, 1201.60, 1904.00, 1339.20]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [145, 98, 152, 108]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3540
  Bounding Box: [1936.00, 1833.60, 2038.40, 1990.40]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [156, 148, 163, 159]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3518
  Bounding Box: [418.80, 1841.60, 642.00, 2030.40]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [37, 149, 54, 162]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3486
  Bounding Box: [1300.80, 1944.00, 1406.40, 2020.80]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [106, 156, 113, 161]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3477
  Bounding Box: [12.95, 1350.40, 169.40, 1502.40]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [6, 110, 17, 121]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3459
  Bounding Box: [1848.00, 278.60, 1944.00, 335.40]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [149, 26, 155, 29]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3452
  Bounding Box: [455.20, 686.00, 612.00, 857.60]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [40, 58, 51, 70]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3447
  Bounding Box: [1104.80, 676.00, 1202.40, 825.60]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [91, 57, 97, 68]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3447
  Bounding Box: [892.00, 600.40, 1096.80, 860.80]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [74, 51, 89, 71]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3442
  Bounding Box: [964.80, 72.20, 1153.60, 295.40]
  Mask Area: 207 pixels
  Mask Ratio: 0.0073
  Mask BBox: [80, 10, 94, 27]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3430
  Bounding Box: [1016.80, 282.80, 1154.40, 481.20]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [84, 27, 94, 41]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3401
  Bounding Box: [576.80, 442.00, 782.40, 616.40]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [50, 39, 65, 52]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3367
  Bounding Box: [493.20, 718.40, 638.80, 859.20]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [43, 61, 53, 71]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3364
  Bounding Box: [1600.00, 1832.00, 1689.60, 1931.20]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [129, 148, 135, 154]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3354
  Bounding Box: [1.20, 1505.60, 79.80, 1638.40]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [5, 122, 8, 131]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3345
  Bounding Box: [1603.20, 8.40, 1734.40, 181.00]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [130, 5, 139, 18]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3315
  Bounding Box: [1581.60, 11.85, 1700.80, 199.60]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [128, 5, 136, 19]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1692.80, 144.50, 1817.60, 231.00]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [137, 16, 145, 21]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3296
  Bounding Box: [432.40, 734.40, 563.60, 968.00]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [38, 62, 48, 79]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3289
  Bounding Box: [0.00, 18.20, 66.00, 259.20]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [4, 6, 9, 24]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3286
  Bounding Box: [528.00, 1867.20, 650.40, 1998.40]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [46, 150, 54, 160]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [732.00, 23.90, 882.40, 140.90]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [62, 6, 70, 13]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [556.80, 337.00, 710.40, 549.60]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [48, 31, 59, 46]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3259
  Bounding Box: [756.80, 1231.20, 856.00, 1352.80]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [64, 101, 68, 109]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3259
  Bounding Box: [1028.80, 1282.40, 1152.00, 1397.60]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [85, 105, 93, 113]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3259
  Bounding Box: [1601.60, 1684.80, 1720.00, 1835.20]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [130, 136, 138, 147]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3259
  Bounding Box: [638.40, 1982.40, 760.00, 2048.00]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [54, 159, 63, 163]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3250
  Bounding Box: [1026.40, 1112.00, 1152.80, 1323.20]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [85, 91, 94, 107]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3230
  Bounding Box: [1992.00, 480.80, 2043.20, 648.00]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [160, 42, 163, 54]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3230
  Bounding Box: [458.40, 844.80, 905.60, 1276.80]
  Mask Area: 670 pixels
  Mask Ratio: 0.0237
  Mask BBox: [40, 70, 74, 103]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [1504.80, 1132.80, 1668.80, 1324.80]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [122, 93, 134, 107]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3213
  Bounding Box: [545.60, 1688.00, 656.80, 1825.60]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [47, 136, 55, 146]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.3210
  Bounding Box: [540.80, 1657.60, 740.80, 1824.00]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [47, 134, 61, 146]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.3196
  Bounding Box: [924.80, 1417.60, 1086.40, 1612.80]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [77, 115, 88, 129]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.3196
  Bounding Box: [1383.20, 919.20, 1656.00, 1156.00]
  Mask Area: 229 pixels
  Mask Ratio: 0.0081
  Mask BBox: [113, 76, 133, 94]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.3191
  Bounding Box: [1611.20, 1664.00, 1768.00, 1852.80]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [130, 134, 141, 148]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.3191
  Bounding Box: [1763.20, 1220.80, 1926.40, 1465.60]
  Mask Area: 196 pixels
  Mask Ratio: 0.0069
  Mask BBox: [142, 100, 154, 118]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.3188
  Bounding Box: [336.60, 796.80, 540.40, 988.80]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [31, 67, 46, 81]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.3188
  Bounding Box: [1760.00, 1382.40, 1910.40, 1681.60]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [142, 112, 153, 135]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.3186
  Bounding Box: [1030.40, 1312.00, 1155.20, 1425.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [85, 107, 94, 112]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.3186
  Bounding Box: [1964.80, 715.20, 2041.60, 840.00]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [158, 60, 163, 69]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.3186
  Bounding Box: [1872.00, 1247.20, 2038.40, 1488.80]
  Mask Area: 181 pixels
  Mask Ratio: 0.0064
  Mask BBox: [151, 102, 163, 120]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.3174
  Bounding Box: [442.80, 1560.00, 565.20, 1656.00]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [39, 126, 47, 133]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.3169
  Bounding Box: [992.00, 408.00, 1113.60, 633.60]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [82, 36, 90, 53]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.3154
  Bounding Box: [212.40, 1002.40, 368.40, 1122.40]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [21, 83, 32, 91]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.3154
  Bounding Box: [936.80, 1688.00, 1053.60, 1841.60]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [78, 137, 86, 147]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.3140
  Bounding Box: [1472.80, 919.20, 1665.60, 1132.00]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [120, 76, 134, 92]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.3113
  Bounding Box: [0.25, 138.00, 69.60, 315.20]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [5, 15, 9, 28]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.3110
  Bounding Box: [1188.80, 1375.20, 1332.80, 1490.40]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [97, 112, 108, 120]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.3108
  Bounding Box: [463.60, 1554.40, 567.60, 1632.00]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [41, 126, 48, 131]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.3108
  Bounding Box: [1140.00, 1819.20, 1247.20, 1940.80]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [94, 147, 101, 155]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.3108
  Bounding Box: [1119.20, 1841.60, 1288.80, 2020.80]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [92, 148, 104, 161]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [1684.80, 478.40, 1787.20, 564.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [136, 42, 143, 48]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.3071
  Bounding Box: [1606.40, 1870.40, 1881.60, 2020.80]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [130, 151, 150, 161]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.3069
  Bounding Box: [724.40, 181.00, 783.20, 255.00]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [61, 19, 65, 23]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.3052
  Bounding Box: [204.40, 889.60, 338.00, 1011.20]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [20, 74, 30, 82]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.3044
  Bounding Box: [1286.40, 291.40, 1427.20, 362.40]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [105, 27, 115, 32]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.3027
  Bounding Box: [524.80, 1688.00, 663.20, 1905.60]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [45, 136, 55, 152]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.3025
  Bounding Box: [366.00, 1692.80, 496.40, 1827.20]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [33, 137, 42, 146]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [1921.60, 1801.60, 2036.80, 1904.00]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [155, 145, 163, 152]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [1921.60, 1827.20, 2036.80, 1929.60]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [155, 147, 163, 154]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.3005
  Bounding Box: [1948.80, 270.40, 2048.00, 399.20]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [157, 26, 164, 35]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.3005
  Bounding Box: [1500.00, 1627.20, 1616.00, 1729.60]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [122, 132, 130, 139]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2986
  Bounding Box: [742.40, 805.60, 926.40, 1074.40]
  Mask Area: 202 pixels
  Mask Ratio: 0.0072
  Mask BBox: [62, 67, 76, 87]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2974
  Bounding Box: [756.80, 1259.20, 851.20, 1382.40]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [64, 103, 68, 111]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2961
  Bounding Box: [1481.60, 1466.40, 1608.00, 1567.20]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [120, 119, 129, 126]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2949
  Bounding Box: [1841.60, 881.60, 2046.40, 1105.60]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [151, 73, 163, 90]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2927
  Bounding Box: [648.00, 0.00, 849.60, 162.80]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [55, 4, 70, 16]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2915
  Bounding Box: [692.00, 1174.40, 811.20, 1300.80]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [59, 97, 67, 105]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2903
  Bounding Box: [1060.00, 205.80, 1234.40, 412.00]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [87, 21, 100, 36]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2886
  Bounding Box: [1732.80, 1856.00, 1918.40, 1964.80]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [140, 149, 153, 157]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2866
  Bounding Box: [740.40, 1311.20, 865.60, 1583.20]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [62, 107, 71, 127]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2861
  Bounding Box: [592.40, 1551.20, 768.00, 1769.60]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [51, 126, 63, 142]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2852
  Bounding Box: [1537.60, 736.40, 1608.00, 856.80]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [125, 62, 129, 70]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2852
  Bounding Box: [1667.20, 148.20, 1785.60, 233.40]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [135, 16, 143, 22]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [708.80, 129.90, 784.00, 222.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [60, 15, 65, 21]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [1774.40, 1580.80, 1934.40, 1699.20]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [143, 128, 155, 136]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2815
  Bounding Box: [965.60, 1109.60, 1119.20, 1301.60]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [80, 91, 91, 105]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2800
  Bounding Box: [224.40, 893.60, 365.20, 1108.00]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [22, 74, 32, 90]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2795
  Bounding Box: [1996.80, 1474.40, 2048.00, 1627.20]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [160, 120, 165, 131]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2793
  Bounding Box: [576.00, 683.60, 661.60, 821.60]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [49, 58, 55, 68]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2781
  Bounding Box: [607.20, 1776.00, 678.40, 1849.60]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [52, 143, 56, 148]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2781
  Bounding Box: [214.80, 1777.60, 411.60, 2024.00]
  Mask Area: 217 pixels
  Mask Ratio: 0.0077
  Mask BBox: [21, 143, 36, 162]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2778
  Bounding Box: [912.80, 209.80, 1104.80, 376.40]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [76, 21, 90, 33]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2776
  Bounding Box: [1274.40, 438.40, 1450.40, 614.40]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [104, 39, 117, 50]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2776
  Bounding Box: [1558.40, 1472.80, 1766.40, 1651.20]
  Mask Area: 181 pixels
  Mask Ratio: 0.0064
  Mask BBox: [126, 120, 141, 132]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2744
  Bounding Box: [1134.40, 174.80, 1262.40, 403.60]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [93, 18, 102, 35]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2744
  Bounding Box: [482.40, 1533.60, 564.00, 1625.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [42, 124, 47, 130]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2739
  Bounding Box: [1282.40, 519.60, 1376.80, 615.60]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [105, 45, 111, 52]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2712
  Bounding Box: [1912.00, 1819.20, 2048.00, 2008.00]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [154, 147, 165, 160]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2710
  Bounding Box: [874.40, 1020.00, 938.40, 1125.60]
  Mask Area: 15 pixels
  Mask Ratio: 0.0005
  Mask BBox: [73, 85, 76, 88]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2708
  Bounding Box: [1346.40, 354.80, 1437.60, 410.00]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [110, 32, 116, 36]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2705
  Bounding Box: [832.80, 1033.60, 916.00, 1142.40]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [70, 85, 75, 93]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2688
  Bounding Box: [1556.00, 1538.40, 1771.20, 1689.60]
  Mask Area: 165 pixels
  Mask Ratio: 0.0058
  Mask BBox: [126, 125, 142, 135]

