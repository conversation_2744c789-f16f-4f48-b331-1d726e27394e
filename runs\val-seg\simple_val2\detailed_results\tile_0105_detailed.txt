Image: tile_0105.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8579
  Bounding Box: [1126.40, 1332.00, 1401.60, 1586.40]
  Mask Area: 270 pixels
  Mask Ratio: 0.0096
  Mask BBox: [92, 109, 112, 127]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8540
  Bounding Box: [249.60, 71.40, 438.80, 295.80]
  Mask Area: 182 pixels
  Mask Ratio: 0.0064
  Mask BBox: [24, 10, 38, 27]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8403
  Bounding Box: [843.20, 804.00, 1100.80, 1084.00]
  Mask Area: 324 pixels
  Mask Ratio: 0.0115
  Mask BBox: [71, 67, 89, 88]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8374
  Bounding Box: [984.00, 1122.40, 1233.60, 1405.60]
  Mask Area: 313 pixels
  Mask Ratio: 0.0111
  Mask BBox: [81, 92, 100, 113]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8325
  Bounding Box: [92.50, 948.00, 316.00, 1164.00]
  Mask Area: 219 pixels
  Mask Ratio: 0.0078
  Mask BBox: [12, 79, 28, 94]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8315
  Bounding Box: [34.20, 1732.80, 333.80, 1905.60]
  Mask Area: 186 pixels
  Mask Ratio: 0.0066
  Mask BBox: [7, 140, 30, 152]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8291
  Bounding Box: [713.60, 21.70, 876.80, 261.20]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [60, 6, 72, 24]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8208
  Bounding Box: [727.20, 1143.20, 842.40, 1423.20]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [61, 94, 69, 115]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8174
  Bounding Box: [1811.20, 1530.40, 2048.00, 1766.40]
  Mask Area: 259 pixels
  Mask Ratio: 0.0092
  Mask BBox: [147, 124, 164, 141]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8145
  Bounding Box: [1120.80, 620.00, 1407.20, 965.60]
  Mask Area: 410 pixels
  Mask Ratio: 0.0145
  Mask BBox: [92, 53, 113, 78]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8140
  Bounding Box: [1265.60, 1819.20, 1468.80, 2024.00]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [103, 147, 118, 160]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8115
  Bounding Box: [1307.20, 1568.80, 1544.00, 1825.60]
  Mask Area: 299 pixels
  Mask Ratio: 0.0106
  Mask BBox: [107, 127, 124, 146]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.8105
  Bounding Box: [802.40, 1290.40, 981.60, 1503.20]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [67, 105, 80, 121]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.7993
  Bounding Box: [2.25, 1396.80, 185.80, 1755.20]
  Mask Area: 326 pixels
  Mask Ratio: 0.0116
  Mask BBox: [5, 114, 18, 141]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7983
  Bounding Box: [210.40, 786.40, 364.00, 1013.60]
  Mask Area: 157 pixels
  Mask Ratio: 0.0056
  Mask BBox: [21, 66, 32, 83]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7964
  Bounding Box: [1469.60, 404.00, 1625.60, 569.60]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [119, 36, 130, 48]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7959
  Bounding Box: [1912.00, 311.60, 2048.00, 514.80]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [154, 29, 164, 44]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7939
  Bounding Box: [1937.60, 179.80, 2046.40, 299.80]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [156, 19, 163, 27]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7939
  Bounding Box: [1240.00, 1005.60, 1428.80, 1295.20]
  Mask Area: 261 pixels
  Mask Ratio: 0.0092
  Mask BBox: [101, 83, 115, 105]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7871
  Bounding Box: [200.00, 331.20, 295.20, 470.40]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [20, 30, 27, 40]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7871
  Bounding Box: [1372.00, 880.00, 1525.60, 1001.60]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [112, 73, 122, 80]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7871
  Bounding Box: [522.40, 451.20, 725.60, 779.20]
  Mask Area: 269 pixels
  Mask Ratio: 0.0095
  Mask BBox: [45, 40, 59, 64]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7861
  Bounding Box: [1546.40, 165.40, 1784.00, 300.20]
  Mask Area: 138 pixels
  Mask Ratio: 0.0049
  Mask BBox: [126, 17, 143, 27]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7808
  Bounding Box: [1632.00, 1289.60, 1852.80, 1529.60]
  Mask Area: 224 pixels
  Mask Ratio: 0.0079
  Mask BBox: [132, 105, 147, 123]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7783
  Bounding Box: [383.20, 1135.20, 586.40, 1552.80]
  Mask Area: 344 pixels
  Mask Ratio: 0.0122
  Mask BBox: [34, 93, 49, 122]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7764
  Bounding Box: [1462.40, 1840.00, 1648.00, 2048.00]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [119, 148, 131, 163]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7749
  Bounding Box: [94.60, 1889.60, 263.80, 2046.40]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [12, 152, 22, 163]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7744
  Bounding Box: [1507.20, 623.20, 1801.60, 1076.00]
  Mask Area: 621 pixels
  Mask Ratio: 0.0220
  Mask BBox: [122, 53, 144, 88]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7710
  Bounding Box: [975.20, 10.45, 1208.80, 138.40]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [81, 5, 98, 14]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7710
  Bounding Box: [616.80, 1363.20, 767.20, 1545.60]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [53, 111, 63, 124]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7686
  Bounding Box: [746.80, 1756.80, 995.20, 2038.40]
  Mask Area: 314 pixels
  Mask Ratio: 0.0111
  Mask BBox: [63, 142, 81, 163]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7661
  Bounding Box: [109.80, 7.15, 295.00, 150.40]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [13, 5, 27, 15]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7622
  Bounding Box: [836.80, 1062.40, 1064.00, 1304.00]
  Mask Area: 199 pixels
  Mask Ratio: 0.0071
  Mask BBox: [70, 87, 87, 105]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7598
  Bounding Box: [135.90, 1392.00, 393.60, 1689.60]
  Mask Area: 316 pixels
  Mask Ratio: 0.0112
  Mask BBox: [15, 113, 33, 135]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7598
  Bounding Box: [1545.60, 1538.40, 1868.80, 2022.40]
  Mask Area: 651 pixels
  Mask Ratio: 0.0231
  Mask BBox: [126, 125, 149, 161]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7573
  Bounding Box: [1924.80, 619.60, 2033.60, 754.00]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [155, 53, 162, 62]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7573
  Bounding Box: [959.20, 1676.80, 1122.40, 1865.60]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [79, 135, 91, 149]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7544
  Bounding Box: [1894.40, 1156.80, 2048.00, 1440.00]
  Mask Area: 247 pixels
  Mask Ratio: 0.0088
  Mask BBox: [152, 95, 164, 116]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7515
  Bounding Box: [1033.60, 1553.60, 1238.40, 1758.40]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [85, 126, 100, 141]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7500
  Bounding Box: [619.60, 286.00, 803.20, 446.00]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [53, 27, 66, 38]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7456
  Bounding Box: [529.60, 1505.60, 662.40, 1620.80]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [46, 122, 55, 130]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7427
  Bounding Box: [712.40, 1617.60, 810.40, 1752.00]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [60, 131, 67, 140]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7397
  Bounding Box: [1702.40, 572.00, 1936.00, 815.20]
  Mask Area: 230 pixels
  Mask Ratio: 0.0081
  Mask BBox: [137, 49, 155, 67]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7393
  Bounding Box: [1084.00, 559.20, 1247.20, 720.80]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [89, 48, 101, 58]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7393
  Bounding Box: [1776.00, 1410.40, 1939.20, 1522.40]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [143, 115, 155, 122]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7354
  Bounding Box: [1614.40, 353.60, 1771.20, 592.80]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [131, 32, 142, 50]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7305
  Bounding Box: [989.60, 1388.00, 1136.80, 1536.80]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [82, 113, 92, 123]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7300
  Bounding Box: [787.20, 928.00, 899.20, 1064.00]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [66, 77, 74, 87]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7295
  Bounding Box: [1875.20, 1875.20, 2038.40, 2048.00]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [151, 151, 163, 164]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7290
  Bounding Box: [1131.20, 123.10, 1372.80, 328.00]
  Mask Area: 213 pixels
  Mask Ratio: 0.0075
  Mask BBox: [93, 14, 111, 28]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.7290
  Bounding Box: [215.80, 424.80, 403.20, 586.40]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [21, 38, 35, 49]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.7236
  Bounding Box: [1385.60, 965.60, 1564.80, 1352.80]
  Mask Area: 302 pixels
  Mask Ratio: 0.0107
  Mask BBox: [113, 80, 126, 109]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.7231
  Bounding Box: [34.30, 125.70, 277.20, 336.80]
  Mask Area: 243 pixels
  Mask Ratio: 0.0086
  Mask BBox: [7, 14, 25, 30]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.7192
  Bounding Box: [276.20, 652.80, 426.40, 837.60]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [26, 55, 37, 69]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.7188
  Bounding Box: [834.40, 200.40, 1053.60, 540.40]
  Mask Area: 332 pixels
  Mask Ratio: 0.0118
  Mask BBox: [70, 20, 86, 46]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.7183
  Bounding Box: [727.20, 401.20, 874.40, 574.80]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [61, 36, 72, 48]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.7183
  Bounding Box: [337.60, 576.80, 506.40, 792.80]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [31, 50, 43, 65]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.7153
  Bounding Box: [416.40, 64.30, 569.20, 355.60]
  Mask Area: 210 pixels
  Mask Ratio: 0.0074
  Mask BBox: [37, 10, 48, 31]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.7119
  Bounding Box: [1231.20, 506.80, 1389.60, 639.60]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [101, 44, 111, 52]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.7090
  Bounding Box: [1356.00, 29.20, 1519.20, 300.00]
  Mask Area: 221 pixels
  Mask Ratio: 0.0078
  Mask BBox: [110, 7, 122, 27]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.7070
  Bounding Box: [596.40, 1944.00, 718.80, 2036.80]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [51, 156, 60, 163]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.7036
  Bounding Box: [858.40, 87.80, 1063.20, 232.20]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [72, 11, 87, 21]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.7036
  Bounding Box: [1731.20, 300.00, 1817.60, 444.80]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [140, 28, 145, 37]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.7012
  Bounding Box: [0.00, 1779.20, 110.20, 1977.60]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [4, 143, 12, 158]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6929
  Bounding Box: [1567.20, 1157.60, 1731.20, 1365.60]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [127, 95, 139, 110]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6929
  Bounding Box: [1744.00, 294.60, 1961.60, 579.20]
  Mask Area: 218 pixels
  Mask Ratio: 0.0077
  Mask BBox: [141, 28, 157, 49]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6841
  Bounding Box: [14.60, 673.60, 183.40, 968.00]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [6, 57, 18, 79]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6826
  Bounding Box: [331.80, 923.20, 510.40, 1123.20]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [30, 77, 43, 90]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6812
  Bounding Box: [1102.40, 984.80, 1297.60, 1218.40]
  Mask Area: 190 pixels
  Mask Ratio: 0.0067
  Mask BBox: [91, 81, 105, 99]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6772
  Bounding Box: [923.20, 678.00, 1075.20, 808.80]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [77, 57, 87, 66]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6738
  Bounding Box: [1393.60, 1305.60, 1558.40, 1492.80]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [113, 106, 125, 120]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6704
  Bounding Box: [1726.40, 1219.20, 1851.20, 1302.40]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [139, 100, 148, 105]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6675
  Bounding Box: [1907.20, 750.40, 2048.00, 1083.20]
  Mask Area: 274 pixels
  Mask Ratio: 0.0097
  Mask BBox: [153, 63, 164, 88]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6665
  Bounding Box: [1.05, 1124.00, 100.20, 1396.00]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [5, 92, 11, 113]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6553
  Bounding Box: [1683.20, 912.80, 1942.40, 1212.00]
  Mask Area: 278 pixels
  Mask Ratio: 0.0098
  Mask BBox: [136, 76, 155, 98]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6509
  Bounding Box: [1587.20, 1032.80, 1721.60, 1146.40]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [128, 85, 138, 92]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6489
  Bounding Box: [390.00, 11.95, 531.60, 97.20]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [35, 5, 45, 11]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6470
  Bounding Box: [1197.60, 4.85, 1340.00, 127.20]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [98, 5, 108, 13]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6465
  Bounding Box: [247.60, 267.40, 430.00, 455.20]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [24, 25, 37, 39]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6460
  Bounding Box: [1396.00, 700.40, 1528.80, 877.60]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [114, 59, 123, 72]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6436
  Bounding Box: [1554.40, 1356.80, 1644.80, 1564.80]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [126, 110, 132, 124]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6367
  Bounding Box: [832.80, 574.80, 976.80, 702.00]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [70, 49, 80, 58]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6348
  Bounding Box: [689.60, 1013.60, 815.20, 1204.00]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [58, 84, 67, 98]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6284
  Bounding Box: [413.60, 354.00, 620.00, 538.00]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [37, 32, 52, 46]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6245
  Bounding Box: [454.40, 786.40, 664.80, 992.80]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [40, 66, 55, 81]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6196
  Bounding Box: [824.80, 1053.60, 940.00, 1173.60]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [69, 87, 76, 94]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.6191
  Bounding Box: [1577.60, 284.80, 1731.20, 401.60]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [128, 27, 139, 35]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.6138
  Bounding Box: [1704.00, 8.15, 2004.80, 184.60]
  Mask Area: 251 pixels
  Mask Ratio: 0.0089
  Mask BBox: [138, 5, 160, 18]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.6074
  Bounding Box: [63.50, 308.80, 245.20, 708.80]
  Mask Area: 330 pixels
  Mask Ratio: 0.0117
  Mask BBox: [9, 29, 23, 59]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.6069
  Bounding Box: [751.20, 667.60, 858.40, 952.00]
  Mask Area: 138 pixels
  Mask Ratio: 0.0049
  Mask BBox: [63, 57, 71, 78]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.6060
  Bounding Box: [1164.00, 328.80, 1293.60, 524.80]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [95, 30, 105, 44]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.5986
  Bounding Box: [0.00, 1982.40, 98.60, 2046.40]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [4, 159, 11, 163]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5981
  Bounding Box: [1058.40, 1806.40, 1255.20, 2011.20]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [87, 146, 102, 161]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5952
  Bounding Box: [466.80, 1047.20, 534.80, 1132.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [41, 86, 45, 92]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5947
  Bounding Box: [1378.40, 1415.20, 1557.60, 1604.80]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [113, 115, 124, 128]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5938
  Bounding Box: [911.20, 1910.40, 1044.00, 2044.80]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [76, 154, 85, 163]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5913
  Bounding Box: [77.20, 1108.00, 378.00, 1410.40]
  Mask Area: 473 pixels
  Mask Ratio: 0.0168
  Mask BBox: [11, 91, 33, 114]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5830
  Bounding Box: [288.20, 1718.40, 688.80, 2048.00]
  Mask Area: 616 pixels
  Mask Ratio: 0.0218
  Mask BBox: [27, 139, 57, 163]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5767
  Bounding Box: [930.40, 1223.20, 1008.80, 1344.80]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [77, 100, 82, 109]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5713
  Bounding Box: [1325.60, 984.00, 1434.40, 1075.20]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [108, 81, 116, 87]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5693
  Bounding Box: [182.80, 1654.40, 326.00, 1737.60]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [19, 134, 29, 139]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5669
  Bounding Box: [1913.60, 141.70, 2038.40, 320.00]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [154, 16, 163, 28]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5640
  Bounding Box: [594.40, 943.20, 702.40, 1029.60]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [51, 78, 58, 84]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5586
  Bounding Box: [7.90, 966.40, 81.90, 1084.80]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [5, 80, 10, 88]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5532
  Bounding Box: [707.20, 1451.20, 996.80, 1729.60]
  Mask Area: 333 pixels
  Mask Ratio: 0.0118
  Mask BBox: [60, 118, 81, 139]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5464
  Bounding Box: [564.00, 28.80, 748.00, 287.20]
  Mask Area: 200 pixels
  Mask Ratio: 0.0071
  Mask BBox: [49, 7, 62, 26]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5449
  Bounding Box: [1060.00, 808.00, 1132.00, 923.20]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [87, 68, 91, 76]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5410
  Bounding Box: [426.40, 1584.00, 749.60, 1836.80]
  Mask Area: 362 pixels
  Mask Ratio: 0.0128
  Mask BBox: [38, 128, 62, 147]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5352
  Bounding Box: [1984.00, 1068.80, 2044.80, 1193.60]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [159, 88, 163, 95]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5293
  Bounding Box: [287.00, 2.15, 403.20, 78.20]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [27, 5, 35, 9]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5239
  Bounding Box: [0.00, 244.20, 83.40, 404.40]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [4, 24, 9, 35]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5225
  Bounding Box: [572.40, 1052.00, 722.80, 1309.60]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [49, 87, 60, 106]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5176
  Bounding Box: [253.60, 92.20, 544.80, 300.20]
  Mask Area: 289 pixels
  Mask Ratio: 0.0102
  Mask BBox: [25, 12, 46, 27]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5142
  Bounding Box: [1389.60, 1977.60, 1490.40, 2038.40]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [113, 159, 118, 163]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5132
  Bounding Box: [1934.40, 1780.80, 2048.00, 1918.40]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [156, 144, 164, 153]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5098
  Bounding Box: [908.80, 663.20, 1084.80, 853.60]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [75, 56, 88, 70]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5078
  Bounding Box: [1640.00, 607.20, 1723.20, 723.20]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [133, 52, 138, 60]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5039
  Bounding Box: [964.00, 541.60, 1092.00, 707.20]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [80, 47, 89, 59]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5000
  Bounding Box: [1587.20, 0.00, 1734.40, 151.80]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [128, 4, 139, 15]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.4976
  Bounding Box: [1111.20, 1704.00, 1317.60, 1822.40]
  Mask Area: 112 pixels
  Mask Ratio: 0.0040
  Mask BBox: [91, 138, 106, 146]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.4912
  Bounding Box: [1692.80, 1934.40, 1849.60, 2043.20]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [137, 156, 148, 163]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.4858
  Bounding Box: [1512.00, 376.00, 1758.40, 576.80]
  Mask Area: 242 pixels
  Mask Ratio: 0.0086
  Mask BBox: [123, 34, 141, 49]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.4805
  Bounding Box: [1364.80, 311.60, 1488.00, 414.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [111, 29, 120, 35]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.4756
  Bounding Box: [361.60, 1112.80, 444.80, 1202.40]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [33, 91, 38, 97]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.4731
  Bounding Box: [1947.20, 1444.00, 2027.20, 1538.40]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [157, 117, 162, 124]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.4731
  Bounding Box: [1977.60, 1435.20, 2032.00, 1545.60]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [159, 117, 162, 124]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.4724
  Bounding Box: [696.40, 1016.00, 880.00, 1193.60]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [59, 84, 72, 96]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4722
  Bounding Box: [333.20, 1409.60, 468.40, 1560.00]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [31, 115, 40, 123]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.4702
  Bounding Box: [1272.80, 1526.40, 1338.40, 1619.20]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [104, 124, 108, 130]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4673
  Bounding Box: [1210.40, 1255.20, 1301.60, 1354.40]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [99, 103, 105, 109]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4639
  Bounding Box: [1258.40, 1241.60, 1354.40, 1337.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [103, 101, 109, 107]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4624
  Bounding Box: [222.80, 1345.60, 318.40, 1419.20]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [22, 110, 28, 114]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4609
  Bounding Box: [541.20, 1367.20, 644.40, 1517.60]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [47, 111, 54, 122]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4580
  Bounding Box: [4.05, 386.40, 56.40, 490.40]
  Mask Area: 21 pixels
  Mask Ratio: 0.0007
  Mask BBox: [5, 35, 7, 42]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4548
  Bounding Box: [1854.40, 1737.60, 1995.20, 1846.40]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [149, 140, 159, 148]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4460
  Bounding Box: [1550.40, 1.15, 1710.40, 181.40]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [126, 5, 137, 18]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4436
  Bounding Box: [1929.60, 47.90, 2048.00, 194.00]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [155, 8, 163, 19]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4436
  Bounding Box: [1881.60, 480.00, 2012.80, 615.20]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [151, 42, 161, 52]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4417
  Bounding Box: [400.00, 524.40, 554.40, 618.80]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [36, 45, 46, 51]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4417
  Bounding Box: [1516.80, 6.75, 1620.80, 179.60]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [123, 5, 130, 18]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4370
  Bounding Box: [1386.40, 689.20, 1573.60, 905.60]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [114, 58, 122, 74]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4331
  Bounding Box: [1828.80, 1580.80, 2040.00, 1856.00]
  Mask Area: 296 pixels
  Mask Ratio: 0.0105
  Mask BBox: [147, 128, 163, 148]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4316
  Bounding Box: [1800.00, 1125.60, 1928.00, 1293.60]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [145, 92, 154, 105]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4307
  Bounding Box: [2.20, 475.20, 84.10, 688.80]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [5, 42, 10, 57]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4287
  Bounding Box: [285.20, 613.20, 517.20, 821.60]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [27, 52, 44, 68]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4285
  Bounding Box: [226.60, 439.20, 480.80, 600.00]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [22, 39, 41, 50]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4268
  Bounding Box: [1064.80, 269.40, 1162.40, 521.60]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [88, 26, 94, 44]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4263
  Bounding Box: [1905.60, 633.20, 2020.80, 779.20]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [153, 54, 161, 62]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4263
  Bounding Box: [1565.60, 1168.00, 1824.00, 1344.00]
  Mask Area: 209 pixels
  Mask Ratio: 0.0074
  Mask BBox: [127, 96, 146, 108]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4253
  Bounding Box: [2.90, 992.80, 88.90, 1108.00]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [5, 82, 10, 90]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4238
  Bounding Box: [5.20, 401.60, 66.30, 516.80]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [5, 36, 8, 44]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4211
  Bounding Box: [1380.80, 1330.40, 1564.80, 1575.20]
  Mask Area: 198 pixels
  Mask Ratio: 0.0070
  Mask BBox: [112, 108, 126, 127]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4202
  Bounding Box: [1806.40, 1327.20, 1899.20, 1436.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [146, 108, 152, 116]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4194
  Bounding Box: [1558.40, 183.40, 1766.40, 378.00]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [126, 19, 141, 33]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4192
  Bounding Box: [509.20, 753.20, 701.20, 969.60]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [44, 63, 58, 79]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4136
  Bounding Box: [1800.00, 235.40, 1956.80, 326.20]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [145, 23, 156, 29]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4116
  Bounding Box: [1268.00, 1545.60, 1327.20, 1641.60]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [104, 125, 107, 132]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4116
  Bounding Box: [1073.60, 536.80, 1315.20, 717.60]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [89, 46, 106, 58]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4092
  Bounding Box: [214.20, 695.20, 396.80, 997.60]
  Mask Area: 242 pixels
  Mask Ratio: 0.0086
  Mask BBox: [21, 59, 34, 81]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4065
  Bounding Box: [1710.40, 287.80, 1857.60, 484.40]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [138, 27, 149, 41]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4055
  Bounding Box: [1000.00, 1827.20, 1201.60, 2041.60]
  Mask Area: 172 pixels
  Mask Ratio: 0.0061
  Mask BBox: [83, 147, 97, 163]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.4031
  Bounding Box: [1236.00, 450.00, 1428.00, 646.00]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [101, 40, 115, 54]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.3970
  Bounding Box: [1352.80, 0.00, 1445.60, 84.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [110, 4, 116, 10]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.3928
  Bounding Box: [520.40, 0.00, 622.80, 84.00]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [45, 4, 52, 9]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.3872
  Bounding Box: [0.00, 218.40, 93.40, 358.40]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [4, 22, 10, 31]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.3853
  Bounding Box: [868.80, 1092.80, 1196.80, 1363.20]
  Mask Area: 407 pixels
  Mask Ratio: 0.0144
  Mask BBox: [72, 90, 97, 110]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.3843
  Bounding Box: [368.00, 2.25, 510.40, 119.60]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [33, 5, 43, 12]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.3821
  Bounding Box: [1189.60, 293.20, 1452.00, 550.00]
  Mask Area: 296 pixels
  Mask Ratio: 0.0105
  Mask BBox: [97, 27, 117, 46]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.3816
  Bounding Box: [1825.60, 1306.40, 1896.00, 1418.40]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [147, 107, 152, 114]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.3813
  Bounding Box: [1891.20, 350.40, 2035.20, 604.80]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [152, 32, 162, 50]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.3794
  Bounding Box: [1298.40, 997.60, 1564.00, 1322.40]
  Mask Area: 426 pixels
  Mask Ratio: 0.0151
  Mask BBox: [106, 82, 126, 107]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3772
  Bounding Box: [1137.60, 1974.40, 1260.80, 2032.00]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [93, 159, 102, 162]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3748
  Bounding Box: [1401.60, 1960.00, 1486.40, 2048.00]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [114, 158, 118, 163]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3711
  Bounding Box: [981.60, 1572.80, 1224.80, 1835.20]
  Mask Area: 290 pixels
  Mask Ratio: 0.0103
  Mask BBox: [81, 127, 99, 147]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3701
  Bounding Box: [226.40, 1979.20, 331.20, 2040.00]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [22, 159, 28, 163]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3696
  Bounding Box: [1708.80, 1376.00, 1964.80, 1542.40]
  Mask Area: 199 pixels
  Mask Ratio: 0.0071
  Mask BBox: [138, 112, 157, 124]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3679
  Bounding Box: [595.20, 957.60, 729.60, 1044.00]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [51, 79, 60, 85]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3667
  Bounding Box: [582.80, 1921.60, 730.80, 2048.00]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [50, 155, 61, 163]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3652
  Bounding Box: [1172.00, 1964.80, 1335.20, 2032.00]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [96, 158, 108, 162]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3621
  Bounding Box: [864.80, 8.10, 948.00, 85.90]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [72, 5, 78, 10]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3604
  Bounding Box: [1936.00, 638.80, 2048.00, 777.60]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [156, 54, 163, 62]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3579
  Bounding Box: [1030.40, 1101.60, 1097.60, 1192.80]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [85, 91, 89, 97]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3535
  Bounding Box: [913.60, 1194.40, 1022.40, 1354.40]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [76, 98, 83, 109]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3518
  Bounding Box: [560.80, 136.70, 706.40, 309.60]
  Mask Area: 138 pixels
  Mask Ratio: 0.0049
  Mask BBox: [48, 15, 59, 28]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3508
  Bounding Box: [12.10, 1356.00, 130.70, 1426.40]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [5, 110, 13, 115]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3506
  Bounding Box: [140.20, 320.80, 302.60, 481.60]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [15, 30, 27, 41]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3459
  Bounding Box: [1993.60, 518.00, 2044.80, 635.60]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [160, 45, 163, 53]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3367
  Bounding Box: [79.30, 649.60, 149.10, 749.60]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [11, 55, 15, 60]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3359
  Bounding Box: [928.00, 1886.40, 1136.00, 2036.80]
  Mask Area: 159 pixels
  Mask Ratio: 0.0056
  Mask BBox: [77, 152, 92, 163]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3350
  Bounding Box: [919.20, 20.10, 1192.80, 182.80]
  Mask Area: 215 pixels
  Mask Ratio: 0.0076
  Mask BBox: [76, 6, 97, 18]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3345
  Bounding Box: [428.40, 174.40, 570.00, 380.80]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [38, 18, 48, 33]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3342
  Bounding Box: [1768.00, 794.40, 1876.80, 908.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [143, 67, 150, 74]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3328
  Bounding Box: [1217.60, 1225.60, 1321.60, 1358.40]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [100, 100, 107, 108]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3315
  Bounding Box: [610.40, 25.55, 754.40, 220.00]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [52, 6, 62, 21]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3311
  Bounding Box: [22.30, 14.45, 144.50, 131.20]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [6, 6, 15, 14]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [164.40, 1553.60, 266.80, 1678.40]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [17, 126, 24, 135]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3267
  Bounding Box: [1277.60, 287.40, 1484.00, 497.60]
  Mask Area: 192 pixels
  Mask Ratio: 0.0068
  Mask BBox: [104, 27, 119, 42]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3237
  Bounding Box: [788.80, 531.60, 968.00, 694.80]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [66, 46, 79, 58]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [1567.20, 0.00, 1812.80, 169.20]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [127, 4, 145, 17]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3218
  Bounding Box: [1481.60, 297.20, 1571.20, 402.00]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [120, 28, 126, 35]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3218
  Bounding Box: [1777.60, 828.00, 1880.00, 930.40]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [143, 69, 150, 76]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3213
  Bounding Box: [853.60, 589.60, 1050.40, 773.60]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [71, 51, 86, 64]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3203
  Bounding Box: [170.60, 1609.60, 287.00, 1728.00]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [18, 130, 26, 138]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3186
  Bounding Box: [0.00, 1740.80, 253.60, 1948.80]
  Mask Area: 283 pixels
  Mask Ratio: 0.0100
  Mask BBox: [4, 140, 23, 156]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3179
  Bounding Box: [812.80, 549.60, 950.40, 672.80]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [68, 47, 78, 56]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3169
  Bounding Box: [745.20, 1169.60, 862.40, 1460.80]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [63, 96, 71, 118]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3169
  Bounding Box: [1371.20, 817.60, 1451.20, 913.60]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [112, 68, 115, 75]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3162
  Bounding Box: [864.80, 1958.40, 943.20, 2041.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [72, 157, 77, 163]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3159
  Bounding Box: [1883.20, 195.00, 2040.00, 372.00]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [152, 20, 163, 33]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3157
  Bounding Box: [1574.40, 1341.60, 1664.00, 1540.00]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [127, 109, 132, 124]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3152
  Bounding Box: [1570.40, 1319.20, 1792.00, 1538.40]
  Mask Area: 227 pixels
  Mask Ratio: 0.0080
  Mask BBox: [127, 108, 143, 124]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3145
  Bounding Box: [402.40, 8.80, 565.60, 214.80]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [36, 5, 48, 20]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3110
  Bounding Box: [4.80, 540.40, 79.70, 726.80]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [5, 47, 10, 60]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [79.40, 387.20, 271.80, 769.60]
  Mask Area: 345 pixels
  Mask Ratio: 0.0122
  Mask BBox: [11, 35, 25, 64]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3091
  Bounding Box: [1900.80, 489.60, 2025.60, 749.60]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [153, 43, 162, 62]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3083
  Bounding Box: [452.80, 1527.20, 732.00, 1766.40]
  Mask Area: 277 pixels
  Mask Ratio: 0.0098
  Mask BBox: [40, 124, 61, 141]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3071
  Bounding Box: [1630.40, 269.60, 1803.20, 428.80]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [132, 26, 144, 37]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3040
  Bounding Box: [142.50, 0.00, 330.80, 129.40]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [16, 4, 29, 14]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3040
  Bounding Box: [1921.60, 14.05, 2040.00, 147.20]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [155, 6, 163, 15]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3013
  Bounding Box: [2000.00, 490.40, 2048.00, 626.40]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [161, 43, 164, 52]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3013
  Bounding Box: [888.80, 0.55, 983.20, 77.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [74, 5, 80, 10]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3000
  Bounding Box: [478.80, 1972.80, 602.80, 2043.20]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [42, 159, 51, 163]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.2983
  Bounding Box: [999.20, 1549.60, 1074.40, 1689.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [83, 126, 87, 135]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.2971
  Bounding Box: [366.40, 513.60, 509.60, 604.00]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [33, 45, 43, 51]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.2942
  Bounding Box: [1644.80, 382.80, 1801.60, 606.80]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [133, 34, 144, 51]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.2942
  Bounding Box: [1872.00, 1737.60, 2048.00, 1916.80]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [151, 140, 163, 153]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.2915
  Bounding Box: [165.00, 1377.60, 423.20, 1612.80]
  Mask Area: 251 pixels
  Mask Ratio: 0.0089
  Mask BBox: [17, 112, 37, 129]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.2900
  Bounding Box: [1636.80, 316.80, 1822.40, 582.40]
  Mask Area: 246 pixels
  Mask Ratio: 0.0087
  Mask BBox: [132, 29, 146, 49]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.2893
  Bounding Box: [1697.60, 1196.00, 1822.40, 1288.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [137, 98, 146, 104]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.2893
  Bounding Box: [1697.60, 1221.60, 1822.40, 1314.40]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [137, 100, 146, 106]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.2891
  Bounding Box: [791.20, 880.80, 1000.80, 1117.60]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [66, 73, 82, 91]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.2881
  Bounding Box: [1700.80, 284.40, 1803.20, 444.40]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [137, 27, 144, 37]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.2881
  Bounding Box: [35.30, 3.55, 240.00, 161.60]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [7, 5, 22, 16]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.2869
  Bounding Box: [1750.40, 1147.20, 1920.00, 1302.40]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [141, 94, 153, 105]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.2866
  Bounding Box: [173.20, 307.60, 277.20, 447.60]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [18, 29, 25, 38]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.2849
  Bounding Box: [448.40, 1014.40, 547.60, 1142.40]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [40, 84, 46, 92]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.2834
  Bounding Box: [1792.00, 1386.40, 1993.60, 1540.00]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [144, 113, 159, 124]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [45.10, 1838.40, 236.80, 2036.80]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [8, 148, 22, 163]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.2815
  Bounding Box: [1212.80, 1571.20, 1288.00, 1648.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [99, 127, 104, 132]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.2800
  Bounding Box: [1015.20, 154.00, 1156.00, 479.60]
  Mask Area: 243 pixels
  Mask Ratio: 0.0086
  Mask BBox: [84, 17, 94, 41]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.2795
  Bounding Box: [0.00, 461.60, 99.80, 626.40]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [4, 41, 9, 52]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.2795
  Bounding Box: [853.60, 1940.80, 957.60, 2046.40]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [71, 156, 78, 163]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.2786
  Bounding Box: [1894.40, 496.80, 1990.40, 629.60]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [152, 43, 159, 53]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.2778
  Bounding Box: [1197.60, 1248.00, 1288.80, 1336.00]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [98, 102, 104, 108]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.2778
  Bounding Box: [1223.20, 1248.00, 1314.40, 1336.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [100, 102, 106, 108]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.2778
  Bounding Box: [1197.60, 1273.60, 1288.80, 1361.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [98, 104, 104, 109]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.2778
  Bounding Box: [1223.20, 1273.60, 1314.40, 1361.60]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [100, 104, 106, 109]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.2771
  Bounding Box: [859.20, 172.60, 1110.40, 480.00]
  Mask Area: 318 pixels
  Mask Ratio: 0.0113
  Mask BBox: [72, 18, 90, 41]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.2764
  Bounding Box: [1443.20, 0.00, 1638.40, 217.60]
  Mask Area: 223 pixels
  Mask Ratio: 0.0079
  Mask BBox: [117, 4, 131, 20]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.2751
  Bounding Box: [1324.80, 4.70, 1433.60, 80.20]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [108, 5, 115, 10]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.2747
  Bounding Box: [835.20, 113.00, 1040.00, 264.60]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [70, 13, 82, 21]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.2744
  Bounding Box: [432.80, 807.20, 600.80, 997.60]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [38, 68, 50, 81]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.2725
  Bounding Box: [1571.20, 1142.40, 1715.20, 1302.40]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [127, 94, 137, 105]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.2720
  Bounding Box: [487.60, 312.60, 776.00, 494.40]
  Mask Area: 200 pixels
  Mask Ratio: 0.0071
  Mask BBox: [43, 29, 64, 42]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.2705
  Bounding Box: [21.40, 1328.00, 121.80, 1422.40]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [6, 108, 13, 115]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.2698
  Bounding Box: [0.00, 376.40, 44.40, 474.80]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [3, 34, 7, 41]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.2698
  Bounding Box: [9.40, 376.40, 70.00, 474.80]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [5, 34, 7, 41]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.2698
  Bounding Box: [0.00, 402.00, 44.40, 500.40]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [3, 36, 7, 43]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.2690
  Bounding Box: [3.50, 1029.60, 100.90, 1132.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [5, 85, 10, 92]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.2678
  Bounding Box: [851.20, 1028.80, 958.40, 1147.20]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [71, 85, 76, 93]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.2664
  Bounding Box: [625.60, 14.50, 868.80, 240.80]
  Mask Area: 267 pixels
  Mask Ratio: 0.0095
  Mask BBox: [53, 6, 71, 22]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.2651
  Bounding Box: [1076.80, 249.60, 1220.80, 548.00]
  Mask Area: 214 pixels
  Mask Ratio: 0.0076
  Mask BBox: [89, 24, 99, 46]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2639
  Bounding Box: [1198.40, 968.80, 1390.40, 1300.00]
  Mask Area: 256 pixels
  Mask Ratio: 0.0091
  Mask BBox: [98, 81, 112, 105]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2629
  Bounding Box: [319.20, 0.00, 439.20, 98.60]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [29, 4, 38, 11]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2598
  Bounding Box: [578.00, 936.00, 680.40, 1022.40]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [50, 78, 57, 83]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2598
  Bounding Box: [578.00, 961.60, 680.40, 1048.00]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [50, 80, 57, 85]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2588
  Bounding Box: [186.80, 1848.00, 289.20, 1956.80]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [19, 149, 26, 156]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2576
  Bounding Box: [1883.20, 1740.80, 2033.60, 1852.80]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [152, 140, 162, 148]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2571
  Bounding Box: [1133.60, 1952.00, 1260.00, 2044.80]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [93, 157, 102, 163]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2568
  Bounding Box: [668.80, 0.00, 802.40, 52.80]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [57, 4, 66, 8]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2563
  Bounding Box: [1170.40, 23.30, 1328.80, 147.90]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [96, 6, 107, 15]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2561
  Bounding Box: [246.20, 1953.60, 320.20, 2030.40]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [24, 157, 29, 162]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2546
  Bounding Box: [0.00, 1964.80, 62.50, 2048.00]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [3, 158, 8, 164]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2546
  Bounding Box: [0.00, 1990.40, 62.50, 2048.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [3, 160, 8, 166]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2546
  Bounding Box: [3.60, 1990.40, 88.10, 2048.00]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [5, 160, 10, 166]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2542
  Bounding Box: [1612.80, 999.20, 1878.40, 1202.40]
  Mask Area: 192 pixels
  Mask Ratio: 0.0068
  Mask BBox: [130, 83, 150, 97]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2534
  Bounding Box: [157.80, 1498.40, 360.00, 1739.20]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [17, 122, 32, 139]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2517
  Bounding Box: [356.80, 32.70, 500.80, 131.50]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [32, 7, 43, 12]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2517
  Bounding Box: [1782.40, 217.60, 1942.40, 356.40]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [144, 21, 155, 31]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2510
  Bounding Box: [600.40, 301.80, 771.20, 471.20]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [53, 28, 64, 40]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2510
  Bounding Box: [302.60, 1045.60, 375.20, 1130.40]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [28, 86, 33, 92]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2496
  Bounding Box: [1795.20, 1302.40, 1907.20, 1412.80]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [146, 106, 152, 114]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2494
  Bounding Box: [1131.20, 943.20, 1260.80, 1013.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [93, 78, 102, 82]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2490
  Bounding Box: [685.60, 1583.20, 788.80, 1729.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [58, 128, 65, 139]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2489
  Bounding Box: [1825.60, 1793.60, 1905.60, 1947.20]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [147, 145, 152, 156]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2482
  Bounding Box: [386.80, 836.00, 451.60, 936.80]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [35, 70, 39, 76]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2478
  Bounding Box: [1096.80, 1715.20, 1288.80, 1929.60]
  Mask Area: 191 pixels
  Mask Ratio: 0.0068
  Mask BBox: [90, 138, 104, 154]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2474
  Bounding Box: [1514.40, 1596.80, 1572.00, 1763.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [123, 129, 125, 141]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2474
  Bounding Box: [639.60, 984.80, 808.00, 1175.20]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [54, 81, 67, 95]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2448
  Bounding Box: [940.00, 1912.00, 1076.00, 2048.00]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [78, 154, 88, 164]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2440
  Bounding Box: [849.60, 0.00, 920.00, 50.50]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [71, 4, 75, 7]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2440
  Bounding Box: [875.20, 0.00, 945.60, 50.50]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [73, 4, 77, 7]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2440
  Bounding Box: [849.60, 5.10, 920.00, 76.10]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [71, 5, 75, 9]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2437
  Bounding Box: [16.55, 0.00, 125.40, 81.70]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [6, 3, 13, 10]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2418
  Bounding Box: [1726.40, 1904.00, 1940.80, 2048.00]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [139, 153, 155, 164]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2415
  Bounding Box: [26.85, 1758.40, 142.00, 1953.60]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [7, 142, 15, 156]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2410
  Bounding Box: [1688.00, 685.20, 1774.40, 820.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [136, 58, 142, 68]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2394
  Bounding Box: [1217.60, 0.00, 1404.80, 137.80]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [100, 4, 113, 13]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2386
  Bounding Box: [796.00, 0.40, 903.20, 69.30]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [67, 5, 74, 9]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2386
  Bounding Box: [1768.00, 182.80, 1937.60, 303.20]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [143, 19, 155, 27]

