Image: tile_0051.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.9072
  Bounding Box: [864.80, 368.00, 1205.60, 772.80]
  Mask Area: 578 pixels
  Mask Ratio: 0.0205
  Mask BBox: [72, 33, 98, 64]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8682
  Bounding Box: [1077.60, 830.40, 1333.60, 1096.00]
  Mask Area: 314 pixels
  Mask Ratio: 0.0111
  Mask BBox: [89, 69, 108, 89]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8457
  Bounding Box: [0.00, 190.00, 334.60, 745.20]
  Mask Area: 927 pixels
  Mask Ratio: 0.0328
  Mask BBox: [3, 19, 30, 62]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8423
  Bounding Box: [1635.20, 0.00, 1811.20, 184.00]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [132, 4, 144, 18]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8403
  Bounding Box: [1492.00, 1865.60, 1752.00, 2048.00]
  Mask Area: 246 pixels
  Mask Ratio: 0.0087
  Mask BBox: [121, 150, 140, 165]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8340
  Bounding Box: [1296.80, 202.40, 1450.40, 377.60]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [106, 20, 117, 33]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8223
  Bounding Box: [1392.80, 1752.00, 1560.80, 1976.00]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [113, 142, 125, 158]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8213
  Bounding Box: [399.20, 1346.40, 601.60, 1576.80]
  Mask Area: 221 pixels
  Mask Ratio: 0.0078
  Mask BBox: [36, 110, 50, 127]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8188
  Bounding Box: [491.60, 312.80, 699.60, 502.40]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [43, 29, 58, 43]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8115
  Bounding Box: [482.40, 962.40, 658.40, 1218.40]
  Mask Area: 176 pixels
  Mask Ratio: 0.0062
  Mask BBox: [42, 80, 55, 99]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8101
  Bounding Box: [459.20, 162.00, 633.60, 299.20]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [40, 17, 52, 27]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8076
  Bounding Box: [441.20, 542.40, 660.40, 744.80]
  Mask Area: 190 pixels
  Mask Ratio: 0.0067
  Mask BBox: [39, 47, 55, 62]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.8062
  Bounding Box: [1649.60, 198.60, 1844.80, 530.40]
  Mask Area: 296 pixels
  Mask Ratio: 0.0105
  Mask BBox: [133, 21, 148, 45]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.8008
  Bounding Box: [1357.60, 10.10, 1576.80, 265.20]
  Mask Area: 219 pixels
  Mask Ratio: 0.0078
  Mask BBox: [111, 5, 125, 24]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7983
  Bounding Box: [1023.20, 1774.40, 1172.00, 1953.60]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [84, 143, 95, 155]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7939
  Bounding Box: [11.70, 8.25, 274.80, 193.80]
  Mask Area: 230 pixels
  Mask Ratio: 0.0081
  Mask BBox: [5, 5, 25, 19]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7842
  Bounding Box: [706.00, 871.20, 897.60, 965.60]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [60, 73, 70, 79]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7837
  Bounding Box: [907.20, 742.00, 1019.20, 883.20]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [75, 62, 83, 72]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7778
  Bounding Box: [1472.80, 1456.00, 1694.40, 1817.60]
  Mask Area: 332 pixels
  Mask Ratio: 0.0118
  Mask BBox: [120, 118, 135, 145]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7764
  Bounding Box: [231.20, 740.00, 452.00, 925.60]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [23, 62, 37, 76]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7764
  Bounding Box: [1624.00, 657.60, 1899.20, 920.00]
  Mask Area: 338 pixels
  Mask Ratio: 0.0120
  Mask BBox: [131, 56, 152, 75]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7617
  Bounding Box: [590.40, 1378.40, 753.60, 1575.20]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [51, 112, 62, 126]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7617
  Bounding Box: [65.50, 878.40, 311.20, 1099.20]
  Mask Area: 271 pixels
  Mask Ratio: 0.0096
  Mask BBox: [10, 73, 28, 89]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7612
  Bounding Box: [584.40, 1193.60, 734.80, 1390.40]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [50, 98, 60, 112]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7598
  Bounding Box: [296.20, 471.20, 482.40, 737.60]
  Mask Area: 214 pixels
  Mask Ratio: 0.0076
  Mask BBox: [28, 42, 41, 61]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7539
  Bounding Box: [544.00, 1894.40, 696.00, 2048.00]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [47, 152, 58, 164]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7476
  Bounding Box: [1883.20, 1092.80, 2036.80, 1332.80]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [152, 90, 163, 107]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7466
  Bounding Box: [1329.60, 900.00, 1478.40, 1109.60]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [108, 75, 118, 90]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7441
  Bounding Box: [1819.20, 1512.00, 1934.40, 1694.40]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [147, 123, 155, 136]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7427
  Bounding Box: [1833.60, 1721.60, 2025.60, 2016.00]
  Mask Area: 240 pixels
  Mask Ratio: 0.0085
  Mask BBox: [148, 139, 162, 159]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7422
  Bounding Box: [132.40, 761.20, 241.20, 894.40]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [15, 64, 22, 73]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7388
  Bounding Box: [508.00, 1673.60, 695.20, 1888.00]
  Mask Area: 192 pixels
  Mask Ratio: 0.0068
  Mask BBox: [44, 135, 58, 151]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7383
  Bounding Box: [372.00, 1611.20, 541.60, 1860.80]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [34, 130, 46, 149]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7363
  Bounding Box: [694.00, 1143.20, 916.00, 1351.20]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [59, 94, 74, 109]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7349
  Bounding Box: [524.80, 4.40, 637.60, 148.00]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [45, 5, 53, 14]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7339
  Bounding Box: [144.40, 1752.00, 297.20, 1915.20]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [16, 141, 27, 153]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7319
  Bounding Box: [952.00, 972.00, 1075.20, 1130.40]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [79, 80, 87, 92]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7314
  Bounding Box: [974.40, 789.60, 1099.20, 968.80]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [81, 66, 89, 78]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7300
  Bounding Box: [662.40, 430.40, 826.40, 648.00]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [56, 38, 66, 54]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7285
  Bounding Box: [412.80, 0.00, 543.20, 160.60]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [37, 4, 46, 16]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7251
  Bounding Box: [1838.40, 980.00, 2001.60, 1096.80]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [148, 81, 160, 88]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7236
  Bounding Box: [1700.80, 1801.60, 1892.80, 2003.20]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [137, 145, 151, 160]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7236
  Bounding Box: [1928.00, 1493.60, 2036.80, 1724.80]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [155, 121, 163, 137]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7231
  Bounding Box: [1272.80, 613.60, 1466.40, 828.80]
  Mask Area: 177 pixels
  Mask Ratio: 0.0063
  Mask BBox: [104, 52, 118, 68]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7183
  Bounding Box: [1188.00, 1375.20, 1424.80, 1859.20]
  Mask Area: 565 pixels
  Mask Ratio: 0.0200
  Mask BBox: [98, 112, 115, 149]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7148
  Bounding Box: [992.80, 1881.60, 1090.40, 2012.80]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [82, 151, 89, 161]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7124
  Bounding Box: [1506.40, 0.00, 1641.60, 93.60]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [122, 4, 132, 11]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7080
  Bounding Box: [823.20, 48.20, 967.20, 360.80]
  Mask Area: 181 pixels
  Mask Ratio: 0.0064
  Mask BBox: [69, 9, 79, 32]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7061
  Bounding Box: [594.80, 701.20, 769.60, 838.40]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [51, 59, 64, 69]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7012
  Bounding Box: [658.80, 119.00, 847.20, 281.80]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [56, 15, 70, 26]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.7002
  Bounding Box: [168.00, 698.00, 335.20, 811.20]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [18, 59, 30, 67]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.6987
  Bounding Box: [0.00, 1782.40, 138.80, 1977.60]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [4, 144, 14, 158]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.6909
  Bounding Box: [660.40, 261.60, 863.20, 446.80]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [56, 25, 71, 38]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.6890
  Bounding Box: [279.80, 912.00, 440.80, 1123.20]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [26, 76, 38, 91]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.6875
  Bounding Box: [1154.40, 1083.20, 1308.00, 1233.60]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [95, 89, 106, 100]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.6821
  Bounding Box: [1779.20, 543.60, 1932.80, 742.80]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [143, 47, 154, 59]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6792
  Bounding Box: [800.00, 1544.00, 1062.40, 2048.00]
  Mask Area: 562 pixels
  Mask Ratio: 0.0199
  Mask BBox: [67, 125, 86, 165]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6768
  Bounding Box: [838.40, 1209.60, 1187.20, 1590.40]
  Mask Area: 630 pixels
  Mask Ratio: 0.0223
  Mask BBox: [70, 99, 96, 128]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6758
  Bounding Box: [1908.80, 1352.80, 2020.80, 1468.00]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [154, 110, 161, 118]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6738
  Bounding Box: [1026.40, 1580.80, 1124.00, 1680.00]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [85, 128, 91, 134]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6626
  Bounding Box: [1355.20, 274.20, 1576.00, 454.00]
  Mask Area: 189 pixels
  Mask Ratio: 0.0067
  Mask BBox: [110, 26, 127, 39]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6621
  Bounding Box: [1160.80, 1291.20, 1268.00, 1425.60]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [95, 105, 102, 113]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6577
  Bounding Box: [676.40, 1844.80, 754.00, 1953.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [57, 149, 62, 156]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6567
  Bounding Box: [1932.80, 566.80, 2041.60, 914.40]
  Mask Area: 217 pixels
  Mask Ratio: 0.0077
  Mask BBox: [155, 49, 163, 75]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6543
  Bounding Box: [713.60, 942.40, 968.00, 1164.80]
  Mask Area: 242 pixels
  Mask Ratio: 0.0086
  Mask BBox: [60, 78, 79, 94]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6538
  Bounding Box: [14.85, 1628.80, 148.80, 1852.80]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [6, 132, 15, 148]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6450
  Bounding Box: [1314.40, 514.00, 1511.20, 660.40]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [107, 45, 122, 55]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6411
  Bounding Box: [1932.80, 484.40, 2032.00, 594.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [155, 42, 162, 50]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6411
  Bounding Box: [1820.80, 1051.20, 1897.60, 1156.80]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [147, 87, 151, 94]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6353
  Bounding Box: [1974.40, 1728.00, 2035.20, 1827.20]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [159, 139, 162, 146]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6343
  Bounding Box: [536.80, 762.80, 689.60, 942.40]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [46, 64, 57, 77]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6318
  Bounding Box: [1529.60, 543.20, 1683.20, 709.60]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [124, 47, 135, 59]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6304
  Bounding Box: [419.60, 1971.20, 557.20, 2035.20]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [37, 158, 47, 162]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6299
  Bounding Box: [1422.40, 1696.00, 1524.80, 1782.40]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [116, 137, 123, 142]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6279
  Bounding Box: [609.60, 940.00, 684.00, 1034.40]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [52, 78, 57, 84]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6245
  Bounding Box: [1329.60, 443.20, 1430.40, 525.60]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [108, 39, 115, 44]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6147
  Bounding Box: [298.00, 106.30, 450.80, 203.40]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [29, 13, 39, 19]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6138
  Bounding Box: [1931.20, 1351.20, 2048.00, 1477.60]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [155, 110, 164, 118]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6133
  Bounding Box: [728.80, 23.65, 856.80, 139.40]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [61, 6, 70, 14]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6094
  Bounding Box: [1966.40, 1723.20, 2048.00, 1812.80]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [158, 139, 164, 145]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6089
  Bounding Box: [1793.60, 6.80, 1889.60, 81.00]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [145, 5, 150, 10]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6074
  Bounding Box: [438.40, 827.20, 512.00, 1035.20]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [39, 69, 43, 84]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6011
  Bounding Box: [1411.20, 833.60, 1473.60, 937.60]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [115, 70, 119, 75]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.5962
  Bounding Box: [1843.20, 422.00, 1964.80, 526.80]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [148, 37, 157, 45]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.5815
  Bounding Box: [712.40, 1649.60, 804.00, 1822.40]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [60, 133, 66, 146]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.5796
  Bounding Box: [530.80, 1560.80, 662.00, 1678.40]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [46, 126, 54, 134]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.5776
  Bounding Box: [1102.40, 704.80, 1403.20, 920.80]
  Mask Area: 292 pixels
  Mask Ratio: 0.0103
  Mask BBox: [91, 60, 113, 75]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.5762
  Bounding Box: [830.40, 632.40, 937.60, 726.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [69, 54, 75, 60]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.5742
  Bounding Box: [503.60, 469.20, 602.80, 536.40]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [44, 41, 51, 45]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.5732
  Bounding Box: [580.40, 788.00, 713.20, 938.40]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [50, 66, 59, 77]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.5718
  Bounding Box: [620.00, 10.15, 761.60, 108.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [53, 5, 63, 12]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.5674
  Bounding Box: [0.00, 1082.40, 99.20, 1256.80]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [4, 89, 11, 102]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5625
  Bounding Box: [263.40, 720.80, 440.00, 1007.20]
  Mask Area: 246 pixels
  Mask Ratio: 0.0087
  Mask BBox: [25, 61, 38, 82]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5615
  Bounding Box: [0.00, 1315.20, 97.40, 1427.20]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [4, 107, 11, 115]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5591
  Bounding Box: [1147.20, 1907.20, 1334.40, 2035.20]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [94, 153, 108, 162]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5571
  Bounding Box: [1470.40, 878.40, 1619.20, 1212.80]
  Mask Area: 268 pixels
  Mask Ratio: 0.0095
  Mask BBox: [119, 73, 130, 98]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5566
  Bounding Box: [419.20, 1084.80, 609.60, 1411.20]
  Mask Area: 291 pixels
  Mask Ratio: 0.0103
  Mask BBox: [37, 89, 51, 114]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5562
  Bounding Box: [287.80, 39.05, 386.40, 117.80]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [27, 8, 33, 13]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5562
  Bounding Box: [627.20, 452.00, 814.40, 698.40]
  Mask Area: 181 pixels
  Mask Ratio: 0.0064
  Mask BBox: [53, 40, 67, 58]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5498
  Bounding Box: [388.80, 1856.00, 508.00, 2009.60]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [35, 150, 43, 160]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5488
  Bounding Box: [979.20, 0.00, 1278.40, 229.20]
  Mask Area: 374 pixels
  Mask Ratio: 0.0133
  Mask BBox: [81, 3, 103, 21]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5444
  Bounding Box: [1232.80, 1212.00, 1309.60, 1316.00]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [101, 99, 106, 106]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5415
  Bounding Box: [1625.60, 1398.40, 1724.80, 1528.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [131, 114, 138, 123]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5386
  Bounding Box: [1068.00, 1961.60, 1172.00, 2022.40]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [88, 158, 95, 161]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5366
  Bounding Box: [832.00, 705.60, 913.60, 849.60]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [69, 60, 75, 70]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5361
  Bounding Box: [782.40, 588.00, 865.60, 672.80]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [66, 50, 71, 56]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5337
  Bounding Box: [1860.80, 173.00, 2043.20, 302.20]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [150, 18, 163, 27]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5317
  Bounding Box: [1604.80, 889.60, 1835.20, 1324.80]
  Mask Area: 437 pixels
  Mask Ratio: 0.0155
  Mask BBox: [130, 74, 147, 107]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5303
  Bounding Box: [1854.40, 0.00, 2048.00, 108.80]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [149, 4, 164, 12]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5283
  Bounding Box: [91.80, 1910.40, 252.60, 2048.00]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [12, 155, 23, 164]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5254
  Bounding Box: [851.20, 848.80, 937.60, 936.80]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [71, 71, 77, 77]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5195
  Bounding Box: [710.00, 663.60, 856.00, 815.20]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [60, 56, 69, 67]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5186
  Bounding Box: [726.80, 1500.00, 866.40, 1648.00]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [61, 122, 71, 132]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5176
  Bounding Box: [0.20, 674.00, 84.50, 786.40]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [5, 57, 10, 65]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5171
  Bounding Box: [1149.60, 1236.00, 1228.00, 1308.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [94, 101, 99, 106]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5146
  Bounding Box: [1830.40, 74.90, 2022.40, 194.40]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [149, 10, 161, 19]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5132
  Bounding Box: [1864.00, 366.80, 2048.00, 489.20]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [150, 33, 165, 42]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5093
  Bounding Box: [764.40, 1338.40, 842.40, 1413.60]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [64, 109, 69, 113]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5020
  Bounding Box: [1552.80, 158.60, 1672.00, 328.60]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [126, 17, 134, 29]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.4990
  Bounding Box: [619.60, 540.00, 777.60, 712.80]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [53, 47, 64, 59]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.4976
  Bounding Box: [146.40, 706.80, 303.20, 832.00]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [16, 60, 27, 68]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.4966
  Bounding Box: [2.20, 991.20, 66.60, 1096.80]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [5, 82, 8, 89]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.4966
  Bounding Box: [651.20, 677.60, 868.00, 835.20]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [55, 57, 71, 69]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.4951
  Bounding Box: [1403.20, 823.20, 1496.00, 922.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [114, 69, 119, 76]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.4941
  Bounding Box: [54.60, 1918.40, 211.40, 2048.00]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [9, 154, 20, 164]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.4907
  Bounding Box: [1825.60, 888.80, 1908.80, 968.80]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [147, 74, 153, 79]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.4888
  Bounding Box: [738.80, 1694.40, 872.00, 1892.80]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [62, 137, 72, 151]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4873
  Bounding Box: [290.40, 49.35, 406.80, 146.20]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [27, 8, 35, 15]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.4858
  Bounding Box: [140.20, 1712.00, 226.60, 1792.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [15, 138, 21, 143]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4858
  Bounding Box: [515.20, 1185.60, 721.60, 1403.20]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [45, 97, 60, 113]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4844
  Bounding Box: [1399.20, 1398.40, 1519.20, 1619.20]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [114, 114, 122, 130]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4834
  Bounding Box: [1578.40, 427.60, 1715.20, 583.60]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [128, 38, 136, 49]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4795
  Bounding Box: [1851.20, 394.00, 2004.80, 513.20]
  Mask Area: 112 pixels
  Mask Ratio: 0.0040
  Mask BBox: [149, 35, 160, 44]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4790
  Bounding Box: [1078.40, 1027.20, 1195.20, 1121.60]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [89, 85, 97, 91]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4731
  Bounding Box: [1907.20, 807.20, 1990.40, 976.80]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [153, 68, 159, 78]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4722
  Bounding Box: [1093.60, 634.40, 1236.00, 753.60]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [90, 54, 99, 62]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4712
  Bounding Box: [1744.00, 1477.60, 1859.20, 1600.00]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [141, 120, 149, 128]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4673
  Bounding Box: [803.20, 462.40, 872.00, 608.80]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [67, 41, 71, 51]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4653
  Bounding Box: [1069.60, 1017.60, 1215.20, 1144.00]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [88, 84, 98, 93]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4619
  Bounding Box: [1162.40, 1888.00, 1303.20, 2019.20]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [95, 152, 105, 161]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4604
  Bounding Box: [661.20, 972.80, 781.60, 1097.60]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [56, 80, 65, 89]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4602
  Bounding Box: [1507.20, 568.00, 1664.00, 774.40]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [122, 49, 133, 64]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4548
  Bounding Box: [737.20, 1867.20, 835.20, 1940.80]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [62, 150, 69, 154]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4529
  Bounding Box: [1141.60, 1648.00, 1212.00, 1859.20]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [94, 133, 98, 149]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4500
  Bounding Box: [853.60, 292.00, 922.40, 395.20]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [71, 27, 76, 34]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4436
  Bounding Box: [846.40, 1300.00, 926.40, 1394.40]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [71, 106, 76, 112]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4436
  Bounding Box: [1295.20, 206.40, 1524.00, 429.60]
  Mask Area: 230 pixels
  Mask Ratio: 0.0081
  Mask BBox: [106, 21, 123, 37]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4426
  Bounding Box: [322.40, 370.80, 442.40, 503.60]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [30, 33, 38, 42]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4421
  Bounding Box: [736.80, 772.80, 845.60, 894.40]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [62, 65, 70, 73]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4402
  Bounding Box: [6.70, 1241.60, 94.30, 1339.20]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [5, 101, 11, 108]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4358
  Bounding Box: [1055.20, 1950.40, 1168.80, 2040.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [87, 157, 95, 162]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4331
  Bounding Box: [1257.60, 164.80, 1425.60, 377.60]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [103, 17, 115, 33]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4312
  Bounding Box: [1812.80, 1072.80, 1876.80, 1188.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [146, 88, 150, 96]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4297
  Bounding Box: [1359.20, 1875.20, 1447.20, 1977.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [111, 151, 117, 158]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4282
  Bounding Box: [1702.40, 1980.80, 1824.00, 2044.80]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [137, 159, 146, 163]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4253
  Bounding Box: [1784.00, 523.60, 1924.80, 670.80]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [144, 45, 154, 56]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4253
  Bounding Box: [1569.60, 146.60, 1691.20, 371.20]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [127, 16, 136, 32]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4207
  Bounding Box: [1150.40, 448.80, 1278.40, 610.40]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [94, 40, 103, 51]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4202
  Bounding Box: [1324.80, 1942.40, 1443.20, 2048.00]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [108, 156, 116, 163]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4121
  Bounding Box: [1750.40, 1726.40, 1836.80, 1828.80]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [141, 139, 147, 146]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4121
  Bounding Box: [1804.80, 1152.80, 1894.40, 1244.00]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [145, 95, 151, 101]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.4111
  Bounding Box: [0.70, 1493.60, 67.90, 1627.20]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [5, 121, 8, 131]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.4111
  Bounding Box: [407.60, 1942.40, 547.60, 2048.00]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [36, 156, 46, 163]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.4102
  Bounding Box: [1979.20, 299.20, 2033.60, 384.00]
  Mask Area: 21 pixels
  Mask Ratio: 0.0007
  Mask BBox: [159, 28, 162, 33]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.4082
  Bounding Box: [1136.80, 1280.80, 1248.80, 1402.40]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [94, 105, 101, 113]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.4004
  Bounding Box: [1920.00, 493.60, 2048.00, 614.40]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [154, 43, 164, 50]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.3999
  Bounding Box: [1809.60, 1526.40, 1908.80, 1724.80]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [146, 124, 153, 138]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.3994
  Bounding Box: [1577.60, 89.80, 1644.80, 153.00]
  Mask Area: 16 pixels
  Mask Ratio: 0.0006
  Mask BBox: [128, 12, 131, 15]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.3984
  Bounding Box: [718.80, 441.20, 880.00, 634.00]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [61, 39, 70, 53]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.3984
  Bounding Box: [1694.40, 572.80, 1800.00, 660.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [137, 49, 144, 55]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.3979
  Bounding Box: [602.80, 944.80, 726.00, 1068.00]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [52, 78, 60, 87]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3960
  Bounding Box: [1721.60, 1755.20, 2006.40, 2001.60]
  Mask Area: 294 pixels
  Mask Ratio: 0.0104
  Mask BBox: [139, 142, 160, 159]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3955
  Bounding Box: [1043.20, 109.60, 1278.40, 276.80]
  Mask Area: 181 pixels
  Mask Ratio: 0.0064
  Mask BBox: [86, 13, 103, 25]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3938
  Bounding Box: [894.40, 1100.00, 1025.60, 1220.00]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [74, 90, 84, 99]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3931
  Bounding Box: [543.20, 1880.00, 772.80, 2040.00]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [47, 151, 64, 163]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3918
  Bounding Box: [924.00, 1694.40, 1173.60, 1982.40]
  Mask Area: 277 pixels
  Mask Ratio: 0.0098
  Mask BBox: [77, 137, 95, 158]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3914
  Bounding Box: [816.00, 1632.00, 873.60, 1696.00]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [68, 132, 71, 136]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3904
  Bounding Box: [153.30, 1932.80, 294.40, 2048.00]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [16, 155, 26, 164]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3899
  Bounding Box: [63.80, 1089.60, 455.20, 1740.80]
  Mask Area: 1108 pixels
  Mask Ratio: 0.0393
  Mask BBox: [10, 90, 39, 139]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3896
  Bounding Box: [587.60, 5.15, 740.40, 131.00]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [50, 5, 61, 14]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3853
  Bounding Box: [1804.80, 1397.60, 1926.40, 1487.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [145, 114, 154, 120]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3843
  Bounding Box: [261.80, 173.20, 471.20, 392.40]
  Mask Area: 165 pixels
  Mask Ratio: 0.0058
  Mask BBox: [25, 18, 40, 34]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3831
  Bounding Box: [588.00, 1388.80, 828.00, 1600.00]
  Mask Area: 236 pixels
  Mask Ratio: 0.0084
  Mask BBox: [50, 113, 68, 128]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3826
  Bounding Box: [1556.80, 1817.60, 1652.80, 1881.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [126, 146, 133, 150]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3823
  Bounding Box: [543.20, 10.55, 704.80, 142.20]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [47, 5, 59, 15]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3816
  Bounding Box: [1539.20, 1120.80, 1715.20, 1365.60]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [125, 92, 137, 110]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3789
  Bounding Box: [666.00, 1588.80, 750.00, 1678.40]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [57, 129, 62, 135]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3787
  Bounding Box: [1088.00, 1035.20, 1276.80, 1188.80]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [89, 85, 103, 96]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3767
  Bounding Box: [910.40, 758.00, 1067.20, 917.60]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [76, 64, 87, 75]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3762
  Bounding Box: [1140.00, 1310.40, 1245.60, 1427.20]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [94, 107, 101, 115]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3738
  Bounding Box: [1497.60, 703.20, 1654.40, 813.60]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [121, 59, 132, 67]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3738
  Bounding Box: [195.80, 169.20, 283.40, 243.20]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [20, 18, 26, 22]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3730
  Bounding Box: [1825.60, 436.40, 1963.20, 600.40]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [147, 39, 157, 50]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3730
  Bounding Box: [5.75, 1795.20, 183.80, 2019.20]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [5, 145, 18, 161]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3689
  Bounding Box: [710.40, 1.50, 854.40, 117.90]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [60, 5, 70, 13]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3674
  Bounding Box: [679.60, 103.00, 963.20, 306.20]
  Mask Area: 247 pixels
  Mask Ratio: 0.0088
  Mask BBox: [58, 13, 79, 27]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3618
  Bounding Box: [1293.60, 525.20, 1504.80, 737.20]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [106, 46, 121, 61]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3611
  Bounding Box: [271.40, 44.30, 376.40, 140.30]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [26, 8, 33, 14]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3577
  Bounding Box: [518.80, 1001.60, 682.80, 1225.60]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [45, 83, 57, 99]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3567
  Bounding Box: [1864.00, 1496.80, 2027.20, 1712.00]
  Mask Area: 189 pixels
  Mask Ratio: 0.0067
  Mask BBox: [150, 121, 162, 137]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3562
  Bounding Box: [1492.80, 1404.00, 1580.80, 1503.20]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [121, 114, 127, 121]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3562
  Bounding Box: [1814.40, 1180.80, 1942.40, 1336.00]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [146, 97, 155, 108]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3530
  Bounding Box: [4.90, 1020.00, 79.90, 1127.20]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [5, 84, 10, 92]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3525
  Bounding Box: [311.40, 402.40, 420.00, 516.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [29, 36, 36, 43]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3518
  Bounding Box: [650.40, 1084.80, 746.40, 1158.40]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [55, 89, 62, 94]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3501
  Bounding Box: [427.20, 1979.20, 580.80, 2048.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [38, 159, 47, 164]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3501
  Bounding Box: [1413.60, 1681.60, 1532.00, 1832.00]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [115, 136, 123, 147]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3472
  Bounding Box: [390.00, 1880.00, 554.00, 2043.20]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [35, 151, 47, 163]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3469
  Bounding Box: [1116.80, 1510.40, 1220.80, 1648.00]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [92, 122, 98, 132]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3435
  Bounding Box: [320.80, 1836.80, 516.80, 2019.20]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [30, 148, 44, 161]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3423
  Bounding Box: [852.80, 677.20, 988.80, 861.60]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [71, 57, 81, 71]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3403
  Bounding Box: [1445.60, 1240.80, 1556.00, 1396.00]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [117, 101, 125, 113]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3372
  Bounding Box: [497.20, 736.80, 581.20, 852.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [43, 62, 49, 68]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3367
  Bounding Box: [1563.20, 77.00, 1633.60, 149.40]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [127, 11, 131, 15]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3367
  Bounding Box: [1588.80, 77.00, 1659.20, 149.40]
  Mask Area: 15 pixels
  Mask Ratio: 0.0005
  Mask BBox: [129, 11, 131, 15]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3367
  Bounding Box: [1563.20, 102.60, 1633.60, 175.00]
  Mask Area: 19 pixels
  Mask Ratio: 0.0007
  Mask BBox: [127, 13, 131, 17]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3367
  Bounding Box: [1588.80, 102.60, 1659.20, 175.00]
  Mask Area: 10 pixels
  Mask Ratio: 0.0004
  Mask BBox: [129, 13, 131, 16]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3367
  Bounding Box: [1396.80, 1148.00, 1473.60, 1250.40]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [114, 94, 119, 101]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3354
  Bounding Box: [270.80, 10.00, 376.00, 121.60]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [26, 5, 33, 13]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3333
  Bounding Box: [799.20, 1616.00, 861.60, 1689.60]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [67, 131, 71, 135]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3333
  Bounding Box: [824.80, 1616.00, 887.20, 1689.60]
  Mask Area: 15 pixels
  Mask Ratio: 0.0005
  Mask BBox: [69, 131, 71, 135]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3333
  Bounding Box: [799.20, 1641.60, 861.60, 1715.20]
  Mask Area: 19 pixels
  Mask Ratio: 0.0007
  Mask BBox: [67, 133, 71, 136]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3333
  Bounding Box: [824.80, 1641.60, 887.20, 1715.20]
  Mask Area: 11 pixels
  Mask Ratio: 0.0004
  Mask BBox: [69, 133, 71, 136]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3333
  Bounding Box: [901.60, 1204.80, 1157.60, 1424.00]
  Mask Area: 284 pixels
  Mask Ratio: 0.0101
  Mask BBox: [75, 99, 93, 115]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3328
  Bounding Box: [1294.40, 118.40, 1392.00, 226.00]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [106, 14, 112, 21]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3325
  Bounding Box: [965.60, 1988.80, 1040.80, 2048.00]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [80, 160, 85, 163]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3320
  Bounding Box: [1880.00, 1363.20, 2048.00, 1483.20]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [151, 111, 164, 119]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3311
  Bounding Box: [1290.40, 914.40, 1354.40, 1005.60]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [105, 76, 109, 82]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3306
  Bounding Box: [218.80, 1739.20, 328.40, 1860.80]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [22, 140, 29, 149]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [166.00, 1961.60, 283.20, 2048.00]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [17, 158, 26, 163]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3298
  Bounding Box: [689.20, 1913.60, 790.40, 2012.80]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [58, 154, 65, 161]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3293
  Bounding Box: [21.00, 1916.80, 150.20, 2041.60]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [6, 154, 15, 163]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3276
  Bounding Box: [244.00, 1744.00, 337.20, 1836.80]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [24, 141, 30, 145]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3271
  Bounding Box: [2.25, 110.80, 64.60, 240.00]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [5, 13, 8, 22]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [159.40, 1720.00, 326.20, 1899.20]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [17, 142, 29, 152]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [716.00, 1939.20, 876.00, 2041.60]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [60, 156, 72, 163]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3223
  Bounding Box: [1027.20, 1555.20, 1156.80, 1673.60]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [85, 126, 94, 134]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3218
  Bounding Box: [494.80, 358.40, 685.20, 544.80]
  Mask Area: 198 pixels
  Mask Ratio: 0.0070
  Mask BBox: [43, 32, 57, 46]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.3215
  Bounding Box: [1050.40, 0.00, 1328.80, 242.80]
  Mask Area: 374 pixels
  Mask Ratio: 0.0133
  Mask BBox: [87, 4, 107, 22]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.3201
  Bounding Box: [999.20, 799.20, 1114.40, 1004.00]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [83, 67, 89, 82]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.3201
  Bounding Box: [1269.60, 11.05, 1349.60, 119.80]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [104, 5, 109, 13]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.3188
  Bounding Box: [628.00, 1153.60, 889.60, 1376.00]
  Mask Area: 242 pixels
  Mask Ratio: 0.0086
  Mask BBox: [54, 95, 73, 111]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.3186
  Bounding Box: [681.60, 892.80, 872.00, 1008.00]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [58, 74, 72, 82]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.3186
  Bounding Box: [752.00, 10.95, 894.40, 134.40]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [63, 5, 73, 14]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.3167
  Bounding Box: [1476.00, 406.40, 1572.00, 532.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [120, 36, 126, 45]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.3127
  Bounding Box: [1912.00, 1979.20, 2036.80, 2043.20]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [154, 159, 163, 163]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.3115
  Bounding Box: [0.00, 1689.60, 124.20, 1875.20]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [4, 136, 13, 150]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.3096
  Bounding Box: [1905.60, 199.60, 2040.00, 315.60]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [153, 20, 163, 28]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.3086
  Bounding Box: [0.00, 826.40, 61.30, 956.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [4, 69, 8, 78]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.3079
  Bounding Box: [697.60, 1654.40, 837.60, 1849.60]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [59, 134, 69, 148]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [1248.80, 358.20, 1362.40, 513.20]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [102, 32, 110, 44]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [1944.00, 296.40, 2048.00, 385.20]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [158, 28, 164, 34]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [130.70, 718.00, 292.40, 886.40]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [15, 61, 26, 73]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.3054
  Bounding Box: [1259.20, 100.50, 1406.40, 230.00]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [103, 12, 112, 21]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.3052
  Bounding Box: [291.20, 97.60, 468.00, 295.20]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [27, 12, 40, 27]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.3049
  Bounding Box: [493.60, 236.20, 660.80, 468.80]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [43, 23, 55, 40]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.3042
  Bounding Box: [1092.80, 263.60, 1286.40, 377.60]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [90, 25, 104, 33]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.3042
  Bounding Box: [1148.00, 1654.40, 1221.60, 1798.40]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [94, 134, 98, 144]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.3032
  Bounding Box: [982.40, 1910.40, 1084.80, 2041.60]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [81, 154, 88, 163]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.3032
  Bounding Box: [1008.00, 1910.40, 1110.40, 2041.60]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [83, 154, 90, 163]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.3025
  Bounding Box: [732.80, 1424.00, 864.00, 1609.60]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [62, 116, 71, 129]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [729.60, 734.40, 851.20, 878.40]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [61, 62, 70, 72]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2998
  Bounding Box: [0.00, 989.60, 52.20, 1088.80]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [3, 82, 8, 89]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2998
  Bounding Box: [0.00, 1015.20, 52.20, 1114.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [3, 84, 8, 91]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2981
  Bounding Box: [1726.40, 1700.80, 1832.00, 1819.20]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [139, 137, 147, 146]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2979
  Bounding Box: [1798.40, 1176.80, 1964.80, 1413.60]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [145, 96, 157, 114]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2971
  Bounding Box: [1772.80, 1477.60, 1926.40, 1667.20]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [143, 120, 154, 134]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2959
  Bounding Box: [582.80, 960.00, 675.60, 1060.80]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [51, 79, 56, 86]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2925
  Bounding Box: [0.60, 143.20, 69.90, 268.00]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [5, 16, 8, 23]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2925
  Bounding Box: [1003.20, 803.20, 1308.80, 1064.00]
  Mask Area: 374 pixels
  Mask Ratio: 0.0133
  Mask BBox: [83, 67, 106, 87]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2917
  Bounding Box: [612.00, 957.60, 700.00, 1058.40]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [52, 79, 58, 86]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2903
  Bounding Box: [1667.20, 261.00, 1932.80, 542.40]
  Mask Area: 369 pixels
  Mask Ratio: 0.0131
  Mask BBox: [135, 25, 154, 46]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2886
  Bounding Box: [14.30, 1145.60, 407.60, 1577.60]
  Mask Area: 718 pixels
  Mask Ratio: 0.0254
  Mask BBox: [10, 94, 35, 127]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2886
  Bounding Box: [944.80, 742.00, 1088.80, 948.80]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [78, 62, 89, 78]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2874
  Bounding Box: [626.80, 1090.40, 737.20, 1189.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [53, 90, 61, 96]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2874
  Bounding Box: [1168.00, 1950.40, 1363.20, 2033.60]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [96, 157, 110, 162]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2874
  Bounding Box: [1544.80, 1144.00, 1681.60, 1312.00]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [125, 94, 135, 106]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2869
  Bounding Box: [1032.00, 1576.00, 1144.00, 1710.40]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [85, 128, 93, 137]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2864
  Bounding Box: [1753.60, 1182.40, 1984.00, 1480.00]
  Mask Area: 320 pixels
  Mask Ratio: 0.0113
  Mask BBox: [141, 97, 158, 119]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2859
  Bounding Box: [26.85, 784.00, 129.20, 880.00]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [7, 66, 14, 72]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2849
  Bounding Box: [674.00, 396.80, 746.80, 478.40]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [57, 35, 62, 41]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2847
  Bounding Box: [639.20, 87.80, 830.40, 256.20]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [56, 12, 68, 24]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2837
  Bounding Box: [1182.40, 1520.00, 1419.20, 1971.20]
  Mask Area: 511 pixels
  Mask Ratio: 0.0181
  Mask BBox: [97, 123, 114, 157]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2822
  Bounding Box: [405.20, 1990.40, 538.00, 2048.00]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [36, 160, 46, 166]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2815
  Bounding Box: [950.40, 1977.60, 1022.40, 2041.60]
  Mask Area: 21 pixels
  Mask Ratio: 0.0007
  Mask BBox: [79, 159, 83, 163]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2815
  Bounding Box: [976.00, 1977.60, 1048.00, 2041.60]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [81, 159, 85, 163]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2815
  Bounding Box: [950.40, 2003.20, 1022.40, 2048.00]
  Mask Area: 15 pixels
  Mask Ratio: 0.0005
  Mask BBox: [79, 161, 83, 164]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2815
  Bounding Box: [976.00, 2003.20, 1048.00, 2048.00]
  Mask Area: 15 pixels
  Mask Ratio: 0.0005
  Mask BBox: [81, 161, 85, 164]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2810
  Bounding Box: [1905.60, 472.80, 2020.80, 583.20]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [153, 41, 161, 49]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2798
  Bounding Box: [1026.40, 260.20, 1292.00, 403.60]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [85, 25, 104, 35]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2791
  Bounding Box: [139.20, 676.00, 312.00, 796.80]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [15, 57, 28, 66]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2788
  Bounding Box: [1729.60, 1692.80, 1822.40, 1776.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [140, 137, 146, 142]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2786
  Bounding Box: [1995.20, 1692.80, 2048.00, 1820.80]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [160, 137, 163, 146]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2778
  Bounding Box: [116.60, 794.40, 226.60, 920.80]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [15, 67, 21, 73]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2771
  Bounding Box: [617.60, 1353.60, 777.60, 1552.00]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [53, 110, 64, 125]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2771
  Bounding Box: [6.70, 874.40, 63.60, 992.80]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [5, 73, 8, 81]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2766
  Bounding Box: [1836.80, 1032.00, 1920.00, 1145.60]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [148, 85, 151, 93]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2764
  Bounding Box: [143.70, 785.60, 249.60, 921.60]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [16, 66, 22, 73]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2756
  Bounding Box: [1480.80, 0.00, 1675.20, 105.80]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [120, 3, 134, 12]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2751
  Bounding Box: [298.60, 72.60, 446.80, 175.20]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [28, 10, 38, 17]

