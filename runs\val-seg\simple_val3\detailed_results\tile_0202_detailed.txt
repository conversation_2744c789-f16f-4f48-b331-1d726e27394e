Image: tile_0202.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8564
  Bounding Box: [802.40, 1827.20, 999.20, 2048.00]
  Mask Area: 246 pixels
  Mask Ratio: 0.0087
  Mask BBox: [67, 147, 82, 165]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8389
  Bounding Box: [1436.80, 476.80, 1747.20, 787.20]
  Mask Area: 423 pixels
  Mask Ratio: 0.0150
  Mask BBox: [117, 42, 140, 65]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8223
  Bounding Box: [57.80, 1865.60, 229.00, 2048.00]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [9, 150, 21, 164]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8198
  Bounding Box: [760.40, 1532.80, 895.20, 1686.40]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [64, 125, 73, 135]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8188
  Bounding Box: [902.40, 0.00, 1016.00, 142.00]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [75, 4, 83, 13]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8140
  Bounding Box: [410.80, 380.40, 659.60, 605.20]
  Mask Area: 251 pixels
  Mask Ratio: 0.0089
  Mask BBox: [37, 34, 55, 51]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8120
  Bounding Box: [966.40, 1732.80, 1171.20, 2040.00]
  Mask Area: 275 pixels
  Mask Ratio: 0.0097
  Mask BBox: [80, 140, 95, 163]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8115
  Bounding Box: [612.80, 1715.20, 844.80, 2025.60]
  Mask Area: 298 pixels
  Mask Ratio: 0.0106
  Mask BBox: [52, 139, 69, 162]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8003
  Bounding Box: [1319.20, 726.00, 1503.20, 1192.00]
  Mask Area: 439 pixels
  Mask Ratio: 0.0156
  Mask BBox: [108, 61, 121, 97]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.7979
  Bounding Box: [524.80, 958.40, 784.80, 1208.00]
  Mask Area: 275 pixels
  Mask Ratio: 0.0097
  Mask BBox: [45, 79, 65, 98]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.7979
  Bounding Box: [465.60, 83.60, 713.60, 299.20]
  Mask Area: 203 pixels
  Mask Ratio: 0.0072
  Mask BBox: [41, 11, 59, 25]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.7935
  Bounding Box: [645.60, 319.20, 970.40, 632.00]
  Mask Area: 467 pixels
  Mask Ratio: 0.0165
  Mask BBox: [55, 29, 79, 53]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.7920
  Bounding Box: [766.80, 617.20, 889.60, 807.20]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [64, 53, 73, 66]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.7896
  Bounding Box: [1313.60, 26.95, 1548.80, 217.20]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [107, 7, 124, 20]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7876
  Bounding Box: [1486.40, 1715.20, 1673.60, 2048.00]
  Mask Area: 336 pixels
  Mask Ratio: 0.0119
  Mask BBox: [121, 138, 134, 164]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7837
  Bounding Box: [862.40, 162.80, 1040.00, 421.20]
  Mask Area: 171 pixels
  Mask Ratio: 0.0061
  Mask BBox: [72, 17, 85, 36]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7788
  Bounding Box: [1763.20, 1260.00, 1881.60, 1418.40]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [142, 103, 150, 113]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7783
  Bounding Box: [554.40, 1185.60, 761.60, 1382.40]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [49, 97, 62, 110]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7744
  Bounding Box: [643.20, 599.20, 789.60, 766.40]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [55, 51, 65, 63]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7715
  Bounding Box: [1036.00, 180.40, 1224.80, 424.40]
  Mask Area: 187 pixels
  Mask Ratio: 0.0066
  Mask BBox: [85, 19, 99, 37]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7710
  Bounding Box: [105.70, 0.00, 278.40, 223.60]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [13, 4, 25, 19]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7681
  Bounding Box: [0.00, 1160.80, 162.40, 1372.00]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [4, 95, 16, 110]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7642
  Bounding Box: [1848.00, 1376.80, 2043.20, 1603.20]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [149, 112, 163, 129]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7622
  Bounding Box: [1913.60, 1766.40, 2048.00, 1993.60]
  Mask Area: 177 pixels
  Mask Ratio: 0.0063
  Mask BBox: [154, 142, 165, 159]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7612
  Bounding Box: [1104.00, 0.00, 1252.80, 152.70]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [91, 4, 101, 15]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7612
  Bounding Box: [1292.00, 1296.80, 1541.60, 1546.40]
  Mask Area: 273 pixels
  Mask Ratio: 0.0097
  Mask BBox: [105, 106, 122, 124]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7607
  Bounding Box: [682.40, 825.60, 941.60, 1068.80]
  Mask Area: 269 pixels
  Mask Ratio: 0.0095
  Mask BBox: [58, 69, 76, 87]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7598
  Bounding Box: [1705.60, 1852.80, 1891.20, 2048.00]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [138, 149, 151, 164]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7529
  Bounding Box: [447.20, 1715.20, 618.40, 1942.40]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [39, 139, 52, 155]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7485
  Bounding Box: [261.20, 59.20, 466.80, 313.60]
  Mask Area: 210 pixels
  Mask Ratio: 0.0074
  Mask BBox: [25, 9, 40, 28]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7480
  Bounding Box: [1550.40, 1412.80, 1700.80, 1620.80]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [126, 115, 136, 130]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7451
  Bounding Box: [1702.40, 1116.80, 1820.80, 1312.00]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [137, 92, 146, 106]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7441
  Bounding Box: [1731.20, 41.00, 1948.80, 255.20]
  Mask Area: 189 pixels
  Mask Ratio: 0.0067
  Mask BBox: [140, 8, 156, 23]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7432
  Bounding Box: [932.80, 975.20, 1083.20, 1109.60]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [77, 81, 88, 90]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7422
  Bounding Box: [0.00, 1657.60, 129.60, 1856.00]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [4, 134, 14, 148]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7397
  Bounding Box: [1218.40, 1625.60, 1378.40, 1804.80]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [100, 132, 109, 144]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7373
  Bounding Box: [32.10, 1076.00, 197.60, 1216.80]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [7, 89, 18, 99]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7358
  Bounding Box: [1165.60, 1798.40, 1434.40, 2028.80]
  Mask Area: 258 pixels
  Mask Ratio: 0.0091
  Mask BBox: [96, 145, 116, 162]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7285
  Bounding Box: [693.20, 5.40, 882.40, 102.20]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [59, 5, 71, 11]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7227
  Bounding Box: [1588.80, 324.60, 1758.40, 508.00]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [129, 30, 141, 43]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7222
  Bounding Box: [476.80, 737.60, 672.00, 987.20]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [42, 62, 56, 81]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7212
  Bounding Box: [1366.40, 1546.40, 1558.40, 1672.00]
  Mask Area: 112 pixels
  Mask Ratio: 0.0040
  Mask BBox: [111, 125, 125, 134]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7183
  Bounding Box: [1047.20, 770.40, 1223.20, 892.00]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [86, 65, 98, 73]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7173
  Bounding Box: [173.40, 1216.80, 308.20, 1405.60]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [18, 100, 28, 113]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7148
  Bounding Box: [1659.20, 1707.20, 1777.60, 1832.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [134, 138, 141, 146]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7139
  Bounding Box: [215.40, 1947.20, 344.60, 2030.40]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [21, 157, 29, 162]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7114
  Bounding Box: [1515.20, 56.30, 1643.20, 330.40]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [123, 9, 132, 29]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7085
  Bounding Box: [497.60, 1652.80, 702.40, 1780.80]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [43, 134, 58, 142]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7085
  Bounding Box: [174.40, 240.40, 360.00, 511.60]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [18, 23, 32, 43]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.6982
  Bounding Box: [10.10, 1412.80, 276.80, 1630.40]
  Mask Area: 227 pixels
  Mask Ratio: 0.0080
  Mask BBox: [5, 115, 25, 129]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.6982
  Bounding Box: [1697.60, 698.40, 1912.00, 964.00]
  Mask Area: 215 pixels
  Mask Ratio: 0.0076
  Mask BBox: [137, 59, 153, 79]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.6982
  Bounding Box: [1493.60, 1044.80, 1710.40, 1440.00]
  Mask Area: 372 pixels
  Mask Ratio: 0.0132
  Mask BBox: [121, 86, 137, 116]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.6938
  Bounding Box: [3.50, 9.00, 119.30, 234.00]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [5, 5, 12, 22]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.6924
  Bounding Box: [876.80, 1534.40, 977.60, 1659.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [73, 124, 79, 133]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.6890
  Bounding Box: [1366.40, 1676.80, 1480.00, 1814.40]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [111, 135, 119, 145]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.6880
  Bounding Box: [591.60, 0.00, 690.00, 88.20]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [51, 4, 57, 10]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6831
  Bounding Box: [295.00, 1269.60, 474.00, 1514.40]
  Mask Area: 221 pixels
  Mask Ratio: 0.0078
  Mask BBox: [28, 104, 41, 122]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6826
  Bounding Box: [1538.40, 917.60, 1763.20, 1228.00]
  Mask Area: 291 pixels
  Mask Ratio: 0.0103
  Mask BBox: [125, 76, 141, 99]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6812
  Bounding Box: [336.00, 4.20, 448.80, 102.20]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [31, 5, 39, 11]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6807
  Bounding Box: [1277.60, 1132.80, 1367.20, 1299.20]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [104, 93, 110, 105]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6758
  Bounding Box: [0.00, 355.20, 290.00, 840.80]
  Mask Area: 688 pixels
  Mask Ratio: 0.0244
  Mask BBox: [4, 32, 26, 69]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6738
  Bounding Box: [1258.40, 65.20, 1503.20, 496.00]
  Mask Area: 438 pixels
  Mask Ratio: 0.0155
  Mask BBox: [103, 10, 121, 42]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6729
  Bounding Box: [1880.00, 6.25, 2036.80, 188.60]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [151, 5, 163, 18]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6719
  Bounding Box: [726.40, 179.60, 908.80, 360.40]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [61, 19, 74, 31]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6685
  Bounding Box: [499.20, 1526.40, 660.80, 1676.80]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [43, 124, 54, 134]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6650
  Bounding Box: [2.55, 933.60, 111.20, 1130.40]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [5, 77, 12, 92]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6650
  Bounding Box: [1548.80, 758.40, 1724.80, 913.60]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [125, 64, 138, 75]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6641
  Bounding Box: [920.00, 1375.20, 1118.40, 1684.80]
  Mask Area: 293 pixels
  Mask Ratio: 0.0104
  Mask BBox: [76, 112, 91, 135]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6631
  Bounding Box: [1186.40, 736.40, 1346.40, 1049.60]
  Mask Area: 190 pixels
  Mask Ratio: 0.0067
  Mask BBox: [97, 62, 109, 85]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6567
  Bounding Box: [934.40, 392.80, 1214.40, 778.40]
  Mask Area: 390 pixels
  Mask Ratio: 0.0138
  Mask BBox: [77, 35, 98, 62]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6562
  Bounding Box: [1865.60, 852.80, 2038.40, 1326.40]
  Mask Area: 364 pixels
  Mask Ratio: 0.0129
  Mask BBox: [150, 71, 163, 106]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6543
  Bounding Box: [1193.60, 185.00, 1262.40, 309.00]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [98, 19, 102, 28]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6543
  Bounding Box: [659.60, 795.20, 753.20, 875.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [56, 67, 62, 72]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6519
  Bounding Box: [1971.20, 6.65, 2048.00, 146.00]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [158, 5, 164, 15]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6440
  Bounding Box: [1201.60, 498.00, 1382.40, 769.60]
  Mask Area: 186 pixels
  Mask Ratio: 0.0066
  Mask BBox: [98, 43, 111, 64]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6426
  Bounding Box: [16.80, 1562.40, 170.40, 1712.00]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [6, 127, 16, 137]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6392
  Bounding Box: [1628.80, 85.20, 1728.00, 233.20]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [132, 11, 138, 22]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6377
  Bounding Box: [1923.20, 1520.00, 2016.00, 1766.40]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [155, 123, 161, 141]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6362
  Bounding Box: [449.20, 1113.60, 574.80, 1369.60]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [40, 91, 47, 110]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6348
  Bounding Box: [1873.60, 1937.60, 1947.20, 2030.40]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [151, 156, 156, 162]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6318
  Bounding Box: [1923.20, 342.80, 2048.00, 641.20]
  Mask Area: 187 pixels
  Mask Ratio: 0.0066
  Mask BBox: [155, 31, 164, 54]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6318
  Bounding Box: [796.80, 1387.20, 907.20, 1542.40]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [67, 113, 74, 123]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6294
  Bounding Box: [1260.00, 426.00, 1404.00, 582.80]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [103, 38, 112, 48]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6284
  Bounding Box: [1375.20, 1185.60, 1504.80, 1313.60]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [112, 97, 120, 106]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6274
  Bounding Box: [1833.60, 1611.20, 1974.40, 1848.00]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [148, 130, 158, 148]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6265
  Bounding Box: [606.00, 1928.00, 706.00, 2048.00]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [52, 155, 59, 163]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.6230
  Bounding Box: [1540.00, 1654.40, 1691.20, 1769.60]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [125, 134, 136, 142]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.6216
  Bounding Box: [502.00, 263.20, 645.20, 399.20]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [44, 25, 54, 35]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.6182
  Bounding Box: [1732.80, 1408.00, 1873.60, 1641.60]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [140, 114, 150, 132]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.6172
  Bounding Box: [422.40, 1934.40, 598.40, 2027.20]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [37, 156, 50, 162]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.6167
  Bounding Box: [1683.20, 1520.00, 1779.20, 1702.40]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [136, 123, 142, 136]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.6157
  Bounding Box: [598.80, 888.00, 690.00, 985.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [51, 74, 57, 79]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.6152
  Bounding Box: [370.80, 263.20, 502.00, 423.20]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [33, 25, 43, 37]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.6143
  Bounding Box: [1936.00, 1538.40, 2048.00, 1769.60]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [156, 125, 163, 142]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.6123
  Bounding Box: [744.40, 1284.80, 843.20, 1372.80]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [63, 105, 69, 111]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.6001
  Bounding Box: [896.80, 709.20, 1084.00, 852.80]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [75, 60, 88, 70]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5952
  Bounding Box: [26.30, 824.00, 124.70, 940.80]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [7, 69, 13, 77]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5942
  Bounding Box: [1383.20, 1689.60, 1495.20, 1843.20]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [113, 136, 119, 147]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5864
  Bounding Box: [1030.40, 1.50, 1116.80, 83.40]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [85, 5, 91, 10]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5854
  Bounding Box: [712.40, 1172.00, 788.80, 1272.80]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [60, 96, 65, 102]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5767
  Bounding Box: [1763.20, 390.00, 1888.00, 482.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [142, 35, 151, 41]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5752
  Bounding Box: [432.40, 1952.00, 614.80, 2048.00]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [38, 157, 52, 163]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5679
  Bounding Box: [173.40, 1035.20, 324.60, 1235.20]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [18, 85, 29, 100]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5601
  Bounding Box: [1127.20, 1308.00, 1332.00, 1592.00]
  Mask Area: 247 pixels
  Mask Ratio: 0.0088
  Mask BBox: [93, 107, 108, 127]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5566
  Bounding Box: [1323.20, 1507.20, 1401.60, 1593.60]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [108, 122, 113, 128]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5566
  Bounding Box: [1072.00, 909.60, 1216.00, 1108.00]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [88, 76, 98, 90]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5474
  Bounding Box: [116.60, 872.80, 281.80, 1024.80]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [14, 73, 25, 84]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5469
  Bounding Box: [1056.00, 1100.80, 1313.60, 1302.40]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [87, 90, 106, 105]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5356
  Bounding Box: [186.00, 1633.60, 459.20, 2027.20]
  Mask Area: 460 pixels
  Mask Ratio: 0.0163
  Mask BBox: [19, 132, 39, 162]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5332
  Bounding Box: [670.80, 185.20, 782.40, 318.80]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [57, 19, 65, 28]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5283
  Bounding Box: [1415.20, 201.00, 1527.20, 324.60]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [115, 20, 123, 29]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5259
  Bounding Box: [1702.40, 620.80, 1782.40, 739.20]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [137, 53, 142, 61]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5244
  Bounding Box: [0.00, 722.80, 71.30, 859.20]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [4, 61, 9, 71]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5229
  Bounding Box: [118.60, 807.20, 240.20, 925.60]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [14, 68, 22, 76]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5176
  Bounding Box: [1806.40, 990.40, 1908.80, 1104.00]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [146, 82, 152, 90]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5166
  Bounding Box: [203.20, 1383.20, 289.20, 1456.80]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [20, 113, 26, 117]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5117
  Bounding Box: [1377.60, 1966.40, 1516.80, 2040.00]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [112, 158, 121, 163]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5107
  Bounding Box: [1870.40, 1302.40, 2030.40, 1400.00]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [151, 106, 162, 111]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5088
  Bounding Box: [1227.20, 1560.00, 1310.40, 1636.80]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [100, 126, 106, 131]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.5083
  Bounding Box: [944.00, 840.00, 1144.00, 1043.20]
  Mask Area: 159 pixels
  Mask Ratio: 0.0056
  Mask BBox: [78, 70, 93, 85]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.5073
  Bounding Box: [1583.20, 2.50, 1752.00, 102.90]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [128, 5, 140, 12]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.5073
  Bounding Box: [772.00, 1150.40, 893.60, 1268.80]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [65, 94, 73, 103]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.5054
  Bounding Box: [450.80, 1675.20, 666.80, 1908.80]
  Mask Area: 219 pixels
  Mask Ratio: 0.0078
  Mask BBox: [40, 135, 56, 153]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.5034
  Bounding Box: [2.85, 232.00, 143.20, 450.40]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [5, 23, 14, 38]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.5024
  Bounding Box: [820.00, 1258.40, 952.80, 1434.40]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [69, 103, 78, 116]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.4990
  Bounding Box: [0.50, 1347.20, 100.70, 1427.20]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [5, 110, 11, 115]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.4839
  Bounding Box: [1784.00, 563.20, 1966.40, 806.40]
  Mask Area: 177 pixels
  Mask Ratio: 0.0063
  Mask BBox: [144, 48, 157, 66]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4731
  Bounding Box: [157.20, 1221.60, 362.80, 1431.20]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [17, 100, 32, 115]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.4727
  Bounding Box: [780.80, 1196.80, 896.00, 1286.40]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [65, 98, 73, 104]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4609
  Bounding Box: [0.00, 1878.40, 73.80, 2035.20]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [4, 151, 9, 162]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4556
  Bounding Box: [1008.80, 78.00, 1119.20, 240.40]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [83, 11, 90, 22]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4500
  Bounding Box: [1084.80, 374.40, 1260.80, 549.60]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [89, 34, 102, 46]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4475
  Bounding Box: [779.20, 1696.00, 873.60, 1785.60]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [65, 137, 72, 143]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4431
  Bounding Box: [1284.00, 22.80, 1541.60, 317.60]
  Mask Area: 261 pixels
  Mask Ratio: 0.0092
  Mask BBox: [105, 6, 124, 28]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4402
  Bounding Box: [962.40, 860.80, 1228.00, 1096.00]
  Mask Area: 208 pixels
  Mask Ratio: 0.0074
  Mask BBox: [80, 72, 99, 89]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4387
  Bounding Box: [219.40, 1934.40, 306.60, 2040.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [22, 156, 27, 163]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4353
  Bounding Box: [111.10, 1345.60, 194.20, 1432.00]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [13, 110, 19, 115]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4336
  Bounding Box: [326.80, 533.60, 542.80, 762.40]
  Mask Area: 221 pixels
  Mask Ratio: 0.0078
  Mask BBox: [30, 46, 46, 63]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4331
  Bounding Box: [1028.80, 356.20, 1256.00, 668.00]
  Mask Area: 328 pixels
  Mask Ratio: 0.0116
  Mask BBox: [85, 32, 102, 56]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4326
  Bounding Box: [704.00, 1654.40, 803.20, 1740.80]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [59, 134, 66, 139]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4243
  Bounding Box: [1196.80, 173.60, 1276.80, 346.00]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [98, 18, 103, 31]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4243
  Bounding Box: [1771.20, 1625.60, 1851.20, 1750.40]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [143, 131, 148, 140]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4233
  Bounding Box: [1544.00, 320.80, 1774.40, 566.40]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [125, 30, 142, 48]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4216
  Bounding Box: [765.20, 1057.60, 882.40, 1228.80]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [64, 87, 72, 99]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4207
  Bounding Box: [789.60, 1328.00, 920.80, 1531.20]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [66, 108, 75, 123]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4197
  Bounding Box: [1731.20, 208.80, 2006.40, 416.80]
  Mask Area: 276 pixels
  Mask Ratio: 0.0098
  Mask BBox: [140, 21, 160, 36]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4197
  Bounding Box: [932.00, 912.80, 1106.40, 1116.00]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [77, 76, 90, 90]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4187
  Bounding Box: [541.20, 590.40, 661.20, 714.40]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [47, 51, 55, 59]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4182
  Bounding Box: [688.00, 180.40, 865.60, 340.00]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [58, 19, 71, 30]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4177
  Bounding Box: [0.00, 281.40, 122.00, 464.80]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [4, 26, 13, 38]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4172
  Bounding Box: [1116.80, 1492.80, 1187.20, 1611.20]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [92, 121, 96, 129]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4150
  Bounding Box: [1670.40, 1715.20, 1814.40, 1897.60]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [135, 138, 145, 152]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4131
  Bounding Box: [9.90, 740.80, 88.10, 886.40]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [5, 62, 10, 73]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4131
  Bounding Box: [449.20, 995.20, 523.60, 1089.60]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [40, 82, 44, 88]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4097
  Bounding Box: [313.00, 1476.00, 520.80, 1739.20]
  Mask Area: 241 pixels
  Mask Ratio: 0.0085
  Mask BBox: [29, 120, 44, 139]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4070
  Bounding Box: [303.20, 780.00, 516.00, 1040.80]
  Mask Area: 308 pixels
  Mask Ratio: 0.0109
  Mask BBox: [28, 65, 44, 85]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4048
  Bounding Box: [573.60, 591.20, 764.80, 747.20]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [49, 51, 63, 62]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4009
  Bounding Box: [766.40, 626.40, 976.00, 800.00]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [64, 53, 80, 66]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.3926
  Bounding Box: [436.00, 1055.20, 567.20, 1282.40]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [39, 87, 47, 104]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.3909
  Bounding Box: [1264.00, 0.00, 1337.60, 71.80]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [103, 4, 108, 8]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.3774
  Bounding Box: [1049.60, 242.00, 1240.00, 531.60]
  Mask Area: 268 pixels
  Mask Ratio: 0.0095
  Mask BBox: [86, 23, 100, 45]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.3762
  Bounding Box: [428.80, 1884.80, 619.20, 2048.00]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [38, 152, 52, 164]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.3752
  Bounding Box: [1806.40, 1132.80, 1876.80, 1265.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [146, 93, 149, 102]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.3743
  Bounding Box: [24.60, 0.00, 234.80, 218.20]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [6, 4, 22, 21]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.3730
  Bounding Box: [1385.60, 1720.00, 1484.80, 1880.00]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [113, 139, 119, 150]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.3721
  Bounding Box: [501.20, 20.90, 606.80, 98.90]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [44, 6, 51, 11]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.3701
  Bounding Box: [98.20, 820.80, 219.00, 905.60]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [12, 69, 21, 74]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.3694
  Bounding Box: [1343.20, 1974.40, 1500.00, 2048.00]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [109, 159, 121, 163]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.3652
  Bounding Box: [784.80, 1544.00, 968.80, 1700.80]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [66, 125, 79, 136]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.3647
  Bounding Box: [840.00, 14.05, 924.80, 140.40]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [70, 6, 76, 14]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.3643
  Bounding Box: [872.00, 1732.80, 980.80, 1860.80]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [73, 140, 80, 149]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3635
  Bounding Box: [861.60, 540.00, 965.60, 704.80]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [72, 47, 79, 59]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3635
  Bounding Box: [1106.40, 1979.20, 1207.20, 2040.00]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [91, 159, 98, 163]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3630
  Bounding Box: [281.00, 1600.00, 367.20, 1718.40]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [26, 129, 32, 137]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3616
  Bounding Box: [823.20, 1761.60, 989.60, 2027.20]
  Mask Area: 211 pixels
  Mask Ratio: 0.0075
  Mask BBox: [69, 142, 81, 162]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3616
  Bounding Box: [284.00, 57.50, 534.40, 280.00]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [27, 9, 45, 25]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3604
  Bounding Box: [1180.00, 748.80, 1476.00, 1107.20]
  Mask Area: 518 pixels
  Mask Ratio: 0.0184
  Mask BBox: [97, 63, 119, 90]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3589
  Bounding Box: [779.20, 1057.60, 865.60, 1192.00]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [65, 87, 71, 97]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3584
  Bounding Box: [879.20, 16.05, 1002.40, 157.60]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [74, 6, 82, 13]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3572
  Bounding Box: [1816.00, 627.60, 1976.00, 810.40]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [146, 54, 158, 67]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3542
  Bounding Box: [1346.40, 1489.60, 1541.60, 1675.20]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [110, 121, 124, 134]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3530
  Bounding Box: [1076.00, 1158.40, 1320.80, 1320.00]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [89, 95, 107, 107]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3523
  Bounding Box: [1176.80, 1426.40, 1316.00, 1592.00]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [96, 116, 106, 128]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3513
  Bounding Box: [404.80, 53.80, 527.20, 219.80]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [36, 9, 43, 21]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3503
  Bounding Box: [1322.40, 1525.60, 1434.40, 1600.00]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [108, 124, 116, 128]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3469
  Bounding Box: [789.60, 16.85, 944.80, 130.00]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [66, 6, 77, 14]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3459
  Bounding Box: [579.60, 912.80, 677.20, 1004.00]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [50, 76, 56, 79]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3459
  Bounding Box: [605.20, 912.80, 702.80, 1004.00]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [52, 76, 57, 79]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3459
  Bounding Box: [327.20, 0.00, 496.80, 93.00]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [30, 4, 42, 11]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3459
  Bounding Box: [1763.20, 500.40, 1984.00, 772.80]
  Mask Area: 283 pixels
  Mask Ratio: 0.0100
  Mask BBox: [142, 44, 158, 64]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3455
  Bounding Box: [498.00, 1451.20, 675.60, 1684.80]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [43, 118, 56, 135]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3452
  Bounding Box: [1606.40, 1010.40, 1833.60, 1308.00]
  Mask Area: 300 pixels
  Mask Ratio: 0.0106
  Mask BBox: [130, 83, 147, 106]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3447
  Bounding Box: [518.80, 699.60, 574.80, 776.00]
  Mask Area: 17 pixels
  Mask Ratio: 0.0006
  Mask BBox: [45, 59, 48, 64]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3447
  Bounding Box: [859.20, 1219.20, 1019.20, 1369.60]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [72, 100, 83, 110]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3447
  Bounding Box: [905.60, 656.00, 1115.20, 848.80]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [75, 58, 91, 70]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3438
  Bounding Box: [1095.20, 1100.80, 1282.40, 1236.80]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [90, 90, 104, 100]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3403
  Bounding Box: [324.80, 1002.40, 460.00, 1119.20]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [30, 83, 39, 91]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3337
  Bounding Box: [747.20, 1678.40, 897.60, 1793.60]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [63, 136, 74, 144]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3325
  Bounding Box: [721.60, 1662.40, 859.20, 1780.80]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [61, 134, 71, 143]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3298
  Bounding Box: [1993.60, 0.00, 2048.00, 133.60]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [160, 4, 165, 14]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3293
  Bounding Box: [1306.40, 1531.20, 1386.40, 1616.00]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [107, 124, 112, 130]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3293
  Bounding Box: [1332.00, 1531.20, 1412.00, 1616.00]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [109, 124, 113, 130]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3284
  Bounding Box: [1541.60, 1564.00, 1702.40, 1774.40]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [125, 127, 136, 142]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [1509.60, 865.60, 1575.20, 1046.40]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [122, 72, 127, 85]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [424.40, 257.20, 641.20, 407.60]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [38, 25, 54, 35]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3259
  Bounding Box: [1123.20, 1955.20, 1200.00, 2048.00]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [92, 157, 97, 163]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3247
  Bounding Box: [692.80, 1624.00, 788.00, 1729.60]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [59, 131, 65, 139]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3235
  Bounding Box: [1750.40, 318.20, 1939.20, 491.20]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [141, 29, 155, 42]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [665.60, 1349.60, 791.20, 1472.80]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [56, 110, 65, 119]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [1798.40, 927.20, 1913.60, 1096.80]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [145, 77, 153, 89]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3218
  Bounding Box: [1517.60, 1328.80, 1622.40, 1461.60]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [123, 108, 129, 117]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3203
  Bounding Box: [1593.60, 7.50, 1721.60, 214.40]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [129, 5, 138, 20]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3191
  Bounding Box: [0.00, 228.00, 181.80, 532.00]
  Mask Area: 171 pixels
  Mask Ratio: 0.0061
  Mask BBox: [4, 22, 18, 45]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3186
  Bounding Box: [1883.20, 1731.20, 2040.00, 1984.00]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [152, 140, 163, 158]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3154
  Bounding Box: [1748.80, 1280.80, 1857.60, 1436.00]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [141, 105, 149, 116]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3149
  Bounding Box: [11.65, 845.60, 119.60, 965.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [5, 71, 13, 79]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3149
  Bounding Box: [12.95, 1336.80, 130.40, 1424.80]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [6, 109, 14, 115]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3149
  Bounding Box: [1207.20, 1501.60, 1311.20, 1636.80]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [99, 122, 106, 131]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3137
  Bounding Box: [631.60, 490.40, 714.80, 604.80]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [54, 43, 59, 51]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3105
  Bounding Box: [763.60, 1392.00, 851.20, 1537.60]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [64, 113, 70, 124]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3098
  Bounding Box: [1412.80, 49.80, 1630.40, 271.00]
  Mask Area: 260 pixels
  Mask Ratio: 0.0092
  Mask BBox: [115, 8, 131, 25]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [1977.60, 665.60, 2035.20, 833.60]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [159, 56, 162, 69]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [1482.40, 339.20, 1620.80, 468.00]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [120, 31, 128, 40]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [506.80, 1532.00, 683.60, 1740.80]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [44, 124, 57, 139]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [280.20, 1030.40, 475.20, 1284.80]
  Mask Area: 248 pixels
  Mask Ratio: 0.0088
  Mask BBox: [26, 85, 41, 104]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3079
  Bounding Box: [1841.60, 466.00, 2043.20, 776.80]
  Mask Area: 350 pixels
  Mask Ratio: 0.0124
  Mask BBox: [148, 41, 163, 64]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3059
  Bounding Box: [450.40, 0.00, 632.80, 60.20]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [40, 4, 53, 8]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3037
  Bounding Box: [683.60, 22.40, 876.00, 135.20]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [58, 6, 70, 12]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3025
  Bounding Box: [126.10, 872.00, 307.20, 1142.40]
  Mask Area: 223 pixels
  Mask Ratio: 0.0079
  Mask BBox: [14, 73, 27, 93]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3020
  Bounding Box: [488.80, 1374.40, 679.20, 1547.20]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [43, 112, 57, 124]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [1253.60, 0.00, 1328.80, 46.60]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [102, 3, 107, 7]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [1279.20, 0.00, 1354.40, 46.60]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [104, 3, 108, 7]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [1279.20, 3.40, 1354.40, 72.20]
  Mask Area: 16 pixels
  Mask Ratio: 0.0006
  Mask BBox: [104, 5, 108, 8]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [1881.60, 820.80, 2048.00, 1249.60]
  Mask Area: 448 pixels
  Mask Ratio: 0.0159
  Mask BBox: [151, 69, 166, 101]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3013
  Bounding Box: [998.40, 1168.00, 1110.40, 1355.20]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [82, 96, 90, 109]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3010
  Bounding Box: [0.00, 1841.60, 71.40, 1998.40]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [4, 148, 9, 160]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3008
  Bounding Box: [1359.20, 1947.20, 1509.60, 2048.00]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [111, 157, 121, 163]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3003
  Bounding Box: [314.80, 1064.00, 530.00, 1326.40]
  Mask Area: 278 pixels
  Mask Ratio: 0.0098
  Mask BBox: [29, 88, 45, 107]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.2998
  Bounding Box: [577.60, 1900.80, 687.20, 2032.00]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [50, 153, 57, 162]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.2998
  Bounding Box: [102.60, 764.40, 265.80, 940.00]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [13, 64, 24, 77]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.2986
  Bounding Box: [7.95, 1816.00, 77.40, 1924.80]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 146, 10, 154]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.2974
  Bounding Box: [1347.20, 1568.00, 1539.20, 1692.80]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [110, 127, 124, 136]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.2966
  Bounding Box: [215.60, 1856.00, 388.40, 2048.00]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [21, 149, 34, 165]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.2959
  Bounding Box: [1246.40, 1105.60, 1353.60, 1284.80]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [102, 92, 109, 104]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.2959
  Bounding Box: [1506.40, 912.80, 1567.20, 1063.20]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [122, 76, 126, 86]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.2957
  Bounding Box: [1128.00, 1979.20, 1236.80, 2033.60]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [93, 159, 99, 162]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.2942
  Bounding Box: [1742.40, 401.60, 1873.60, 498.40]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [141, 36, 150, 42]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.2939
  Bounding Box: [2009.60, 1078.40, 2048.00, 1209.60]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [161, 89, 164, 98]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.2939
  Bounding Box: [679.20, 1560.80, 768.80, 1667.20]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [58, 126, 64, 134]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.2937
  Bounding Box: [1744.00, 374.40, 1875.20, 470.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [141, 34, 150, 40]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.2930
  Bounding Box: [392.80, 1520.80, 667.20, 1697.60]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [35, 123, 56, 136]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.2922
  Bounding Box: [289.00, 1092.80, 443.20, 1284.80]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [27, 90, 38, 104]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.2915
  Bounding Box: [1764.80, 454.00, 1953.60, 626.00]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [142, 40, 156, 52]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.2898
  Bounding Box: [109.80, 794.40, 221.40, 885.60]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [13, 67, 21, 73]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.2891
  Bounding Box: [141.20, 790.40, 245.20, 881.60]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [16, 66, 23, 72]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.2886
  Bounding Box: [1865.60, 1899.20, 1945.60, 2020.80]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [150, 153, 155, 161]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.2886
  Bounding Box: [1891.20, 1899.20, 1971.20, 2020.80]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [152, 153, 157, 161]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.2881
  Bounding Box: [1883.20, 1928.00, 1976.00, 2048.00]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [152, 155, 158, 163]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.2876
  Bounding Box: [1772.80, 1.20, 1888.00, 62.10]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [143, 5, 151, 8]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.2866
  Bounding Box: [0.00, 724.40, 51.50, 844.80]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [3, 61, 8, 69]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.2847
  Bounding Box: [1112.80, 870.40, 1200.80, 942.40]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [91, 72, 97, 77]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.2837
  Bounding Box: [1912.00, 0.00, 2048.00, 161.00]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [154, 3, 165, 16]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2837
  Bounding Box: [1222.40, 996.00, 1307.20, 1100.00]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [100, 82, 106, 89]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2830
  Bounding Box: [97.80, 960.80, 159.80, 1074.40]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [12, 80, 16, 87]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [1099.20, 376.80, 1352.00, 596.80]
  Mask Area: 208 pixels
  Mask Ratio: 0.0074
  Mask BBox: [90, 34, 109, 50]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2805
  Bounding Box: [307.40, 13.60, 499.20, 218.40]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [29, 6, 42, 21]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2798
  Bounding Box: [900.80, 1795.20, 988.80, 1907.20]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [75, 145, 80, 150]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2795
  Bounding Box: [798.40, 1125.60, 910.40, 1250.40]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [67, 92, 75, 101]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2793
  Bounding Box: [765.60, 1422.40, 904.80, 1689.60]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [64, 116, 74, 135]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2791
  Bounding Box: [1657.60, 1516.00, 1756.80, 1692.80]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [134, 123, 141, 136]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2783
  Bounding Box: [1243.20, 0.70, 1326.40, 68.20]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [102, 5, 107, 8]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2771
  Bounding Box: [23.20, 21.60, 144.60, 256.60]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [6, 6, 15, 24]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2771
  Bounding Box: [1501.60, 1636.80, 1672.00, 1748.80]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [122, 132, 134, 140]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2766
  Bounding Box: [530.00, 281.00, 666.80, 413.20]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [46, 26, 56, 36]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2766
  Bounding Box: [783.20, 1054.40, 954.40, 1206.40]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [66, 87, 78, 98]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2764
  Bounding Box: [202.20, 1913.60, 328.60, 2022.40]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [20, 154, 29, 161]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2759
  Bounding Box: [1100.80, 1514.40, 1177.60, 1630.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [90, 123, 95, 131]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2759
  Bounding Box: [1126.40, 1514.40, 1203.20, 1630.40]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [92, 123, 97, 131]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2759
  Bounding Box: [894.40, 1445.60, 1099.20, 1736.00]
  Mask Area: 332 pixels
  Mask Ratio: 0.0118
  Mask BBox: [74, 117, 89, 139]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2751
  Bounding Box: [670.80, 768.00, 777.60, 868.80]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [57, 64, 64, 71]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2751
  Bounding Box: [758.80, 794.40, 892.00, 880.80]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [64, 67, 73, 70]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2747
  Bounding Box: [410.80, 276.00, 509.20, 398.00]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [37, 26, 43, 33]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2737
  Bounding Box: [1104.00, 764.80, 1332.80, 971.20]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [91, 64, 108, 79]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2732
  Bounding Box: [244.20, 1900.80, 343.00, 2016.00]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [24, 155, 29, 161]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2708
  Bounding Box: [398.80, 0.00, 585.20, 58.40]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [36, 3, 49, 8]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2698
  Bounding Box: [615.60, 1478.40, 693.20, 1580.80]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [53, 120, 58, 127]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2693
  Bounding Box: [775.20, 1223.20, 885.60, 1298.40]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [65, 100, 73, 105]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2693
  Bounding Box: [1316.80, 1212.80, 1516.80, 1504.00]
  Mask Area: 304 pixels
  Mask Ratio: 0.0108
  Mask BBox: [107, 99, 122, 121]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2686
  Bounding Box: [613.20, 610.80, 758.00, 785.60]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [52, 52, 63, 65]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2683
  Bounding Box: [1753.60, 1340.80, 1926.40, 1612.80]
  Mask Area: 239 pixels
  Mask Ratio: 0.0085
  Mask BBox: [141, 109, 154, 129]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2671
  Bounding Box: [1752.00, 398.40, 1896.00, 530.40]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [141, 36, 152, 45]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2664
  Bounding Box: [1072.00, 0.00, 1225.60, 141.00]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [88, 3, 99, 15]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2651
  Bounding Box: [1828.80, 976.00, 1931.20, 1091.20]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [147, 81, 152, 89]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2649
  Bounding Box: [11.85, 780.80, 181.20, 944.00]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [5, 65, 18, 77]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2646
  Bounding Box: [1832.00, 909.60, 2024.00, 1205.60]
  Mask Area: 238 pixels
  Mask Ratio: 0.0084
  Mask BBox: [148, 76, 162, 98]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2646
  Bounding Box: [1657.60, 27.70, 1920.00, 240.40]
  Mask Area: 192 pixels
  Mask Ratio: 0.0068
  Mask BBox: [134, 7, 153, 22]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2627
  Bounding Box: [1486.40, 1635.20, 1625.60, 1724.80]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [121, 132, 130, 138]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2622
  Bounding Box: [795.20, 629.60, 912.00, 816.00]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [67, 54, 75, 67]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2622
  Bounding Box: [1742.40, 464.00, 1905.60, 612.00]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [141, 41, 152, 51]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2615
  Bounding Box: [341.80, 274.00, 476.80, 493.20]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [31, 26, 41, 39]

