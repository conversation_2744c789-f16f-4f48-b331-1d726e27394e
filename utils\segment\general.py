import contextlib
import glob
import inspect
import logging
import logging.config
import math
import os
import platform
import random
import re
import signal
import socket
import subprocess
import sys
import time
import urllib.parse
import urllib.request
from datetime import datetime
from itertools import repeat
from multiprocessing.pool import ThreadPool
from pathlib import Path
from subprocess import check_output
from typing import Optional
from zipfile import ZipFile, is_zipfile
from tarfile import is_tarfile

import cv2
import numpy as np
import packaging.version
import pandas as pd
import pkg_resources as pkg
import torch
import torchvision
import yaml
from PIL import Image, ImageDraw, ImageFont

from utils.downloads import gsutil_getsize
from utils.segment.metrics import box_iou, fitness
from ultralytics.utils.checks import check_requirements

from utils import TryExcept, emojis

# Settings
TQDM_BAR_FORMAT = "{l_bar}{bar:10}{r_bar}"  # tqdm bar format
FONT = "Arial.ttf"  # https://github.com/ultralytics/assets/releases/download/v0.0.0/Arial.ttf

FILE = Path(__file__).resolve()
ROOT = FILE.parents[2]  # YOLOv5根目录

# 常量定义
DATASETS_DIR = ROOT / 'datasets'  # 数据集目录
NUM_THREADS = min(8, max(1, os.cpu_count() - 1))  # YOLOv5多进程线程数
TQDM_BAR_FORMAT = '{l_bar}{bar:10}{r_bar}'  # tqdm进度条格式
FONT = 'Arial.ttf'  # 字体文件名

def is_writeable(dir, test=False):
    """检查目录是否可写，如果 `test=True` 则通过创建临时文件进行测试。"""
    if not test:
        return os.access(dir, os.W_OK)  # Windows上可能有问题
    file = Path(dir) / "tmp.txt"
    try:
        with open(file, "w"):  # 以写权限打开文件
            pass
        file.unlink()  # 删除文件
        return True
    except OSError:
        return False

def user_config_dir(dir="Ultralytics", env_var="YOLOV5_CONFIG_DIR"):
    """返回用户配置目录路径，优先使用环境变量 `YOLOV5_CONFIG_DIR`，否则使用操作系统特定路径。"""
    if env := os.getenv(env_var):
        path = Path(env)  # 使用环境变量
    else:
        cfg = {"Windows": "AppData/Roaming", "Linux": ".config", "Darwin": "Library/Application Support"}  # 3个操作系统目录
        path = Path.home() / cfg.get(platform.system(), "")  # 操作系统特定配置目录
        path = (path if is_writeable(path) else Path("/tmp")) / dir  # GCP和AWS lambda修复，只有/tmp可写
    path.mkdir(exist_ok=True)  # 如果需要则创建
    return path

CONFIG_DIR = user_config_dir()  # Ultralytics设置目录

def is_ascii(s=''):
    """检查字符串是否为ASCII编码，返回True表示是ASCII，False表示包含非ASCII字符。"""
    # 将字符串转换为ASCII编码，如果包含非ASCII字符则会抛出异常
    s = str(s)  # 转换为字符串
    return len(s.encode().decode('ascii', 'ignore')) == len(s)


def is_colab():
    """检查当前环境是否为Google Colab实例；如果是Colab返回True，否则返回False。"""
    return "google.colab" in sys.modules


def is_kaggle():
    """通过验证环境变量检查当前环境是否为Kaggle Notebook。"""
    return os.environ.get("PWD") == "/kaggle/working" and os.environ.get("KAGGLE_URL_BASE") == "https://www.kaggle.com"

# 日志配置
LOGGING_NAME = "yolov5"

def is_jupyter():
    """
    Check if the current script is running inside a Jupyter Notebook. Verified on Colab, Jupyterlab, Kaggle, Paperspace.

    Returns:
        bool: True if running inside a Jupyter Notebook, False otherwise.
    """
    with contextlib.suppress(Exception):
        from IPython import get_ipython

        return get_ipython() is not None
    return False

def increment_path(path, exist_ok=False, sep="", mkdir=False):
    """
    Generates an incremented file or directory path if it exists, with optional mkdir; args: path, exist_ok=False,
    sep="", mkdir=False.

    Example: runs/exp --> runs/exp{sep}2, runs/exp{sep}3, ... etc
    """
    path = Path(path)  # os-agnostic
    if path.exists() and not exist_ok:
        path, suffix = (path.with_suffix(""), path.suffix) if path.is_file() else (path, "")

        # Method 1
        for n in range(2, 9999):
            p = f"{path}{sep}{n}{suffix}"  # increment path
            if not os.path.exists(p):  #
                break
        path = Path(p)

    if mkdir:
        path.mkdir(parents=True, exist_ok=True)  # make directory

    return path

def make_divisible(x, divisor):
    """Adjusts `x` to be divisible by `divisor`, returning the nearest greater or equal value."""
    if isinstance(divisor, torch.Tensor):
        divisor = int(divisor.max())  # to int
    return math.ceil(x / divisor) * divisor

def colorstr(*input):
    """
    Colors a string using ANSI escape codes, e.g., colorstr('blue', 'hello world').

    See https://en.wikipedia.org/wiki/ANSI_escape_code.
    """
    *args, string = input if len(input) > 1 else ("blue", "bold", input[0])  # color arguments, string
    colors = {
        "black": "\033[30m",  # basic colors
        "red": "\033[31m",
        "green": "\033[32m",
        "yellow": "\033[33m",
        "blue": "\033[34m",
        "magenta": "\033[35m",
        "cyan": "\033[36m",
        "white": "\033[37m",
        "bright_black": "\033[90m",  # bright colors
        "bright_red": "\033[91m",
        "bright_green": "\033[92m",
        "bright_yellow": "\033[93m",
        "bright_blue": "\033[94m",
        "bright_magenta": "\033[95m",
        "bright_cyan": "\033[96m",
        "bright_white": "\033[97m",
        "end": "\033[0m",  # misc
        "bold": "\033[1m",
        "underline": "\033[4m",
    }
    return "".join(colors[x] for x in args) + f"{string}" + colors["end"]

def check_version(current="0.0.0", minimum="0.0.0", name="version ", pinned=False, hard=False, verbose=False):
    """Checks if the current version meets the minimum required version, exits or warns based on parameters."""
    current, minimum = (packaging.version.parse(x) for x in (current, minimum))
    result = (current == minimum) if pinned else (current >= minimum)  # bool
    s = f"WARNING ⚠️ {name}{minimum} is required by YOLOv5, but {name}{current} is currently installed"  # string
    if hard:
        assert result, emojis(s)  # assert min requirements met
    if verbose and not result:
        LOGGER.warning(s)
    return result


def file_date(path=__file__):
    """Returns a human-readable file modification date in 'YYYY-M-D' format, given a file path."""
    t = datetime.fromtimestamp(Path(path).stat().st_mtime)
    return f"{t.year}-{t.month}-{t.day}"


def segments2boxes(segments):
    """Convert segment labels to box labels, i.e. (cls, xy1, xy2, ...) to (cls, xywh)."""
    boxes = []
    for s in segments:
        x, y = s.T  # segment xy
        boxes.append([x.min(), y.min(), x.max(), y.max()])  # cls, xyxy
    return xyxy2xywh(np.array(boxes))  # cls, xywh

def set_logging(name=LOGGING_NAME, verbose=True):
    """配置日志输出，设置指定的详细程度；`name` 设置日志器名称，`verbose` 控制日志级别。"""
    rank = int(os.getenv("RANK", -1))  # 多GPU训练中的进程排名
    level = logging.INFO if verbose and rank in {-1, 0} else logging.ERROR
    logging.config.dictConfig(
        {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {name: {"format": "%(message)s"}},
            "handlers": {
                name: {
                    "class": "logging.StreamHandler",
                    "formatter": name,
                    "level": level,
                }
            },
            "loggers": {
                name: {
                    "level": level,
                    "handlers": [name],
                    "propagate": False,
                }
            },
        }
    )

# 初始化日志
set_logging(LOGGING_NAME)  # 在定义LOGGER之前运行
LOGGER = logging.getLogger(LOGGING_NAME)  # 全局定义（用于train.py, val.py, detect.py等）
if platform.system() == "Windows":
    for fn in LOGGER.info, LOGGER.warning:
        setattr(LOGGER, fn.__name__, lambda x: fn(emojis(x)))  # emoji安全日志


def print_args(args: Optional[dict] = None, show_file=True, show_func=False):
    """打印函数参数（可选）到日志中。"""
    x = inspect.currentframe().f_back  # 调用者帧
    file, _, func, _, _ = inspect.getframeinfo(x)
    if args is None:
        args, _, _, values = inspect.getargvalues(x)
        args = {k: v for k, v in values.items() if k in args}
    try:
        file = Path(file).resolve().relative_to(ROOT).with_suffix('')
    except ValueError:
        file = Path(file).stem
    s = (f'{file}: ' if show_file else '') + (f'{func}: ' if show_func else '')
    LOGGER.info(colorstr(s) + ', '.join(f'{k}={v}' for k, v in args.items()))

def xywh2xyxy(x):
    """Convert nx4 boxes from [x, y, w, h] to [x1, y1, x2, y2] where xy1=top-left, xy2=bottom-right."""
    y = x.clone() if isinstance(x, torch.Tensor) else np.copy(x)
    y[..., 0] = x[..., 0] - x[..., 2] / 2  # top left x
    y[..., 1] = x[..., 1] - x[..., 3] / 2  # top left y
    y[..., 2] = x[..., 0] + x[..., 2] / 2  # bottom right x
    y[..., 3] = x[..., 1] + x[..., 3] / 2  # bottom right y
    return y

def xywhn2xyxy(x, w=640, h=640, padw=0, padh=0):
    """Convert nx4 boxes from [x, y, w, h] normalized to [x1, y1, x2, y2] where xy1=top-left, xy2=bottom-right."""
    y = x.clone() if isinstance(x, torch.Tensor) else np.copy(x)
    y[..., 0] = w * (x[..., 0] - x[..., 2] / 2) + padw  # top left x
    y[..., 1] = h * (x[..., 1] - x[..., 3] / 2) + padh  # top left y
    y[..., 2] = w * (x[..., 0] + x[..., 2] / 2) + padw  # bottom right x
    y[..., 3] = h * (x[..., 1] + x[..., 3] / 2) + padh  # bottom right y
    return y

def xyn2xy(x, w=640, h=640, padw=0, padh=0):
    """Convert normalized segments into pixel segments, shape (n,2)."""
    y = x.clone() if isinstance(x, torch.Tensor) else np.copy(x)
    y[..., 0] = w * x[..., 0] + padw  # top left x
    y[..., 1] = h * x[..., 1] + padh  # top left y
    return y

def clip_boxes(boxes, shape):
    """Clips bounding box coordinates (xyxy) to fit within the specified image shape (height, width)."""
    if isinstance(boxes, torch.Tensor):  # faster individually
        boxes[..., 0].clamp_(0, shape[1])  # x1
        boxes[..., 1].clamp_(0, shape[0])  # y1
        boxes[..., 2].clamp_(0, shape[1])  # x2
        boxes[..., 3].clamp_(0, shape[0])  # y2
    else:  # np.array (faster grouped)
        boxes[..., [0, 2]] = boxes[..., [0, 2]].clip(0, shape[1])  # x1, x2
        boxes[..., [1, 3]] = boxes[..., [1, 3]].clip(0, shape[0])  # y1, y2

def xyxy2xywh(x):
    """Convert nx4 boxes from [x1, y1, x2, y2] to [x, y, w, h] where xy1=top-left, xy2=bottom-right."""
    y = x.clone() if isinstance(x, torch.Tensor) else np.copy(x)
    y[..., 0] = (x[..., 0] + x[..., 2]) / 2  # x center
    y[..., 1] = (x[..., 1] + x[..., 3]) / 2  # y center
    y[..., 2] = x[..., 2] - x[..., 0]  # width
    y[..., 3] = x[..., 3] - x[..., 1]  # height
    return y
    
def xyxy2xywhn(x, w=640, h=640, clip=False, eps=0.0):
    """Convert nx4 boxes from [x1, y1, x2, y2] to [x, y, w, h] normalized where xy1=top-left, xy2=bottom-right."""
    if clip:
        clip_boxes(x, (h - eps, w - eps))  # warning: inplace clip
    y = x.clone() if isinstance(x, torch.Tensor) else np.copy(x)
    y[..., 0] = ((x[..., 0] + x[..., 2]) / 2) / w  # x center
    y[..., 1] = ((x[..., 1] + x[..., 3]) / 2) / h  # y center
    y[..., 2] = (x[..., 2] - x[..., 0]) / w  # width
    y[..., 3] = (x[..., 3] - x[..., 1]) / h  # height
    return y

def crop_mask(masks, boxes):
    """
    裁剪 mask，只保留预测框范围内的区域，其余置零。
    Args:
        masks: [n, h, w] tensor，n个mask
        boxes: [n, 4] tensor，bbox坐标 (x1,y1,x2,y2)，相对坐标
    """
    n, h, w = masks.shape
    x1, y1, x2, y2 = torch.chunk(boxes[:, :, None], 4, 1)  # 拆成四个坐标
    r = torch.arange(w, device=masks.device, dtype=x1.dtype)[None, None, :]  # 宽度坐标
    c = torch.arange(h, device=masks.device, dtype=x1.dtype)[None, :, None]  # 高度坐标
    return masks * ((r >= x1) * (r < x2) * (c >= y1) * (c < y2))  # 裁剪区域


def process_mask_upsample(protos, masks_in, bboxes, shape):
    """
    先上采样，再裁剪 mask。
    Args:
        protos: [mask_dim, mask_h, mask_w] 掩码原型
        masks_in: [n, mask_dim] NMS 后的掩码系数
        bboxes: [n, 4] 对应的预测框
        shape: 输入图像尺寸 (h, w)
    Return: 上采样后的二值mask [n,h,w]
    """
    c, mh, mw = protos.shape
    masks = (masks_in @ protos.float().view(c, -1)).sigmoid().view(-1, mh, mw)
    masks = F.interpolate(masks[None], shape, mode="bilinear", align_corners=False)[0]
    masks = crop_mask(masks, bboxes)
    return masks.gt_(0.5)  # 二值化


def process_mask(protos, masks_in, bboxes, shape, upsample=False):
    """
    先裁剪，再上采样 mask。
    Args:
        protos: [mask_dim, mask_h, mask_w]
        masks_in: [n, mask_dim]
        bboxes: [n, 4]
        shape: 输入图像大小 (h,w)
        upsample: 是否在最后上采样
    """
    c, mh, mw = protos.shape
    ih, iw = shape
    masks = (masks_in @ protos.float().view(c, -1)).sigmoid().view(-1, mh, mw)

    # 将预测框缩放到特征图大小
    downsampled_bboxes = bboxes.clone()
    downsampled_bboxes[:, 0] *= mw / iw
    downsampled_bboxes[:, 2] *= mw / iw
    downsampled_bboxes[:, 3] *= mh / ih
    downsampled_bboxes[:, 1] *= mh / ih

    masks = crop_mask(masks, downsampled_bboxes)
    if upsample:  # 上采样到输入图像大小
        masks = F.interpolate(masks[None], shape, mode="bilinear", align_corners=False)[0]
    return masks.gt_(0.5)


def process_mask_native(protos, masks_in, bboxes, shape):
    """
    官方原生实现，先上采样，再裁剪，考虑 padding。
    """
    c, mh, mw = protos.shape
    masks = (masks_in @ protos.float().view(c, -1)).sigmoid().view(-1, mh, mw)
    # 计算填充
    gain = min(mh / shape[0], mw / shape[1])
    pad = (mw - shape[1] * gain) / 2, (mh - shape[0] * gain) / 2
    top, left = int(pad[1]), int(pad[0])
    bottom, right = int(mh - pad[1]), int(mw - pad[0])
    masks = masks[:, top:bottom, left:right]

    masks = F.interpolate(masks[None], shape, mode="bilinear", align_corners=False)[0]
    masks = crop_mask(masks, bboxes)
    return masks.gt_(0.5)


def scale_image(im1_shape, masks, im0_shape, ratio_pad=None):
    """
    将 mask 从模型输入尺寸缩放回原图尺寸。
    Args:
        im1_shape: 模型输入尺寸 [h,w]
        im0_shape: 原图尺寸 [h,w,3]
        masks: [h,w,num] mask
    """
    if ratio_pad is None:
        gain = min(im1_shape[0] / im0_shape[0], im1_shape[1] / im0_shape[1])
        pad = (im1_shape[1] - im0_shape[1] * gain) / 2, (im1_shape[0] - im0_shape[0] * gain) / 2
    else:
        pad = ratio_pad[1]
    top, left = int(pad[1]), int(pad[0])
    bottom, right = int(im1_shape[0] - pad[1]), int(im1_shape[1] - pad[0])

    masks = masks[top:bottom, left:right]
    masks = cv2.resize(masks, (im0_shape[1], im0_shape[0]))  # 缩放回原图
    if len(masks.shape) == 2:
        masks = masks[:, :, None]
    return masks


def mask_iou(mask1, mask2, eps=1e-7):
    """
    计算 mask1 与 mask2 的两两 IoU（交并比）。
    Args:
        mask1: [N, n] 预测mask
        mask2: [M, n] GT mask
    Return: IoU [N,M]
    """
    intersection = torch.matmul(mask1, mask2.t()).clamp(0)
    union = (mask1.sum(1)[:, None] + mask2.sum(1)[None]) - intersection
    return intersection / (union + eps)


def masks_iou(mask1, mask2, eps=1e-7):
    """
    计算预测 mask 与 GT mask 的逐元素 IoU。
    Args:
        mask1: [N, n]
        mask2: [N, n]
    Return: IoU (N,)
    """
    intersection = (mask1 * mask2).sum(1).clamp(0)
    union = (mask1.sum(1) + mask2.sum(1))[None] - intersection
    return intersection / (union + eps)


def masks2segments(masks, strategy="largest"):
    """
    将二值 mask 转换为多边形轮廓 (segments)。
    Args:
        masks: (n,h,w) 二值mask
        strategy:
            "concat"  → 拼接所有轮廓
            "largest" → 只取面积最大的轮廓
    Return: 多边形点集列表
    """
    segments = []
    for x in masks.int().cpu().numpy().astype("uint8"):
        c = cv2.findContours(x, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]
        if c:
            if strategy == "concat":
                c = np.concatenate([x.reshape(-1, 2) for x in c])
            elif strategy == "largest":
                c = np.array(c[np.array([len(x) for x in c]).argmax()]).reshape(-1, 2)
        else:
            c = np.zeros((0, 2))
        segments.append(c.astype("float32"))
    return segments


def coco80_to_coco91_class():
    """
    Converts COCO 80-class index to COCO 91-class index used in the paper.

    Reference: https://tech.amikelive.com/node-718/what-object-categories-labels-are-in-coco-dataset/
    """
    # a = np.loadtxt('data/coco.names', dtype='str', delimiter='\n')
    # b = np.loadtxt('data/coco_paper.names', dtype='str', delimiter='\n')
    # x1 = [list(a[i] == b).index(True) + 1 for i in range(80)]  # darknet to coco
    # x2 = [list(b[i] == a).index(True) if any(b[i] == a) else None for i in range(91)]  # coco to darknet
    return [
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        10,
        11,
        13,
        14,
        15,
        16,
        17,
        18,
        19,
        20,
        21,
        22,
        23,
        24,
        25,
        27,
        28,
        31,
        32,
        33,
        34,
        35,
        36,
        37,
        38,
        39,
        40,
        41,
        42,
        43,
        44,
        46,
        47,
        48,
        49,
        50,
        51,
        52,
        53,
        54,
        55,
        56,
        57,
        58,
        59,
        60,
        61,
        62,
        63,
        64,
        65,
        67,
        70,
        72,
        73,
        74,
        75,
        76,
        77,
        78,
        79,
        80,
        81,
        82,
        84,
        85,
        86,
        87,
        88,
        89,
        90,
    ]


def git_describe(path=None):
    """
    Returns a human-readable git description of the repository at `path`, or an empty string on failure.

    Example output is 'fv5.0-5-g3e25f1e'. See https://git-scm.com/docs/git-describe.
    """
    try:
        if path is None:
            path = Path(__file__).parent.parent.parent  # YOLOv5 root directory
        assert (Path(path) / ".git").is_dir()
        return check_output(f"git -C {path} describe --tags --long --always", shell=True).decode()[:-1]
    except Exception:
        return ""


def check_imshow(warn=False):
    """Checks environment support for image display; warns on failure if `warn=True`."""
    try:
        assert not is_jupyter()
        assert not is_docker()
        cv2.imshow("test", np.zeros((1, 1, 3)))
        cv2.waitKey(1)
        cv2.destroyAllWindows()
        cv2.waitKey(1)
        return True
    except Exception as e:
        if warn:
            LOGGER.warning(f"WARNING ⚠️ Environment does not support cv2.imshow() or PIL Image.show()\n{e}")
        return False


def is_docker():
    """检查当前环境是否在Docker容器中运行。"""
    file = Path("/proc/self/cgroup")
    return file.exists() and "docker" in file.read_text()


def check_suffix(file="yolov5s.pt", suffix=(".pt",), msg=""):
    """验证文件或文件列表是否具有可接受的后缀，如果不符合则抛出错误。"""
    if file and suffix:
        if isinstance(suffix, str):
            suffix = [suffix]
        for f in file if isinstance(file, (list, tuple)) else [file]:
            s = Path(f).suffix.lower()  # 文件后缀
            if len(s):
                assert s in suffix, f"{msg}{f} acceptable suffix is {suffix}"


def check_file(file, suffix=""):
    """搜索/下载文件，检查其后缀（如果提供），并返回文件路径。"""
    check_suffix(file, suffix)  # 可选
    file = str(file)  # 转换为字符串
    if os.path.isfile(file) or not file:  # 文件存在
        return file
    elif file.startswith(("http:/", "https:/")):  # 下载
        url = file  # 警告：Pathlib将://转换为:/
        file = Path(urllib.parse.unquote(file).split("?")[0]).name  # '%2F'转换为'/'，分割https://url.com/file.txt?auth
        if os.path.isfile(file):
            LOGGER.info(f"Found {url} locally at {file}")  # 文件已存在
        else:
            LOGGER.info(f"Downloading {url} to {file}...")
            torch.hub.download_url_to_file(url, file)
            assert Path(file).exists() and Path(file).stat().st_size > 0, f"File download failed: {url}"  # 检查
        return file
    elif file.startswith("clearml://"):  # ClearML数据集ID
        assert "clearml" in sys.modules, (
            "ClearML is not installed, so cannot use ClearML dataset. Try running 'pip install clearml'."
        )
        return file
    else:  # 搜索
        files = []
        for d in "data", "models", "utils":  # 搜索目录
            files.extend(glob.glob(str(ROOT / d / "**" / file), recursive=True))  # 查找文件
        assert len(files), f"File not found: {file}"  # 断言找到文件
        assert len(files) == 1, f"Multiple files match '{file}', specify exact path: {files}"  # 断言唯一
        return files[0]  # 返回文件


def scale_boxes(img1_shape, boxes, img0_shape, ratio_pad=None):
    """Rescales (xyxy) bounding boxes from img1_shape to img0_shape, optionally using provided `ratio_pad`."""
    if ratio_pad is None:  # calculate from img0_shape
        gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
        pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
    else:
        gain = ratio_pad[0][0]
        pad = ratio_pad[1]

    boxes[..., [0, 2]] -= pad[0]  # x padding
    boxes[..., [1, 3]] -= pad[1]  # y padding
    boxes[..., :4] /= gain
    clip_boxes(boxes, img0_shape)
    return boxes


def box_iou(box1, box2, eps=1e-7):
    """Calculate intersection-over-union (IoU) of boxes. Both sets of boxes are expected to be in (x1, y1, x2, y2) format."""
    # inter(N,M) = (rb(N,M,2) - lt(N,M,2)).clamp(0).prod(2)
    (a1, a2), (b1, b2) = box1.unsqueeze(1).chunk(2, 2), box2.unsqueeze(0).chunk(2, 2)
    inter = (torch.min(a2, b2) - torch.max(a1, b1)).clamp(0).prod(2)

    # IoU = inter / (area1 + area2 - inter)
    return inter / ((a2 - a1).prod(2) + (b2 - b1).prod(2) - inter + eps)


def torch_load(file, map_location=None):
    """Loads a PyTorch model with error handling for different file formats and map locations."""
    try:
        return torch.load(file, map_location=map_location)
    except Exception as e:
        LOGGER.warning(f"WARNING ⚠️ Error loading {file}: {e}")
        return None


def strip_optimizer(f="best.pt", s=""):
    """
    Strips optimizer and optionally saves checkpoint to finalize training; arguments are file path 'f' and save path
    's'.

    Example: from utils.general import *; strip_optimizer()
    """
    x = torch_load(f, map_location=torch.device("cpu"))
    if x.get("ema"):
        x["model"] = x["ema"]  # replace model with ema
    for k in "optimizer", "best_fitness", "ema", "updates":  # keys
        x[k] = None
    x["epoch"] = -1
    x["model"].half()  # to FP16
    for p in x["model"].parameters():
        p.requires_grad = False
    torch.save(x, s or f)
    mb = os.path.getsize(s or f) / 1e6  # filesize
    LOGGER.info(f"Optimizer stripped from {f},{f' saved as {s},' if s else ''} {mb:.1f}MB")


def init_seeds(seed=0, deterministic=False):
    """
    Initializes RNG seeds and sets deterministic options if specified.

    See https://pytorch.org/docs/stable/notes/randomness.html
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)  # for Multi-GPU, exception safe
    # torch.backends.cudnn.benchmark = True  # AutoBatch problem https://github.com/ultralytics/yolov5/issues/9287
    if deterministic and check_version(torch.__version__, "1.12.0"):  # https://github.com/ultralytics/yolov5/pull/8213
        torch.use_deterministic_algorithms(True)
        torch.backends.cudnn.deterministic = True
        os.environ["CUBLAS_WORKSPACE_CONFIG"] = ":4096:8"
        os.environ["PYTHONHASHSEED"] = str(seed)


def intersect_dicts(da, db, exclude=()):
    """Returns intersection of `da` and `db` dicts with matching keys and shapes, excluding `exclude` keys; uses `da`
    values.
    """
    return {k: v for k, v in da.items() if k in db and all(x not in k for x in exclude) and v.shape == db[k].shape}


def get_latest_run(search_dir="."):
    """Returns the path to the most recent 'last.pt' file in /runs to resume from, searches in `search_dir`."""
    last_list = glob.glob(f"{search_dir}/**/last*.pt", recursive=True)
    return max(last_list, key=os.path.getctime) if last_list else ""


def one_cycle(y1=0.0, y2=1.0, steps=100):
    """
    Generates a lambda for a sinusoidal ramp from y1 to y2 over 'steps'.

    See https://arxiv.org/pdf/1812.01187.pdf for details.
    """
    return lambda x: ((1 - math.cos(x * math.pi / steps)) / 2) * (y2 - y1) + y1


def labels_to_class_weights(labels, nc=80):
    """Calculates class weights from labels to handle class imbalance in training; input shape: (n, 5)."""
    if labels[0] is None:  # no labels loaded
        return torch.Tensor()

    labels = np.concatenate(labels, 0)  # labels.shape = (866643, 5) for COCO
    classes = labels[:, 0].astype(int)  # labels = [class xywh]
    weights = np.bincount(classes, minlength=nc)  # occurrences per class

    # Prepend gridpoint count (for uCE training)
    # gpi = ((320 / 32 * np.array([1, 2, 4])) ** 2 * 3).sum()  # gridpoints per image
    # weights = np.hstack([gpi * len(labels)  - weights.sum() * 9, weights * 9]) ** 0.5  # prepend gridpoints to start

    weights[weights == 0] = 1  # replace empty bins with 1
    weights = 1 / weights  # number of targets per class
    weights /= weights.sum()  # normalize
    return torch.from_numpy(weights).float()


def labels_to_image_weights(labels, nc=80, class_weights=np.ones(80)):
    """Calculates image weights from labels using class weights for weighted sampling."""
    # Usage: index = random.choices(range(n), weights=image_weights, k=1)  # weighted image sample
    class_counts = np.array([np.bincount(x[:, 0].astype(int), minlength=nc) for x in labels])
    return (class_weights.reshape(1, nc) * class_counts).sum(1)


def print_mutation(keys, results, hyp, save_dir, bucket, prefix=colorstr("evolve: ")):
    """Logs evolution results and saves to CSV and YAML in `save_dir`, optionally syncs with `bucket`."""
    evolve_csv = save_dir / "evolve.csv"
    evolve_yaml = save_dir / "hyp_evolve.yaml"
    keys = tuple(keys) + tuple(hyp.keys())  # [results + hyps]
    keys = tuple(x.strip() for x in keys)
    vals = results + tuple(hyp.values())
    n = len(keys)

    # Download (optional)
    if bucket:
        url = f"gs://{bucket}/evolve.csv"
        if gsutil_getsize(url) > (evolve_csv.stat().st_size if evolve_csv.exists() else 0):
            subprocess.run(["gsutil", "cp", f"{url}", f"{save_dir}"])  # download evolve.csv if larger than local

    # Log to evolve.csv
    s = "" if evolve_csv.exists() else (("%20s," * n % keys).rstrip(",") + "\n")  # add header
    with open(evolve_csv, "a") as f:
        f.write(s + ("%20.5g," * n % vals).rstrip(",") + "\n")

    # Save yaml
    with open(evolve_yaml, "w") as f:
        data = pd.read_csv(evolve_csv, skipinitialspace=True)
        data = data.rename(columns=lambda x: x.strip())  # strip keys
        i = np.argmax(fitness(data.values[:, :4]))  #
        generations = len(data)
        f.write(
            "# YOLOv5 Hyperparameter Evolution Results\n"
            + f"# Best generation: {i}\n"
            + f"# Last generation: {generations - 1}\n"
            + "# "
            + ", ".join(f"{x.strip():>20s}" for x in keys[:7])
            + "\n"
            + "# "
            + ", ".join(f"{x:>20.5g}" for x in data.values[i, :7])
            + "\n\n"
        )
        yaml.safe_dump(data.loc[i][7:].to_dict(), f, sort_keys=False)

    # Print to screen
    LOGGER.info(
        prefix
        + f"{generations} generations finished, current result:\n"
        + prefix
        + ", ".join(f"{x.strip():>20s}" for x in keys)
        + "\n"
        + prefix
        + ", ".join(f"{x:20.5g}" for x in vals)
        + "\n\n"
    )

    if bucket:
        subprocess.run(["gsutil", "cp", f"{evolve_csv}", f"{evolve_yaml}", f"gs://{bucket}"])  # upload


def increment_path(path, exist_ok=False, sep="", mkdir=False):
    """
    Generates an incremented file or directory path if it exists, with optional mkdir; args: path, exist_ok=False,
    sep="", mkdir=False.

    Example: runs/exp --> runs/exp{sep}2, runs/exp{sep}3, ... etc
    """
    path = Path(path)  # os-agnostic
    if path.exists() and not exist_ok:
        path, suffix = (path.with_suffix(""), path.suffix) if path.is_file() else (path, "")

        # Method 1
        for n in range(2, 9999):
            p = f"{path}{sep}{n}{suffix}"  # increment path
            if not os.path.exists(p):  #
                break
        path = Path(p)

        # Method 2 (deprecated)
        # dirs = glob.glob(f"{path}{sep}*")  # similar paths
        # matches = [re.search(rf"{path.stem}{sep}(\d+)", d) for d in dirs]
        # i = [int(m.groups()[0]) for m in matches if m]  # indices
        # n = max(i) + 1 if i else 2  # increment number
        # path = Path(f"{path}{sep}{n}{suffix}")  # increment path

    if mkdir:
        path.mkdir(parents=True, exist_ok=True)  # make directory

    return path


class WorkingDirectory(contextlib.ContextDecorator):
    """Context manager/decorator to temporarily change the working directory within a 'with' statement or decorator."""

    def __init__(self, new_dir):
        """Initializes a context manager/decorator to temporarily change the working directory."""
        self.dir = new_dir  # new dir
        self.cwd = Path.cwd().resolve()  # current dir

    def __enter__(self):
        """Temporarily changes the working directory within a 'with' statement context."""
        os.chdir(self.dir)

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Restores the original working directory upon exiting a 'with' statement context."""
        os.chdir(self.cwd)


def make_divisible(x, divisor):
    """Adjusts `x` to be divisible by `divisor`, returning the nearest greater or equal value."""
    if isinstance(divisor, torch.Tensor):
        divisor = int(divisor.max())  # to int
    return math.ceil(x / divisor) * divisor


def check_online():
    """通过尝试创建到"*******"端口443的连接来检查互联网连接，如果第一次尝试失败则重试一次。"""
    def run_once():
        """通过尝试创建到"*******"端口443的连接来检查互联网连接。"""
        try:
            socket.create_connection(("*******", 443), 5)  # check host accessibility
            return True
        except OSError:
            return False

    return run_once() or run_once()  # check twice to increase robustness to intermittent connectivity issues


@TryExcept()
@WorkingDirectory(ROOT)
def check_git_status(repo="ultralytics/yolov5", branch="master"):
    """检查YOLOv5代码是否与仓库保持最新，如果落后则建议'git pull'；错误时返回信息性消息。"""
    url = f"https://github.com/{repo}"
    msg = f", for updates see {url}"
    s = colorstr("github: ")  # string
    assert Path(".git").exists(), s + "skipping check (not a git repository)" + msg
    assert check_online(), s + "skipping check (offline)" + msg

    splits = re.split(pattern=r"\s", string=check_output("git remote -v", shell=True).decode())
    matches = [repo in s for s in splits]
    if any(matches):
        remote = splits[matches.index(True) - 1]
    else:
        remote = "ultralytics"
        check_output(f"git remote add {remote} {url}", shell=True)
    check_output(f"git fetch {remote}", shell=True, timeout=5)  # git fetch
    local_branch = check_output("git rev-parse --abbrev-ref HEAD", shell=True).decode().strip()  # checked out
    n = int(check_output(f"git rev-list {local_branch}..{remote}/{branch} --count", shell=True))  # commits behind
    if n > 0:
        pull = "git pull" if remote == "origin" else f"git pull {remote} {branch}"
        s += f"⚠️ YOLOv5 is out of date by {n} commit{'s' * (n > 1)}. Use '{pull}' or 'git clone {url}' to update."
    else:
        s += f"up to date with {url} ✅"
    LOGGER.info(s)


@WorkingDirectory(ROOT)
def check_git_info(path="."):
    """检查YOLOv5 git信息，返回包含远程URL、分支名称和提交哈希的字典。"""
    check_requirements("gitpython")
    import git

    try:
        repo = git.Repo(path)
        remote = repo.remotes.origin.url.replace(".git", "")  # i.e. 'https://github.com/ultralytics/yolov5'
        commit = repo.head.commit.hexsha  # i.e. '3134699c73af83aac2a481435550b968d5792c0d'
        try:
            branch = repo.active_branch.name  # i.e. 'main'
        except TypeError:  # not on any branch
            branch = None  # i.e. 'detached HEAD' state
        return {"remote": remote, "branch": branch, "commit": commit}
    except git.exc.InvalidGitRepositoryError:  # path is not a git dir
        return {"remote": None, "branch": None, "commit": None}


def check_img_size(imgsz, s=32, floor=0):
    """调整图像大小以能被步长`s`整除，支持int或list/tuple输入，返回调整后的大小。"""
    if isinstance(imgsz, int):  # integer i.e. img_size=640
        new_size = max(make_divisible(imgsz, int(s)), floor)
    else:  # list i.e. img_size=[640, 480]
        imgsz = list(imgsz)  # convert to list if tuple
        new_size = [max(make_divisible(x, int(s)), floor) for x in imgsz]
    if new_size != imgsz:
        LOGGER.warning(f"WARNING ⚠️ --img-size {imgsz} must be multiple of max stride {s}, updating to {new_size}")
    return new_size


def check_yaml(file, suffix=(".yaml", ".yml")):
    """搜索/下载YAML文件，验证其后缀（.yaml或.yml），并返回文件路径。"""
    return check_file(file, suffix)


class Profile(contextlib.AbstractContextManager):
    """
    YOLOv5 性能分析器。用于测量代码块的执行时间。
    
    使用方法:
        with Profile() as dt:
            pass  # 要测量的代码
        print(f'执行时间: {dt.t:.3f}s')
    
    或者:
        dt = Profile()
        dt.start()
        # 要测量的代码
        dt.stop()
        print(f'执行时间: {dt.t:.3f}s')
    """

    def __init__(self, t=0.0):
        """
        初始化性能分析器。
        
        Args:
            t (float): 初始时间值，默认为0.0
        """
        self.t = t
        self.cuda = torch.cuda.is_available()

    def __enter__(self):
        """
        进入上下文管理器，开始计时。
        
        Returns:
            Profile: 返回自身实例
        """
        self.start = self.time()
        return self

    def __exit__(self, type, value, traceback):
        """
        退出上下文管理器，结束计时并计算总时间。
        
        Args:
            type: 异常类型
            value: 异常值
            traceback: 异常追踪信息
        """
        self.dt = self.time() - self.start  # 计算时间差
        self.t += self.dt  # 累加到总时间

    def time(self):
        """
        获取当前时间戳。如果CUDA可用，会同步CUDA操作。
        
        Returns:
            float: 当前时间戳
        """
        if self.cuda:
            torch.cuda.synchronize()  # 同步CUDA操作
        return time.time()

    def start(self):
        """
        手动开始计时。
        """
        self.start_time = self.time()

    def stop(self):
        """
        手动结束计时并更新总时间。
        """
        self.dt = self.time() - self.start_time
        self.t += self.dt


def yaml_load(file="data.yaml"):
    """
    安全加载YAML文件并返回Python字典。
    
    Args:
        file (str): YAML文件路径
        
    Returns:
        dict: 解析后的YAML内容
    """
    with open(file, errors="ignore") as f:
        return yaml.safe_load(f)


def scale_boxes(img1_shape, boxes, img0_shape, ratio_pad=None):
    """
    将边界框从img1_shape缩放到img0_shape，可选择使用提供的ratio_pad。
    
    Args:
        img1_shape: 输入图像尺寸 (height, width)
        boxes: 边界框坐标 (xyxy格式)
        img0_shape: 目标图像尺寸 (height, width)
        ratio_pad: 可选的缩放比例和填充信息
        
    Returns:
        boxes: 缩放后的边界框坐标
    """
    if ratio_pad is None:  # 从img0_shape计算
        gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain = old / new
        pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
    else:
        gain = ratio_pad[0][0]
        pad = ratio_pad[1]

    boxes[..., [0, 2]] -= pad[0]  # x padding
    boxes[..., [1, 3]] -= pad[1]  # y padding
    boxes[..., :4] /= gain
    clip_boxes(boxes, img0_shape)
    return boxes


def segment2box(segment, width=640, height=640):
    """
    将1个分割标签转换为1个边界框标签，应用图像内约束，即 (xy1, xy2, ...) 到 (xyxy)。
    
    Args:
        segment: 分割点坐标
        width: 图像宽度
        height: 图像高度
        
    Returns:
        np.array: 边界框坐标 [x1, y1, x2, y2]
    """
    x, y = segment.T  # segment xy
    inside = (x >= 0) & (y >= 0) & (x <= width) & (y <= height)
    x, y = x[inside], y[inside]
    return np.array([x.min(), y.min(), x.max(), y.max()]) if any(x) else np.zeros((1, 4))  # xyxy


def resample_segments(segments, n=1000):
    """
    将分割段重新采样为固定数量的点，以保持一致的表示。
    
    Args:
        segments: 分割段列表
        n: 重新采样的点数
        
    Returns:
        list: 重新采样后的分割段
    """
    for i, s in enumerate(segments):
        s = np.concatenate((s, s[0:1, :]), axis=0)
        x = np.linspace(0, len(s) - 1, n)
        xp = np.arange(len(s))
        segments[i] = np.concatenate([np.interp(x, xp, s[:, i]) for i in range(2)]).reshape(2, -1).T  # segment xy
    return segments


def non_max_suppression(
    prediction,
    conf_thres=0.25,
    iou_thres=0.45,
    classes=None,
    agnostic=False,
    multi_label=False,
    labels=(),
    max_det=300,
    nm=0,  # number of masks
):
    """
    Non-Maximum Suppression (NMS) on inference results to reject overlapping detections.

    Returns:
         list of detections, on (n,6) tensor per image [xyxy, conf, cls]
    """
    # Checks
    assert 0 <= conf_thres <= 1, f"Invalid Confidence threshold {conf_thres}, valid values are between 0.0 and 1.0"
    assert 0 <= iou_thres <= 1, f"Invalid IoU {iou_thres}, valid values are between 0.0 and 1.0"
    if isinstance(prediction, (list, tuple)):  # YOLOv5 model in validation model, output = (inference_out, loss_out)
        prediction = prediction[0]  # select only inference output

    device = prediction.device
    mps = "mps" in device.type  # Apple MPS
    if mps:  # MPS not fully supported yet, convert tensors to CPU before NMS
        prediction = prediction.cpu()
    bs = prediction.shape[0]  # batch size
    nc = prediction.shape[2] - nm - 5  # number of classes
    xc = prediction[..., 4] > conf_thres  # candidates

    # Settings
    max_wh = 7680  # (pixels) maximum box width and height
    max_nms = 30000  # maximum number of boxes into torchvision.ops.nms()
    time_limit = 0.5 + 0.05 * bs  # seconds to quit after
    redundant = True  # require redundant detections
    multi_label &= nc > 1  # multiple labels per box (adds 0.5ms/img)
    merge = False  # use merge-NMS

    t = time.time()
    mi = 5 + nc  # mask start index
    output = [torch.zeros((0, 6 + nm), device=prediction.device)] * bs
    for xi, x in enumerate(prediction):  # image index, image inference
        # Apply constraints
        x = x[xc[xi]]  # confidence

        # Cat apriori labels if autolabelling
        if labels and len(labels[xi]):
            lb = labels[xi]
            v = torch.zeros((len(lb), nc + nm + 5), device=x.device)
            v[:, :4] = lb[:, 1:5]  # box
            v[:, 4] = 1.0  # conf
            v[range(len(lb)), lb[:, 0].long() + 5] = 1.0  # cls
            x = torch.cat((x, v), 0)

        # If none remain process next image
        if not x.shape[0]:
            continue

        # Compute conf
        x[:, 5:] *= x[:, 4:5]  # conf = obj_conf * cls_conf

        # Box/Mask
        box = xywh2xyxy(x[:, :4])  # center_x, center_y, width, height) to (x1, y1, x2, y2)
        mask = x[:, mi:]  # zero columns if no masks

        # Detections matrix nx6 (xyxy, conf, cls)
        if multi_label:
            i, j = (x[:, 5:mi] > conf_thres).nonzero(as_tuple=False).T
            x = torch.cat((box[i], x[i, 5 + j, None], j[:, None].float(), mask[i]), 1)
        else:  # best class only
            conf, j = x[:, 5:mi].max(1, keepdim=True)
            x = torch.cat((box, conf, j.float(), mask), 1)[conf.view(-1) > conf_thres]

        # Filter by class
        if classes is not None:
            x = x[(x[:, 5:6] == torch.tensor(classes, device=x.device)).any(1)]

        # Check shape
        n = x.shape[0]  # number of boxes
        if not n:  # no boxes
            continue
        x = x[x[:, 4].argsort(descending=True)[:max_nms]]  # sort by confidence and remove excess boxes

        # Batched NMS
        c = x[:, 5:6] * (0 if agnostic else max_wh)  # classes
        boxes, scores = x[:, :4] + c, x[:, 4]  # boxes (offset by class), scores
        i = torchvision.ops.nms(boxes, scores, iou_thres)  # NMS
        i = i[:max_det]  # limit detections
        if merge and (1 < n < 3e3):  # Merge NMS (boxes merged using weighted mean)
            # update boxes as boxes(i,4) = weights(i,n) * boxes(n,4)
            iou = box_iou(boxes[i], boxes) > iou_thres  # iou matrix
            weights = iou * scores[None]  # box weights
            x[i, :4] = torch.mm(weights, x[:, :4]).float() / weights.sum(1, keepdim=True)  # merged boxes
            if redundant:
                i = i[iou.sum(1) > 1]  # require redundancy

        output[xi] = x[i]
        if mps:
            output[xi] = output[xi].to(device)
        if (time.time() - t) > time_limit:
            LOGGER.warning(f"WARNING ⚠️ NMS time limit {time_limit:.3f}s exceeded")
            break  # time limit exceeded

    return output


def emojis(str=""):
    """返回字符串的emoji安全版本，在Windows平台上去除emoji。"""
    return str.encode().decode("ascii", "ignore") if platform.system() == "Windows" else str


def yaml_load(file="data.yaml"):
    """安全地加载并返回由 `file` 参数指定的YAML文件的内容。"""
    with open(file, errors="ignore") as f:
        return yaml.safe_load(f)


def clean_str(s):
    """
    通过将特殊字符替换为下划线来清理字符串，例如 `clean_str('#example!')` 返回 '_example_'。
    """
    return re.sub(pattern="[|@#!¡·$€%&()=?¿^*;:,¨´><+]", repl="_", string=s)


def colorstr(*input):
    """
    使用ANSI转义码为字符串着色，例如 colorstr('blue', 'hello world')。
    
    参考: https://en.wikipedia.org/wiki/ANSI_escape_code
    """
    *args, string = input if len(input) > 1 else ("blue", "bold", input[0])  # 颜色参数，字符串
    colors = {
        "black": "\033[30m",  # 基本颜色
        "red": "\033[31m",
        "green": "\033[32m",
        "yellow": "\033[33m",
        "blue": "\033[34m",
        "magenta": "\033[35m",
        "cyan": "\033[36m",
        "white": "\033[37m",
        "bright_black": "\033[90m",  # 亮色
        "bright_red": "\033[91m",
        "bright_green": "\033[92m",
        "bright_yellow": "\033[93m",
        "bright_blue": "\033[94m",
        "bright_magenta": "\033[95m",
        "bright_cyan": "\033[96m",
        "bright_white": "\033[97m",
        "end": "\033[0m",  # 其他
        "bold": "\033[1m",
        "underline": "\033[4m",
    }
    return "".join(colors[x] for x in args) + f"{string}" + colors["end"]


def yaml_save(file="data.yaml", data=None):
    """安全地将 `data` 保存到指定的YAML文件，将 `Path` 对象转换为字符串；`data` 是一个字典。"""
    if data is None:
        data = {}
    with open(file, "w") as f:
        yaml.safe_dump({k: str(v) if isinstance(v, Path) else v for k, v in data.items()}, f, sort_keys=False)


def unzip_file(file, path=None, exclude=(".DS_Store", "__MACOSX")):
    """将 `file` 解压到 `path`（默认：文件的父目录），排除包含 `exclude` 中任何内容的文件名。"""
    if path is None:
        path = Path(file).parent  # 默认路径
    with ZipFile(file) as zipObj:
        for f in zipObj.namelist():  # 列出zip中的所有归档文件名
            if all(x not in f for x in exclude):
                zipObj.extract(f, path=path)


def url2file(url):
    """将URL字符串转换为有效的文件名，去除协议、域名和任何查询参数。
    
    示例：https://url.com/file.txt?auth -> file.txt
    """
    url = str(Path(url)).replace(":/", "://")  # Pathlib将 :// 转换为 :/
    return Path(urllib.parse.unquote(url)).name.split("?")[0]  # '%2F' 转换为 '/'，分割 https://url.com/file.txt?auth


def download(url, dir=".", unzip=True, delete=True, curl=False, threads=1, retry=3):
    """并发下载并可选择解压文件，支持重试和curl回退。"""

    def download_one(url, dir):
        """从 `url` 下载单个文件到 `dir`，支持重试和可选的curl回退。"""
        success = True
        if os.path.isfile(url):
            f = Path(url)  # 文件名
        else:  # 不存在
            f = dir / Path(url).name
            LOGGER.info(f"Downloading {url} to {f}...")
            for i in range(retry + 1):
                try:
                    torch.hub.download_url_to_file(url, f, progress=threads == 1)  # torch下载
                    success = f.is_file()
                except Exception as e:
                    success = False
                    LOGGER.warning(f"Download failure: {e}")
                if success:
                    break
                elif i < retry:
                    LOGGER.warning(f"⚠️ Download failure, retrying {i + 1}/{retry} {url}...")
                else:
                    LOGGER.warning(f"❌ Failed to download {url}...")

        if unzip and success and (f.suffix == ".gz" or is_zipfile(f) or is_tarfile(f)):
            LOGGER.info(f"Unzipping {f}...")
            if is_zipfile(f):
                unzip_file(f, dir)  # 解压
            elif is_tarfile(f):
                subprocess.run(["tar", "xf", f, "--directory", f.parent], check=True)  # 解压
            elif f.suffix == ".gz":
                subprocess.run(["tar", "xfz", f, "--directory", f.parent], check=True)  # 解压
            if delete:
                f.unlink()  # 删除zip

    dir = Path(dir)
    dir.mkdir(parents=True, exist_ok=True)  # 创建目录
    if threads > 1:
        pool = ThreadPool(threads)
        pool.imap(lambda x: download_one(*x), zip(url, repeat(dir)))  # 多线程
        pool.close()
        pool.join()
    else:
        for u in [url] if isinstance(url, (str, Path)) else url:
            download_one(u, dir)


def check_font(font=FONT, progress=False):
    """确保指定字体存在或从Ultralytics资源下载，可选择显示进度。"""
    font = Path(font)
    file = CONFIG_DIR / font.name
    if not font.exists() and not file.exists():
        url = f"https://github.com/ultralytics/assets/releases/download/v0.0.0/{font.name}"
        LOGGER.info(f"Downloading {url} to {file}...")
        torch.hub.download_url_to_file(url, str(file), progress=progress)


def check_dataset(data, autodownload=True):
    """验证和/或自动下载数据集，返回其配置作为字典。"""
    # 下载（可选）
    extract_dir = ""
    if isinstance(data, (str, Path)) and (is_zipfile(data) or is_tarfile(data)):
        download(data, dir=f"{DATASETS_DIR}/{Path(data).stem}", unzip=True, delete=False, curl=False, threads=1)
        data = next((DATASETS_DIR / Path(data).stem).rglob("*.yaml"))
        extract_dir, autodownload = data.parent, False

    # 读取yaml（可选）
    if isinstance(data, (str, Path)):
        data = yaml_load(data)  # 字典

    # 检查
    for k in "train", "val", "names":
        assert k in data, emojis(f"data.yaml '{k}:' field missing ❌")
    if isinstance(data["names"], (list, tuple)):  # 旧数组格式
        data["names"] = dict(enumerate(data["names"]))  # 转换为字典
    assert all(isinstance(k, int) for k in data["names"].keys()), "data.yaml names keys must be integers, i.e. 2: car"
    data["nc"] = len(data["names"])

    # 解析路径
    path = Path(extract_dir or data.get("path") or "")  # 可选的 'path' 默认为 '.'
    if not path.is_absolute():
        path = (ROOT / path).resolve()
        data["path"] = path  # 下载脚本
    for k in "train", "val", "test":
        if data.get(k):  # 添加路径前缀
            if isinstance(data[k], str):
                x = (path / data[k]).resolve()
                if not x.exists() and data[k].startswith("../"):
                    x = (path / data[k][3:]).resolve()
                data[k] = str(x)
            else:
                data[k] = [str((path / x).resolve()) for x in data[k]]

    # 解析yaml
    train, val, test, s = (data.get(x) for x in ("train", "val", "test", "download"))
    if val:
        val = [Path(x).resolve() for x in (val if isinstance(val, list) else [val])]  # val路径
        if not all(x.exists() for x in val):
            LOGGER.info("\nDataset not found ⚠️, missing paths %s" % [str(x) for x in val if not x.exists()])
            if not s or not autodownload:
                raise Exception("Dataset not found ❌")
            t = time.time()
            if s.startswith("http") and s.endswith(".zip"):  # URL
                f = Path(s).name  # 文件名
                LOGGER.info(f"Downloading {s} to {f}...")
                torch.hub.download_url_to_file(s, f)
                Path(DATASETS_DIR).mkdir(parents=True, exist_ok=True)  # 创建根目录
                unzip_file(f, path=DATASETS_DIR)  # 解压
                Path(f).unlink()  # 删除zip
                r = None  # 成功
            elif s.startswith("bash "):  # bash脚本
                LOGGER.info(f"Running {s} ...")
                r = subprocess.run(s, shell=True)
            else:  # python脚本
                r = exec(s, {"yaml": data})  # 返回None
            dt = f"({round(time.time() - t, 1)}s)"
            s = f"success ✅ {dt}, saved to {colorstr('bold', DATASETS_DIR)}" if r in (0, None) else f"failure {dt} ❌"
            LOGGER.info(f"Dataset download {s}")
    check_font("Arial.ttf" if is_ascii(data["names"]) else "Arial.Unicode.ttf", progress=True)  # 下载字体
    return data  # 字典


def check_amp(model):
    """检查PyTorch AMP功能是否适用于模型，如果AMP正常工作则返回True，否则返回False。"""
    from models.common import AutoShape, DetectMultiBackend

    def amp_allclose(model, im):
        """比较FP32和AMP模型推理输出，确保它们在10%绝对容差内接近。"""
        m = AutoShape(model, verbose=False)  # 模型
        a = m(im).xywhn[0]  # FP32推理
        m.amp = True
        b = m(im).xywhn[0]  # AMP推理
        return a.shape == b.shape and torch.allclose(a, b, atol=0.1)  # 接近10%绝对容差

    prefix = colorstr("AMP: ")
    device = next(model.parameters()).device  # 获取模型设备
    if device.type in ("cpu", "mps"):
        return False  # AMP仅在CUDA设备上使用
    f = ROOT / "data" / "images" / "bus.jpg"  # 检查图像
    im = f if f.exists() else "https://ultralytics.com/images/bus.jpg" if check_online() else np.ones((640, 640, 3))
    try:
        assert amp_allclose(deepcopy(model), im) or amp_allclose(DetectMultiBackend("yolov5n.pt", device), im)
        LOGGER.info(f"{prefix}checks passed ✅")
        return True
    except Exception:
        help_url = "https://github.com/ultralytics/yolov5/issues/7908"
        LOGGER.warning(f"{prefix}checks failed ❌, disabling Automatic Mixed Precision. See {help_url}")
        return False


def check_online():
    """通过尝试创建到 \"*******\" 端口443的连接来检查互联网连接，如果第一次尝试失败则重试一次。"""
    import socket

    def run_once():
        """通过尝试创建到 \"*******\" 端口443的连接来检查互联网连接。"""
        try:
            socket.create_connection(("*******", 443), 5)  # 检查主机可访问性
            return True
        except OSError:
            return False

    return run_once() or run_once()  # 检查两次以增强对间歇性连接问题的鲁棒性

def clip_segments(segments, shape):
    """Clips segment coordinates (xy1, xy2, ...) to an image's boundaries given its shape (height, width)."""
    if isinstance(segments, torch.Tensor):  # faster individually
        segments[:, 0].clamp_(0, shape[1])  # x
        segments[:, 1].clamp_(0, shape[0])  # y
    else:  # np.array (faster grouped)
        segments[:, 0] = segments[:, 0].clip(0, shape[1])  # x
        segments[:, 1] = segments[:, 1].clip(0, shape[0])  # y
        
def scale_segments(img1_shape, segments, img0_shape, ratio_pad=None, normalize=False):
    """Rescales segment coordinates from img1_shape to img0_shape, optionally normalizing them with custom padding."""
    if ratio_pad is None:  # calculate from img0_shape
        gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
        pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
    else:
        gain = ratio_pad[0][0]
        pad = ratio_pad[1]

    segments[:, 0] -= pad[0]  # x padding
    segments[:, 1] -= pad[1]  # y padding
    segments /= gain
    clip_segments(segments, img0_shape)
    if normalize:
        segments[:, 0] /= img0_shape[1]  # width
        segments[:, 1] /= img0_shape[0]  # height
    return segments
