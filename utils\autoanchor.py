# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license
"""AutoAnchor utils - 自动锚点工具模块。"""

import random

import numpy as np
import torch
import yaml
from tqdm import tqdm
TQDM_BAR_FORMAT = "{l_bar}{bar:10}{r_bar}"  # tqdm bar format
from utils import TryExcept
from utils.segment.general import LOGGER, TQDM_BAR_FORMAT, colorstr

PREFIX = colorstr("AutoAnchor: ")


def check_anchor_order(m):
    """在YOLOv5的Detect()模块中，检查并根据需要纠正锚点顺序与步幅（stride）的关系。"""
    a = m.anchors.prod(-1).mean(-1).view(-1)  # 每个输出层的平均锚点面积
    da = a[-1] - a[0]  # 锚点面积的差值
    ds = m.stride[-1] - m.stride[0]  # 步幅的差值
    if da and (da.sign() != ds.sign()):  # 检查符号是否一致，判断顺序是否相同
        LOGGER.info(f"{PREFIX}正在反转锚点顺序")
        m.anchors[:] = m.anchors.flip(0)


@TryExcept(f"{PREFIX}ERROR")
def check_anchors(dataset, model, thr=4.0, imgsz=640):
    """评估锚点与数据集的拟合程度，并在必要时进行调整，支持自定义阈值和图像大小。"""
    m = model.module.model[-1] if hasattr(model, "module") else model.model[-1]  # Detect()模块
    shapes = imgsz * dataset.shapes / dataset.shapes.max(1, keepdims=True)
    scale = np.random.uniform(0.9, 1.1, size=(shapes.shape[0], 1))  # 增强尺度
    wh = torch.tensor(np.concatenate([l[:, 3:5] * s for s, l in zip(shapes * scale, dataset.labels)])).float()  # 宽高

    def metric(k):  # 计算度量
        """为YOLOv5锚点评估计算比率度量、高于阈值的锚点数和最佳可能召回率。"""
        r = wh[:, None] / k[None]
        x = torch.min(r, 1 / r).min(2)[0]  # 比率度量
        best = x.max(1)[0]  # 最佳x
        aat = (x > 1 / thr).float().sum(1).mean()  # 高于阈值的锚点数
        bpr = (best > 1 / thr).float().mean()  # 最佳可能召回率
        return bpr, aat

    stride = m.stride.to(m.anchors.device).view(-1, 1, 1)  # 模型步幅
    anchors = m.anchors.clone() * stride  # 当前锚点
    bpr, aat = metric(anchors.cpu().view(-1, 2))
    s = f"\n{PREFIX}{aat:.2f} 锚点/目标, {bpr:.3f} 最佳可能召回率 (BPR). "
    if bpr > 0.98:  # 重新计算的阈值
        LOGGER.info(f"{s}当前锚点与数据集拟合良好 ✅")
    else:
        LOGGER.info(f"{s}锚点与数据集拟合不佳 ⚠️, 尝试改进...")
        na = m.anchors.numel() // 2  # 锚点数量
        anchors = kmean_anchors(dataset, n=na, img_size=imgsz, thr=thr, gen=1000, verbose=False)
        new_bpr = metric(anchors)[0]
        if new_bpr > bpr:  # 替换锚点
            anchors = torch.tensor(anchors, device=m.anchors.device).type_as(m.anchors)
            m.anchors[:] = anchors.clone().view_as(m.anchors)
            check_anchor_order(m)  # 必须在像素空间（而非网格空间）
            m.anchors /= stride
            s = f"{PREFIX}完成 ✅ (可选: 更新模型 *.yaml 文件以在将来使用这些锚点)"
        else:
            s = f"{PREFIX}完成 ⚠️ (原始锚点优于新锚点，继续使用原始锚点)"
        LOGGER.info(s)


def kmean_anchors(dataset="./data/coco128.yaml", n=9, img_size=640, thr=4.0, gen=1000, verbose=True):
    """
    从训练数据集创建经过k-means进化的锚点。

    参数:
        dataset: 数据集 *.yaml 文件路径，或已加载的数据集
        n: 锚点数量
        img_size: 用于训练的图像大小
        thr: 锚点-标签宽高比阈值超参数 hyp['anchor_t']，用于训练，默认=4.0
        gen: 使用遗传算法进化锚点的代数
        verbose: 打印所有结果

    返回:
        k: 经过k-means进化的锚点

    用法:
        from utils.autoanchor import *; _ = kmean_anchors()
    """
    from scipy.cluster.vq import kmeans

    npr = np.random
    thr = 1 / thr

    def metric(k, wh):  # compute metrics
        """Computes ratio metric, anchors above threshold, and best possible recall for YOLOv5 anchor evaluation."""
        r = wh[:, None] / k[None]
        x = torch.min(r, 1 / r).min(2)[0]  # ratio metric
        # x = wh_iou(wh, torch.tensor(k))  # iou metric
        return x, x.max(1)[0]  # x, best_x

    def anchor_fitness(k):  # mutation fitness
        """Evaluates fitness of YOLOv5 anchors by computing recall and ratio metrics for an anchor evolution process."""
        _, best = metric(torch.tensor(k, dtype=torch.float32), wh)
        return (best * (best > thr).float()).mean()  # fitness

    def print_results(k, verbose=True):
        """Sorts and logs kmeans-evolved anchor metrics and best possible recall values for YOLOv5 anchor evaluation."""
        k = k[np.argsort(k.prod(1))]  # sort small to large
        x, best = metric(k, wh0)
        bpr, aat = (best > thr).float().mean(), (x > thr).float().mean() * n  # best possible recall, anch > thr
        s = (
            f"{PREFIX}thr={thr:.2f}: {bpr:.4f} best possible recall, {aat:.2f} anchors past thr\n"
            f"{PREFIX}n={n}, img_size={img_size}, metric_all={x.mean():.3f}/{best.mean():.3f}-mean/best, "
            f"past_thr={x[x > thr].mean():.3f}-mean: "
        )
        for x in k:
            s += "%i,%i, " % (round(x[0]), round(x[1]))
        if verbose:
            LOGGER.info(s[:-2])
        return k

    if isinstance(dataset, str):  # *.yaml file
        with open(dataset, errors="ignore") as f:
            data_dict = yaml.safe_load(f)  # model dict
        from utils.segment.dataloaders import LoadImagesAndLabelsAndMasks

        dataset = LoadImagesAndLabelsAndMasks(data_dict["train"], augment=True, rect=True)

    # 获取标签宽高
    shapes = img_size * dataset.shapes / dataset.shapes.max(1, keepdims=True)
    wh0 = np.concatenate([l[:, 3:5] * s for s, l in zip(shapes, dataset.labels)])  # 宽高

    # 过滤
    i = (wh0 < 3.0).any(1).sum()
    if i:
        LOGGER.info(f"{PREFIX}警告 ⚠️ 发现极小目标: {i} 个标签（共 {len(wh0)} 个）尺寸小于3像素")
    wh = wh0[(wh0 >= 2.0).any(1)].astype(np.float32)  # 过滤 > 2 像素
    # wh = wh * (npr.rand(wh.shape[0], 1) * 0.9 + 0.1)  # 乘以随机尺度 0-1

    # K-means初始化
    try:
        LOGGER.info(f"{PREFIX}在 {len(wh)} 个点上为 {n} 个锚点运行k-means...")
        assert n <= len(wh)  # 应用过度确定约束
        s = wh.std(0)  # 白化的标准差
        k = kmeans(wh / s, n, iter=30)[0] * s  # 点
        assert n == len(k)  # 如果wh不足或过于相似，kmeans可能返回比请求更少的点
    except Exception:
        LOGGER.warning(f"{PREFIX}警告 ⚠️ 从k-means切换到随机初始化策略")
        k = np.sort(npr.rand(n * 2)).reshape(n, 2) * img_size  # 随机初始化
    wh, wh0 = (torch.tensor(x, dtype=torch.float32) for x in (wh, wh0))
    k = print_results(k, verbose=False)

    # 进化
    f, sh, mp, s = anchor_fitness(k), k.shape, 0.9, 0.1  # 适应度, 代数, 变异概率, 标准差
    pbar = tqdm(range(gen), bar_format=TQDM_BAR_FORMAT)  # 进度条
    for _ in pbar:
        v = np.ones(sh)
        while (v == 1).all():  # 变异直到发生变化（防止重复）
            v = ((npr.random(sh) < mp) * random.random() * npr.randn(*sh) * s + 1).clip(0.3, 3.0)
        kg = (k.copy() * v).clip(min=2.0)
        fg = anchor_fitness(kg)
        if fg > f:
            f, k = fg, kg.copy()
            pbar.desc = f"{PREFIX}使用遗传算法进化锚点: 适应度 = {f:.4f}"
            if verbose:
                print_results(k, verbose)

    return print_results(k).astype(np.float32)
