# -*- coding: utf-8 -*-
"""
PixelAdapter
------------
将来自 YOLOv5 颈部的多尺度特征 (P3/P4/P5) 适配为 SegFormer/Mask2Former 风格 head 所需的：
- value:         [B, \sum_l Hl*Wl, C]
- spatial_shapes:[L, 2]，例如 [(20,20),(40,40),(80,80)]
- level_start_index: [L]，各尺度起始下标偏移
- valid_ratios:  [B, L, 2]，若无 padding 可置 1
- mask_features: [B, C, Hm, Wm]（例如 160x160），用于与 mask_embed 做点积生成高分辨率掩码

约定：
- 输入尺度顺序：P3(80x80)、P4(40x40)、P5(20x20)
- 输出拼接顺序：P5 → P4 → P3（从小到大），以便 level_start_index = [0, 400, 2000]
"""

import math
from typing import List, Tuple

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.nn.init as init


def _build_2d_sincos_pos_embed(h: int, w: int, dim: int, device=None, dtype=torch.float32) -> torch.Tensor:
    """
    生成 2D 正余弦位置编码（与 ViT/DETR 常见实现一致），shape: [h*w, dim]
    """
    if dim % 4 != 0:
        raise ValueError(f"pos embed dim {dim} must be divisible by 4.")

    y, x = torch.meshgrid(
        torch.arange(h, device=device, dtype=dtype),
        torch.arange(w, device=device, dtype=dtype),
        indexing="ij",
    )
    omega = torch.arange(dim // 4, device=device, dtype=dtype)
    omega = 1.0 / (10000 ** (omega / (dim // 4)))

    # [h,w,dim/2]
    out_y = torch.einsum("hw,c->hwc", y, omega)
    out_x = torch.einsum("hw,c->hwc", x, omega)

    pos = torch.cat([torch.sin(out_x), torch.cos(out_x), torch.sin(out_y), torch.cos(out_y)], dim=-1)  # [h,w,dim]
    pos = pos.reshape(h * w, dim)
    return pos


class PixelAdapter(nn.Module):
    """
    将多尺度特征对齐到统一通道 C，并组织为 transformer 可消费的 memory。
    默认输入每层通道 = 256；如不同，可在构造时传 in_channels。
    """

    def __init__(
        self,
        embed_dim: int = 256,
        mask_h: int = 160,
        mask_w: int = 160,
        in_channels: Tuple[int, int, int] = (256, 256, 256),  # (P3, P4, P5)
        use_level_embed: bool = True,
        add_pos_embed: bool = True,
    ):
        super().__init__()
        self.embed_dim = embed_dim
        self.mask_h, self.mask_w = mask_h, mask_w
        self.use_level_embed = use_level_embed
        self.add_pos_embed = add_pos_embed

        # 1x1 投影到统一通道 C
        self.proj_p3 = nn.Conv2d(in_channels[0], embed_dim, kernel_size=1, bias=True)
        self.proj_p4 = nn.Conv2d(in_channels[1], embed_dim, kernel_size=1, bias=True)
        self.proj_p5 = nn.Conv2d(in_channels[2], embed_dim, kernel_size=1, bias=True)

        # 用 P3 生成高分辨率 mask_features（或可融合 P3/P4 再上采样）
        self.mask_proj = nn.Conv2d(in_channels[0], embed_dim, kernel_size=1, bias=True)

        # 学习的 level embedding（可提高多尺度可分性）
        if use_level_embed:
            self.level_embed = nn.Parameter(torch.zeros(3, embed_dim))  # L=3
            init.trunc_normal_(self.level_embed, std=0.02)

    @staticmethod
    def _flatten_and_permute(x: torch.Tensor) -> torch.Tensor:
        """
        [B, C, H, W] -> [B, H*W, C]
        """
        b, c, h, w = x.shape
        return x.flatten(2).transpose(1, 2).contiguous()

    def forward(self, feats: List[torch.Tensor]):
        """
        Args:
            feats: [P3, P4, P5] = [[B, C3, 80,80], [B, C4, 40,40], [B, C5, 20,20]]
        Returns:
            value:             [B, (400+1600+6400), C]
            spatial_shapes:    [3, 2] -> [[20,20],[40,40],[80,80]]
            level_start_index: [3]    -> [0, 400, 2000]
            valid_ratios:      [B, 3, 2]（此处默认全1）
            mask_features:     [B, C, mask_h, mask_w]（通常 160x160）
        """
        assert len(feats) == 3, "PixelAdapter 期望输入 [P3, P4, P5] 三个尺度"

        p3, p4, p5 = feats  # [B, C, 80,80], [B, C, 40,40], [B, C, 20,20]
        b = p3.size(0)
        device = p3.device
        dtype = p3.dtype

        # 1) 对齐通道
        p3e = self.proj_p3(p3)  # [B,C,80,80]
        p4e = self.proj_p4(p4)  # [B,C,40,40]
        p5e = self.proj_p5(p5)  # [B,C,20,20]

        # 2) 构造 memory 拼接顺序：P5 → P4 → P3（从小到大）
        #    并可选加入每层 level embed 和 2D 正余弦 pos embed（与 MSDeformAttn 兼容的表征）
        def with_embed_and_pos(x: torch.Tensor, level_idx: int) -> torch.Tensor:
            # x: [B, C, H, W]
            h, w = x.shape[-2:]
            x_flat = self._flatten_and_permute(x)  # [B, H*W, C]
            if self.use_level_embed:
                x_flat = x_flat + self.level_embed[level_idx].view(1, 1, -1)
            if self.add_pos_embed:
                pos = _build_2d_sincos_pos_embed(h, w, self.embed_dim, device=device, dtype=dtype)  # [H*W, C]
                x_flat = x_flat + pos.view(1, h * w, self.embed_dim).to(dtype)
            return x_flat

        v5 = with_embed_and_pos(p5e, level_idx=0)  # [B, 400, C]
        v4 = with_embed_and_pos(p4e, level_idx=1)  # [B, 1600, C]
        v3 = with_embed_and_pos(p3e, level_idx=2)  # [B, 6400, C]

        value = torch.cat([v5, v4, v3], dim=1)  # [B, 8400, C]

        # 3) spatial_shapes / level_start_index / valid_ratios（无 padding 情况）
        spatial_shapes = torch.as_tensor(
            [[20, 20], [40, 40], [80, 80]], device=device, dtype=torch.long
        )  # [3,2]
        level_start_index = torch.as_tensor([0, 20 * 20, 20 * 20 + 40 * 40], device=device, dtype=torch.long)
        valid_ratios = torch.ones((b, 3, 2), device=device, dtype=value.dtype)  # [B,3,2]

        # 4) 高分辨率 mask_features：用 P3 走一个 1x1 再上采样到 (mask_h, mask_w)
        mask_features = self.mask_proj(p3)  # [B,C,80,80]
        mask_features = F.interpolate(
            mask_features, size=(self.mask_h, self.mask_w), mode="bilinear", align_corners=False
        )  # [B,C,mask_h,mask_w]

        return value, spatial_shapes, level_start_index, valid_ratios, mask_features
