Image: tile_0068.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8525
  Bounding Box: [77.00, 1734.40, 342.20, 2038.40]
  Mask Area: 349 pixels
  Mask Ratio: 0.0124
  Mask BBox: [11, 140, 29, 163]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8486
  Bounding Box: [1264.00, 1031.20, 1411.20, 1303.20]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [103, 85, 114, 103]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8423
  Bounding Box: [122.40, 1561.60, 309.60, 1766.40]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [14, 126, 28, 140]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8394
  Bounding Box: [876.80, 579.20, 1046.40, 794.40]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [73, 50, 85, 66]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8359
  Bounding Box: [657.60, 182.40, 899.20, 491.60]
  Mask Area: 349 pixels
  Mask Ratio: 0.0124
  Mask BBox: [56, 19, 74, 42]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8320
  Bounding Box: [933.60, 1391.20, 1132.00, 1862.40]
  Mask Area: 430 pixels
  Mask Ratio: 0.0152
  Mask BBox: [77, 113, 92, 149]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8242
  Bounding Box: [9.40, 338.40, 205.60, 530.40]
  Mask Area: 165 pixels
  Mask Ratio: 0.0058
  Mask BBox: [5, 31, 20, 45]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8232
  Bounding Box: [774.40, 1636.80, 956.80, 1816.00]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [65, 132, 76, 145]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8223
  Bounding Box: [0.00, 0.00, 154.60, 288.40]
  Mask Area: 237 pixels
  Mask Ratio: 0.0084
  Mask BBox: [3, 4, 16, 26]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8218
  Bounding Box: [1700.80, 1750.40, 1937.60, 1984.00]
  Mask Area: 247 pixels
  Mask Ratio: 0.0088
  Mask BBox: [137, 141, 154, 158]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8154
  Bounding Box: [0.00, 1272.80, 158.40, 1450.40]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [4, 105, 16, 117]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8154
  Bounding Box: [472.40, 552.80, 750.00, 702.40]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [41, 48, 59, 58]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.8145
  Bounding Box: [764.80, 1798.40, 929.60, 2016.00]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [64, 145, 76, 160]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.8130
  Bounding Box: [284.60, 0.00, 400.40, 162.80]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [27, 4, 35, 16]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.8115
  Bounding Box: [138.00, 985.60, 342.00, 1142.40]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [15, 81, 28, 93]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.8105
  Bounding Box: [1257.60, 600.80, 1448.00, 820.80]
  Mask Area: 192 pixels
  Mask Ratio: 0.0068
  Mask BBox: [103, 51, 117, 68]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.8096
  Bounding Box: [525.20, 1232.00, 718.80, 1470.40]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [46, 101, 60, 117]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.8076
  Bounding Box: [11.15, 511.20, 148.60, 656.80]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [5, 44, 15, 55]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.8062
  Bounding Box: [1090.40, 218.60, 1255.20, 444.00]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [90, 22, 101, 37]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.8008
  Bounding Box: [897.60, 1828.80, 1148.80, 2046.40]
  Mask Area: 268 pixels
  Mask Ratio: 0.0095
  Mask BBox: [75, 147, 93, 163]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7959
  Bounding Box: [451.20, 1886.40, 606.40, 2048.00]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [40, 152, 51, 164]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7871
  Bounding Box: [1124.00, 0.00, 1264.80, 217.00]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [92, 4, 102, 20]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7861
  Bounding Box: [331.60, 1137.60, 532.40, 1332.80]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [30, 93, 45, 106]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7861
  Bounding Box: [448.40, 87.30, 688.40, 367.20]
  Mask Area: 269 pixels
  Mask Ratio: 0.0095
  Mask BBox: [40, 11, 57, 32]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7842
  Bounding Box: [1264.80, 43.80, 1423.20, 223.80]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [103, 8, 115, 21]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7798
  Bounding Box: [1052.00, 438.00, 1256.80, 733.20]
  Mask Area: 218 pixels
  Mask Ratio: 0.0077
  Mask BBox: [87, 39, 102, 61]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7769
  Bounding Box: [748.80, 1091.20, 926.40, 1318.40]
  Mask Area: 182 pixels
  Mask Ratio: 0.0064
  Mask BBox: [63, 90, 76, 106]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7764
  Bounding Box: [4.10, 1120.00, 168.00, 1284.80]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [5, 92, 17, 103]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7754
  Bounding Box: [876.00, 326.40, 1044.00, 536.00]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [73, 30, 85, 45]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7754
  Bounding Box: [178.20, 1282.40, 302.20, 1426.40]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [18, 105, 27, 115]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7686
  Bounding Box: [1491.20, 1065.60, 1657.60, 1406.40]
  Mask Area: 245 pixels
  Mask Ratio: 0.0087
  Mask BBox: [121, 88, 133, 113]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7686
  Bounding Box: [1876.80, 1065.60, 2048.00, 1356.80]
  Mask Area: 238 pixels
  Mask Ratio: 0.0084
  Mask BBox: [151, 88, 164, 109]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7676
  Bounding Box: [776.80, 450.80, 927.20, 642.00]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [65, 40, 76, 54]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7666
  Bounding Box: [579.20, 356.60, 711.20, 540.00]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [50, 32, 58, 46]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7666
  Bounding Box: [1269.60, 1792.00, 1381.60, 1987.20]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [104, 144, 111, 159]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7661
  Bounding Box: [423.20, 698.00, 657.60, 1063.20]
  Mask Area: 438 pixels
  Mask Ratio: 0.0155
  Mask BBox: [38, 59, 55, 87]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7632
  Bounding Box: [396.40, 0.00, 590.80, 178.00]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [35, 4, 49, 17]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7598
  Bounding Box: [329.80, 1315.20, 454.40, 1416.00]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [30, 107, 39, 114]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7588
  Bounding Box: [1854.40, 796.00, 1998.40, 906.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [149, 67, 158, 74]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7559
  Bounding Box: [148.00, 434.40, 454.40, 833.60]
  Mask Area: 486 pixels
  Mask Ratio: 0.0172
  Mask BBox: [16, 38, 39, 69]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7539
  Bounding Box: [1952.00, 1611.20, 2044.80, 1761.60]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [157, 130, 163, 141]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7422
  Bounding Box: [1635.20, 1066.40, 1772.80, 1327.20]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [132, 88, 141, 107]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7422
  Bounding Box: [589.60, 1724.80, 782.40, 2041.60]
  Mask Area: 246 pixels
  Mask Ratio: 0.0087
  Mask BBox: [51, 139, 65, 163]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7407
  Bounding Box: [1726.40, 1609.60, 1832.00, 1747.20]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [139, 130, 147, 140]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7393
  Bounding Box: [698.40, 1272.80, 952.80, 1458.40]
  Mask Area: 197 pixels
  Mask Ratio: 0.0070
  Mask BBox: [59, 104, 78, 117]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7388
  Bounding Box: [1244.80, 216.80, 1379.20, 355.20]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [102, 21, 111, 31]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7334
  Bounding Box: [299.20, 176.40, 502.40, 485.20]
  Mask Area: 264 pixels
  Mask Ratio: 0.0094
  Mask BBox: [28, 18, 43, 39]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7261
  Bounding Box: [126.00, 1360.80, 302.80, 1556.00]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [14, 111, 26, 125]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7231
  Bounding Box: [164.60, 268.80, 357.00, 464.80]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [17, 25, 31, 40]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7231
  Bounding Box: [1141.60, 1785.60, 1276.00, 2022.40]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [94, 145, 103, 161]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.7227
  Bounding Box: [1006.40, 363.60, 1120.00, 512.40]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [83, 33, 91, 44]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.7207
  Bounding Box: [1611.20, 620.80, 1688.00, 866.40]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [130, 53, 135, 71]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.7168
  Bounding Box: [126.70, 896.00, 218.20, 1006.40]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [14, 74, 21, 82]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.7124
  Bounding Box: [1226.40, 872.80, 1368.80, 1104.80]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [100, 73, 110, 90]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.7104
  Bounding Box: [797.60, 1500.80, 938.40, 1664.00]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [67, 122, 76, 133]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.7085
  Bounding Box: [658.40, 828.80, 964.00, 1062.40]
  Mask Area: 299 pixels
  Mask Ratio: 0.0106
  Mask BBox: [57, 69, 79, 86]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.7031
  Bounding Box: [0.00, 867.20, 134.40, 1102.40]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [4, 72, 13, 90]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6987
  Bounding Box: [1809.60, 855.20, 1976.00, 1052.00]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [146, 71, 158, 86]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6973
  Bounding Box: [146.60, 1164.80, 311.40, 1305.60]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [16, 95, 28, 105]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6968
  Bounding Box: [1120.00, 1263.20, 1320.00, 1676.80]
  Mask Area: 355 pixels
  Mask Ratio: 0.0126
  Mask BBox: [92, 103, 107, 134]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6963
  Bounding Box: [1000.80, 151.10, 1077.60, 307.60]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [83, 17, 88, 28]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6899
  Bounding Box: [1600.00, 900.00, 1792.00, 1066.40]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [129, 75, 143, 86]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6895
  Bounding Box: [143.60, 633.60, 344.00, 890.40]
  Mask Area: 184 pixels
  Mask Ratio: 0.0065
  Mask BBox: [16, 54, 30, 73]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6870
  Bounding Box: [331.00, 1835.20, 470.00, 2046.40]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [30, 149, 40, 163]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6758
  Bounding Box: [1479.20, 727.20, 1614.40, 895.20]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [120, 61, 130, 73]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6758
  Bounding Box: [1345.60, 1590.40, 1460.80, 1804.80]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [110, 129, 118, 144]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6748
  Bounding Box: [653.20, 1697.60, 779.20, 1857.60]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [56, 137, 63, 149]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6743
  Bounding Box: [1592.00, 1357.60, 1780.80, 1557.60]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [129, 111, 143, 125]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6738
  Bounding Box: [1389.60, 691.20, 1532.00, 883.20]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [113, 58, 123, 72]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6738
  Bounding Box: [1824.00, 440.00, 2044.80, 749.60]
  Mask Area: 264 pixels
  Mask Ratio: 0.0094
  Mask BBox: [147, 39, 163, 61]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6641
  Bounding Box: [0.00, 635.20, 170.80, 937.60]
  Mask Area: 266 pixels
  Mask Ratio: 0.0094
  Mask BBox: [4, 54, 17, 77]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6577
  Bounding Box: [529.20, 421.60, 594.00, 549.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [46, 37, 50, 45]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6562
  Bounding Box: [570.40, 1500.80, 783.20, 1699.20]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [49, 122, 65, 136]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6514
  Bounding Box: [49.85, 1510.40, 177.40, 1609.60]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [8, 122, 17, 128]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6509
  Bounding Box: [1668.80, 1713.60, 1796.80, 1825.60]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [135, 138, 144, 146]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6499
  Bounding Box: [491.60, 1843.20, 585.20, 1907.20]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [43, 148, 49, 152]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6484
  Bounding Box: [1451.20, 1564.80, 1694.40, 2048.00]
  Mask Area: 646 pixels
  Mask Ratio: 0.0229
  Mask BBox: [118, 127, 136, 165]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6455
  Bounding Box: [297.60, 1673.60, 457.60, 1843.20]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [28, 135, 39, 146]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6436
  Bounding Box: [1229.60, 1622.40, 1396.00, 1820.80]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [101, 131, 113, 146]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6436
  Bounding Box: [1343.20, 880.80, 1509.60, 1149.60]
  Mask Area: 198 pixels
  Mask Ratio: 0.0070
  Mask BBox: [109, 74, 121, 93]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6431
  Bounding Box: [712.40, 1487.20, 799.20, 1628.80]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [60, 121, 66, 131]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6318
  Bounding Box: [1804.80, 151.00, 2032.00, 293.80]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [145, 16, 161, 26]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6294
  Bounding Box: [1622.40, 1224.00, 1737.60, 1374.40]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [131, 100, 139, 111]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6265
  Bounding Box: [242.40, 1488.00, 356.40, 1604.80]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [23, 121, 31, 129]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6182
  Bounding Box: [943.20, 713.60, 1220.00, 1025.60]
  Mask Area: 352 pixels
  Mask Ratio: 0.0125
  Mask BBox: [78, 60, 98, 83]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6118
  Bounding Box: [1686.40, 264.00, 1900.80, 462.40]
  Mask Area: 203 pixels
  Mask Ratio: 0.0072
  Mask BBox: [136, 25, 152, 40]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.6035
  Bounding Box: [1856.00, 1370.40, 1974.40, 1616.00]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [149, 112, 158, 130]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.6030
  Bounding Box: [153.40, 0.60, 300.60, 155.00]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [16, 5, 27, 16]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.5996
  Bounding Box: [1723.20, 1974.40, 1838.40, 2032.00]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [139, 159, 147, 162]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.5986
  Bounding Box: [1724.80, 0.00, 1945.60, 196.00]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [141, 4, 155, 18]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.5908
  Bounding Box: [1683.20, 769.60, 1827.20, 923.20]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [136, 65, 144, 76]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.5898
  Bounding Box: [1094.40, 957.60, 1238.40, 1066.40]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [90, 79, 99, 87]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5864
  Bounding Box: [736.80, 650.00, 898.40, 870.40]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [62, 55, 74, 71]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5835
  Bounding Box: [1880.00, 286.00, 2004.80, 434.80]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [151, 27, 160, 37]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5835
  Bounding Box: [1310.40, 1302.40, 1475.20, 1593.60]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [107, 106, 119, 128]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5806
  Bounding Box: [1681.60, 0.00, 1771.20, 94.10]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [136, 4, 142, 11]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5767
  Bounding Box: [1830.40, 686.00, 1936.00, 819.20]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [147, 58, 155, 67]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5757
  Bounding Box: [1168.00, 694.40, 1288.00, 860.80]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [96, 59, 103, 71]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5718
  Bounding Box: [680.80, 53.20, 822.40, 185.60]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [58, 9, 68, 18]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5718
  Bounding Box: [269.60, 904.00, 385.60, 998.40]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [26, 75, 34, 81]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5654
  Bounding Box: [961.60, 68.50, 1049.60, 164.40]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [80, 10, 85, 15]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5654
  Bounding Box: [2.60, 362.80, 194.20, 643.60]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [5, 33, 19, 54]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5649
  Bounding Box: [1538.40, 1283.20, 1654.40, 1416.00]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [127, 105, 133, 113]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5620
  Bounding Box: [1548.80, 2.90, 1670.40, 99.90]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [125, 5, 134, 9]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5620
  Bounding Box: [1223.20, 1976.00, 1354.40, 2036.80]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [100, 159, 109, 163]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5605
  Bounding Box: [1790.40, 1261.60, 1892.80, 1392.80]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [144, 103, 151, 112]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5601
  Bounding Box: [1956.80, 1452.00, 2036.80, 1590.40]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [157, 118, 163, 128]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5601
  Bounding Box: [779.20, 0.45, 1001.60, 130.80]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [65, 5, 82, 14]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5601
  Bounding Box: [945.60, 1058.40, 1256.00, 1396.00]
  Mask Area: 434 pixels
  Mask Ratio: 0.0154
  Mask BBox: [78, 87, 102, 113]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5591
  Bounding Box: [1897.60, 277.60, 2035.20, 476.80]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [153, 26, 162, 41]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5581
  Bounding Box: [1307.20, 1832.00, 1422.40, 2046.40]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [107, 148, 115, 163]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5562
  Bounding Box: [888.00, 135.70, 1025.60, 333.20]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [74, 15, 84, 30]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5527
  Bounding Box: [478.80, 1043.20, 759.60, 1232.00]
  Mask Area: 181 pixels
  Mask Ratio: 0.0064
  Mask BBox: [42, 86, 63, 100]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5493
  Bounding Box: [1916.80, 9.00, 2048.00, 129.00]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [154, 5, 164, 14]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5493
  Bounding Box: [1920.00, 1956.80, 2035.20, 2036.80]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [154, 157, 162, 163]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5493
  Bounding Box: [1651.20, 350.00, 1788.80, 617.20]
  Mask Area: 187 pixels
  Mask Ratio: 0.0066
  Mask BBox: [133, 32, 143, 52]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5410
  Bounding Box: [1401.60, 497.60, 1595.20, 750.40]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [114, 43, 128, 62]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5396
  Bounding Box: [642.40, 0.00, 756.80, 86.60]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [55, 4, 63, 10]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5361
  Bounding Box: [1333.60, 1868.80, 1437.60, 2048.00]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [109, 150, 116, 164]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.5342
  Bounding Box: [1205.60, 139.70, 1272.80, 240.60]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [99, 15, 103, 22]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.5332
  Bounding Box: [1412.80, 706.80, 1593.60, 908.80]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [115, 60, 128, 74]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.5308
  Bounding Box: [1985.60, 720.00, 2040.00, 902.40]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [160, 61, 163, 74]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.5298
  Bounding Box: [1276.80, 630.00, 1508.80, 844.80]
  Mask Area: 225 pixels
  Mask Ratio: 0.0080
  Mask BBox: [104, 54, 121, 69]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.5254
  Bounding Box: [1691.20, 528.00, 1864.00, 815.20]
  Mask Area: 184 pixels
  Mask Ratio: 0.0065
  Mask BBox: [137, 46, 149, 67]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.5195
  Bounding Box: [368.40, 1396.00, 537.20, 1567.20]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [33, 114, 45, 126]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.5190
  Bounding Box: [1025.60, 633.20, 1094.40, 742.00]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [85, 54, 89, 61]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.5190
  Bounding Box: [648.00, 724.00, 743.20, 847.20]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [55, 61, 61, 70]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.5186
  Bounding Box: [784.00, 1545.60, 944.00, 1792.00]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [66, 125, 76, 143]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.5122
  Bounding Box: [674.40, 1195.20, 762.40, 1267.20]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [57, 98, 63, 102]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.5093
  Bounding Box: [963.20, 152.40, 1088.00, 321.20]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [80, 16, 88, 29]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.5034
  Bounding Box: [97.20, 1657.60, 173.20, 1820.80]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [12, 134, 17, 146]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.5024
  Bounding Box: [454.40, 1288.00, 545.60, 1384.00]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [40, 105, 45, 112]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.5024
  Bounding Box: [1273.60, 1614.40, 1444.80, 1832.00]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [104, 131, 116, 147]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.5020
  Bounding Box: [1188.80, 587.20, 1249.60, 684.80]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [97, 50, 101, 57]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4888
  Bounding Box: [1287.20, 961.60, 1448.80, 1270.40]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [105, 80, 117, 103]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4883
  Bounding Box: [670.40, 720.40, 735.20, 834.40]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [57, 61, 61, 69]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4873
  Bounding Box: [135.50, 868.00, 248.00, 997.60]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [15, 72, 23, 81]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4834
  Bounding Box: [1195.20, 794.40, 1281.60, 892.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [98, 67, 104, 73]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4817
  Bounding Box: [0.00, 1563.20, 131.00, 1924.80]
  Mask Area: 189 pixels
  Mask Ratio: 0.0067
  Mask BBox: [4, 127, 14, 154]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4739
  Bounding Box: [904.00, 1094.40, 1160.00, 1366.40]
  Mask Area: 388 pixels
  Mask Ratio: 0.0137
  Mask BBox: [75, 90, 94, 110]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4731
  Bounding Box: [300.20, 1603.20, 419.60, 1702.40]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [28, 130, 36, 136]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4717
  Bounding Box: [1926.40, 317.40, 2048.00, 524.00]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [155, 29, 164, 44]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4714
  Bounding Box: [756.80, 1100.80, 1019.20, 1323.20]
  Mask Area: 295 pixels
  Mask Ratio: 0.0105
  Mask BBox: [64, 90, 83, 107]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4697
  Bounding Box: [760.00, 860.80, 963.20, 1068.80]
  Mask Area: 191 pixels
  Mask Ratio: 0.0068
  Mask BBox: [64, 72, 79, 86]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4678
  Bounding Box: [336.20, 739.20, 447.60, 944.00]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [31, 62, 37, 77]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4648
  Bounding Box: [1080.80, 1729.60, 1173.60, 1857.60]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [89, 140, 95, 149]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4629
  Bounding Box: [1392.80, 224.20, 1660.80, 611.20]
  Mask Area: 456 pixels
  Mask Ratio: 0.0162
  Mask BBox: [113, 22, 133, 51]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4624
  Bounding Box: [760.00, 1806.40, 1068.80, 2020.80]
  Mask Area: 304 pixels
  Mask Ratio: 0.0108
  Mask BBox: [64, 146, 87, 161]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4619
  Bounding Box: [1744.00, 926.40, 1881.60, 1083.20]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [141, 77, 150, 86]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4534
  Bounding Box: [912.00, 1133.60, 1064.00, 1311.20]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [76, 93, 85, 106]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4529
  Bounding Box: [375.20, 442.00, 513.60, 546.80]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [34, 39, 44, 46]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4490
  Bounding Box: [1958.40, 331.60, 2038.40, 530.80]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [157, 30, 163, 45]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4485
  Bounding Box: [905.60, 1438.40, 971.20, 1569.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [75, 117, 79, 126]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4456
  Bounding Box: [315.20, 1556.00, 431.20, 1683.20]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [29, 126, 37, 135]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4456
  Bounding Box: [447.60, 1852.80, 603.60, 2006.40]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [39, 149, 50, 160]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4441
  Bounding Box: [0.00, 1368.00, 85.80, 1512.00]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [4, 111, 9, 122]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4421
  Bounding Box: [259.00, 1811.20, 464.80, 2041.60]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [25, 146, 40, 163]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4382
  Bounding Box: [1742.40, 1527.20, 1793.60, 1614.40]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [141, 124, 143, 130]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4368
  Bounding Box: [460.80, 1862.40, 670.40, 2048.00]
  Mask Area: 186 pixels
  Mask Ratio: 0.0066
  Mask BBox: [40, 150, 56, 164]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4321
  Bounding Box: [1047.20, 246.00, 1236.00, 464.40]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [86, 24, 100, 40]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4287
  Bounding Box: [87.00, 282.20, 353.00, 491.20]
  Mask Area: 212 pixels
  Mask Ratio: 0.0075
  Mask BBox: [11, 27, 31, 42]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.4277
  Bounding Box: [592.40, 0.00, 682.00, 85.40]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [51, 4, 57, 10]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.4248
  Bounding Box: [517.60, 1244.80, 825.60, 1465.60]
  Mask Area: 292 pixels
  Mask Ratio: 0.0103
  Mask BBox: [46, 102, 68, 118]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.4211
  Bounding Box: [1557.60, 99.80, 1728.00, 301.00]
  Mask Area: 181 pixels
  Mask Ratio: 0.0064
  Mask BBox: [126, 12, 138, 27]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.4189
  Bounding Box: [1521.60, 1371.20, 1761.60, 1598.40]
  Mask Area: 242 pixels
  Mask Ratio: 0.0086
  Mask BBox: [123, 112, 141, 128]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.4167
  Bounding Box: [319.60, 4.85, 570.00, 171.20]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [29, 5, 48, 17]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.4141
  Bounding Box: [1249.60, 53.00, 1422.40, 331.80]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [102, 9, 115, 29]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.4111
  Bounding Box: [213.40, 191.00, 301.00, 293.00]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [21, 19, 27, 26]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.4087
  Bounding Box: [1107.20, 1250.40, 1230.40, 1383.20]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [91, 102, 100, 112]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.4006
  Bounding Box: [1089.60, 1684.80, 1201.60, 1851.20]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [90, 136, 97, 148]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.4004
  Bounding Box: [1493.60, 1429.60, 1648.00, 1556.00]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [121, 116, 130, 125]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3979
  Bounding Box: [1924.80, 1846.40, 2001.60, 1955.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [155, 149, 160, 156]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3975
  Bounding Box: [1972.80, 888.80, 2043.20, 1028.00]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [159, 74, 163, 84]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3950
  Bounding Box: [807.20, 669.60, 944.80, 852.00]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [68, 57, 77, 70]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3909
  Bounding Box: [804.00, 349.40, 996.00, 610.40]
  Mask Area: 197 pixels
  Mask Ratio: 0.0070
  Mask BBox: [67, 32, 81, 51]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3899
  Bounding Box: [155.20, 1298.40, 306.40, 1508.00]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [17, 106, 27, 121]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3899
  Bounding Box: [1654.40, 282.80, 1846.40, 536.40]
  Mask Area: 226 pixels
  Mask Ratio: 0.0080
  Mask BBox: [134, 27, 148, 45]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3872
  Bounding Box: [1902.40, 1000.80, 2027.20, 1104.80]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [153, 83, 162, 90]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3857
  Bounding Box: [306.00, 1696.00, 474.80, 2003.20]
  Mask Area: 206 pixels
  Mask Ratio: 0.0073
  Mask BBox: [28, 137, 41, 160]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3831
  Bounding Box: [172.80, 1289.60, 405.60, 1417.60]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [18, 105, 35, 114]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3831
  Bounding Box: [172.40, 225.60, 454.00, 467.60]
  Mask Area: 288 pixels
  Mask Ratio: 0.0102
  Mask BBox: [18, 22, 39, 40]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3823
  Bounding Box: [465.20, 1588.80, 598.80, 1848.00]
  Mask Area: 157 pixels
  Mask Ratio: 0.0056
  Mask BBox: [41, 129, 50, 148]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3794
  Bounding Box: [1763.20, 100.80, 2019.20, 293.60]
  Mask Area: 235 pixels
  Mask Ratio: 0.0083
  Mask BBox: [142, 12, 161, 26]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3782
  Bounding Box: [284.40, 788.80, 434.00, 1017.60]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [27, 66, 37, 83]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3730
  Bounding Box: [1111.20, 1668.80, 1208.80, 1806.40]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [91, 135, 98, 145]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3721
  Bounding Box: [1932.80, 1425.60, 2044.80, 1588.80]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [155, 116, 163, 128]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3711
  Bounding Box: [1609.60, 1169.60, 1740.80, 1396.80]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [130, 96, 139, 113]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3657
  Bounding Box: [1016.80, 131.80, 1096.80, 303.00]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [84, 15, 89, 27]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3657
  Bounding Box: [284.80, 1249.60, 362.00, 1360.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [27, 102, 32, 110]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3652
  Bounding Box: [13.45, 278.80, 108.20, 352.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [6, 26, 12, 31]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3647
  Bounding Box: [1905.60, 0.00, 2043.20, 97.00]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [153, 4, 163, 11]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3647
  Bounding Box: [1758.40, 1445.60, 1966.40, 1769.60]
  Mask Area: 279 pixels
  Mask Ratio: 0.0099
  Mask BBox: [142, 117, 157, 142]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3643
  Bounding Box: [1241.60, 1945.60, 1369.60, 2048.00]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [101, 156, 110, 163]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3625
  Bounding Box: [95.00, 1660.80, 201.60, 1785.60]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [12, 134, 19, 143]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3594
  Bounding Box: [1172.80, 95.00, 1296.00, 227.40]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [96, 12, 105, 21]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3574
  Bounding Box: [1273.60, 1820.80, 1408.00, 2022.40]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [104, 147, 113, 161]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3550
  Bounding Box: [624.00, 0.00, 781.60, 124.30]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [53, 4, 65, 13]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3545
  Bounding Box: [783.20, 116.40, 901.60, 196.40]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [66, 14, 74, 18]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3545
  Bounding Box: [992.00, 1809.60, 1280.00, 2043.20]
  Mask Area: 295 pixels
  Mask Ratio: 0.0105
  Mask BBox: [82, 146, 103, 163]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3542
  Bounding Box: [582.40, 303.60, 779.20, 537.20]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [50, 28, 64, 45]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3540
  Bounding Box: [1755.20, 887.20, 1928.00, 1093.60]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [142, 74, 154, 89]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3535
  Bounding Box: [1550.40, 538.40, 1624.00, 639.20]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [126, 47, 130, 53]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3528
  Bounding Box: [955.20, 347.00, 1121.60, 541.20]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [79, 32, 91, 46]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3518
  Bounding Box: [897.60, 1668.80, 974.40, 1787.20]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [75, 135, 80, 143]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3516
  Bounding Box: [1760.00, 1001.60, 1888.00, 1286.40]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [142, 83, 151, 104]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3503
  Bounding Box: [280.60, 888.00, 404.00, 982.40]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [26, 74, 35, 80]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3503
  Bounding Box: [1724.80, 458.80, 2019.20, 750.80]
  Mask Area: 366 pixels
  Mask Ratio: 0.0130
  Mask BBox: [139, 40, 161, 62]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3491
  Bounding Box: [476.40, 1705.60, 587.60, 1843.20]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [42, 138, 49, 147]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3469
  Bounding Box: [988.80, 370.00, 1097.60, 537.20]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [82, 33, 89, 44]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3464
  Bounding Box: [1128.00, 536.40, 1259.20, 698.80]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [93, 46, 102, 58]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3425
  Bounding Box: [1593.60, 331.80, 1667.20, 414.80]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [129, 30, 134, 36]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3416
  Bounding Box: [583.20, 7.60, 664.00, 87.20]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [50, 5, 55, 9]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3408
  Bounding Box: [1872.00, 1244.00, 1968.00, 1397.60]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [151, 102, 157, 113]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3408
  Bounding Box: [870.40, 1441.60, 982.40, 1556.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [72, 117, 78, 125]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3408
  Bounding Box: [1600.00, 1553.60, 1744.00, 1675.20]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [130, 126, 140, 134]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3403
  Bounding Box: [1900.80, 1657.60, 2038.40, 1939.20]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [153, 134, 163, 155]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3359
  Bounding Box: [1672.00, 400.00, 1771.20, 635.20]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [135, 36, 142, 53]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3354
  Bounding Box: [1231.20, 1192.80, 1394.40, 1394.40]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [101, 98, 112, 112]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3354
  Bounding Box: [1849.60, 887.20, 2006.40, 1090.40]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [149, 74, 160, 89]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3347
  Bounding Box: [1745.60, 615.60, 1937.60, 822.40]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [141, 53, 155, 68]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3347
  Bounding Box: [306.40, 1561.60, 462.40, 1721.60]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [28, 126, 40, 138]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3337
  Bounding Box: [1947.20, 1356.80, 2036.80, 1472.00]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [157, 110, 163, 118]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3333
  Bounding Box: [1529.60, 0.00, 1641.60, 87.70]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [124, 4, 132, 9]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3289
  Bounding Box: [1595.20, 1208.00, 1716.80, 1379.20]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [129, 99, 138, 111]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3289
  Bounding Box: [1809.60, 653.20, 1969.60, 826.40]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [146, 58, 157, 68]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3286
  Bounding Box: [1632.00, 605.60, 1712.00, 840.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [132, 52, 137, 69]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3286
  Bounding Box: [1336.00, 0.80, 1452.80, 74.10]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [109, 5, 116, 8]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3252
  Bounding Box: [1240.00, 444.00, 1376.00, 651.20]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [101, 39, 111, 54]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3252
  Bounding Box: [326.20, 1197.60, 499.20, 1439.20]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [31, 98, 42, 116]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3237
  Bounding Box: [1573.60, 1240.00, 1737.60, 1414.40]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [127, 101, 139, 114]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3230
  Bounding Box: [472.80, 1816.00, 576.00, 1892.80]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [41, 146, 48, 151]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3203
  Bounding Box: [1627.20, 827.20, 1825.60, 1052.80]
  Mask Area: 220 pixels
  Mask Ratio: 0.0078
  Mask BBox: [132, 69, 146, 86]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3186
  Bounding Box: [1792.00, 1374.40, 1945.60, 1726.40]
  Mask Area: 241 pixels
  Mask Ratio: 0.0085
  Mask BBox: [144, 112, 155, 138]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3181
  Bounding Box: [1920.00, 1601.60, 2041.60, 1867.20]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [154, 130, 163, 149]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3179
  Bounding Box: [147.60, 963.20, 404.80, 1172.80]
  Mask Area: 184 pixels
  Mask Ratio: 0.0065
  Mask BBox: [16, 80, 35, 95]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3169
  Bounding Box: [1870.40, 1593.60, 2048.00, 1916.80]
  Mask Area: 214 pixels
  Mask Ratio: 0.0076
  Mask BBox: [151, 129, 164, 153]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3167
  Bounding Box: [218.00, 0.00, 410.00, 158.40]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [22, 4, 36, 16]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3157
  Bounding Box: [1430.40, 546.40, 1611.20, 860.00]
  Mask Area: 257 pixels
  Mask Ratio: 0.0091
  Mask BBox: [116, 47, 129, 71]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.3147
  Bounding Box: [1360.00, 251.20, 1452.80, 402.00]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [111, 24, 117, 33]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.3145
  Bounding Box: [12.30, 1556.00, 122.10, 1788.80]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [5, 126, 13, 143]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.3123
  Bounding Box: [1239.20, 7.60, 1316.00, 84.90]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [101, 5, 106, 10]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.3088
  Bounding Box: [1588.80, 314.00, 1662.40, 438.80]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [129, 29, 133, 36]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.3088
  Bounding Box: [1936.00, 1381.60, 2044.80, 1535.20]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [156, 112, 163, 123]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.3081
  Bounding Box: [174.40, 7.55, 306.80, 113.60]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [18, 5, 27, 12]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.3079
  Bounding Box: [1817.60, 1519.20, 2032.00, 1801.60]
  Mask Area: 257 pixels
  Mask Ratio: 0.0091
  Mask BBox: [146, 123, 162, 144]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [1238.40, 505.60, 1364.80, 641.60]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [101, 44, 110, 54]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.3069
  Bounding Box: [496.80, 1817.60, 602.40, 1897.60]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [43, 146, 49, 152]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [1819.20, 796.00, 1998.40, 1031.20]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [147, 67, 159, 84]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.3059
  Bounding Box: [1040.80, 1192.00, 1250.40, 1422.40]
  Mask Area: 198 pixels
  Mask Ratio: 0.0070
  Mask BBox: [86, 98, 101, 115]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.3057
  Bounding Box: [1969.60, 1956.80, 2048.00, 2046.40]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [158, 157, 164, 163]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.3052
  Bounding Box: [68.20, 1497.60, 200.60, 1590.40]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [10, 121, 19, 128]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.3044
  Bounding Box: [1776.00, 434.80, 1859.20, 522.80]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [143, 38, 149, 44]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.3040
  Bounding Box: [1694.40, 744.00, 1841.60, 892.80]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [137, 63, 147, 73]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.3040
  Bounding Box: [275.20, 1012.00, 528.80, 1308.00]
  Mask Area: 282 pixels
  Mask Ratio: 0.0100
  Mask BBox: [26, 84, 45, 106]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.3037
  Bounding Box: [67.00, 1521.60, 199.60, 1625.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [10, 123, 17, 128]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.3030
  Bounding Box: [921.60, 1691.20, 982.40, 1809.60]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [76, 137, 80, 145]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.3025
  Bounding Box: [1137.60, 918.40, 1368.00, 1081.60]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [93, 76, 110, 88]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.2983
  Bounding Box: [1707.20, 1583.20, 1816.00, 1724.80]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [138, 128, 145, 138]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.2974
  Bounding Box: [686.40, 1485.60, 781.60, 1611.20]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [58, 121, 65, 129]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.2966
  Bounding Box: [608.00, 1412.80, 716.80, 1532.80]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [52, 115, 59, 123]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.2959
  Bounding Box: [1803.20, 664.40, 1918.40, 804.80]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [145, 58, 153, 66]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.2959
  Bounding Box: [181.80, 882.40, 273.80, 1002.40]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [19, 73, 25, 80]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2949
  Bounding Box: [192.80, 166.20, 297.60, 285.80]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [20, 17, 27, 26]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2937
  Bounding Box: [1509.60, 1352.00, 1675.20, 1555.20]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [122, 110, 134, 125]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2910
  Bounding Box: [1651.20, 1694.40, 1769.60, 1819.20]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [133, 137, 142, 146]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2910
  Bounding Box: [378.80, 1800.00, 478.00, 1947.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [34, 145, 41, 154]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2903
  Bounding Box: [1937.60, 1576.00, 2048.00, 1822.40]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [156, 128, 164, 146]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2883
  Bounding Box: [912.80, 1056.00, 1039.20, 1147.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [76, 87, 85, 93]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2878
  Bounding Box: [0.00, 1161.60, 147.40, 1289.60]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [3, 95, 15, 104]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2874
  Bounding Box: [1062.40, 81.20, 1145.60, 296.40]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [87, 11, 93, 27]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2861
  Bounding Box: [1467.20, 246.40, 1720.00, 617.60]
  Mask Area: 439 pixels
  Mask Ratio: 0.0156
  Mask BBox: [119, 24, 138, 52]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2849
  Bounding Box: [1865.60, 206.80, 2035.20, 463.60]
  Mask Area: 188 pixels
  Mask Ratio: 0.0067
  Mask BBox: [150, 21, 162, 40]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2834
  Bounding Box: [1001.60, 630.00, 1076.80, 731.60]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [83, 54, 88, 61]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2834
  Bounding Box: [1001.60, 655.60, 1076.80, 757.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [83, 56, 88, 62]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2830
  Bounding Box: [1097.60, 1233.60, 1313.60, 1435.20]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [90, 101, 106, 115]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2830
  Bounding Box: [1371.20, 317.20, 1601.60, 630.00]
  Mask Area: 307 pixels
  Mask Ratio: 0.0109
  Mask BBox: [112, 29, 129, 53]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2827
  Bounding Box: [1573.60, 1547.20, 1753.60, 1720.00]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [130, 125, 140, 138]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [1103.20, 1715.20, 1236.00, 1910.40]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [91, 138, 100, 153]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2815
  Bounding Box: [1656.00, 1574.40, 1835.20, 1731.20]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [134, 127, 147, 139]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2812
  Bounding Box: [1827.20, 0.00, 2048.00, 164.40]
  Mask Area: 191 pixels
  Mask Ratio: 0.0068
  Mask BBox: [147, 4, 164, 16]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2808
  Bounding Box: [337.80, 1314.40, 512.00, 1527.20]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [31, 107, 43, 123]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2803
  Bounding Box: [1812.80, 642.00, 1902.40, 738.80]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [146, 55, 152, 61]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2788
  Bounding Box: [680.40, 14.15, 913.60, 179.80]
  Mask Area: 215 pixels
  Mask Ratio: 0.0076
  Mask BBox: [58, 6, 75, 18]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2783
  Bounding Box: [476.00, 1840.00, 574.40, 1923.20]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [42, 148, 48, 153]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2783
  Bounding Box: [1660.80, 0.00, 1753.60, 65.20]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [134, 2, 140, 9]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2783
  Bounding Box: [1686.40, 0.00, 1779.20, 65.20]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [136, 2, 142, 9]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2778
  Bounding Box: [1196.80, 110.40, 1278.40, 227.20]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [98, 13, 103, 21]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2778
  Bounding Box: [1222.40, 110.40, 1304.00, 227.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [100, 13, 103, 21]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2764
  Bounding Box: [676.40, 469.60, 784.80, 627.20]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [57, 41, 65, 52]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2744
  Bounding Box: [948.80, 95.30, 1027.20, 186.60]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [79, 12, 84, 18]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2744
  Bounding Box: [974.40, 95.30, 1052.80, 186.60]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [81, 12, 86, 18]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2739
  Bounding Box: [1076.80, 938.40, 1214.40, 1045.60]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [90, 79, 98, 85]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2739
  Bounding Box: [532.80, 380.80, 670.40, 539.20]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [46, 34, 56, 46]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2732
  Bounding Box: [284.80, 1384.00, 372.80, 1508.80]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [27, 113, 33, 121]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2727
  Bounding Box: [1253.60, 517.60, 1428.00, 791.20]
  Mask Area: 228 pixels
  Mask Ratio: 0.0081
  Mask BBox: [102, 45, 115, 65]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2722
  Bounding Box: [64.70, 1525.60, 155.20, 1649.60]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [10, 124, 16, 129]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2712
  Bounding Box: [1732.80, 1514.40, 1816.00, 1686.40]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [140, 123, 145, 135]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2705
  Bounding Box: [626.00, 0.00, 739.60, 66.10]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [53, 3, 61, 9]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2705
  Bounding Box: [670.40, 495.20, 800.00, 663.20]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [57, 43, 66, 55]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2705
  Bounding Box: [347.00, 1808.00, 464.00, 1974.40]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [32, 146, 40, 158]

