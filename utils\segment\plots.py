# YOLOv5 🚀 by Ultralytics, AGPL-3.0 license
"""
分割可视化工具（合并版）
用于绘制分割结果的可视化图像，支持单模态和多模态（PPL/XPL）可视化
包含原有的分割可视化功能和多模态岩心薄片双模态分割模型的可视化功能
"""

# ========================= 导入模块 =========================
import contextlib
import math
import os
from copy import copy
from pathlib import Path
from urllib.error import URLError

import cv2
import matplotlib
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sn
from scipy.ndimage import gaussian_filter1d
import torch
from PIL import Image, ImageDraw, ImageFont

from .. import threaded, TryExcept

from .general import LOGGER, colorstr, xyxy2xywh, xywh2xyxy, clip_boxes, increment_path
from .metrics import fitness


def feature_visualization(x, module_type, stage, n=32, save_dir=Path("runs/detect/exp")):
    """
    x:              Features to be visualized
    module_type:    Module type
    stage:          Module stage within model
    n:              Maximum number of feature maps to plot
    save_dir:       Directory to save results.
    """
    if ("Detect" not in module_type) and (
        "Segment" not in module_type
    ):  # 'Detect' for Object Detect task,'Segment' for Segment task
        batch, channels, height, width = x.shape  # batch, channels, height, width
        if height > 1 and width > 1:
            f = save_dir / f"stage{stage}_{module_type.split('.')[-1]}_features.png"  # filename

            blocks = torch.chunk(x[0].cpu(), channels, dim=0)  # select batch index 0, block by channels
            n = min(n, channels)  # number of plots
            fig, ax = plt.subplots(math.ceil(n / 8), 8, tight_layout=True)  # 8 rows x n/8 cols
            ax = ax.ravel()
            plt.subplots_adjust(wspace=0.05, hspace=0.05)
            for i in range(n):
                ax[i].imshow(blocks[i].squeeze())  # cmap='gray'
                ax[i].axis("off")

            LOGGER.info(f"Saving {f}... ({n}/{channels})")
            plt.savefig(f, dpi=300, bbox_inches="tight")
            plt.close()
            np.save(str(f.with_suffix(".npy")), x[0].cpu().numpy())  # npy save
            
# ========================= Colors 类定义 =========================
class Colors:
    """提供基于Ultralytics配色方案的RGB调色板，用于可视化任务。"""

    def __init__(self):
        """
        使用从Ultralytics配色方案派生的调色板初始化Colors类，将十六进制代码转换为RGB。
        颜色来源于 `hex = matplotlib.colors.TABLEAU_COLORS.values()`。
        """
        hexs = (
            "FF3838", "FF9D97", "FF701F", "FFB21D", "CFD231", "48F90A", "92CC17", "3DDB86",
            "1A9334", "00D4BB", "2C99A8", "00C2FF", "344593", "6473FF", "0018EC", "8438FF",
            "520085", "CB38FF", "FF95C8", "FF37C7",
        )
        self.palette = [self.hex2rgb(f"#{c}") for c in hexs]
        self.n = len(self.palette)

    def __call__(self, i, bgr=False):
        """通过索引`i`从调色板返回颜色，如果`bgr=True`则返回BGR格式，否则返回RGB格式；`i`是整数索引。"""
        c = self.palette[int(i) % self.n]
        return (c[2], c[1], c[0]) if bgr else c

    @staticmethod
    def hex2rgb(h):
        """将十六进制颜色`h`转换为RGB元组（PIL兼容），顺序为(R, G, B)。"""
        return tuple(int(h[1 + i : 1 + i + 2], 16) for i in (0, 2, 4))


# 创建全局colors实例
colors = Colors()


# ========================= Annotator 类定义 =========================
class Annotator:
    """
    本地图像标注器类，用于在图像上绘制边界框、文本和其他标注。
    支持PIL和OpenCV两种图像处理方式，提供与ultralytics.utils.plotting.Annotator兼容的接口。
    """

    def __init__(self, im, line_width=None, font_size=None, font='Arial.ttf', pil=False, example='abc'):
        """
        初始化Annotator实例。
        
        Args:
            im: 输入图像，可以是numpy数组或PIL图像
            line_width: 线条宽度，默认为图像尺寸的1/200
            font_size: 字体大小，默认为图像尺寸的1/40
            font: 字体文件名或路径
            pil: 是否使用PIL模式进行绘制
            example: 示例文本，用于计算字体尺寸
        """
        # 处理输入图像
        if isinstance(im, Image.Image):
            self.im = im
        else:
            # numpy数组转PIL图像
            if im.dtype != np.uint8:
                im = (im * 255).astype(np.uint8)
            if len(im.shape) == 3 and im.shape[2] == 3:
                # RGB格式
                self.im = Image.fromarray(im)
            else:
                # 灰度或其他格式
                self.im = Image.fromarray(im)
        
        self.pil = pil or not hasattr(ImageDraw, 'Draw')
        
        # 获取图像尺寸
        self.im_width, self.im_height = self.im.size
        
        # 设置线条宽度
        if line_width is None:
            line_width = max(round(sum(self.im.size) / 2 * 0.003), 2)
        self.lw = line_width
        
        # 设置字体
        if self.pil:
            self.draw = ImageDraw.Draw(self.im)
            # 尝试加载字体
            try:
                # 尝试系统字体路径
                font_paths = [
                    font,
                    f'/System/Library/Fonts/{font}',
                    f'/usr/share/fonts/truetype/dejavu/{font}',
                    f'C:/Windows/Fonts/{font}',
                    'arial.ttf',
                    'Arial.ttf'
                ]
                
                self.font = None
                for font_path in font_paths:
                    try:
                        if font_size is None:
                            # 根据图像大小自动计算字体大小
                            font_size = max(round(sum(self.im.size) / 2 * 0.035), 12)
                        self.font = ImageFont.truetype(font_path, font_size)
                        break
                    except (OSError, IOError):
                        continue
                
                # 如果无法加载TrueType字体，使用默认字体
                if self.font is None:
                    self.font = ImageFont.load_default()
                    
            except Exception:
                self.font = ImageFont.load_default()
        else:
            # OpenCV模式（当前实现主要基于PIL）
            self.im = np.asarray(self.im)
    
    def box_label(self, box, label='', color=(128, 128, 128), txt_color=(255, 255, 255), rotated=False):
        """
        在图像上绘制带标签的边界框。
        
        Args:
            box: 边界框坐标 [x1, y1, x2, y2]
            label: 标签文本
            color: 边界框颜色 (R, G, B)
            txt_color: 文本颜色 (R, G, B)
            rotated: 是否为旋转框（暂不支持）
        """
        if isinstance(box, torch.Tensor):
            box = box.tolist()
        
        # 确保坐标为整数
        x1, y1, x2, y2 = [int(x) for x in box]
        
        # 绘制边界框
        self.rectangle([x1, y1, x2, y2], outline=color, width=self.lw)
        
        # 绘制标签
        if label:
            # 计算文本尺寸
            if self.pil and self.font:
                # 使用PIL计算文本边界框
                bbox = self.draw.textbbox((0, 0), label, font=self.font)
                w, h = bbox[2] - bbox[0], bbox[3] - bbox[1]
            else:
                # 简单估算
                w, h = len(label) * 6, 11
            
            # 确保标签在图像范围内
            outside = y1 - h >= 0  # 标签是否在框外
            
            # 绘制标签背景
            label_y1 = y1 - h if outside else y1
            label_y2 = y1 if outside else y1 + h
            self.rectangle([x1, label_y1, x1 + w, label_y2], fill=color, outline=color, width=0)
            
            # 绘制文本
            self.text([x1, label_y1], label, txt_color=txt_color)
    
    def rectangle(self, xy, fill=None, outline=None, width=1):
        """
        绘制矩形。
        
        Args:
            xy: 矩形坐标 [x1, y1, x2, y2]
            fill: 填充颜色
            outline: 边框颜色
            width: 边框宽度
        """
        if self.pil:
            self.draw.rectangle(xy, fill=fill, outline=outline, width=width)
        else:
            # OpenCV实现（如果需要）
            pass
    
    def text(self, xy, text, txt_color=(255, 255, 255), anchor=None):
        """
        在指定位置绘制文本。
        
        Args:
            xy: 文本位置 [x, y]
            text: 文本内容
            txt_color: 文本颜色 (R, G, B)
            anchor: 文本锚点（PIL参数）
        """
        if self.pil:
            self.draw.text(xy, text, fill=txt_color, font=self.font, anchor=anchor)
        else:
            # OpenCV实现（如果需要）
            pass
    
    def get_txt_color(self, color, txt_color=None):
        """
        根据背景颜色获取合适的文本颜色。
        
        Args:
            color: 背景颜色 (R, G, B)
            txt_color: 指定的文本颜色，如果为None则自动选择
        
        Returns:
            合适的文本颜色 (R, G, B)
        """
        if txt_color is not None:
            return txt_color
        
        # 计算背景颜色的亮度
        if isinstance(color, (list, tuple)) and len(color) >= 3:
            brightness = (color[0] * 0.299 + color[1] * 0.587 + color[2] * 0.114)
            return (255, 255, 255) if brightness < 128 else (0, 0, 0)
        else:
            return (255, 255, 255)  # 默认白色
    
    def get_bbox_dimension(self, text):
        """
        计算文本的边界框尺寸。
        
        Args:
            text: 文本内容
        
        Returns:
            (width, height): 文本的宽度和高度
        """
        if self.pil and self.font:
            bbox = self.draw.textbbox((0, 0), text, font=self.font)
            return bbox[2] - bbox[0], bbox[3] - bbox[1]
        else:
            # 简单估算
            return len(text) * 6, 11
    
    def fromarray(self, im):
        """
        从numpy数组更新图像。
        
        Args:
            im: numpy图像数组
        """
        if im.dtype != np.uint8:
            im = (im * 255).astype(np.uint8)
        self.im = Image.fromarray(im)
        if self.pil:
            self.draw = ImageDraw.Draw(self.im)
    
    def result(self):
        """
        返回标注后的图像。
        
        Returns:
            PIL图像或numpy数组
        """
        return np.asarray(self.im) if not self.pil else self.im


# ========================= 工具函数 =========================
def xywh2xyxy(x):
    """将nx4边界框从[x, y, w, h]转换为[x1, y1, x2, y2]，其中xy1=左上角，xy2=右下角。"""
    y = x.clone() if isinstance(x, torch.Tensor) else np.copy(x)
    y[..., 0] = x[..., 0] - x[..., 2] / 2  # 左上角x
    y[..., 1] = x[..., 1] - x[..., 3] / 2  # 左上角y
    y[..., 2] = x[..., 0] + x[..., 2] / 2  # 右下角x
    y[..., 3] = x[..., 1] + x[..., 3] / 2  # 右下角y
    return y


def output_to_target(output, max_det=300):
    """Converts YOLOv5 model output to [batch_id, class_id, x, y, w, h, conf] format for plotting, limiting detections
    to `max_det`.
    """
    targets = []
    for i, o in enumerate(output):
        box, conf, cls = o[:max_det, :6].cpu().split((4, 1, 1), 1)
        j = torch.full((conf.shape[0], 1), i)
        targets.append(torch.cat((j, cls, xyxy2xywh(box), conf), 1))
    return torch.cat(targets, 0).numpy()

def _to_hwc_uint8(img):
    """将CHW/Tensor（∈[0,1]或[0,255]）转换为HWC uint8（RGB）。"""
    if isinstance(img, torch.Tensor):
        img = img.detach().cpu().float().numpy()
    if img.ndim == 3 and img.shape[0] in (1, 3):  # CHW -> HWC
        img = img.transpose(1, 2, 0)
    if img.max() <= 1.0:
        img = (img * 255.0).clip(0, 255)
    return img.astype(np.uint8)


def _ensure_mask_hw(mask, h, w):
    """
    将掩码变为HxW，必要时从[B,H,W]或[1,H,W]取第0张，并resize到目标大小（最近邻）。
    掩码必须是"类别索引图"（多类单通道），背景=0。
    """
    if isinstance(mask, torch.Tensor):
        mask = mask.detach().cpu().numpy()
    mask = np.asarray(mask)
    # 常见形状：[H,W] / [1,H,W] / [B,H,W]
    if mask.ndim == 3:
        mask = mask[0]
    if mask.shape != (h, w):
        mask = cv2.resize(mask.astype(np.uint8), (w, h), interpolation=cv2.INTER_NEAREST)
    return mask.astype(np.int32)


def _unique_non_bg(mask_hw):
    """返回掩码中的非背景类别集合（背景=0）。"""
    s = set(np.unique(mask_hw).tolist())
    s.discard(0)
    return s


def _filter_cls_set(cls_set, selected_cls):
    """按selected_cls过滤类别集合；selected_cls为None则不做过滤。"""
    if selected_cls is None:
        return set(int(c) for c in cls_set)
    allow = set(int(c) for c in selected_cls)
    return set(int(c) for c in cls_set if int(c) in allow)


def _overlay_multiclass_mask_rgb(img_rgb, mask_hw, cls_ids, alpha=0.45):
    """
    在RGB图像上按类别集合cls_ids半透明叠加掩码。
    - img_rgb: HxWx3 uint8 (RGB)
    - mask_hw: HxW int32（0为背景）
    - cls_ids: 需要显示的类别id集合/列表（不含0）
    - alpha: 透明度
    返回：叠加后的新RGB图像
    """
    out = img_rgb.copy()
    H, W = mask_hw.shape
    # 将视图展平，便于按掩码布尔索引做向量化混合
    out_flat = out.reshape(-1, 3).astype(np.float32)
    m_flat = mask_hw.flatten()
    for cid in sorted(set(int(c) for c in cls_ids if int(c) != 0)):
        sel = (m_flat == cid)
        if not np.any(sel):
            continue
        # colors(..., bgr=False) -> RGB三元组
        col = np.array(colors(cid, bgr=False), dtype=np.float32)
        out_flat[sel] = out_flat[sel] * (1.0 - alpha) + col * alpha
    return out_flat.reshape(H, W, 3).astype(np.uint8)


def _draw_legend_rgb(canvas_rgb, cls_ids, names=None, to='left', margin=12, box=18, gap=6):
    """
    在RGB画布上绘制图例（类别色块+名称）。
    to: 'left' | 'right'
    """
    if not cls_ids:
        return
    H, W = canvas_rgb.shape[:2]
    x0 = margin if to == 'left' else max(margin, W - margin - 260)
    y = margin + 8
    for cid in sorted([c for c in set(cls_ids) if c != 0]):
        # 色块（RGB）
        c = tuple(int(v) for v in colors(int(cid), bgr=False))
        cv2.rectangle(canvas_rgb, (x0, y), (x0 + box, y + box), c, thickness=-1)
        # 类别名
        if names is not None:
            if isinstance(names, dict):
                label = names.get(int(cid), f"id={int(cid)}")
            else:
                # 假设names为list/tuple，去掉id=前缀
                if 0 <= int(cid) < len(names):
                    label = names[int(cid)]
                else:
                    label = f"类别{int(cid)}"
        else:
            label = f"类别{int(cid)}"
        
        # 使用更好的文字颜色和字体
        text_color = (255, 255, 255)  # 白色文字
        cv2.putText(canvas_rgb, label, (x0 + box + 8, y + box - 4),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, text_color, 1, cv2.LINE_AA)
        y += box + gap


# ========================= 多模态可视化函数 =========================
@threaded
def plot_multimodal_images_and_masks(
    images,
    targets,
    masks,
    paths=None,
    fname="multimodal_images.jpg",
    names=None,
    selected_cls=None,     # 新增：仅显示这些类别（如[1,3,6]），不传则显示掩码中出现的所有非背景类别
    legend_side="none",    # 默认不显示图例色块
    alpha=0.45             # 掩码透明度
):
    """
    绘制多模态图像网格，显示PPL、XPL原图，并将同一套掩码**同时**覆盖到成对的两张图像上。

    Args:
        images: 图像数据，可以是tuple(ppl_imgs, xpl_imgs)或单个tensor（仅PPL）
                - ppl_imgs/xpl_imgs: [B,3,H,W]（Tensor/Numpy）
        targets: 目标标签（此处不画框，仅保留参数以兼容外部调用）
        masks: 分割掩码：
               - 常见形状：[B,H,W]或[B,1,H,W]；每个像素是类别id（0为背景）
        paths: 图像路径（可选，用于将来在标题显示）
        fname: 保存文件名
        names: 类别名称列表或{id: name}字典
        selected_cls: 仅绘制这些类别（None表示全部非背景）
        legend_side: 图例位置（'left' | 'right' | 'none'）
        alpha: 掩码透明度
    """
    # ---------- 处理多模态输入 ----------
    if isinstance(images, tuple):
        ppl_images, xpl_images = images
        is_multimodal = True
    else:
        ppl_images = images
        xpl_images = None
        is_multimodal = False

    # ---------- 转为numpy ----------
    if isinstance(ppl_images, torch.Tensor):
        ppl_images = ppl_images.detach().cpu().float().numpy()
    if xpl_images is not None and isinstance(xpl_images, torch.Tensor):
        xpl_images = xpl_images.detach().cpu().float().numpy()
    if isinstance(masks, torch.Tensor):
        masks = masks.detach().cpu().numpy()

    max_size = 1920                    # 最终输出最长边不超过该值
    max_subplots = 8 if is_multimodal else 16
    bs, _, h, w = ppl_images.shape
    bs = min(bs, max_subplots)

    # ---------- 归一化到[0,255] ----------
    if np.max(ppl_images[0]) <= 1:
        ppl_images = ppl_images * 255.0
    ppl_images = ppl_images.astype(np.uint8)
    if xpl_images is not None:
        if np.max(xpl_images[0]) <= 1:
            xpl_images = xpl_images * 255.0
        xpl_images = xpl_images.astype(np.uint8)

    # ---------- 计算网格布局 ----------
    if is_multimodal:
        # 成对显示：一对(PPL+XPL)占两列
        max_pairs_per_row = 2
        pairs_count = bs
        pairs_per_row = min(max_pairs_per_row, pairs_count)
        pair_rows = int(np.ceil(pairs_count / pairs_per_row))
        cols = pairs_per_row * 2
        rows = pair_rows
        mosaic_h = rows * h
        mosaic_w = cols * w
    else:
        ns = int(np.ceil(bs ** 0.5))
        rows = cols = ns
        mosaic_h = rows * h
        mosaic_w = cols * w

    # ---------- 创建白底画布（RGB） ----------
    mosaic = np.full((mosaic_h, mosaic_w, 3), 255, dtype=np.uint8)

    # ---------- 先铺原图 ----------
    for i in range(bs):
        if is_multimodal:
            # 成对布局
            pair_idx = i
            pair_row = pair_idx // pairs_per_row
            pair_col = pair_idx % pairs_per_row

            ppl_x = pair_col * 2 * w
            ppl_y = pair_row * h
            xpl_x = pair_col * 2 * w + w
            xpl_y = pair_row * h

            ppl_im_rgb = ppl_images[i].transpose(1, 2, 0)  # CHW -> HWC
            mosaic[ppl_y:ppl_y + h, ppl_x:ppl_x + w, :] = ppl_im_rgb

            if xpl_images is not None:
                xpl_im_rgb = xpl_images[i].transpose(1, 2, 0)
                mosaic[xpl_y:xpl_y + h, xpl_x:xpl_x + w, :] = xpl_im_rgb

            # 面板角标
            cv2.putText(mosaic, 'PPL', (ppl_x + 10, ppl_y + 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            cv2.putText(mosaic, 'XPL', (xpl_x + 10, xpl_y + 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        else:
            # 单模态
            x = (i % cols) * w
            y = (i // cols) * h
            im_rgb = ppl_images[i].transpose(1, 2, 0)
            mosaic[y:y + h, x:x + w, :] = im_rgb

    # ---------- 等比例缩放（仅对整幅mosaic） ----------
    scale = max_size / max(mosaic_h, mosaic_w)
    if scale < 1.0:
        new_h = int(round(mosaic_h * scale))
        new_w = int(round(mosaic_w * scale))
        mosaic = cv2.resize(mosaic, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
    else:
        scale = 1.0  # 不缩放

    # ---------- 叠加掩码（成对两张图同时覆盖） ----------
    for i in range(bs):
        # 取第i张的掩码
        if masks is None:
            continue
        if masks.ndim == 4:   # [B,1,H,W]
            mask_i = masks[i, 0]
        elif masks.ndim == 3: # [B,H,W]
            mask_i = masks[i]
        else:
            continue

        # 统一到原图大小，再在缩放后的mosaic上叠加
        mask_hw = _ensure_mask_hw(mask_i, h, w)
        cls_all = _unique_non_bg(mask_hw)
        cls_show = _filter_cls_set(cls_all, selected_cls)

        if is_multimodal:
            # 成对偏移（未缩放的原像素）
            pair_idx = i
            pair_row = pair_idx // pairs_per_row
            pair_col = pair_idx % pairs_per_row

            ppl_x = pair_col * 2 * w
            ppl_y = pair_row * h
            xpl_x = pair_col * 2 * w + w
            xpl_y = pair_row * h

            # 计算缩放后的贴放位置与尺寸
            tgt_w = int(round(w * scale))
            tgt_h = int(round(h * scale))

            # 先将mask缩放到mosaic当前分区大小
            mask_small = cv2.resize(mask_hw.astype(np.uint8), (tgt_w, tgt_h), interpolation=cv2.INTER_NEAREST)
            mask_small = mask_small.astype(np.int32)

            # PPL区域
            y0 = int(round(ppl_y * scale))
            x0 = int(round(ppl_x * scale))
            y1 = y0 + tgt_h
            x1 = x0 + tgt_w
            ppl_roi = mosaic[y0:y1, x0:x1, :]
            ppl_ovr = _overlay_multiclass_mask_rgb(ppl_roi, mask_small, cls_show, alpha=alpha)
            mosaic[y0:y1, x0:x1, :] = ppl_ovr

            # XPL区域（与PPL同一套掩码）
            y0 = int(round(xpl_y * scale))
            x0 = int(round(xpl_x * scale))
            y1 = y0 + tgt_h
            x1 = x0 + tgt_w
            xpl_roi = mosaic[y0:y1, x0:x1, :]
            xpl_ovr = _overlay_multiclass_mask_rgb(xpl_roi, mask_small, cls_show, alpha=alpha)
            mosaic[y0:y1, x0:x1, :] = xpl_ovr
        else:
            # 单模态网格
            tgt_w = int(round(w * scale))
            tgt_h = int(round(h * scale))
            mask_small = cv2.resize(mask_hw.astype(np.uint8), (tgt_w, tgt_h), interpolation=cv2.INTER_NEAREST)
            mask_small = mask_small.astype(np.int32)

            x = (i % cols) * w
            y = (i // cols) * h
            y0 = int(round(y * scale))
            x0 = int(round(x * scale))
            y1 = y0 + tgt_h
            x1 = x0 + tgt_w
            roi = mosaic[y0:y1, x0:x1, :]
            ovr = _overlay_multiclass_mask_rgb(roi, mask_small, cls_show, alpha=alpha)
            mosaic[y0:y1, x0:x1, :] = ovr

    # ---------- 整幅图例（可选，仅绘制一次） ----------
    if legend_side in ("left", "right"):
        # 汇总所有面板出现过的类别，绘一次图例
        all_cls = set()
        if masks is not None:
            # 兼容[B,1,H,W] / [B,H,W]
            if masks.ndim == 4:
                mm = masks[:, 0]
            elif masks.ndim == 3:
                mm = masks
            else:
                mm = None
            if mm is not None:
                # 采样前bs张中的类别
                for i in range(bs):
                    all_cls |= _unique_non_bg(_ensure_mask_hw(mm[i], h, w))
        # 过滤
        all_cls = _filter_cls_set(all_cls, selected_cls)
        # _draw_legend_rgb(mosaic, all_cls, names=names, to=legend_side)

    # ---------- 保存 ----------
    # mosaic当前为RGB；用cv2.imwrite需转BGR
    out = cv2.cvtColor(mosaic, cv2.COLOR_RGB2BGR)
    cv2.imwrite(str(fname), out)
    return fname


@threaded
def plot_multimodal_comparison(
    ppl_image,
    xpl_image,
    pred_masks,
    gt_masks=None,
    fname="comparison.jpg",
    names=None,
    selected_cls=None,       # 新增：仅显示这些类别（None=全部非背景）
    legend="both",           # 'left' | 'right' | 'both' | 'none' —— 图例显示在哪些列
    alpha=0.45               # 掩码透明度
):
    """
    绘制多模态对照图：将"同一套掩码"**同时**覆盖到PPL与XPL原图上。
    - 若提供gt_masks，则第1行显示预测覆盖（PPL+Pred, XPL+Pred），第2行显示GT覆盖（PPL+GT, XPL+GT）
    - 图例可按列显示（左/右/两侧/不显示）
    """
    # ---------- 图像预处理（RGB HWC uint8） ----------
    ppl_rgb = _to_hwc_uint8(ppl_image)
    xpl_rgb = _to_hwc_uint8(xpl_image)
    h, w = ppl_rgb.shape[:2]

    # ---------- 掩码统一到HxW ----------
    pm = _ensure_mask_hw(pred_masks, h, w)
    with_gt = gt_masks is not None
    if with_gt:
        gm = _ensure_mask_hw(gt_masks, h, w)

    # ---------- 类别集合（由掩码中出现的非背景决定，再按selected_cls过滤） ----------
    cls_all = _unique_non_bg(pm)
    if with_gt:
        cls_all |= _unique_non_bg(gm)
    cls_show = _filter_cls_set(cls_all, selected_cls)

    # ---------- 覆盖到PPL/XPL（预测） ----------
    ppl_pred = _overlay_multiclass_mask_rgb(ppl_rgb, pm, cls_show, alpha=alpha)
    xpl_pred = _overlay_multiclass_mask_rgb(xpl_rgb, pm, cls_show, alpha=alpha)

    # ---------- 覆盖到PPL/XPL（GT） ----------
    if with_gt:
        ppl_gt = _overlay_multiclass_mask_rgb(ppl_rgb, gm, cls_show, alpha=alpha)
        xpl_gt = _overlay_multiclass_mask_rgb(xpl_rgb, gm, cls_show, alpha=alpha)

    # ---------- 拼接马赛克并加标题（RGB） ----------
    if with_gt:
        mosaic = np.zeros((h * 2, w * 2, 3), dtype=np.uint8)
        mosaic[0:h, 0:w] = ppl_pred
        mosaic[0:h, w:2*w] = xpl_pred
        mosaic[h:2*h, 0:w] = ppl_gt
        mosaic[h:2*h, w:2*w] = xpl_gt

        cv2.putText(mosaic, "PPL + Pred", (12, 32), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255,255,255), 2, cv2.LINE_AA)
        cv2.putText(mosaic, "XPL + Pred", (w + 12, 32), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255,255,255), 2, cv2.LINE_AA)
        cv2.putText(mosaic, "PPL + GT", (12, h + 32), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255,255,255), 2, cv2.LINE_AA)
        cv2.putText(mosaic, "XPL + GT", (w + 12, h + 32), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255,255,255), 2, cv2.LINE_AA)

        # 图例位置
        if legend in ("left", "both"):
            # _draw_legend_rgb(mosaic[0:h, 0:w], cls_show, names=names, to='left')
            # _draw_legend_rgb(mosaic[h:2*h, 0:w], cls_show, names=names, to='left')
            pass
        if legend in ("right", "both"):
            # _draw_legend_rgb(mosaic[0:h, w:2*w], cls_show, names=names, to='right')
            # _draw_legend_rgb(mosaic[h:2*h, w:2*w], cls_show, names=names, to='right')
            pass

    else:
        mosaic = np.zeros((h, w * 2, 3), dtype=np.uint8)
        mosaic[0:h, 0:w] = ppl_pred
        mosaic[0:h, w:2*w] = xpl_pred

        cv2.putText(mosaic, "PPL + Pred", (12, 32), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255,255,255), 2, cv2.LINE_AA)
        cv2.putText(mosaic, "XPL + Pred", (w + 12, 32), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255,255,255), 2, cv2.LINE_AA)

        if legend in ("left", "both"):
            # _draw_legend_rgb(mosaic[0:h, 0:w], cls_show, names=names, to='left')
            pass
        if legend in ("right", "both"):
            # _draw_legend_rgb(mosaic[0:h, w:2*w], cls_show, names=names, to='right')
            pass

    # ---------- 保存（转BGR以兼容cv2.imwrite） ----------
    out = cv2.cvtColor(mosaic, cv2.COLOR_RGB2BGR)
    cv2.imwrite(str(fname), out)
    return fname


# ========================= 兼容性函数 =========================
def _create_mask_visualization(image, mask, alpha=0.45, selected_cls=None, names=None):
    """
    为单张图像创建掩码可视化的兼容性函数。
    
    Args:
        image: 输入图像（numpy array或tensor）
        mask: 分割掩码
        alpha: 透明度
        selected_cls: 选择的类别
        names: 类别名称
    
    Returns:
        可视化后的图像
    """
    # 转换为标准格式
    img_rgb = _to_hwc_uint8(image)
    h, w = img_rgb.shape[:2]
    mask_hw = _ensure_mask_hw(mask, h, w)
    
    # 获取类别
    cls_all = _unique_non_bg(mask_hw)
    cls_show = _filter_cls_set(cls_all, selected_cls)
    
    # 叠加掩码
    result = _overlay_multiclass_mask_rgb(img_rgb, mask_hw, cls_show, alpha=alpha)
    
    return result


# ========================= 原有的单模态可视化函数 =========================
@threaded
def plot_images_and_masks(images, targets, masks, paths=None, fname="images.jpg", names=None):
    """Plots a grid of images, their labels, and masks with optional resizing and annotations, saving to fname."""

    # Handle multimodal input (tuple of tensors) or single modal input (single tensor)
    if isinstance(images, tuple):
        # For multimodal input, use the enhanced multimodal visualization
        return plot_multimodal_images_and_masks(images, targets, masks, paths, fname, names)

    # Single modal processing (original logic)
    if isinstance(images, torch.Tensor):
        images = images.cpu().float().numpy()
    if isinstance(targets, torch.Tensor):
        targets = targets.cpu().numpy()
    if isinstance(masks, torch.Tensor):
        masks = masks.cpu().numpy().astype(int)

    max_size = 1920  # max image size
    max_subplots = 16  # max image subplots, i.e. 4x4
    bs, _, h, w = images.shape  # batch size, _, height, width
    bs = min(bs, max_subplots)  # limit plot images
    ns = np.ceil(bs**0.5)  # number of subplots (square)
    if np.max(images[0]) <= 1:
        images *= 255  # de-normalise (optional)

    # Build Image
    mosaic = np.full((int(ns * h), int(ns * w), 3), 255, dtype=np.uint8)  # init
    for i, im in enumerate(images):
        if i == max_subplots:  # if last batch has fewer images than we expect
            break
        x, y = int(w * (i // ns)), int(h * (i % ns))  # block origin
        im = im.transpose(1, 2, 0)
        mosaic[y : y + h, x : x + w, :] = im

    # Resize (optional)
    scale = max_size / ns / max(h, w)
    if scale < 1:
        h = math.ceil(scale * h)
        w = math.ceil(scale * w)
        mosaic = cv2.resize(mosaic, tuple(int(x * ns) for x in (w, h)))

    # Annotate
    fs = int((h + w) * ns * 0.01)  # font size
    annotator = Annotator(mosaic, line_width=round(fs / 10), font_size=fs, pil=True, example=names)
    for i in range(i + 1):
        x, y = int(w * (i // ns)), int(h * (i % ns))  # block origin
        annotator.rectangle([x, y, x + w, y + h], None, (255, 255, 255), width=2)  # borders
        if paths:
            annotator.text([x + 5, y + 5], text=Path(paths[i]).name[:40], txt_color=(220, 220, 220))  # filenames
        if len(targets) > 0:
            idx = targets[:, 0] == i
            ti = targets[idx]  # image targets

            boxes = xywh2xyxy(ti[:, 2:6]).T
            classes = ti[:, 1].astype("int")
            labels = ti.shape[1] == 6  # labels if no conf column
            conf = None if labels else ti[:, 6]  # check for confidence presence (label vs pred)

            if boxes.shape[1]:
                if boxes.max() <= 1.01:  # if normalized with tolerance 0.01
                    boxes[[0, 2]] *= w  # scale to pixels
                    boxes[[1, 3]] *= h
                elif scale < 1:  # absolute coords need scale if image scales
                    boxes *= scale
            boxes[[0, 2]] += x
            boxes[[1, 3]] += y
            for j, box in enumerate(boxes.T.tolist()):
                cls = classes[j]
                color = colors(cls)
                cls = names[cls] if names else cls
                if labels or (conf is not None and conf[j] > 0.001):  # 降低置信度阈值以显示更多预测
                    label = f"{cls}" if labels else f"{cls} {conf[j]:.1f}"
                    annotator.box_label(box, label, color=color)

            # Plot masks - 优化掩码可视化逻辑
            if len(masks) and len(ti) > 0:
                if masks.max() > 1.0:  # mean that masks are overlap
                    image_masks = masks[[i]]  # (1, 640, 640)
                    nl = len(ti)
                    index = np.arange(nl).reshape(nl, 1, 1) + 1
                    image_masks = np.repeat(image_masks, nl, axis=0)
                    image_masks = np.where(image_masks == index, 1.0, 0.0)
                else:
                    image_masks = masks[idx]

                # 确保有足够的掩码数据
                if len(image_masks) >= len(ti):
                    im = np.asarray(annotator.im).copy()
                    for j in range(len(ti)):
                        # 确保索引有效且置信度满足条件
                        if j < len(classes) and (labels or (conf is not None and j < len(conf) and conf[j] > 0.001)):
                            color = colors(classes[j])
                            
                            # 确保掩码索引有效
                            if j < len(image_masks):
                                mh, mw = image_masks[j].shape
                                if mh != h or mw != w:
                                    mask = image_masks[j].astype(np.uint8)
                                    mask = cv2.resize(mask, (w, h))
                                    mask = mask.astype(bool)
                                else:
                                    mask = image_masks[j].astype(bool)
                                
                                # 确保掩码有有效区域
                                if np.any(mask):
                                    try:
                                        # 应用掩码到图像区域
                                        mask_region = im[y : y + h, x : x + w, :]
                                        mask_region[mask] = (
                                            mask_region[mask] * 0.3 + np.array(color) * 0.7
                                        )
                                        im[y : y + h, x : x + w, :] = mask_region
                                    except Exception as e:
                                        # 如果出现异常，记录但继续处理其他掩码
                                        print(f"Warning: Error applying mask {j}: {e}")
                    annotator.fromarray(im)
    annotator.im.save(fname)  # save


def plot_results_with_masks(file="path/to/results.csv", dir="", best=True):
    """
    Plots training results from CSV files, plotting best or last result highlights based on `best` parameter.

    Example: from utils.plots import *; plot_results('path/to/results.csv')
    """
    save_dir = Path(file).parent if file else Path(dir)
    fig, ax = plt.subplots(2, 8, figsize=(18, 6), tight_layout=True)
    ax = ax.ravel()
    files = list(save_dir.glob("results*.csv"))
    assert len(files), f"No results.csv files found in {save_dir.resolve()}, nothing to plot."
    for f in files:
        try:
            data = pd.read_csv(f)
            index = np.argmax(
                0.9 * data.values[:, 8] + 0.1 * data.values[:, 7] + 0.9 * data.values[:, 12] + 0.1 * data.values[:, 11]
            )
            s = [x.strip() for x in data.columns]
            x = data.values[:, 0]
            for i, j in enumerate([1, 2, 3, 4, 5, 6, 9, 10, 13, 14, 15, 16, 7, 8, 11, 12]):
                y = data.values[:, j]
                # y[y == 0] = np.nan  # don't show zero values
                ax[i].plot(x, y, marker=".", label=f.stem, linewidth=2, markersize=2)
                if best:
                    # best
                    ax[i].scatter(index, y[index], color="r", label=f"best:{index}", marker="*", linewidth=3)
                    ax[i].set_title(s[j] + f"\n{round(y[index], 5)}")
                else:
                    # last
                    ax[i].scatter(x[-1], y[-1], color="r", label="last", marker="*", linewidth=3)
                    ax[i].set_title(s[j] + f"\n{round(y[-1], 5)}")
                # if j in [8, 9, 10]:  # share train and val loss y axes
                #     ax[i].get_shared_y_axes().join(ax[i], ax[i - 5])
        except Exception as e:
            print(f"Warning: Plotting error for {f}: {e}")
    ax[1].legend()
    fig.savefig(save_dir / "results.png", dpi=200)
    plt.close()




def plot_images(images, targets, paths=None, fname="images.jpg", names=None, max_size=1920, max_subplots=16):
    """Plots image grid with labels, saving to file, handling detection and segmentation tasks."""
    if isinstance(images, torch.Tensor):
        images = images.cpu().float().numpy()
    if isinstance(targets, torch.Tensor):
        targets = targets.cpu().numpy()

    max_size, max_subplots = min(max_size, 1920), min(max_subplots, 16)  # limit to prevent OOM
    bs, _, h, w = images.shape  # batch size, _, height, width
    bs = min(bs, max_subplots)  # limit plot images
    ns = np.ceil(bs**0.5)  # number of subplots (square)
    if np.max(images[0]) <= 1:
        images *= 255  # de-normalise (optional)

    # Build Image
    mosaic = np.full((int(ns * h), int(ns * w), 3), 255, dtype=np.uint8)  # init
    for i, im in enumerate(images):
        if i == max_subplots:  # if last batch has fewer samples than we expect
            break
        x, y = int(w * (i // ns)), int(h * (i % ns))  # block origin
        im = im.transpose((1, 2, 0))
        mosaic[y : y + h, x : x + w, :] = im

    # Resize (optional)
    scale = max_size / ns / max(h, w)
    if scale < 1:
        h = math.ceil(scale * h)
        w = math.ceil(scale * w)
        mosaic = cv2.resize(mosaic, tuple(int(x * ns) for x in (w, h)))

    # Annotate
    fs = int((h + w) * ns * 0.01)  # font size
    annotator = Annotator(mosaic, line_width=round(fs / 10), font_size=fs, pil=True, example=names)
    for i in range(i + 1):
        x, y = int(w * (i // ns)), int(h * (i % ns))  # block origin
        annotator.rectangle([x, y, x + w, y + h], None, (255, 255, 255), width=2)  # borders
        if paths:
            annotator.text((x + 5, y + 5 + h), text=Path(paths[i]).name[:40], txt_color=(220, 220, 220))  # filenames
        if len(targets) > i:
            ti = targets[targets[:, 0] == i]  # image targets
            boxes = xyxy2xywh(ti[:, 2:6]).T
            classes = ti[:, 1].astype("int")
            labels = ti.shape[1] == 6  # labels if no conf column
            conf = None if labels else ti[:, 6]  # check for confidence presence (label vs pred)

            if boxes.shape[1]:
                if boxes.max() <= 1.01:  # if normalized with tolerance 0.01
                    boxes[[0, 2]] *= w  # scale to pixels
                    boxes[[1, 3]] *= h
                elif scale < 1:  # absolute coords need scale if image scales
                    boxes *= scale
            boxes[[0, 2]] += x
            boxes[[1, 3]] += y
            for j, box in enumerate(boxes.T.tolist()):
                cls = classes[j]
                color = colors(cls)
                cls = names[cls] if names else cls
                if labels or conf[j] > 0.001:  # 降低置信度阈值以显示更多预测
                    label = f"{cls}" if labels else f"{cls} {conf[j]:.1f}"
                    annotator.box_label(box, label, color=color)
    annotator.im.save(fname)  # save


def plot_lr_scheduler(optimizer, scheduler, epochs=300, save_dir=""):
    """Plots learning rate schedule for given optimizer and scheduler, saving the plot to a file."""
    optimizer, scheduler = copy(optimizer), copy(scheduler)  # do not modify originals
    y = []
    for _ in range(epochs):
        scheduler.step()
        y.append(optimizer.param_groups[0]["lr"])
    plt.plot(y, ".-", label="LR")
    plt.xlabel("epoch")
    plt.ylabel("LR")
    plt.grid()
    plt.xlim(0, epochs)
    plt.ylim(0)
    plt.savefig(Path(save_dir) / "LR.png", dpi=200)
    plt.close()


def plot_val_txt():  # from utils.plots import *; plot_val()
    """Plots validation results from YOLO training, comparing model performance across different sizes."""
    x = np.arange(0.5, 0.95, 0.05)
    y = [
        [0.7497, 0.7718, 0.7783, 0.7719, 0.7611, 0.7462, 0.7274, 0.7075, 0.6906],  # P5 640
        [0.7497, 0.7718, 0.7783, 0.7719, 0.7611, 0.7462, 0.7274, 0.7075, 0.6906],  # P5 1280
        [0.7497, 0.7718, 0.7783, 0.7719, 0.7611, 0.7462, 0.7274, 0.7075, 0.6906],  # P6 640
        [0.7497, 0.7718, 0.7783, 0.7719, 0.7611, 0.7462, 0.7274, 0.7075, 0.6906],  # P6 1280
    ]
    plt.figure(figsize=(10, 6), tight_layout=True)
    plt.plot(x, y[0], marker="o", linewidth=2, label="YOLOv5s P5 640")
    plt.plot(x, y[1], marker="s", linewidth=2, label="YOLOv5s P5 1280")
    plt.plot(x, y[2], marker="^", linewidth=2, label="YOLOv5s P6 640")
    plt.plot(x, y[3], marker="D", linewidth=2, label="YOLOv5s P6 1280")
    plt.xlabel("Confidence")
    plt.ylabel("mAP@0.5")
    plt.xlim(0.5, 0.95)
    plt.ylim(0.65, 0.8)
    plt.legend()
    plt.savefig("val.png", dpi=300)


def plot_targets_txt():  # from utils.plots import *; plot_targets_txt()
    """Plots class distribution of training targets from YOLO dataset, saving histogram to 'targets.jpg'."""
    x = torch.load("targets.pt")  # targets
    x = torch.cat([torch.bincount(x[i][:, 1].long(), minlength=80) for i in range(len(x))], 0).float()
    x /= x.sum()
    plt.figure(figsize=(10, 3), tight_layout=True)
    plt.bar(range(len(x)), x)
    plt.xlabel("classes")
    plt.ylabel("instances")
    plt.title("Training targets")
    plt.savefig("targets.jpg", dpi=200)


def plot_val_study(file="", dir="", x=None):  # from utils.plots import *; plot_val_study()
    """Plots validation study results comparing YOLOv5 model performance and speed across different sizes."""
    save_dir = Path(file).parent if file else Path(dir)
    plot2 = False  # plot additional results
    if plot2:
        ax = plt.subplots(2, 4, figsize=(10, 6), tight_layout=True)[1].ravel()

    fig2, ax2 = plt.subplots(1, 1, figsize=(8, 4), tight_layout=True)
    # for f in [save_dir / f'study_coco_{x}.txt' for x in ['yolov5n6', 'yolov5s6', 'yolov5m6', 'yolov5l6', 'yolov5x6']]:
    for f in sorted(save_dir.glob("study*.txt")):
        y = np.loadtxt(f, dtype=np.float32, usecols=[0, 1, 2, 3, 7, 8, 9], ndmin=2).T
        x = np.arange(y.shape[1]) if x is None else np.array(x)
        if plot2:
            s = ["P", "R", "mAP@.5", "mAP@.5:.95", "t_preprocess (ms/img)", "t_inference (ms/img)", "t_NMS (ms/img)"]
            for i in range(7):
                ax[i].plot(x, y[i], ".-", linewidth=2, markersize=8)
                ax[i].set_title(s[i])

        j = y[3].argmax() + 1
        ax2.plot(
            y[5, 1:j],
            y[3, 1:j] * 1e2,
            ".-",
            linewidth=2,
            markersize=8,
            label=f.stem.replace("study_coco_", "").replace("yolo", "YOLO"),
        )

    ax2.plot(
        1e3 / np.array([209, 140, 97, 58, 35, 18]),
        [34.6, 40.5, 43.0, 47.5, 49.7, 51.5],
        "k.-",
        linewidth=2,
        markersize=8,
        alpha=0.25,
        label="EfficientDet",
    )

    ax2.grid(alpha=0.2)
    ax2.set_yticks(np.arange(20, 60, 5))
    ax2.set_xlim(0, 57)
    ax2.set_ylim(25, 55)
    ax2.set_xlabel("GPU Speed (ms/img)")
    ax2.set_ylabel("COCO AP val")
    ax2.legend(loc="lower right")
    f = save_dir / "study.png"
    print(f"Saving {f}...")
    plt.savefig(f, dpi=300)


@TryExcept()  # known issue https://github.com/ultralytics/yolov5/issues/5395
def plot_labels(labels, names=(), save_dir=Path("")):
    """Plots dataset labels, saving correlogram and label images, handles classes, and visualizes bounding boxes."""
    LOGGER.info(f"Plotting labels to {save_dir / 'labels.jpg'}... ")
    c, b = labels[:, 0], labels[:, 1:].transpose()  # classes, boxes
    nc = int(c.max() + 1)  # number of classes
    x = pd.DataFrame(b.transpose(), columns=["x", "y", "width", "height"])

    # seaborn correlogram
    sn.pairplot(x, corner=True, diag_kind="auto", kind="hist", diag_kws=dict(bins=50), plot_kws=dict(pmax=0.9))
    plt.savefig(save_dir / "labels_correlogram.jpg", dpi=200)
    plt.close()

    # matplotlib labels
    matplotlib.use("svg")  # faster
    ax = plt.subplots(2, 2, figsize=(8, 8), tight_layout=True)[1].ravel()
    y = ax[0].hist(c, bins=np.linspace(0, nc, nc + 1) - 0.5, rwidth=0.8)
    with contextlib.suppress(Exception):  # color histogram bars by class
        [y[2].patches[i].set_color([x / 255 for x in colors(i)]) for i in range(nc)]  # known issue #3195
    ax[0].set_ylabel("instances")
    if 0 < len(names) < 30:
        ax[0].set_xticks(range(len(names)))
        ax[0].set_xticklabels(list(names.values()), rotation=90, fontsize=10)
    else:
        ax[0].set_xlabel("classes")
    sn.histplot(x, x="x", y="y", ax=ax[2], bins=50, pmax=0.9)
    sn.histplot(x, x="width", y="height", ax=ax[3], bins=50, pmax=0.9)

    # rectangles
    labels[:, 1:3] = 0.5  # center
    labels[:, 1:] = xywh2xyxy(labels[:, 1:]) * 2000
    img = Image.fromarray(np.ones((2000, 2000, 3), dtype=np.uint8) * 255)
    for cls, *box in labels[:1000]:
        ImageDraw.Draw(img).rectangle(box, width=1, outline=colors(cls))  # plot
    ax[1].imshow(img)
    ax[1].axis("off")

    for a in [0, 1, 2, 3]:
        for s in ["top", "right", "left", "bottom"]:
            ax[a].spines[s].set_visible(False)

    plt.savefig(save_dir / "labels.jpg", dpi=200)
    matplotlib.use("Agg")
    plt.close()

def plot_evolve(evolve_csv="path/to/evolve.csv"):
    """
    Plots hyperparameter evolution results from a given CSV, saving the plot and displaying best results.

    Example: from utils.plots import *; plot_evolve()
    """
    evolve_csv = Path(evolve_csv)
    data = pd.read_csv(evolve_csv)
    keys = [x.strip() for x in data.columns]
    x = data.values
    f = fitness(x)
    j = np.argmax(f)  # max fitness index
    plt.figure(figsize=(10, 12), tight_layout=True)
    matplotlib.rc("font", **{"size": 8})
    print(f"Best results from row {j} of {evolve_csv}:")
    for i, k in enumerate(keys[7:]):
        v = x[:, 7 + i]
        mu = v[j]  # best single result
        plt.subplot(6, 5, i + 1)
        plt.scatter(v, f, c=hist2d(v, f, 20), cmap="viridis", alpha=0.8, edgecolors="none")
        plt.plot(mu, f.max(), "k+", markersize=15)
        plt.title(f"{k} = {mu:.3g}", fontdict={"size": 9})  # limit to 40 characters
        if i % 5 != 0:
            plt.yticks([])
        print(f"{k:>15}: {mu:.3g}")
    f = evolve_csv.with_suffix(".png")  # filename
    plt.savefig(f, dpi=200)
    plt.close()
    print(f"Saved {f}")
    
def plot_results(file="path/to/results.csv", dir=""):
    """
    Plots training results from a 'results.csv' file; accepts file path and directory as arguments.

    Example: from utils.plots import *; plot_results('path/to/results.csv')
    """
    save_dir = Path(file).parent if file else Path(dir)
    fig, ax = plt.subplots(2, 5, figsize=(12, 6), tight_layout=True)
    ax = ax.ravel()
    files = list(save_dir.glob("results*.csv"))
    assert len(files), f"No results.csv files found in {save_dir.resolve()}, nothing to plot."
    for f in files:
        try:
            data = pd.read_csv(f)
            s = [x.strip() for x in data.columns]
            x = data.values[:, 0]
            for i, j in enumerate([1, 2, 3, 4, 5, 8, 9, 10, 6, 7]):
                y = data.values[:, j].astype("float")
                # y[y == 0] = np.nan  # don't show zero values
                ax[i].plot(x, y, marker=".", label=f.stem, linewidth=2, markersize=8)  # actual results
                ax[i].plot(x, gaussian_filter1d(y, sigma=3), ":", label="smooth", linewidth=2)  # smoothing line
                ax[i].set_title(s[j], fontsize=12)
                # if j in [8, 9, 10]:  # share train and val loss y axes
                #     ax[i].get_shared_y_axes().join(ax[i], ax[i - 5])
        except Exception as e:
            LOGGER.info(f"Warning: Plotting error for {f}: {e}")
    ax[1].legend()
    fig.savefig(save_dir / "results.png", dpi=200)
    plt.close()




