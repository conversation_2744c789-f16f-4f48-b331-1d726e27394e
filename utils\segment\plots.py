# YOLOv5 🚀 by Ultralytics, AGPL-3.0 license
"""
分割可视化工具（合并精简版）
- 支持单模态与多模态（PPL/XPL）可视化
- 保留原有图像/掩码网格、对比图、训练结果与学习率/演化/val study/标签分布可视化等功能
- 统一图像/掩码处理流程，修复若干小问题并增强健壮性
"""

# ========================= 导入模块 =========================
import contextlib
import math
from copy import copy
from pathlib import Path

import cv2
import matplotlib
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sn
import torch
from PIL import Image, ImageDraw, ImageFont
from scipy.ndimage import gaussian_filter1d


from utils import TryExcept, threaded  # 导入工具函数：异常处理装饰器和多线程装饰器
from .general import LOGGER, colorstr, xyxy2xywh, xywh2xyxy, clip_boxes, increment_path

# ========================= 通用颜色与标注 =========================
class Colors:
    """Ultralytics 风格调色板（RGB）"""
    def __init__(self):
        hexs = (
            "FF3838", "FF9D97", "FF701F", "FFB21D", "CFD231", "48F90A", "92CC17", "3DDB86",
            "1A9334", "00D4BB", "2C99A8", "00C2FF", "344593", "6473FF", "0018EC", "8438FF",
            "520085", "CB38FF", "FF95C8", "FF37C7",
        )
        self.palette = [self.hex2rgb(f"#{c}") for c in hexs]
        self.n = len(self.palette)

    def __call__(self, i, bgr=False):
        c = self.palette[int(i) % self.n]
        return (c[2], c[1], c[0]) if bgr else c

    @staticmethod
    def hex2rgb(h):
        return tuple(int(h[1 + i:1 + i + 2], 16) for i in (0, 2, 4))

colors = Colors()


class Annotator:
    """
    轻量标注器（统一用 PIL 实现，接口与原版兼容）
    支持：矩形框、文本、从 array 刷新/导出
    """
    def __init__(self, im, line_width=None, font_size=None, font='Arial.ttf', pil=True, example='abc'):
        # 统一转 PIL.Image
        if isinstance(im, Image.Image):
            self.im = im
        else:
            im = im.astype(np.uint8) if isinstance(im, np.ndarray) else np.asarray(im, dtype=np.uint8)
            if im.ndim == 2:
                self.im = Image.fromarray(im)
            elif im.ndim == 3 and im.shape[2] in (3, 4):
                self.im = Image.fromarray(im[..., :3])
            else:
                raise ValueError("Unsupported image shape for Annotator.")
        self.pil = True
        self.draw = ImageDraw.Draw(self.im)

        # 自动线宽/字号
        w, h = self.im.size
        self.lw = line_width or max(round((w + h) * 0.003), 2)
        fs = font_size or max(round((w + h) * 0.035 * 0.5), 12)

        # 尝试加载字体
        tried = [
            font,
            f'/System/Library/Fonts/{font}',
            f'/usr/share/fonts/truetype/dejavu/{font}',
            f'C:/Windows/Fonts/{font}',
            'arial.ttf', 'Arial.ttf'
        ]
        self.font = None
        for p in tried:
            try:
                self.font = ImageFont.truetype(p, fs)
                break
            except Exception:
                continue
        if self.font is None:
            self.font = ImageFont.load_default()

    def rectangle(self, xy, fill=None, outline=None, width=1):
        self.draw.rectangle(xy, fill=fill, outline=outline, width=width)

    def text(self, xy, text, txt_color=(255, 255, 255), anchor=None):
        self.draw.text(xy, text, fill=txt_color, font=self.font, anchor=anchor)

    def box_label(self, box, label='', color=(128, 128, 128), txt_color=(255, 255, 255), rotated=False):
        if isinstance(box, torch.Tensor):
            box = box.tolist()
        x1, y1, x2, y2 = [int(v) for v in box]
        self.rectangle([x1, y1, x2, y2], outline=color, width=self.lw)
        if label:
            # 文本背景框
            bbox = self.draw.textbbox((0, 0), label, font=self.font)
            w, h = bbox[2] - bbox[0], bbox[3] - bbox[1]
            outside = y1 - h >= 0
            yb1, yb2 = (y1 - h, y1) if outside else (y1, y1 + h)
            self.rectangle([x1, yb1, x1 + w, yb2], fill=color, outline=color, width=0)
            self.text((x1, yb1), label, txt_color)

    def fromarray(self, im):
        im = im.astype(np.uint8) if im.dtype != np.uint8 else im
        self.im = Image.fromarray(im)
        self.draw = ImageDraw.Draw(self.im)

    @property
    def np(self):
        return np.asarray(self.im)

    def save(self, fname):
        self.im.save(fname)


# ========================= 通用工具函数 =========================
def _to_hwc_uint8(img):
    """将 Tensor/CHW/归一化图像 → HWC uint8 (RGB)"""
    if isinstance(img, torch.Tensor):
        img = img.detach().cpu().float().numpy()
    if img.ndim == 3 and img.shape[0] in (1, 3):
        img = img.transpose(1, 2, 0)
    if img.max() <= 1.0:
        img = (img * 255.0).clip(0, 255)
    return img.astype(np.uint8)


def _ensure_mask_hw(mask, h, w):
    """
    将掩码变为 HxW int32（类别索引，0 为背景）；必要时取第 0 通道并最近邻缩放
    支持 [H,W] / [1,H,W] / [B,H,W]
    """
    if isinstance(mask, torch.Tensor):
        mask = mask.detach().cpu().numpy()
    mask = np.asarray(mask)
    if mask.ndim == 3:
        mask = mask[0]
    if mask.shape != (h, w):
        mask = cv2.resize(mask.astype(np.uint8), (w, h), interpolation=cv2.INTER_NEAREST)
    return mask.astype(np.int32)


def _unique_non_bg(mask_hw):
    """掩码中的非背景类别集合（背景=0）"""
    s = set(np.unique(mask_hw).tolist())
    s.discard(0)
    return s


def _filter_cls_set(cls_set, selected_cls):
    """按 selected_cls 过滤；为 None 则返回原集合"""
    if selected_cls is None:
        return set(int(c) for c in cls_set)
    allow = set(int(c) for c in selected_cls)
    return set(int(c) for c in cls_set if int(c) in allow)


def _overlay_multiclass_mask_rgb(img_rgb, mask_hw, cls_ids, alpha=0.45):
    """
    将多类（非 0）掩码半透明叠加到 RGB 图像上（向量化实现）
    img_rgb: HxWx3 uint8；mask_hw: HxW int32；cls_ids: 需要显示的类集合
    """
    if not cls_ids:
        return img_rgb
    out = img_rgb.astype(np.float32).reshape(-1, 3)
    m = mask_hw.reshape(-1)
    for cid in sorted([int(c) for c in cls_ids if int(c) != 0]):
        sel = (m == cid)
        if sel.any():
            col = np.array(colors(cid, bgr=False), dtype=np.float32)
            out[sel] = out[sel] * (1.0 - alpha) + col * alpha
    return out.reshape(img_rgb.shape).astype(np.uint8)


def _draw_legend_rgb(canvas_rgb, cls_ids, names=None, to='left', margin=12, box=18, gap=6):
    """在整幅 RGB 画布上绘制图例"""
    if not cls_ids:
        return
    H, W = canvas_rgb.shape[:2]
    x0 = margin if to == 'left' else max(margin, W - margin - 260)
    y = margin + 8
    for cid in sorted([c for c in set(cls_ids) if c != 0]):
        c = tuple(int(v) for v in colors(int(cid), bgr=False))
        cv2.rectangle(canvas_rgb, (x0, y), (x0 + box, y + box), c, thickness=-1)
        if names is not None:
            if isinstance(names, dict):
                label = names.get(int(cid), f"id={int(cid)}")
            else:
                label = names[int(cid)] if 0 <= int(cid) < len(names) else f"类别{int(cid)}"
        else:
            label = f"类别{int(cid)}"
        cv2.putText(canvas_rgb, label, (x0 + box + 8, y + box - 4),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1, cv2.LINE_AA)
        y += box + gap


def hist2d(x, y, n=100):
    """
    计算点密度用于散点着色（供 plot_evolve 使用）
    返回每个点所在栅格的计数值，形状与 x 等长
    """
    x = np.asarray(x, dtype=np.float32)
    y = np.asarray(y, dtype=np.float32)
    H, xe, ye = np.histogram2d(x, y, bins=n)
    xi = np.clip(np.digitize(x, xe) - 1, 0, H.shape[0] - 1)
    yi = np.clip(np.digitize(y, ye) - 1, 0, H.shape[1] - 1)
    return H[xi, yi]


# ========================= 特征可视化 =========================
def feature_visualization(x, module_type, stage, n=32, save_dir=Path("runs/detect/exp")):
    """
    可视化中间特征（非 Detect/Segment 层）
    """
    if ("Detect" in module_type) or ("Segment" in module_type):
        return
    b, c, h, w = x.shape
    if h <= 1 or w <= 1:
        return
    f = save_dir / f"stage{stage}_{module_type.split('.')[-1]}_features.png"
    blocks = torch.chunk(x[0].cpu(), c, dim=0)
    n = min(n, c)
    rows = math.ceil(n / 8)
    fig, ax = plt.subplots(rows, 8, tight_layout=True)
    ax = ax.ravel()
    plt.subplots_adjust(wspace=0.05, hspace=0.05)
    for i in range(n):
        ax[i].imshow(blocks[i].squeeze())
        ax[i].axis("off")
    LOGGER.info(f"Saving {f}... ({n}/{c})")
    plt.savefig(f, dpi=300, bbox_inches="tight")
    plt.close()
    np.save(str(f.with_suffix(".npy")), x[0].cpu().numpy())


# ========================= 多模态可视化 =========================
@threaded
def plot_multimodal_images_and_masks(
    images,
    targets,
    masks,
    paths=None,
    fname="multimodal_images.jpg",
    names=None,
    selected_cls=None,
    legend_side="left",
    alpha=0.45
):
    """
    多模态网格：每对样本 (PPL, XPL) 并排显示；同一套掩码同时覆盖两张图
    - images: tuple(ppl_imgs, xpl_imgs) 或 单 tensor（仅 PPL）
    - masks: [B,H,W] 或 [B,1,H,W]（类别索引，0 背景）
    """
    # 解析输入
    if isinstance(images, tuple):
        ppl_images, xpl_images = images
        is_mm = True
    else:
        ppl_images, xpl_images, is_mm = images, None, False

    # 转 numpy
    def _as_np(x):
        if isinstance(x, torch.Tensor):
            x = x.detach().cpu().float().numpy()
        return x
    ppl_images = _as_np(ppl_images)
    xpl_images = _as_np(xpl_images) if xpl_images is not None else None
    masks = _as_np(masks) if masks is not None else None

    # 归一化到 [0,255] uint8
    def _prep_imgs(x):
        if x is None:
            return None
        if x.max() <= 1:
            x = x * 255.0
        return x.astype(np.uint8)
    ppl_images = _prep_imgs(ppl_images)
    xpl_images = _prep_imgs(xpl_images)

    max_size = 1920
    bs, _, h, w = ppl_images.shape
    bs = int(min(bs, 8 if is_mm else 16))

    # 布局
    if is_mm:
        pairs_per_row = min(2, bs)
        rows = math.ceil(bs / pairs_per_row)
        cols = pairs_per_row * 2
        mosaic_h, mosaic_w = rows * h, cols * w
    else:
        ns = int(math.ceil(bs ** 0.5))
        rows = cols = ns
        mosaic_h, mosaic_w = rows * h, cols * w

    mosaic = np.full((mosaic_h, mosaic_w, 3), 255, dtype=np.uint8)

    # 铺原图
    for i in range(bs):
        if is_mm:
            r, c = divmod(i, pairs_per_row)
            ppl_x, ppl_y = c * 2 * w, r * h
            xpl_x, xpl_y = ppl_x + w, ppl_y
            mosaic[ppl_y:ppl_y + h, ppl_x:ppl_x + w] = ppl_images[i].transpose(1, 2, 0)
            if xpl_images is not None:
                mosaic[xpl_y:xpl_y + h, xpl_x:xpl_x + w] = xpl_images[i].transpose(1, 2, 0)
            cv2.putText(mosaic, 'PPL', (ppl_x + 10, ppl_y + 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255,255,255), 2)
            cv2.putText(mosaic, 'XPL', (xpl_x + 10, xpl_y + 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255,255,255), 2)
        else:
            x = (i % cols) * w
            y = (i // cols) * h
            mosaic[y:y + h, x:x + w] = ppl_images[i].transpose(1, 2, 0)

    # 缩放整幅
    scale = min(1.0, max_size / max(mosaic_h, mosaic_w))
    if scale < 1.0:
        new_h, new_w = int(round(mosaic_h * scale)), int(round(mosaic_w * scale))
        mosaic = cv2.resize(mosaic, (new_w, new_h), interpolation=cv2.INTER_LINEAR)

    # 叠加掩码
    if masks is not None:
        mm = masks[:, 0] if (masks.ndim == 4 and masks.shape[1] == 1) else masks  # [B,H,W]
        for i in range(bs):
            mask_hw = _ensure_mask_hw(mm[i], h, w)
            cls_all = _unique_non_bg(mask_hw)
            cls_show = _filter_cls_set(cls_all, selected_cls)

            tgt_w, tgt_h = int(round(w * scale)), int(round(h * scale))
            mask_small = cv2.resize(mask_hw.astype(np.uint8), (tgt_w, tgt_h), interpolation=cv2.INTER_NEAREST).astype(np.int32)

            if is_mm:
                r, c = divmod(i, pairs_per_row)
                # PPL
                y0 = int(round((r * h) * scale))
                x0 = int(round((c * 2 * w) * scale))
                y1, x1 = y0 + tgt_h, x0 + tgt_w
                roi = mosaic[y0:y1, x0:x1]
                mosaic[y0:y1, x0:x1] = _overlay_multiclass_mask_rgb(roi, mask_small, cls_show, alpha=alpha)
                # XPL
                y0 = int(round((r * h) * scale))
                x0 = int(round((c * 2 * w + w) * scale))
                y1, x1 = y0 + tgt_h, x0 + tgt_w
                roi = mosaic[y0:y1, x0:x1]
                mosaic[y0:y1, x0:x1] = _overlay_multiclass_mask_rgb(roi, mask_small, cls_show, alpha=alpha)
            else:
                x = (i % cols) * w
                y = (i // cols) * h
                y0 = int(round(y * scale))
                x0 = int(round(x * scale))
                y1, x1 = y0 + tgt_h, x0 + tgt_w
                roi = mosaic[y0:y1, x0:x1]
                mosaic[y0:y1, x0:x1] = _overlay_multiclass_mask_rgb(roi, mask_small, cls_show, alpha=alpha)

        # 汇总图例
        if legend_side in ("left", "right"):
            all_cls = set()
            for i in range(bs):
                all_cls |= _unique_non_bg(_ensure_mask_hw(mm[i], h, w))
            all_cls = _filter_cls_set(all_cls, selected_cls)
            _draw_legend_rgb(mosaic, all_cls, names=names, to=legend_side)

    # 保存
    out = cv2.cvtColor(mosaic, cv2.COLOR_RGB2BGR)
    cv2.imwrite(str(fname), out)
    return fname


@threaded
def plot_multimodal_comparison(
    ppl_image,
    xpl_image,
    pred_masks,
    gt_masks=None,
    fname="comparison.jpg",
    names=None,
    selected_cls=None,
    legend="both",
    alpha=0.45
):
    """
    多模态对照：同一套掩码同时覆盖 PPL/XPL；可选显示 GT 行
    legend: 'left' | 'right' | 'both' | 'none'
    """
    ppl_rgb = _to_hwc_uint8(ppl_image)
    xpl_rgb = _to_hwc_uint8(xpl_image)
    h, w = ppl_rgb.shape[:2]

    pm = _ensure_mask_hw(pred_masks, h, w)
    with_gt = gt_masks is not None
    gm = _ensure_mask_hw(gt_masks, h, w) if with_gt else None

    cls_all = _unique_non_bg(pm) | (_unique_non_bg(gm) if with_gt else set())
    cls_show = _filter_cls_set(cls_all, selected_cls)

    ppl_pred = _overlay_multiclass_mask_rgb(ppl_rgb, pm, cls_show, alpha=alpha)
    xpl_pred = _overlay_multiclass_mask_rgb(xpl_rgb, pm, cls_show, alpha=alpha)
    if with_gt:
        ppl_gt = _overlay_multiclass_mask_rgb(ppl_rgb, gm, cls_show, alpha=alpha)
        xpl_gt = _overlay_multiclass_mask_rgb(xpl_rgb, gm, cls_show, alpha=alpha)

    if with_gt:
        mosaic = np.zeros((h * 2, w * 2, 3), dtype=np.uint8)
        mosaic[0:h, 0:w] = ppl_pred
        mosaic[0:h, w:2*w] = xpl_pred
        mosaic[h:2*h, 0:w] = ppl_gt
        mosaic[h:2*h, w:2*w] = xpl_gt

        cv2.putText(mosaic, "PPL + Pred", (12, 32), cv2.FONT_HERSHEY_SIMPLEX, 1, (255,255,255), 2)
        cv2.putText(mosaic, "XPL + Pred", (w + 12, 32), cv2.FONT_HERSHEY_SIMPLEX, 1, (255,255,255), 2)
        cv2.putText(mosaic, "PPL + GT", (12, h + 32), cv2.FONT_HERSHEY_SIMPLEX, 1, (255,255,255), 2)
        cv2.putText(mosaic, "XPL + GT", (w + 12, h + 32), cv2.FONT_HERSHEY_SIMPLEX, 1, (255,255,255), 2)

        if legend in ("left", "both"):
            _draw_legend_rgb(mosaic[0:h, 0:w], cls_show, names=names, to='left')
        if legend in ("right", "both"):
            _draw_legend_rgb(mosaic[0:h, w:2*w], cls_show, names=names, to='right')
    else:
        mosaic = np.zeros((h, w * 2, 3), dtype=np.uint8)
        mosaic[0:h, 0:w] = ppl_pred
        mosaic[0:h, w:2*w] = xpl_pred
        cv2.putText(mosaic, "PPL + Pred", (12, 32), cv2.FONT_HERSHEY_SIMPLEX, 1, (255,255,255), 2)
        cv2.putText(mosaic, "XPL + Pred", (w + 12, 32), cv2.FONT_HERSHEY_SIMPLEX, 1, (255,255,255), 2)

        if legend in ("left", "both"):
            _draw_legend_rgb(mosaic[0:h, 0:w], cls_show, names=names, to='left')
        if legend in ("right", "both"):
            _draw_legend_rgb(mosaic[0:h, w:2*w], cls_show, names=names, to='right')

    out = cv2.cvtColor(mosaic, cv2.COLOR_RGB2BGR)
    cv2.imwrite(str(fname), out)
    return fname


def _create_mask_visualization(image, mask, alpha=0.45, selected_cls=None, names=None):
    """单张图像的掩码叠加（兼容函数）"""
    img_rgb = _to_hwc_uint8(image)
    h, w = img_rgb.shape[:2]
    mask_hw = _ensure_mask_hw(mask, h, w)
    cls_show = _filter_cls_set(_unique_non_bg(mask_hw), selected_cls)
    return _overlay_multiclass_mask_rgb(img_rgb, mask_hw, cls_show, alpha=alpha)


# ========================= 原有单模态网格可视化 =========================
@threaded
def plot_images_and_masks(images, targets, masks, paths=None, fname="images.jpg", names=None):
    """
    单模态网格：图像 + 框 + 掩码（与 YOLOv5 原版兼容）
    - 若传入 images 为 tuple，则内部转调多模态函数
    """
    if isinstance(images, tuple):
        return plot_multimodal_images_and_masks(images, targets, masks, paths, fname, names)

    if isinstance(images, torch.Tensor):
        images = images.cpu().float().numpy()
    if isinstance(targets, torch.Tensor):
        targets = targets.cpu().numpy()
    if isinstance(masks, torch.Tensor):
        masks = masks.cpu().numpy().astype(int)

    max_size, max_subplots = 1920, 16
    bs, _, h, w = images.shape
    bs = min(bs, max_subplots)
    ns = int(math.ceil(bs ** 0.5))
    if np.max(images[0]) <= 1:
        images *= 255

    mosaic = np.full((int(ns * h), int(ns * w), 3), 255, dtype=np.uint8)
    for i, im in enumerate(images[:bs]):
        x, y = int(w * (i // ns)), int(h * (i % ns))
        mosaic[y:y + h, x:x + w] = im.transpose(1, 2, 0)

    scale = max_size / ns / max(h, w)
    if scale < 1:
        h = math.ceil(scale * h)
        w = math.ceil(scale * w)
        mosaic = cv2.resize(mosaic, tuple(int(x * ns) for x in (w, h)))

    fs = int((h + w) * ns * 0.01)
    annotator = Annotator(mosaic, line_width=round(fs / 10), font_size=fs, pil=True, example=names)

    for i in range(bs):
        x, y = int(w * (i // ns)), int(h * (i % ns))
        annotator.rectangle([x, y, x + w, y + h], None, (255, 255, 255), width=2)
        if paths:
            annotator.text((x + 5, y + 5), text=Path(paths[i]).name[:40], txt_color=(220, 220, 220))

        if len(targets) > 0:
            idx = targets[:, 0] == i
            ti = targets[idx]
            boxes = xywh2xyxy(ti[:, 2:6]).T
            classes = ti[:, 1].astype(int)
            labels = ti.shape[1] == 6
            conf = None if labels else ti[:, 6]

            if boxes.shape[1]:
                if boxes.max() <= 1.01:
                    boxes[[0, 2]] *= w
                    boxes[[1, 3]] *= h
                elif scale < 1:
                    boxes *= scale
            boxes[[0, 2]] += x
            boxes[[1, 3]] += y

            # for j, box in enumerate(boxes.T.tolist()):
            #     cls = classes[j]
            #     color = colors(cls)
            #     cls_name = names[cls] if names else cls
            #     if labels or (conf is not None and conf[j] > 0.001):
            #         label = f"{cls_name}" if labels else f"{cls_name} {conf[j]:.1f}"
            #         annotator.box_label(box, label, color=color)

            # 掩码绘制（实例或语义）
            if (masks is not None) and len(ti) > 0:
                if masks.max() > 1.0:  # overlap: (1, H, W) 以实例编号编码
                    image_masks = masks[[i]]  # (1, H, W)
                    nl = len(ti)
                    index = np.arange(nl).reshape(nl, 1, 1) + 1
                    image_masks = np.repeat(image_masks, nl, axis=0)
                    image_masks = np.where(image_masks == index, 1.0, 0.0)
                else:
                    image_masks = masks[idx]  # (N, H, W)

                im = annotator.np.copy()
                for j in range(min(len(ti), len(image_masks))):
                    if labels or (conf is not None and conf[j] > 0.001):
                        color = colors(j)
                        mh, mw = image_masks[j].shape
                        mask = image_masks[j].astype(bool) if (mh == h and mw == w) \
                            else cv2.resize(image_masks[j].astype(np.uint8), (w, h)).astype(bool)
                        if np.any(mask):
                            roi = im[y:y + h, x:x + w]
                            roi[mask] = (roi[mask] * 0.3 + np.array(color) * 0.7)
                            im[y:y + h, x:x + w] = roi
                annotator.fromarray(im)

    annotator.save(fname)


def output_to_target(output, max_det=300):
    """将模型输出转为 [batch_id, class_id, x, y, w, h, conf]，用于绘图"""
    targets = []
    for i, o in enumerate(output):
        box, conf, cls = o[:max_det, :6].cpu().split((4, 1, 1), 1)
        j = torch.full((conf.shape[0], 1), i)
        targets.append(torch.cat((j, cls, xyxy2xywh(box), conf), 1))
    return torch.cat(targets, 0).numpy()


# ========================= 训练结果类可视化 =========================
def plot_results_with_masks(file="path/to/results.csv", dir="", best=True):
    """绘制训练结果（带突出 best/last 指标）"""
    save_dir = Path(file).parent if file else Path(dir)
    fig, ax = plt.subplots(2, 8, figsize=(18, 6), tight_layout=True)
    ax = ax.ravel()
    files = list(save_dir.glob("results*.csv"))
    assert len(files), f"No results.csv files found in {save_dir.resolve()}, nothing to plot."
    for f in files:
        try:
            data = pd.read_csv(f)
            index = np.argmax(
                0.9 * data.values[:, 8] + 0.1 * data.values[:, 7] + 0.9 * data.values[:, 12] + 0.1 * data.values[:, 11]
            )
            s = [x.strip() for x in data.columns]
            x = data.values[:, 0]
            for i, j in enumerate([1, 2, 3, 4, 5, 6, 9, 10, 13, 14, 15, 16, 7, 8, 11, 12]):
                y = data.values[:, j]
                ax[i].plot(x, y, marker=".", label=f.stem, linewidth=2, markersize=2)
                if best:
                    ax[i].scatter(index, y[index], color="r", label=f"best:{index}", marker="*", linewidth=3)
                    ax[i].set_title(s[j] + f"\n{round(y[index], 5)}")
                else:
                    ax[i].scatter(x[-1], y[-1], color="r", label="last", marker="*", linewidth=3)
                    ax[i].set_title(s[j] + f"\n{round(y[-1], 5)}")
        except Exception as e:
            print(f"Warning: Plotting error for {f}: {e}")
    ax[1].legend()
    fig.savefig(save_dir / "results.png", dpi=200)
    plt.close()


def plot_images(images, targets, paths=None, fname="images.jpg", names=None, max_size=1920, max_subplots=16):
    """仅绘制图像与检测框（不含掩码）"""
    if isinstance(images, torch.Tensor):
        images = images.cpu().float().numpy()
    if isinstance(targets, torch.Tensor):
        targets = targets.cpu().numpy()

    max_size, max_subplots = min(max_size, 1920), min(max_subplots, 16)
    bs, _, h, w = images.shape
    bs = min(bs, max_subplots)
    ns = int(math.ceil(bs ** 0.5))
    if np.max(images[0]) <= 1:
        images *= 255

    mosaic = np.full((int(ns * h), int(ns * w), 3), 255, dtype=np.uint8)
    for i, im in enumerate(images[:bs]):
        x, y = int(w * (i // ns)), int(h * (i % ns))
        mosaic[y:y + h, x:x + w] = im.transpose(1, 2, 0)

    scale = max_size / ns / max(h, w)
    if scale < 1:
        h = math.ceil(scale * h)
        w = math.ceil(scale * w)
        mosaic = cv2.resize(mosaic, tuple(int(x * ns) for x in (w, h)))

    fs = int((h + w) * ns * 0.01)
    annotator = Annotator(mosaic, line_width=round(fs / 10), font_size=fs, pil=True, example=names)
    for i in range(bs):
        x, y = int(w * (i // ns)), int(h * (i % ns))
        annotator.rectangle([x, y, x + w, y + h], None, (255, 255, 255), width=2)
        if paths:
            annotator.text((x + 5, y + 5 + h), text=Path(paths[i]).name[:40], txt_color=(220, 220, 220))
        if len(targets) > i:
            ti = targets[targets[:, 0] == i]
            boxes = xyxy2xywh(ti[:, 2:6]).T
            classes = ti[:, 1].astype(int)
            labels = ti.shape[1] == 6
            conf = None if labels else ti[:, 6]

            if boxes.shape[1]:
                if boxes.max() <= 1.01:
                    boxes[[0, 2]] *= w
                    boxes[[1, 3]] *= h
                elif scale < 1:
                    boxes *= scale
            boxes[[0, 2]] += x
            boxes[[1, 3]] += y
            for j, box in enumerate(boxes.T.tolist()):
                cls = classes[j]
                color = colors(cls)
                cls_name = names[cls] if names else cls
                if labels or conf[j] > 0.001:
                    label = f"{cls_name}" if labels else f"{cls_name} {conf[j]:.1f}"
                    annotator.box_label(box, label, color=color)
    annotator.save(fname)


def plot_lr_scheduler(optimizer, scheduler, epochs=300, save_dir=""):
    """绘制学习率调度曲线"""
    optimizer, scheduler = copy(optimizer), copy(scheduler)
    y = []
    for _ in range(epochs):
        scheduler.step()
        y.append(optimizer.param_groups[0]["lr"])
    plt.plot(y, ".-", label="LR")
    plt.xlabel("epoch")
    plt.ylabel("LR")
    plt.grid()
    plt.xlim(0, epochs)
    plt.ylim(0)
    plt.savefig(Path(save_dir) / "LR.png", dpi=200)
    plt.close()


def plot_val_txt():
    """示例：绘制 mAP 随阈值变化曲线（演示占位）"""
    x = np.arange(0.5, 0.95, 0.05)
    y = [
        [0.7497, 0.7718, 0.7783, 0.7719, 0.7611, 0.7462, 0.7274, 0.7075, 0.6906],
        [0.7497, 0.7718, 0.7783, 0.7719, 0.7611, 0.7462, 0.7274, 0.7075, 0.6906],
        [0.7497, 0.7718, 0.7783, 0.7719, 0.7611, 0.7462, 0.7274, 0.7075, 0.6906],
        [0.7497, 0.7718, 0.7783, 0.7719, 0.7611, 0.7462, 0.7274, 0.7075, 0.6906],
    ]
    plt.figure(figsize=(10, 6), tight_layout=True)
    plt.plot(x, y[0], marker="o", linewidth=2, label="YOLOv5s P5 640")
    plt.plot(x, y[1], marker="s", linewidth=2, label="YOLOv5s P5 1280")
    plt.plot(x, y[2], marker="^", linewidth=2, label="YOLOv5s P6 640")
    plt.plot(x, y[3], marker="D", linewidth=2, label="YOLOv5s P6 1280")
    plt.xlabel("Confidence")
    plt.ylabel("mAP@0.5")
    plt.xlim(0.5, 0.95)
    plt.ylim(0.65, 0.8)
    plt.legend()
    plt.savefig("val.png", dpi=300)
    plt.close()


def plot_targets_txt():
    """绘制训练目标类别分布直方图"""
    x = torch.load("targets.pt")
    x = torch.cat([torch.bincount(x[i][:, 1].long(), minlength=80) for i in range(len(x))], 0).float()
    x /= x.sum()
    plt.figure(figsize=(10, 3), tight_layout=True)
    plt.bar(range(len(x)), x)
    plt.xlabel("classes")
    plt.ylabel("instances")
    plt.title("Training targets")
    plt.savefig("targets.jpg", dpi=200)
    plt.close()


def plot_val_study(file="", dir="", x=None):
    """绘制不同模型尺寸的速度-精度对比（val study）"""
    save_dir = Path(file).parent if file else Path(dir)
    fig2, ax2 = plt.subplots(1, 1, figsize=(8, 4), tight_layout=True)
    for f in sorted(save_dir.glob("study*.txt")):
        y = np.loadtxt(f, dtype=np.float32, usecols=[0, 1, 2, 3, 7, 8, 9], ndmin=2).T
        x_axis = np.arange(y.shape[1]) if x is None else np.array(x)
        j = y[3].argmax() + 1
        ax2.plot(
            y[5, 1:j],
            y[3, 1:j] * 1e2,
            ".-",
            linewidth=2,
            markersize=8,
            label=f.stem.replace("study_coco_", "").replace("yolo", "YOLO"),
        )

    ax2.plot(1e3 / np.array([209, 140, 97, 58, 35, 18]),
             [34.6, 40.5, 43.0, 47.5, 49.7, 51.5],
             "k.-", linewidth=2, markersize=8, alpha=0.25, label="EfficientDet")

    ax2.grid(alpha=0.2)
    ax2.set_yticks(np.arange(20, 60, 5))
    ax2.set_xlim(0, 57)
    ax2.set_ylim(25, 55)
    ax2.set_xlabel("GPU Speed (ms/img)")
    ax2.set_ylabel("COCO AP val")
    ax2.legend(loc="lower right")
    f = save_dir / "study.png"
    print(f"Saving {f}...")
    plt.savefig(f, dpi=300)
    plt.close()


@TryExcept()  # known issue https://github.com/ultralytics/yolov5/issues/5395
def plot_labels(labels, names=(), save_dir=Path("")):
    """绘制数据集标签分布、相关性与示例框"""
    LOGGER.info(f"Plotting labels to {save_dir / 'labels.jpg'}... ")
    c, b = labels[:, 0], labels[:, 1:].transpose()
    nc = int(c.max() + 1)
    xdf = pd.DataFrame(b.transpose(), columns=["x", "y", "width", "height"])

    # seaborn 相关图
    sn.pairplot(xdf, corner=True, diag_kind="auto", kind="hist", diag_kws=dict(bins=50), plot_kws=dict(pmax=0.9))
    plt.savefig(save_dir / "labels_correlogram.jpg", dpi=200)
    plt.close()

    # 类直方图与尺寸分布
    matplotlib.use("svg")
    ax = plt.subplots(2, 2, figsize=(8, 8), tight_layout=True)[1].ravel()
    y = ax[0].hist(c, bins=np.linspace(0, nc, nc + 1) - 0.5, rwidth=0.8)
    with contextlib.suppress(Exception):
        [y[2].patches[i].set_color([x / 255 for x in colors(i)]) for i in range(nc)]
    ax[0].set_ylabel("instances")
    if 0 < len(names) < 30:
        ax[0].set_xticks(range(len(names)))
        ax[0].set_xticklabels(list(names.values()) if isinstance(names, dict) else names, rotation=90, fontsize=10)
    else:
        ax[0].set_xlabel("classes")
    sn.histplot(xdf, x="x", y="y", ax=ax[2], bins=50, pmax=0.9)
    sn.histplot(xdf, x="width", y="height", ax=ax[3], bins=50, pmax=0.9)

    # 绘制若干矩形示例
    labels[:, 1:3] = 0.5
    labels[:, 1:] = xywh2xyxy(labels[:, 1:]) * 2000
    img = Image.fromarray(np.ones((2000, 2000, 3), dtype=np.uint8) * 255)
    for cls, *box in labels[:1000]:
        ImageDraw.Draw(img).rectangle(box, width=1, outline=colors(cls))
    ax[1].imshow(img)
    ax[1].axis("off")

    for a in [0, 1, 2, 3]:
        for s in ["top", "right", "left", "bottom"]:
            ax[a].spines[s].set_visible(False)

    plt.savefig(save_dir / "labels.jpg", dpi=200)
    matplotlib.use("Agg")
    plt.close()


def plot_evolve(evolve_csv="path/to/evolve.csv"):
    """绘制超参进化结果（散点按点密度着色），并打印最佳行"""
    evolve_csv = Path(evolve_csv)
    data = pd.read_csv(evolve_csv)
    keys = [x.strip() for x in data.columns]
    x = data.values
    # 与原版一致的适应度
    def fitness(arr):
        return 0.9 * arr[:, 8] + 0.1 * arr[:, 7] + 0.9 * arr[:, 12] + 0.1 * arr[:, 11]
    f = fitness(x)
    j = np.argmax(f)
    plt.figure(figsize=(10, 12), tight_layout=True)
    matplotlib.rc("font", **{"size": 8})
    print(f"Best results from row {j} of {evolve_csv}:")
    for i, k in enumerate(keys[7:]):
        v = x[:, 7 + i]
        mu = v[j]
        plt.subplot(6, 5, i + 1)
        plt.scatter(v, f, c=hist2d(v, f, 20), cmap="viridis", alpha=0.8, edgecolors="none")
        plt.plot(mu, f.max(), "k+", markersize=15)
        plt.title(f"{k} = {mu:.3g}", fontdict={"size": 9})
        if i % 5 != 0:
            plt.yticks([])
        print(f"{k:>15}: {mu:.3g}")
    fimg = evolve_csv.with_suffix(".png")
    plt.savefig(fimg, dpi=200)
    plt.close()
    print(f"Saved {fimg}")


def plot_results(file="path/to/results.csv", dir=""):
    """绘制 results*.csv 的常规训练曲线，并保存平滑对比图"""
    save_dir = Path(file).parent if file else Path(dir)
    fig, ax = plt.subplots(2, 5, figsize=(12, 6), tight_layout=True)
    ax = ax.ravel()
    files = list(save_dir.glob("results*.csv"))
    assert len(files), f"No results.csv files found in {save_dir.resolve()}, nothing to plot."
    for f in files:
        try:
            data = pd.read_csv(f)
            s = [x.strip() for x in data.columns]
            xvals = data.values[:, 0]
            for i, j in enumerate([1, 2, 3, 4, 5, 8, 9, 10, 6, 7]):
                y = data.values[:, j].astype("float")
                ax[i].plot(xvals, y, marker=".", label=f.stem, linewidth=2, markersize=8)
                ax[i].plot(xvals, gaussian_filter1d(y, sigma=3), ":", label="smooth", linewidth=2)
                ax[i].set_title(s[j], fontsize=12)
        except Exception as e:
            LOGGER.info(f"Warning: Plotting error for {f}: {e}")
    ax[1].legend()
    fig.savefig(save_dir / "results.png", dpi=200)
    plt.close()

