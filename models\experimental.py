
"""实验性模块 (Experimental modules)."""

import math
import os
import numpy as np
import torch
import torch.nn as nn

# 设置环境变量以避免Git和JSON缓存问题
os.environ['GIT_PYTHON_REFRESH'] = 'quiet'  # 禁用Git检查警告

from ultralytics.utils.patches import torch_load  # 封装过的 torch.load
from utils.downloads import attempt_download  # 如果本地没有模型文件，会尝试下载


class Sum(nn.Module):
    """加权求和层 (Weighted sum)，可以对多个输入层进行加权求和
       参考论文：https://arxiv.org/abs/1911.09070
    """

    def __init__(self, n, weight=False):
        """
        n: 输入的层数
        weight: 是否使用可学习的权重
        """
        super().__init__()
        self.weight = weight
        self.iter = range(n - 1)  # 除了第一个输入，剩下 n-1 个需要叠加
        if weight:
            # 初始化参数 w（可学习的），通过 sigmoid 激活得到 0~2 之间的权重
            self.w = nn.Parameter(-torch.arange(1.0, n) / 2, requires_grad=True)

    def forward(self, x):
        """前向传播，输入是 n 个张量列表"""
        y = x[0]
        if self.weight:  # 如果有权重，按权重加权求和
            w = torch.sigmoid(self.w) * 2
            for i in self.iter:
                y = y + x[i + 1] * w[i]
        else:  # 否则直接逐层相加
            for i in self.iter:
                y = y + x[i + 1]
        return y


class MixConv2d(nn.Module):
    """混合深度卷积 (Mixed Depth-wise Convolution)，
       可以同时使用多种卷积核（例如 1x1, 3x3）做分组卷积，然后拼接。
       参考论文：https://arxiv.org/abs/1907.09595
    """

    def __init__(self, c1, c2, k=(1, 3), s=1, equal_ch=True):
        """
        c1: 输入通道数
        c2: 输出通道数
        k: 卷积核大小的元组，例如 (1, 3, 5)
        s: 步长 stride
        equal_ch: 是否让每组分配相等通道（True），否则按参数量均匀分配
        """
        super().__init__()
        n = len(k)  # 卷积核数量
        if equal_ch:  # 每组通道相等
            i = torch.linspace(0, n - 1e-6, c2).floor()  # 给输出通道分组
            c_ = [(i == g).sum() for g in range(n)]
        else:  # 使得每组的参数量接近
            b = [c2] + [0] * n
            a = np.eye(n + 1, n, k=-1)
            a -= np.roll(a, 1, axis=1)
            a *= np.array(k) ** 2
            a[0] = 1
            c_ = np.linalg.lstsq(a, b, rcond=None)[0].round()

        # 建立多种卷积核的卷积层
        self.m = nn.ModuleList(
            [nn.Conv2d(c1, int(c_), k, s, k // 2, groups=math.gcd(c1, int(c_)), bias=False) for k, c_ in zip(k, c_)]
        )
        self.bn = nn.BatchNorm2d(c2)
        self.act = nn.SiLU()  # 激活函数

    def forward(self, x):
        """前向传播：多个卷积结果 concat 后做 BN + SiLU 激活"""
        return self.act(self.bn(torch.cat([m(x) for m in self.m], 1)))


class Ensemble(nn.ModuleList):
    """模型集成 (Ensemble)，把多个模型组合在一起做预测"""

    def __init__(self):
        super().__init__()

    def forward(self, x, augment=False, profile=False, visualize=False):
        """
        前向传播：多个模型分别预测，再合并结果
        - 可以做 max/mean 集成（代码中注释掉了）
        - 默认是 concat 输出，交给后处理 NMS 处理
        """
        y = [module(x, augment, profile, visualize)[0] for module in self]
        # y = torch.stack(y).max(0)[0]   # 最大值集成
        # y = torch.stack(y).mean(0)     # 平均集成
        y = torch.cat(y, 1)  # 拼接输出
        return y, None


def attempt_load(weights, device=None, inplace=True, fuse=True):
    """
    加载单个或多个 YOLOv5 模型权重，并返回模型/模型集成 (Ensemble)
    
    参数：
        weights: 模型权重路径，可以是单个文件或列表
        device: 设备 (cpu/cuda)
        inplace: 是否允许 inplace 操作
        fuse: 是否融合 Conv+BN 加速推理
    """
    from models.yolo import Detect, Model

    model = Ensemble()
    for w in weights if isinstance(weights, list) else [weights]:
        ckpt = torch_load(attempt_download(w), map_location="cpu")  # 加载 checkpoint
        ckpt = (ckpt.get("ema") or ckpt["model"]).to(device).float()  # 拿到 FP32 模型

        # 确保 stride 和类别名存在
        if not hasattr(ckpt, "stride"):
            ckpt.stride = torch.tensor([32.0])
        if hasattr(ckpt, "names") and isinstance(ckpt.names, (list, tuple)):
            ckpt.names = dict(enumerate(ckpt.names))  # 转换为 dict

        # eval 模式，融合 Conv+BN
        model.append(ckpt.fuse().eval() if fuse and hasattr(ckpt, "fuse") else ckpt.eval())

    # 遍历模型内部模块，做一些兼容性更新
    for m in model.modules():
        t = type(m)
        if t in (nn.Hardswish, nn.LeakyReLU, nn.ReLU, nn.ReLU6, nn.SiLU, Detect, Model):
            m.inplace = inplace
            if t is Detect and not isinstance(m.anchor_grid, list):
                delattr(m, "anchor_grid")
                setattr(m, "anchor_grid", [torch.zeros(1)] * m.nl)
        elif t is nn.Upsample and not hasattr(m, "recompute_scale_factor"):
            m.recompute_scale_factor = None  # torch 1.11 兼容性

    # 如果只有一个模型，直接返回
    if len(model) == 1:
        return model[-1]

    # 否则返回集成模型
    print(f"Ensemble created with {weights}\n")
    for k in "names", "nc", "yaml":
        setattr(model, k, getattr(model[0], k))
    model.stride = model[torch.argmax(torch.tensor([m.stride.max() for m in model])).int()].stride
    assert all(model[0].nc == m.nc for m in model), f"不同模型类别数不一致: {[m.nc for m in model]}"
    return model
