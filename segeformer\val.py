#!/usr/bin/env python3
# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license
"""
SegFormer-style YOLOv5 Validation Script

Example:
    python -m segformer.val \
        --cfg models/segment/multi-yolov5n-formerhead-seg.yaml \
        --weights runs/segformer/train/segformer-exp1/weights/best.pt \
        --data data-seg/datasets.yaml --imgsz 640 --batch 16 --device 0
"""

import argparse
import os
import sys
from pathlib import Path

import torch

FILE = Path(__file__).resolve()
ROOT = FILE.parents[1]  # yolov5/
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

from models.common import DetectMultiBackend
from utils.torch_utils import select_device
from utils.segment.general import LOGGER, check_yaml, yaml_load, colorstr
from utils.segment.general import check_img_size
from utils.segment.dataloaders import create_multimodal_dataloader
from utils.segformer.metrics import SegmentationEvaluator


def build_model(cfg, nc, device, weights=None):
    model = DetectMultiBackend(cfg, ch=3, nc=nc)
    if weights:
        ckpt = torch.load(weights, map_location="cpu")
        state = ckpt.get("model", ckpt)  # 兼容直接传入 state_dict 的情况
        model.load_state_dict(state, strict=False)
        LOGGER.info(colorstr("weights: ") + str(weights))
    model.to(device).eval()
    return model


@torch.no_grad()
def run_validate(cfg, data_yaml, imgsz=640, batch=16, device="", workers=8, weights=None):
    device = select_device(device)
    data = yaml_load(check_yaml(data_yaml))
    cfg = check_yaml(cfg)

    nc = int(data["nc"])
    multimodal = bool(data.get("multimodal", False))
    imgsz = check_img_size(imgsz, s=32)

    loader, dataset = create_multimodal_dataloader(
        path=str(Path(data["path"]) / data["val"]),
        imgsz=imgsz,
        batch_size=batch,
        stride=32,
        single_cls=False,
        hyp={},
        augment=False,
        cache=False,
        pad=0.0,
        rect=True,
        rank=-1,
        workers=workers,
        image_weights=False,
        quad=False,
        prefix="val: ",
        shuffle=False,
        mask_downsample_ratio=1,
        overlap_mask=False,
        seed=0,
        xpl_path=(str(Path(data["path"]) / data["val_xpl"]) if multimodal else None),
        multimodal=multimodal,
    )

    model = build_model(cfg, nc, device, weights)

    evaluator = SegmentationEvaluator(num_classes=nc, mask_thr=0.5, conf_thr=0.05)
    for batch in loader:
        if multimodal:
            ppl_img, xpl_img, labels_out, paths, shapes, masks = batch
            ppl_img, xpl_img = ppl_img.to(device).float() / 255.0, xpl_img.to(device).float() / 255.0
            images = {"RGB": ppl_img, "X": xpl_img}
            B = ppl_img.shape[0]
        else:
            img, labels_out, paths, shapes, masks = batch
            img = img.to(device).float() / 255.0
            images = img
            B = img.shape[0]

        outputs = model(images)  # {'pred_logits','pred_masks'}
        targets = build_targets(labels_out, masks, B, device)
        evaluator.update(outputs, targets)

    metrics = evaluator.compute()
    LOGGER.info(colorstr("val: ") + f"{SegmentationEvaluator.pretty(metrics)}")
    return metrics


def build_targets(labels_out, masks, batch_size, device):
    targets = []
    lb = labels_out.to(device)
    mk = masks.to(device)
    # 将 labels_out 的第 0 列作为 image_id，把 mk（按顺序）拆回每张图像
    from collections import defaultdict
    idx_per_img = defaultdict(list)
    for i in range(lb.shape[0]):
        b = int(lb[i, 0].item())
        idx_per_img[b].append(i)
    for b in range(batch_size):
        inds = idx_per_img.get(b, [])
        if not inds:
            targets.append({"labels": torch.zeros((0,), dtype=torch.long, device=device),
                            "masks": torch.zeros((0, mk.shape[-2], mk.shape[-1]), dtype=mk.dtype, device=device)})
            continue
        targets.append({"labels": lb[inds, 1].long(), "masks": mk[inds]})
    return targets


def parse_opt():
    parser = argparse.ArgumentParser()
    parser.add_argument("--cfg", type=str, required=True, help="model yaml path")
    parser.add_argument("--weights", type=str, default=None, help="path to weights (best.pt/last.pt)")
    parser.add_argument("--data", type=str, required=True, help="dataset yaml path")
    parser.add_argument("--imgsz", type=int, default=640)
    parser.add_argument("--batch", type=int, default=16)
    parser.add_argument("--device", type=str, default="")
    parser.add_argument("--workers", type=int, default=8)
    return parser.parse_args()


if __name__ == "__main__":
    opt = parse_opt()
    run_validate(opt.cfg, opt.data, imgsz=opt.imgsz, batch=opt.batch, device=opt.device,
                 workers=opt.workers, weights=opt.weights)
