Image: tile_0022.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.9106
  Bounding Box: [443.20, 636.40, 820.80, 1030.40]
  Mask Area: 610 pixels
  Mask Ratio: 0.0216
  Mask BBox: [39, 54, 68, 84]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8447
  Bounding Box: [587.20, 1000.80, 839.20, 1215.20]
  Mask Area: 246 pixels
  Mask Ratio: 0.0087
  Mask BBox: [50, 83, 69, 97]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8403
  Bounding Box: [129.90, 1707.20, 485.20, 2011.20]
  Mask Area: 377 pixels
  Mask Ratio: 0.0134
  Mask BBox: [15, 138, 40, 161]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8105
  Bounding Box: [1016.80, 997.60, 1189.60, 1157.60]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [84, 83, 95, 94]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8101
  Bounding Box: [1404.80, 1587.20, 1625.60, 1792.00]
  Mask Area: 190 pixels
  Mask Ratio: 0.0067
  Mask BBox: [114, 128, 130, 143]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8091
  Bounding Box: [1251.20, 1052.00, 1478.40, 1356.00]
  Mask Area: 274 pixels
  Mask Ratio: 0.0097
  Mask BBox: [102, 87, 118, 109]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.7979
  Bounding Box: [282.40, 956.80, 373.20, 1116.80]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [27, 79, 33, 91]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.7974
  Bounding Box: [5.50, 1438.40, 155.20, 1659.20]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [5, 117, 16, 133]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.7949
  Bounding Box: [1435.20, 321.20, 1608.00, 514.80]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [117, 30, 129, 44]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.7915
  Bounding Box: [1723.20, 1023.20, 1899.20, 1159.20]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [139, 84, 152, 94]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.7891
  Bounding Box: [1072.80, 649.60, 1202.40, 860.00]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [88, 55, 96, 71]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.7876
  Bounding Box: [952.00, 1774.40, 1182.40, 2030.40]
  Mask Area: 277 pixels
  Mask Ratio: 0.0098
  Mask BBox: [79, 143, 96, 162]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.7852
  Bounding Box: [616.40, 222.00, 916.80, 564.40]
  Mask Area: 448 pixels
  Mask Ratio: 0.0159
  Mask BBox: [53, 22, 75, 48]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.7822
  Bounding Box: [836.80, 1072.00, 968.00, 1219.20]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [70, 88, 79, 99]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7783
  Bounding Box: [1078.40, 382.80, 1360.00, 626.80]
  Mask Area: 297 pixels
  Mask Ratio: 0.0105
  Mask BBox: [89, 34, 109, 51]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7729
  Bounding Box: [368.40, 1013.60, 563.60, 1205.60]
  Mask Area: 138 pixels
  Mask Ratio: 0.0049
  Mask BBox: [33, 84, 48, 98]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7720
  Bounding Box: [633.20, 1886.40, 841.60, 2048.00]
  Mask Area: 157 pixels
  Mask Ratio: 0.0056
  Mask BBox: [54, 152, 69, 164]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7666
  Bounding Box: [1750.40, 1536.80, 1878.40, 1689.60]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [141, 125, 150, 135]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7617
  Bounding Box: [0.00, 694.00, 129.20, 968.80]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [4, 59, 14, 79]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7573
  Bounding Box: [1584.00, 335.20, 1760.00, 545.60]
  Mask Area: 171 pixels
  Mask Ratio: 0.0061
  Mask BBox: [128, 31, 141, 46]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7544
  Bounding Box: [824.80, 1496.80, 986.40, 1625.60]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [69, 121, 81, 130]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7539
  Bounding Box: [796.80, 524.80, 921.60, 657.60]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [67, 45, 74, 55]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7476
  Bounding Box: [0.00, 1279.20, 112.40, 1429.60]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [4, 104, 12, 115]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7456
  Bounding Box: [1453.60, 1836.80, 1726.40, 2035.20]
  Mask Area: 205 pixels
  Mask Ratio: 0.0073
  Mask BBox: [118, 148, 138, 162]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7456
  Bounding Box: [523.60, 212.00, 640.40, 363.60]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [45, 21, 54, 32]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7456
  Bounding Box: [1447.20, 1210.40, 1559.20, 1388.00]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [118, 99, 125, 112]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7427
  Bounding Box: [348.00, 502.40, 566.40, 717.60]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [32, 44, 48, 60]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7393
  Bounding Box: [1640.00, 1268.00, 1755.20, 1416.80]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [133, 104, 140, 114]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7358
  Bounding Box: [449.20, 1312.00, 646.80, 1636.80]
  Mask Area: 271 pixels
  Mask Ratio: 0.0096
  Mask BBox: [40, 107, 54, 131]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7310
  Bounding Box: [103.20, 1928.00, 270.40, 2030.40]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [13, 155, 25, 162]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7305
  Bounding Box: [1112.80, 1372.80, 1261.60, 1544.00]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [91, 112, 102, 124]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7290
  Bounding Box: [802.40, 1633.60, 904.80, 1787.20]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [67, 132, 74, 143]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7271
  Bounding Box: [1154.40, 1660.80, 1322.40, 1804.80]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [95, 134, 107, 144]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7261
  Bounding Box: [931.20, 1287.20, 1086.40, 1397.60]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [77, 105, 86, 113]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7144
  Bounding Box: [1870.40, 450.40, 2048.00, 917.60]
  Mask Area: 441 pixels
  Mask Ratio: 0.0156
  Mask BBox: [151, 40, 164, 75]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7139
  Bounding Box: [1614.40, 1793.60, 1806.40, 1921.60]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [131, 145, 145, 152]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7134
  Bounding Box: [119.80, 1150.40, 277.80, 1438.40]
  Mask Area: 217 pixels
  Mask Ratio: 0.0077
  Mask BBox: [14, 94, 25, 116]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7119
  Bounding Box: [284.00, 189.20, 516.80, 508.40]
  Mask Area: 298 pixels
  Mask Ratio: 0.0106
  Mask BBox: [27, 19, 44, 43]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7109
  Bounding Box: [832.00, 1824.00, 972.80, 2022.40]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [69, 147, 79, 160]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7104
  Bounding Box: [1065.60, 551.60, 1161.60, 653.20]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [88, 48, 94, 55]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7104
  Bounding Box: [656.80, 1485.60, 756.00, 1736.00]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [56, 121, 63, 139]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7104
  Bounding Box: [719.60, 1808.00, 868.80, 1939.20]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [62, 146, 71, 155]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7080
  Bounding Box: [17.75, 1670.40, 207.00, 1932.80]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [6, 135, 20, 154]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7065
  Bounding Box: [119.50, 1575.20, 317.60, 1697.60]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [14, 128, 28, 136]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7056
  Bounding Box: [477.20, 1233.60, 585.20, 1310.40]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [42, 101, 49, 106]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.6997
  Bounding Box: [953.60, 814.40, 1097.60, 1022.40]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [79, 68, 89, 83]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.6953
  Bounding Box: [1016.00, 1159.20, 1222.40, 1368.80]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [84, 95, 99, 110]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.6919
  Bounding Box: [1878.40, 1252.80, 2022.40, 1620.80]
  Mask Area: 235 pixels
  Mask Ratio: 0.0083
  Mask BBox: [151, 102, 161, 130]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.6895
  Bounding Box: [1101.60, 646.40, 1340.00, 1065.60]
  Mask Area: 415 pixels
  Mask Ratio: 0.0147
  Mask BBox: [91, 55, 108, 87]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.6890
  Bounding Box: [1596.80, 549.20, 1833.60, 752.40]
  Mask Area: 220 pixels
  Mask Ratio: 0.0078
  Mask BBox: [129, 47, 146, 62]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.6846
  Bounding Box: [0.00, 524.80, 77.90, 648.00]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [4, 45, 9, 54]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.6836
  Bounding Box: [420.00, 1620.80, 534.40, 1742.40]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [37, 131, 45, 140]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.6831
  Bounding Box: [1137.60, 196.80, 1244.80, 362.40]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [93, 20, 101, 32]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.6772
  Bounding Box: [1694.40, 1692.80, 1876.80, 1836.80]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [137, 137, 150, 147]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.6772
  Bounding Box: [0.00, 1928.00, 158.20, 2040.00]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [4, 155, 16, 163]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.6719
  Bounding Box: [1920.00, 908.80, 2048.00, 1057.60]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [154, 75, 164, 85]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6631
  Bounding Box: [1109.60, 1499.20, 1232.80, 1651.20]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [91, 122, 100, 132]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6631
  Bounding Box: [349.00, 677.60, 432.00, 806.40]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [32, 57, 37, 66]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6611
  Bounding Box: [1424.00, 1460.80, 1566.40, 1577.60]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [116, 119, 125, 126]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6577
  Bounding Box: [859.20, 175.60, 1147.20, 342.80]
  Mask Area: 217 pixels
  Mask Ratio: 0.0077
  Mask BBox: [72, 18, 93, 30]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6514
  Bounding Box: [201.00, 168.00, 355.40, 291.20]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [20, 18, 31, 26]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6465
  Bounding Box: [1540.80, 1132.00, 1720.00, 1301.60]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [126, 95, 138, 105]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6431
  Bounding Box: [1702.40, 944.80, 1846.40, 1053.60]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [137, 78, 148, 86]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6431
  Bounding Box: [1852.80, 315.60, 1980.80, 496.40]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [150, 29, 158, 41]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6431
  Bounding Box: [1242.40, 1544.80, 1408.80, 1681.60]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [102, 125, 113, 135]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6421
  Bounding Box: [621.20, 1686.40, 725.20, 1875.20]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [53, 136, 60, 149]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6421
  Bounding Box: [1889.60, 1627.20, 2048.00, 2027.20]
  Mask Area: 375 pixels
  Mask Ratio: 0.0133
  Mask BBox: [152, 132, 164, 162]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6396
  Bounding Box: [453.60, 1809.60, 664.80, 2036.80]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [43, 146, 55, 163]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6362
  Bounding Box: [996.00, 1377.60, 1124.00, 1660.80]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [82, 113, 91, 131]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6318
  Bounding Box: [262.80, 1958.40, 444.40, 2028.80]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [25, 157, 38, 162]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6304
  Bounding Box: [722.40, 1215.20, 880.80, 1392.80]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [61, 99, 72, 112]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6294
  Bounding Box: [1551.20, 1504.80, 1662.40, 1580.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [126, 122, 132, 127]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6284
  Bounding Box: [1982.40, 1061.60, 2043.20, 1178.40]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [159, 87, 163, 95]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6279
  Bounding Box: [1164.80, 576.80, 1264.00, 684.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [95, 50, 102, 56]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6211
  Bounding Box: [111.20, 306.40, 336.00, 540.00]
  Mask Area: 265 pixels
  Mask Ratio: 0.0094
  Mask BBox: [13, 28, 30, 46]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6187
  Bounding Box: [1532.00, 1289.60, 1667.20, 1441.60]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [124, 105, 134, 116]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6167
  Bounding Box: [1149.60, 1774.40, 1434.40, 2048.00]
  Mask Area: 305 pixels
  Mask Ratio: 0.0108
  Mask BBox: [94, 143, 116, 165]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6133
  Bounding Box: [1312.00, 643.20, 1459.20, 850.40]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [107, 55, 117, 70]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6089
  Bounding Box: [807.20, 608.80, 1072.80, 943.20]
  Mask Area: 362 pixels
  Mask Ratio: 0.0128
  Mask BBox: [68, 52, 87, 77]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6045
  Bounding Box: [696.40, 560.80, 888.00, 828.80]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [59, 48, 73, 68]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6006
  Bounding Box: [1736.00, 264.80, 1857.60, 432.00]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [140, 25, 148, 37]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.5938
  Bounding Box: [626.00, 1270.40, 818.40, 1483.20]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [53, 104, 67, 119]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.5869
  Bounding Box: [183.60, 825.60, 263.60, 904.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [19, 69, 24, 74]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.5830
  Bounding Box: [1372.00, 1936.00, 1519.20, 2044.80]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [112, 156, 122, 163]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.5830
  Bounding Box: [287.00, 1392.80, 453.60, 1552.80]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [27, 113, 38, 125]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.5791
  Bounding Box: [1766.40, 504.80, 1897.60, 652.80]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [142, 44, 151, 53]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.5742
  Bounding Box: [1311.20, 1731.20, 1458.40, 1856.00]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [107, 140, 117, 148]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.5693
  Bounding Box: [1299.20, 1360.00, 1392.00, 1457.60]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [106, 111, 112, 117]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.5693
  Bounding Box: [1860.80, 1054.40, 2004.80, 1278.40]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [150, 87, 160, 103]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.5596
  Bounding Box: [1424.00, 1662.40, 1609.60, 1844.80]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [116, 134, 129, 148]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.5576
  Bounding Box: [1713.60, 758.80, 1864.00, 868.00]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [138, 64, 148, 70]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.5527
  Bounding Box: [950.40, 513.60, 1099.20, 656.80]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [79, 45, 89, 54]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5518
  Bounding Box: [7.30, 544.00, 83.10, 672.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [5, 47, 9, 56]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5508
  Bounding Box: [1400.80, 1337.60, 1514.40, 1462.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [114, 109, 122, 118]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5459
  Bounding Box: [128.90, 1443.20, 226.40, 1580.80]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [15, 117, 21, 126]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5415
  Bounding Box: [1310.40, 835.20, 1368.00, 929.60]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [107, 70, 110, 76]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5400
  Bounding Box: [1828.80, 895.20, 1947.20, 1016.80]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [147, 74, 156, 83]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5317
  Bounding Box: [153.60, 1013.60, 442.40, 1384.80]
  Mask Area: 424 pixels
  Mask Ratio: 0.0150
  Mask BBox: [16, 84, 38, 112]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5317
  Bounding Box: [1726.40, 1172.80, 1896.00, 1528.00]
  Mask Area: 336 pixels
  Mask Ratio: 0.0119
  Mask BBox: [139, 96, 152, 123]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5298
  Bounding Box: [636.80, 174.80, 744.80, 313.20]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [54, 18, 61, 28]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5220
  Bounding Box: [1328.80, 414.40, 1535.20, 781.60]
  Mask Area: 316 pixels
  Mask Ratio: 0.0112
  Mask BBox: [108, 37, 123, 65]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5200
  Bounding Box: [1792.00, 835.20, 1884.80, 952.00]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [144, 70, 151, 78]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5186
  Bounding Box: [888.00, 1708.80, 1078.40, 1833.60]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [74, 138, 88, 147]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5181
  Bounding Box: [1296.00, 1465.60, 1380.80, 1548.80]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [106, 119, 111, 124]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5161
  Bounding Box: [1317.60, 787.20, 1560.80, 1089.60]
  Mask Area: 316 pixels
  Mask Ratio: 0.0112
  Mask BBox: [107, 66, 125, 89]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5142
  Bounding Box: [1565.60, 1405.60, 1769.60, 1519.20]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [127, 114, 141, 122]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5117
  Bounding Box: [516.40, 470.00, 666.00, 610.00]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [45, 41, 56, 51]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5093
  Bounding Box: [5.50, 1924.80, 107.30, 2048.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [5, 155, 12, 164]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5063
  Bounding Box: [214.00, 580.40, 343.20, 824.80]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [21, 50, 30, 68]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5054
  Bounding Box: [1817.60, 656.00, 1881.60, 773.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [146, 56, 150, 64]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5005
  Bounding Box: [856.80, 281.20, 943.20, 370.80]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [71, 26, 77, 32]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5000
  Bounding Box: [1543.20, 1020.00, 1633.60, 1165.60]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [125, 84, 131, 94]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.4971
  Bounding Box: [4.40, 1596.80, 75.70, 1702.40]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [5, 129, 9, 136]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.4961
  Bounding Box: [666.00, 1476.00, 798.40, 1747.20]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [57, 120, 66, 140]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.4956
  Bounding Box: [352.80, 747.20, 499.20, 1017.60]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [32, 64, 41, 83]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.4946
  Bounding Box: [1290.40, 593.20, 1488.80, 854.40]
  Mask Area: 229 pixels
  Mask Ratio: 0.0081
  Mask BBox: [105, 51, 120, 70]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.4927
  Bounding Box: [406.00, 1894.40, 524.40, 2032.00]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [36, 152, 44, 162]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.4814
  Bounding Box: [465.60, 314.00, 604.00, 456.40]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [41, 29, 51, 39]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.4800
  Bounding Box: [1057.60, 645.60, 1243.20, 917.60]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [87, 55, 101, 75]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.4756
  Bounding Box: [517.60, 1665.60, 639.20, 1800.00]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [45, 135, 53, 144]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.4741
  Bounding Box: [825.60, 1331.20, 1016.00, 1492.80]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [69, 108, 83, 120]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.4736
  Bounding Box: [1204.80, 1346.40, 1300.80, 1461.60]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [99, 110, 105, 118]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.4692
  Bounding Box: [11.75, 1188.80, 128.40, 1292.80]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [5, 97, 13, 104]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.4624
  Bounding Box: [1684.80, 1549.60, 1758.40, 1656.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [136, 126, 141, 132]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.4617
  Bounding Box: [45.25, 157.20, 206.80, 356.40]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [8, 17, 20, 31]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.4590
  Bounding Box: [324.60, 1603.20, 438.00, 1712.00]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [30, 130, 38, 137]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.4587
  Bounding Box: [1528.80, 1809.60, 1795.20, 1963.20]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [124, 146, 144, 157]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4543
  Bounding Box: [1199.20, 1108.00, 1269.60, 1186.40]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [98, 91, 103, 96]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.4492
  Bounding Box: [782.40, 531.60, 988.80, 654.00]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [66, 46, 81, 55]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4421
  Bounding Box: [168.60, 758.40, 254.60, 825.60]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [18, 64, 23, 68]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4382
  Bounding Box: [289.20, 868.00, 372.40, 954.40]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [27, 72, 32, 78]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4377
  Bounding Box: [457.20, 951.20, 565.20, 1024.80]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [40, 79, 48, 84]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4351
  Bounding Box: [532.00, 1472.00, 662.40, 1662.40]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [46, 120, 55, 133]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4331
  Bounding Box: [1349.60, 421.20, 1506.40, 702.00]
  Mask Area: 192 pixels
  Mask Ratio: 0.0068
  Mask BBox: [110, 37, 121, 58]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4324
  Bounding Box: [1884.80, 1595.20, 2025.60, 1755.20]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [152, 129, 162, 141]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4268
  Bounding Box: [1979.20, 355.00, 2036.80, 453.60]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [159, 32, 163, 39]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4226
  Bounding Box: [817.60, 1007.20, 896.00, 1096.80]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [68, 83, 73, 89]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4211
  Bounding Box: [864.00, 313.40, 987.20, 388.00]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [72, 29, 81, 34]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4087
  Bounding Box: [1530.40, 640.80, 1604.80, 724.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [124, 55, 129, 60]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4060
  Bounding Box: [491.20, 445.60, 642.40, 597.60]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [43, 39, 54, 50]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4055
  Bounding Box: [300.80, 1548.00, 440.00, 1716.80]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [28, 125, 38, 138]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4026
  Bounding Box: [349.60, 1189.60, 485.60, 1308.00]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [32, 97, 41, 106]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4016
  Bounding Box: [8.50, 1083.20, 69.70, 1220.80]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 89, 8, 99]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4016
  Bounding Box: [907.20, 1643.20, 993.60, 1739.20]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [75, 133, 81, 139]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4016
  Bounding Box: [1361.60, 1628.80, 1625.60, 1856.00]
  Mask Area: 221 pixels
  Mask Ratio: 0.0078
  Mask BBox: [111, 132, 130, 148]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4011
  Bounding Box: [1192.80, 1795.20, 1391.20, 2028.80]
  Mask Area: 207 pixels
  Mask Ratio: 0.0073
  Mask BBox: [98, 145, 112, 162]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.3970
  Bounding Box: [1177.60, 215.20, 1324.80, 389.20]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [96, 21, 107, 33]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.3965
  Bounding Box: [9.00, 952.80, 127.20, 1159.20]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [5, 79, 12, 94]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.3960
  Bounding Box: [1442.40, 1728.00, 1598.40, 1849.60]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [117, 139, 128, 148]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.3955
  Bounding Box: [541.60, 198.60, 653.60, 338.60]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [47, 20, 55, 30]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.3923
  Bounding Box: [1341.60, 884.80, 1551.20, 1108.80]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [109, 74, 124, 90]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.3918
  Bounding Box: [1619.20, 724.00, 1696.00, 801.60]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [131, 61, 136, 66]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.3901
  Bounding Box: [1747.20, 1913.60, 1910.40, 2041.60]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [141, 154, 153, 163]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.3872
  Bounding Box: [1449.60, 1436.00, 1580.80, 1556.00]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [118, 117, 127, 125]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.3845
  Bounding Box: [726.80, 1507.20, 859.20, 1705.60]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [61, 122, 71, 137]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.3831
  Bounding Box: [323.20, 692.00, 425.60, 820.80]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [30, 59, 37, 68]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.3811
  Bounding Box: [1667.20, 1236.80, 1840.00, 1451.20]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [135, 101, 147, 117]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.3794
  Bounding Box: [152.50, 914.40, 252.00, 1012.00]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [16, 76, 23, 83]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.3794
  Bounding Box: [12.10, 1112.00, 74.40, 1249.60]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [5, 91, 9, 101]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.3757
  Bounding Box: [80.00, 519.20, 244.80, 771.20]
  Mask Area: 214 pixels
  Mask Ratio: 0.0076
  Mask BBox: [11, 45, 23, 64]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.3752
  Bounding Box: [328.00, 664.00, 419.20, 795.20]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [30, 56, 36, 66]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.3743
  Bounding Box: [1956.80, 1044.80, 2048.00, 1172.80]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [157, 86, 163, 95]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.3711
  Bounding Box: [707.20, 1261.60, 856.00, 1420.00]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [60, 103, 70, 114]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.3701
  Bounding Box: [753.60, 1492.00, 956.80, 1681.60]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [63, 121, 78, 135]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.3679
  Bounding Box: [306.60, 765.60, 380.00, 863.20]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [28, 64, 33, 71]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.3672
  Bounding Box: [290.20, 1140.80, 484.00, 1340.80]
  Mask Area: 172 pixels
  Mask Ratio: 0.0061
  Mask BBox: [27, 94, 41, 108]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.3669
  Bounding Box: [929.60, 374.40, 1094.40, 520.00]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [77, 34, 89, 44]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.3665
  Bounding Box: [958.40, 778.40, 1182.40, 1044.00]
  Mask Area: 189 pixels
  Mask Ratio: 0.0067
  Mask BBox: [79, 65, 95, 85]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.3662
  Bounding Box: [39.90, 1936.00, 231.20, 2048.00]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [8, 156, 22, 164]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.3652
  Bounding Box: [1764.80, 406.00, 1870.40, 557.20]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [142, 36, 150, 47]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.3652
  Bounding Box: [1604.80, 1568.00, 1726.40, 1756.80]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [130, 127, 138, 141]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3647
  Bounding Box: [72.10, 129.80, 344.00, 336.60]
  Mask Area: 248 pixels
  Mask Ratio: 0.0088
  Mask BBox: [10, 15, 30, 30]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3643
  Bounding Box: [1155.20, 187.60, 1267.20, 340.40]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [95, 19, 102, 30]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3606
  Bounding Box: [895.20, 330.60, 997.60, 406.40]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [74, 30, 81, 35]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3606
  Bounding Box: [1558.40, 779.20, 1763.20, 964.80]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [126, 65, 141, 79]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3604
  Bounding Box: [1675.20, 968.00, 1832.00, 1065.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [135, 80, 147, 87]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3599
  Bounding Box: [1643.20, 206.60, 1768.00, 323.80]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [133, 21, 142, 29]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3525
  Bounding Box: [1333.60, 782.40, 1543.20, 1000.00]
  Mask Area: 219 pixels
  Mask Ratio: 0.0078
  Mask BBox: [109, 66, 124, 82]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3525
  Bounding Box: [332.80, 796.00, 471.20, 997.60]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [30, 67, 40, 81]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3523
  Bounding Box: [1313.60, 1342.40, 1416.00, 1448.00]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [107, 109, 114, 117]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3518
  Bounding Box: [537.60, 192.80, 709.60, 352.80]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [46, 20, 59, 31]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3501
  Bounding Box: [1478.40, 1169.60, 1678.40, 1350.40]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [120, 96, 135, 109]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3489
  Bounding Box: [507.20, 1414.40, 676.00, 1684.80]
  Mask Area: 217 pixels
  Mask Ratio: 0.0077
  Mask BBox: [44, 115, 56, 135]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3486
  Bounding Box: [788.00, 1648.00, 888.80, 1817.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [66, 133, 73, 145]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3486
  Bounding Box: [1910.40, 230.60, 2022.40, 336.20]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [154, 23, 161, 30]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3469
  Bounding Box: [1961.60, 249.40, 2041.60, 354.20]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [158, 24, 163, 31]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3469
  Bounding Box: [1353.60, 869.60, 1619.20, 1173.60]
  Mask Area: 325 pixels
  Mask Ratio: 0.0115
  Mask BBox: [110, 72, 130, 95]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3464
  Bounding Box: [661.60, 1851.20, 986.40, 2043.20]
  Mask Area: 250 pixels
  Mask Ratio: 0.0089
  Mask BBox: [56, 149, 81, 163]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3440
  Bounding Box: [730.80, 516.80, 915.20, 733.60]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [62, 45, 74, 61]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3430
  Bounding Box: [536.00, 1659.20, 689.60, 1832.00]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [46, 134, 57, 147]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3420
  Bounding Box: [1186.40, 1359.20, 1293.60, 1484.00]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [97, 111, 105, 119]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3411
  Bounding Box: [1010.40, 544.00, 1176.80, 666.40]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [83, 47, 95, 54]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3408
  Bounding Box: [1534.40, 1516.80, 1656.00, 1612.80]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [124, 123, 132, 128]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3406
  Bounding Box: [883.20, 713.60, 1112.00, 998.40]
  Mask Area: 298 pixels
  Mask Ratio: 0.0106
  Mask BBox: [73, 60, 90, 81]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3401
  Bounding Box: [695.20, 1056.80, 994.40, 1212.00]
  Mask Area: 196 pixels
  Mask Ratio: 0.0069
  Mask BBox: [59, 87, 79, 98]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3401
  Bounding Box: [1005.60, 1425.60, 1186.40, 1664.00]
  Mask Area: 206 pixels
  Mask Ratio: 0.0073
  Mask BBox: [83, 116, 96, 133]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3398
  Bounding Box: [1152.00, 1362.40, 1288.00, 1508.00]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [94, 111, 104, 121]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3376
  Bounding Box: [567.20, 483.20, 688.80, 604.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [49, 42, 57, 51]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3364
  Bounding Box: [1216.00, 1358.40, 1318.40, 1483.20]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [99, 111, 106, 119]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3362
  Bounding Box: [1132.00, 497.60, 1300.00, 679.20]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [93, 43, 105, 57]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3362
  Bounding Box: [1076.00, 592.80, 1237.60, 856.80]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [89, 51, 100, 68]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3337
  Bounding Box: [1419.20, 1355.20, 1524.80, 1481.60]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [115, 110, 123, 119]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3328
  Bounding Box: [1338.40, 206.20, 1426.40, 267.40]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [109, 21, 115, 24]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3320
  Bounding Box: [1201.60, 1299.20, 1292.80, 1428.80]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [98, 106, 104, 115]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3320
  Bounding Box: [186.40, 1699.20, 266.00, 1811.20]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [19, 137, 24, 145]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3289
  Bounding Box: [446.00, 1625.60, 610.00, 1776.00]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [39, 131, 51, 142]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [256.40, 1267.20, 419.60, 1417.60]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [25, 103, 36, 114]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3213
  Bounding Box: [839.20, 269.80, 928.80, 365.20]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [71, 26, 76, 32]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3213
  Bounding Box: [839.20, 295.40, 928.80, 390.80]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [71, 28, 76, 34]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3203
  Bounding Box: [198.80, 1392.00, 293.60, 1510.40]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [20, 113, 26, 120]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3191
  Bounding Box: [1479.20, 1236.80, 1628.80, 1411.20]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [120, 101, 131, 114]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3179
  Bounding Box: [1560.00, 1430.40, 1732.80, 1558.40]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [126, 116, 139, 125]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3149
  Bounding Box: [12.85, 1171.20, 147.80, 1324.80]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [6, 96, 15, 107]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3132
  Bounding Box: [150.10, 989.60, 364.40, 1308.00]
  Mask Area: 304 pixels
  Mask Ratio: 0.0108
  Mask BBox: [16, 82, 32, 106]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3120
  Bounding Box: [901.60, 1313.60, 1074.40, 1411.20]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [75, 107, 86, 114]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3115
  Bounding Box: [1527.20, 1071.20, 1616.00, 1192.80]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [124, 88, 130, 95]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3108
  Bounding Box: [785.60, 551.20, 899.20, 665.60]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [66, 48, 74, 55]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3096
  Bounding Box: [0.00, 1216.00, 184.40, 1430.40]
  Mask Area: 191 pixels
  Mask Ratio: 0.0068
  Mask BBox: [4, 99, 18, 115]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3088
  Bounding Box: [483.20, 1225.60, 608.00, 1376.00]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [42, 100, 51, 111]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3086
  Bounding Box: [995.20, 1023.20, 1172.80, 1184.80]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [82, 84, 95, 96]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3083
  Bounding Box: [723.60, 1716.80, 804.80, 1812.80]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [61, 139, 66, 145]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3081
  Bounding Box: [442.80, 1777.60, 610.00, 1976.00]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [39, 143, 51, 158]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [894.40, 455.20, 988.80, 536.80]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [74, 40, 80, 45]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3071
  Bounding Box: [1801.60, 863.20, 1939.20, 1005.60]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [145, 72, 155, 82]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3049
  Bounding Box: [1046.40, 520.40, 1139.20, 640.40]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [86, 45, 92, 54]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3040
  Bounding Box: [966.40, 1676.80, 1113.60, 1817.60]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [80, 135, 90, 145]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3027
  Bounding Box: [0.00, 515.60, 54.00, 644.40]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [3, 45, 8, 54]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3027
  Bounding Box: [1891.20, 931.20, 2028.80, 1088.00]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [152, 77, 162, 88]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3027
  Bounding Box: [0.00, 1891.20, 130.00, 2025.60]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [4, 152, 14, 162]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [1696.00, 227.00, 1859.20, 408.00]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [137, 22, 149, 35]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.2993
  Bounding Box: [1696.00, 1532.80, 1888.00, 1705.60]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [137, 124, 151, 137]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.2979
  Bounding Box: [1638.40, 1075.20, 1769.60, 1209.60]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [132, 88, 142, 98]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.2979
  Bounding Box: [1688.00, 1942.40, 1828.80, 2048.00]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [136, 156, 146, 163]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.2974
  Bounding Box: [1747.20, 784.00, 1875.20, 908.80]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [141, 66, 150, 74]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.2966
  Bounding Box: [1934.40, 1248.00, 2027.20, 1390.40]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [156, 102, 162, 112]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.2964
  Bounding Box: [274.40, 1207.20, 459.20, 1389.60]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [26, 99, 39, 112]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.2949
  Bounding Box: [1932.80, 338.40, 2048.00, 468.80]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [155, 31, 163, 40]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.2949
  Bounding Box: [26.20, 1684.80, 314.20, 1966.40]
  Mask Area: 313 pixels
  Mask Ratio: 0.0111
  Mask BBox: [7, 136, 28, 157]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.2935
  Bounding Box: [635.60, 1478.40, 734.80, 1712.00]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [54, 120, 61, 137]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.2925
  Bounding Box: [466.00, 1774.40, 551.60, 1902.40]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [41, 143, 46, 152]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.2925
  Bounding Box: [1659.20, 1816.00, 1844.80, 2004.80]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [134, 146, 148, 160]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.2905
  Bounding Box: [537.60, 1477.60, 684.00, 1731.20]
  Mask Area: 176 pixels
  Mask Ratio: 0.0062
  Mask BBox: [46, 120, 57, 139]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.2896
  Bounding Box: [208.80, 1211.20, 438.40, 1435.20]
  Mask Area: 275 pixels
  Mask Ratio: 0.0097
  Mask BBox: [21, 99, 38, 116]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.2891
  Bounding Box: [460.80, 1077.60, 596.00, 1207.20]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [40, 89, 49, 98]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.2888
  Bounding Box: [425.20, 1868.80, 545.20, 2016.00]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [38, 150, 46, 161]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.2886
  Bounding Box: [27.60, 1270.40, 136.00, 1409.60]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [7, 104, 12, 114]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.2886
  Bounding Box: [468.40, 1220.80, 563.60, 1321.60]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [41, 100, 48, 107]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.2878
  Bounding Box: [1050.40, 551.20, 1138.40, 668.80]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [87, 48, 92, 54]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.2869
  Bounding Box: [1093.60, 631.60, 1223.20, 824.00]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [90, 54, 96, 68]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.2859
  Bounding Box: [1303.20, 676.80, 1527.20, 960.00]
  Mask Area: 271 pixels
  Mask Ratio: 0.0096
  Mask BBox: [106, 57, 123, 78]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.2854
  Bounding Box: [347.20, 1619.20, 448.00, 1731.20]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [32, 131, 38, 138]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.2852
  Bounding Box: [1171.20, 1021.60, 1251.20, 1112.80]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [96, 84, 101, 90]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.2847
  Bounding Box: [1747.20, 1726.40, 1875.20, 1867.20]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [141, 139, 150, 149]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.2842
  Bounding Box: [654.40, 156.20, 760.00, 288.20]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [56, 17, 61, 26]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.2837
  Bounding Box: [231.60, 1945.60, 436.00, 2048.00]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [23, 156, 38, 164]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.2834
  Bounding Box: [1672.00, 1256.80, 1774.40, 1412.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [135, 103, 140, 114]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.2803
  Bounding Box: [138.40, 1469.60, 270.80, 1656.00]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [15, 119, 25, 133]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.2800
  Bounding Box: [150.80, 1568.00, 419.60, 1708.80]
  Mask Area: 184 pixels
  Mask Ratio: 0.0065
  Mask BBox: [16, 127, 36, 137]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.2793
  Bounding Box: [10.75, 1284.00, 113.60, 1525.60]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [5, 105, 12, 123]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.2769
  Bounding Box: [250.20, 1329.60, 454.40, 1532.80]
  Mask Area: 201 pixels
  Mask Ratio: 0.0071
  Mask BBox: [24, 108, 39, 123]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.2737
  Bounding Box: [868.80, 304.00, 948.80, 386.80]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [72, 28, 78, 34]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.2737
  Bounding Box: [224.20, 176.00, 453.60, 388.00]
  Mask Area: 230 pixels
  Mask Ratio: 0.0081
  Mask BBox: [22, 18, 39, 34]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2725
  Bounding Box: [130.20, 556.80, 324.20, 800.00]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [15, 48, 29, 66]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2725
  Bounding Box: [1532.80, 1307.20, 1728.00, 1518.40]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [124, 107, 138, 122]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2712
  Bounding Box: [1082.40, 1472.80, 1205.60, 1635.20]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [89, 120, 98, 131]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2693
  Bounding Box: [15.55, 1836.80, 136.80, 1993.60]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [6, 148, 14, 159]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2678
  Bounding Box: [1316.80, 1451.20, 1412.80, 1539.20]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [107, 118, 114, 124]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2678
  Bounding Box: [1316.80, 1476.80, 1412.80, 1564.80]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [107, 120, 114, 126]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2678
  Bounding Box: [304.60, 1372.00, 478.40, 1525.60]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [28, 112, 41, 123]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2673
  Bounding Box: [1.70, 1069.60, 94.20, 1224.80]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [5, 88, 11, 99]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2671
  Bounding Box: [1140.80, 596.80, 1248.00, 702.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [94, 51, 101, 57]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2668
  Bounding Box: [1608.00, 1310.40, 1790.40, 1524.80]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [130, 107, 143, 123]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2661
  Bounding Box: [1484.00, 1980.80, 1625.60, 2048.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [120, 159, 130, 163]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2659
  Bounding Box: [443.60, 1593.60, 558.80, 1715.20]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [39, 129, 47, 137]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2659
  Bounding Box: [868.00, 1175.20, 1023.20, 1301.60]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [72, 96, 83, 105]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2656
  Bounding Box: [848.00, 1266.40, 1056.00, 1469.60]
  Mask Area: 192 pixels
  Mask Ratio: 0.0068
  Mask BBox: [71, 103, 86, 118]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2649
  Bounding Box: [1852.80, 11.20, 2048.00, 215.20]
  Mask Area: 250 pixels
  Mask Ratio: 0.0089
  Mask BBox: [149, 5, 164, 20]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2637
  Bounding Box: [832.80, 1672.00, 1042.40, 1819.20]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [70, 135, 85, 146]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2627
  Bounding Box: [1982.40, 1222.40, 2046.40, 1382.40]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [159, 100, 163, 111]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2617
  Bounding Box: [755.20, 472.40, 918.40, 661.20]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [63, 41, 75, 55]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2612
  Bounding Box: [321.60, 1625.60, 420.00, 1728.00]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [30, 131, 36, 137]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2603
  Bounding Box: [1492.80, 495.60, 1608.00, 600.40]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [121, 43, 129, 50]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2600
  Bounding Box: [172.80, 943.20, 274.80, 1029.60]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [18, 78, 25, 82]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2595
  Bounding Box: [1876.80, 235.80, 1992.00, 317.40]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [151, 23, 159, 28]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2590
  Bounding Box: [1841.60, 1038.40, 1976.00, 1249.60]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [148, 86, 158, 101]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2590
  Bounding Box: [1729.60, 1699.20, 1892.80, 1891.20]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [140, 137, 151, 151]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2588
  Bounding Box: [1289.60, 1390.40, 1390.40, 1520.00]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [105, 113, 112, 122]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2583
  Bounding Box: [380.40, 1611.20, 524.40, 1732.80]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [34, 130, 44, 139]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2576
  Bounding Box: [0.00, 390.40, 121.90, 581.60]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [4, 35, 13, 49]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2573
  Bounding Box: [1483.20, 1168.00, 1555.20, 1233.60]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [120, 96, 125, 100]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2568
  Bounding Box: [1567.20, 1492.80, 1676.80, 1595.20]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [127, 121, 134, 128]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2568
  Bounding Box: [1567.20, 1518.40, 1676.80, 1620.80]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [127, 123, 134, 130]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2568
  Bounding Box: [299.80, 962.40, 390.80, 1144.80]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [28, 80, 34, 93]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2561
  Bounding Box: [1332.00, 832.80, 1397.60, 928.80]
  Mask Area: 11 pixels
  Mask Ratio: 0.0004
  Mask BBox: [109, 70, 110, 75]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2561
  Bounding Box: [1306.40, 858.40, 1372.00, 954.40]
  Mask Area: 21 pixels
  Mask Ratio: 0.0007
  Mask BBox: [107, 72, 110, 78]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2561
  Bounding Box: [1332.00, 858.40, 1397.60, 954.40]
  Mask Area: 7 pixels
  Mask Ratio: 0.0002
  Mask BBox: [109, 72, 110, 75]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2559
  Bounding Box: [1367.20, 10.60, 1461.60, 69.40]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [111, 5, 118, 8]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2556
  Bounding Box: [1553.60, 1275.20, 1688.00, 1416.00]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [126, 104, 135, 114]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2542
  Bounding Box: [1977.60, 325.20, 2038.40, 430.00]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [159, 30, 163, 37]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2542
  Bounding Box: [2003.20, 325.20, 2048.00, 430.00]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [161, 30, 163, 37]

