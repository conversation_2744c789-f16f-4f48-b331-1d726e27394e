#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反向传播验证脚本
整合SegFormer损失函数和双模态数据加载器，验证模型训练可行性
"""

import sys
import torch
import torch.nn as nn
import torch.optim as optim
from pathlib import Path
import numpy as np
from tqdm import tqdm
import yaml

# 添加项目根目录到路径
ROOT = Path(__file__).resolve().parent
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

from models.yolo import Model
from utils.segformer.loss import SetCriterion
from utils.segment.dataloaders import LoadImagesAndLabelsAndMasks
from utils.torch_utils import select_device

def create_mock_dataset():
    """
    创建模拟数据集用于测试
    """
    # 创建模拟图像和标签
    batch_size = 2
    img_size = 640
    
    # 模拟双图像输入 - 需要分别的RGB和X输入
    ppl_images = torch.randn(batch_size, 3, img_size, img_size)
    xpl_images = torch.randn(batch_size, 3, img_size, img_size)
    # 模型期望多输入格式：[RGB_tensor, X_tensor]
    dual_images = [ppl_images, xpl_images]
    
    # 模拟目标数据
    targets = []
    for i in range(batch_size):
        # 每个样本有2-3个目标
        num_objects = np.random.randint(2, 4)
        labels = torch.randint(0, 80, (num_objects,))  # 类别标签
        
        # 创建模拟掩码 [num_objects, H, W]
        masks = torch.zeros(num_objects, img_size//4, img_size//4)  # 下采样4倍
        for j in range(num_objects):
            # 创建随机矩形掩码
            x1, y1 = np.random.randint(0, img_size//8, 2)
            x2, y2 = x1 + np.random.randint(20, 60), y1 + np.random.randint(20, 60)
            x2, y2 = min(x2, img_size//4), min(y2, img_size//4)
            masks[j, y1:y2, x1:x2] = 1.0
        
        targets.append({
            'labels': labels,
            'masks': masks
        })
    
    return dual_images, targets

def test_model_forward():
    """
    测试模型前向传播
    """
    print("=== 测试模型前向传播 ===")
    
    # 加载模型配置
    model_config = 'd:/yolov5/models/segment/multi-yolov5n-mid-segformer.yaml'
    device = select_device('')
    
    try:
        # 创建模型
        model = Model(model_config, ch=6, nc=80)  # 6通道输入，80类
        model = model.to(device)
        model.train()
        
        print(f"✓ 模型创建成功: {sum(p.numel() for p in model.parameters())} 参数")
        
        # 创建模拟数据
        dual_images, targets = create_mock_dataset()
        # 将多输入移动到设备
        dual_images = [img.to(device) for img in dual_images]
        
        # 前向传播
        with torch.no_grad():
            outputs = model(dual_images)
        
        print(f"✓ 前向传播成功")
        print(f"  输出类型: {type(outputs)}")
        
        if isinstance(outputs, dict):
            for key, value in outputs.items():
                if hasattr(value, 'shape'):
                    print(f"  {key}: {value.shape}")
                else:
                    print(f"  {key}: {type(value)}")
        
        return model, dual_images, targets, outputs
        
    except Exception as e:
        print(f"❌ 模型前向传播失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None, None, None

def test_loss_computation():
    """
    测试损失函数计算
    """
    print("\n=== 测试损失函数计算 ===")
    
    try:
        # 设置设备
        device = select_device('')
        
        # 创建模拟输出和目标
        batch_size = 2
        num_queries = 100
        num_classes = 80
        
        # 模拟模型输出
        outputs = {
            'pred_logits': torch.randn(batch_size, num_queries, num_classes + 1).to(device),
            'pred_masks': torch.randn(batch_size, num_queries, 160, 160).to(device),
            'aux_outputs': [
                {
                    'pred_logits': torch.randn(batch_size, num_queries, num_classes + 1).to(device),
                    'pred_masks': torch.randn(batch_size, num_queries, 160, 160).to(device)
                }
            ]
        }
        
        # 创建模拟目标
        targets = []
        for i in range(batch_size):
            target = {
                'labels': torch.randint(0, num_classes, (5,)).to(device),  # 5个目标
                'masks': torch.randint(0, 2, (5, 160, 160), dtype=torch.float32).to(device)
            }
            targets.append(target)
        
        # 创建损失函数
        criterion = SetCriterion(
            num_classes=80,
            eos_coef=0.1,
            cost_class=2.0,
            cost_dice=1.0,
            cost_bce=1.0,
            lambda_ce=1.0,
            lambda_bce=1.0,
            lambda_dice=1.0,
            aux_loss=True,
            pos_weight_bce=1.0
        )
        criterion = criterion.to(device)
        
        print("✓ 损失函数创建成功")
        
        # 计算损失
        loss_dict = criterion(outputs, targets)
        
        print("✓ 损失计算成功")
        for key, value in loss_dict.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.item():.4f}")
            else:
                print(f"  {key}: {value}")
        
        total_loss = loss_dict['loss']
        print(f"\n总损失: {total_loss.item():.4f}")
        
        return criterion, total_loss
        
    except Exception as e:
        print(f"❌ 损失计算失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None

def test_backward_propagation(model, criterion, dual_images, targets):
    """
    测试反向传播和梯度计算
    """
    print("\n=== 测试反向传播 ===")
    
    try:
        # 创建优化器
        optimizer = optim.Adam(model.parameters(), lr=1e-4)
        
        # 清零梯度
        optimizer.zero_grad()
        
        # 前向传播
        outputs = model(dual_images)
        
        # 移动targets到正确设备
        device = dual_images[0].device
        targets_device = []
        for target in targets:
            targets_device.append({
                'labels': target['labels'].to(device),
                'masks': target['masks'].to(device)
            })
        
        # 计算损失
        loss_dict = criterion(outputs, targets_device)
        total_loss = loss_dict['loss']
        
        print(f"前向传播损失: {total_loss.item():.4f}")
        
        # 反向传播
        total_loss.backward()
        
        # 检查梯度
        grad_norm = 0.0
        param_count = 0
        grad_count = 0
        
        for name, param in model.named_parameters():
            if param.grad is not None:
                grad_norm += param.grad.data.norm(2).item() ** 2
                grad_count += 1
            param_count += 1
        
        grad_norm = grad_norm ** 0.5
        
        print(f"✓ 反向传播成功")
        print(f"  参数总数: {param_count}")
        print(f"  有梯度参数: {grad_count}")
        print(f"  梯度范数: {grad_norm:.6f}")
        
        # 参数更新
        optimizer.step()
        print("✓ 参数更新成功")
        
        # 验证参数确实更新了
        optimizer.zero_grad()
        outputs2 = model(dual_images)
        loss_dict2 = criterion(outputs2, targets_device)
        total_loss2 = loss_dict2['loss']
        
        print(f"更新后损失: {total_loss2.item():.4f}")
        print(f"损失变化: {(total_loss2 - total_loss).item():.6f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 反向传播失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_training_loop():
    """
    测试完整的训练循环
    """
    print("\n=== 测试训练循环 ===")
    
    try:
        # 加载模型
        model_config = 'd:/yolov5/models/segment/multi-yolov5n-mid-segformer.yaml'
        device = select_device('')
        
        model = Model(model_config, ch=6, nc=80)
        model = model.to(device)
        model.train()
        
        # 创建损失函数和优化器
        criterion = SetCriterion(
            num_classes=80,
            eos_coef=0.1,
            aux_loss=True
        )
        criterion = criterion.to(device)
        
        optimizer = optim.Adam(model.parameters(), lr=1e-4)
        
        print("开始训练循环测试...")
        
        # 模拟训练几个步骤
        num_steps = 5
        losses = []
        
        for step in range(num_steps):
            # 创建新的模拟数据
            dual_images, targets = create_mock_dataset()
            dual_images = [img.to(device) for img in dual_images]
            
            # 移动targets到设备
            targets_device = []
            for target in targets:
                targets_device.append({
                    'labels': target['labels'].to(device),
                    'masks': target['masks'].to(device)
                })
            
            # 训练步骤
            optimizer.zero_grad()
            outputs = model(dual_images)
            loss_dict = criterion(outputs, targets_device)
            total_loss = loss_dict['loss']
            total_loss.backward()
            optimizer.step()
            
            losses.append(total_loss.item())
            print(f"  Step {step+1}/{num_steps}: Loss = {total_loss.item():.4f}")
        
        print(f"\n✓ 训练循环测试成功")
        print(f"  平均损失: {np.mean(losses):.4f}")
        print(f"  损失标准差: {np.std(losses):.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练循环测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主测试函数
    """
    print("开始反向传播验证测试...\n")
    
    success_count = 0
    total_tests = 4
    
    # 测试1: 模型前向传播
    model, dual_images, targets, outputs = test_model_forward()
    if model is not None:
        success_count += 1
        
        # 测试2: 损失函数计算
        criterion, total_loss = test_loss_computation()
        if criterion is not None:
            success_count += 1
            
            # 测试3: 反向传播
            if test_backward_propagation(model, criterion, dual_images, targets):
                success_count += 1
    
    # 测试4: 完整训练循环
    if test_training_loop():
        success_count += 1
    
    print(f"\n=== 最终测试结果 ===")
    print(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 所有反向传播测试通过！")
        print("\n✅ 模型训练可行性验证成功")
        print("- 支持双图像输入前向传播")
        print("- SegFormer损失函数计算正常")
        print("- 反向传播和梯度计算正确")
        print("- 参数更新机制有效")
        return True
    else:
        print("❌ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)