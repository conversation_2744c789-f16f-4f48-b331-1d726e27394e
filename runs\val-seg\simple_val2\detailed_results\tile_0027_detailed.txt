Image: tile_0027.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8408
  Bounding Box: [82.20, 1676.80, 343.40, 2035.20]
  Mask Area: 410 pixels
  Mask Ratio: 0.0145
  Mask BBox: [11, 135, 30, 160]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8286
  Bounding Box: [1472.80, 1596.80, 1680.00, 1859.20]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [121, 129, 135, 149]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8276
  Bounding Box: [1264.80, 412.40, 1426.40, 563.60]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [103, 37, 115, 48]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8232
  Bounding Box: [497.20, 1368.00, 678.80, 1568.00]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [43, 111, 56, 126]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8218
  Bounding Box: [1148.80, 1777.60, 1307.20, 1979.20]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [94, 143, 106, 158]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8159
  Bounding Box: [744.00, 968.80, 1040.00, 1338.40]
  Mask Area: 484 pixels
  Mask Ratio: 0.0171
  Mask BBox: [63, 80, 85, 108]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8140
  Bounding Box: [628.80, 1902.40, 824.80, 2024.00]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [54, 153, 68, 162]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8066
  Bounding Box: [1394.40, 808.00, 1617.60, 1118.40]
  Mask Area: 307 pixels
  Mask Ratio: 0.0109
  Mask BBox: [113, 68, 130, 91]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8052
  Bounding Box: [299.00, 765.60, 551.20, 976.80]
  Mask Area: 254 pixels
  Mask Ratio: 0.0090
  Mask BBox: [28, 64, 47, 80]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8052
  Bounding Box: [296.40, 1547.20, 502.00, 1780.80]
  Mask Area: 173 pixels
  Mask Ratio: 0.0061
  Mask BBox: [28, 125, 43, 143]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8037
  Bounding Box: [339.20, 1376.00, 503.20, 1550.40]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [31, 112, 43, 124]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8022
  Bounding Box: [972.80, 508.40, 1190.40, 749.20]
  Mask Area: 249 pixels
  Mask Ratio: 0.0088
  Mask BBox: [80, 44, 96, 62]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.7998
  Bounding Box: [230.40, 1358.40, 372.00, 1644.80]
  Mask Area: 182 pixels
  Mask Ratio: 0.0064
  Mask BBox: [22, 111, 33, 132]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.7998
  Bounding Box: [1720.00, 1808.00, 1889.60, 1952.00]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [139, 146, 151, 154]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7979
  Bounding Box: [1452.80, 1451.20, 1622.40, 1598.40]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [118, 118, 130, 128]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7920
  Bounding Box: [1854.40, 1512.80, 2036.80, 1841.60]
  Mask Area: 261 pixels
  Mask Ratio: 0.0092
  Mask BBox: [149, 123, 163, 147]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7881
  Bounding Box: [753.60, 1881.60, 939.20, 2048.00]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [63, 151, 76, 163]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7808
  Bounding Box: [1246.40, 1349.60, 1494.40, 1575.20]
  Mask Area: 212 pixels
  Mask Ratio: 0.0075
  Mask BBox: [102, 110, 118, 127]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7803
  Bounding Box: [813.60, 1326.40, 1032.80, 1572.80]
  Mask Area: 221 pixels
  Mask Ratio: 0.0078
  Mask BBox: [68, 108, 84, 126]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7778
  Bounding Box: [350.40, 1769.60, 525.60, 1942.40]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [32, 143, 45, 155]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7764
  Bounding Box: [447.20, 1907.20, 648.00, 2044.80]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [39, 153, 54, 163]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7720
  Bounding Box: [864.00, 1573.60, 1081.60, 1908.80]
  Mask Area: 288 pixels
  Mask Ratio: 0.0102
  Mask BBox: [72, 127, 87, 153]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7700
  Bounding Box: [7.75, 1691.20, 133.00, 1838.40]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [5, 137, 14, 147]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7676
  Bounding Box: [947.20, 1910.40, 1200.00, 2044.80]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [78, 154, 97, 163]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7593
  Bounding Box: [1438.40, 1075.20, 1555.20, 1227.20]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [117, 88, 124, 98]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7568
  Bounding Box: [1263.20, 1918.40, 1532.00, 2046.40]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [103, 154, 123, 163]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7539
  Bounding Box: [1037.60, 1171.20, 1260.00, 1539.20]
  Mask Area: 355 pixels
  Mask Ratio: 0.0126
  Mask BBox: [86, 96, 102, 124]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7529
  Bounding Box: [484.80, 1572.00, 700.80, 1760.00]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [42, 127, 58, 141]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7515
  Bounding Box: [1554.40, 1270.40, 1640.00, 1435.20]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [126, 104, 132, 115]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7500
  Bounding Box: [21.00, 1276.00, 255.80, 1472.80]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [9, 104, 23, 119]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7437
  Bounding Box: [1708.80, 1585.60, 1846.40, 1780.80]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [138, 128, 148, 143]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7324
  Bounding Box: [1782.40, 1123.20, 2038.40, 1468.80]
  Mask Area: 379 pixels
  Mask Ratio: 0.0134
  Mask BBox: [144, 92, 163, 117]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7280
  Bounding Box: [1752.00, 671.20, 1956.80, 896.80]
  Mask Area: 191 pixels
  Mask Ratio: 0.0068
  Mask BBox: [141, 57, 156, 74]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7271
  Bounding Box: [1237.60, 1520.00, 1506.40, 1881.60]
  Mask Area: 465 pixels
  Mask Ratio: 0.0165
  Mask BBox: [101, 123, 121, 150]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7222
  Bounding Box: [1017.60, 1718.40, 1153.60, 1920.00]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [84, 139, 93, 153]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7202
  Bounding Box: [1544.00, 1040.00, 1723.20, 1294.40]
  Mask Area: 200 pixels
  Mask Ratio: 0.0071
  Mask BBox: [125, 86, 138, 105]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7197
  Bounding Box: [1256.80, 668.00, 1420.00, 871.20]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [103, 57, 114, 72]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7178
  Bounding Box: [159.20, 917.60, 267.20, 1127.20]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [17, 77, 24, 90]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7163
  Bounding Box: [1793.60, 900.80, 2024.00, 1139.20]
  Mask Area: 240 pixels
  Mask Ratio: 0.0085
  Mask BBox: [145, 75, 161, 92]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7056
  Bounding Box: [14.85, 1400.00, 170.80, 1536.00]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [6, 114, 17, 123]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.6997
  Bounding Box: [0.00, 1104.80, 131.60, 1232.80]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [3, 91, 14, 100]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.6963
  Bounding Box: [680.80, 1707.20, 946.40, 1886.40]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [58, 138, 77, 151]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.6885
  Bounding Box: [202.00, 502.80, 404.80, 832.80]
  Mask Area: 318 pixels
  Mask Ratio: 0.0113
  Mask BBox: [20, 44, 35, 69]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.6836
  Bounding Box: [870.40, 440.00, 988.80, 590.40]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [72, 39, 81, 49]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.6772
  Bounding Box: [310.40, 1912.00, 443.20, 2033.60]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [29, 154, 38, 162]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.6748
  Bounding Box: [1584.00, 1536.80, 1715.20, 1691.20]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [128, 125, 136, 135]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.6733
  Bounding Box: [440.00, 588.80, 784.80, 851.20]
  Mask Area: 476 pixels
  Mask Ratio: 0.0169
  Mask BBox: [39, 50, 65, 70]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.6719
  Bounding Box: [318.20, 381.20, 547.20, 591.60]
  Mask Area: 214 pixels
  Mask Ratio: 0.0076
  Mask BBox: [29, 34, 46, 50]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.6714
  Bounding Box: [1004.80, 360.00, 1134.40, 493.60]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [83, 33, 92, 42]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.6709
  Bounding Box: [475.20, 1934.40, 622.40, 2048.00]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [42, 156, 52, 164]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.6699
  Bounding Box: [1572.80, 1841.60, 1697.60, 2008.00]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [127, 148, 136, 160]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.6660
  Bounding Box: [153.40, 1460.80, 239.40, 1606.40]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [16, 119, 22, 129]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.6660
  Bounding Box: [1756.80, 1459.20, 1900.80, 1648.00]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [142, 118, 152, 132]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.6636
  Bounding Box: [558.00, 825.60, 788.00, 1139.20]
  Mask Area: 361 pixels
  Mask Ratio: 0.0128
  Mask BBox: [48, 69, 65, 92]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.6626
  Bounding Box: [670.40, 1130.40, 845.60, 1476.00]
  Mask Area: 233 pixels
  Mask Ratio: 0.0083
  Mask BBox: [57, 93, 70, 119]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.6621
  Bounding Box: [756.00, 584.80, 1007.20, 874.40]
  Mask Area: 353 pixels
  Mask Ratio: 0.0125
  Mask BBox: [64, 50, 82, 72]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6611
  Bounding Box: [1121.60, 354.80, 1260.80, 446.00]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [92, 32, 102, 38]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6611
  Bounding Box: [1244.80, 374.40, 1355.20, 476.80]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [102, 34, 109, 41]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6597
  Bounding Box: [51.00, 1530.40, 187.20, 1788.80]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [8, 124, 17, 143]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6587
  Bounding Box: [261.20, 888.00, 435.20, 1163.20]
  Mask Area: 181 pixels
  Mask Ratio: 0.0064
  Mask BBox: [25, 74, 37, 94]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6577
  Bounding Box: [1891.20, 7.35, 2044.80, 109.00]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [152, 5, 162, 12]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6553
  Bounding Box: [1095.20, 452.80, 1216.80, 579.20]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [90, 40, 99, 47]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6543
  Bounding Box: [1896.00, 385.60, 2027.20, 656.80]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [153, 35, 162, 55]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6533
  Bounding Box: [1164.80, 1008.80, 1440.00, 1376.80]
  Mask Area: 491 pixels
  Mask Ratio: 0.0174
  Mask BBox: [95, 83, 116, 111]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6489
  Bounding Box: [1960.00, 659.20, 2046.40, 843.20]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [158, 56, 163, 69]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6489
  Bounding Box: [775.20, 859.20, 932.00, 976.00]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [65, 72, 76, 80]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6411
  Bounding Box: [1617.60, 1322.40, 1812.80, 1544.80]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [131, 108, 145, 124]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6392
  Bounding Box: [4.85, 1479.20, 71.20, 1648.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [5, 120, 9, 132]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6387
  Bounding Box: [1880.00, 1804.80, 1979.20, 1929.60]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [151, 146, 156, 154]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6377
  Bounding Box: [1202.40, 568.80, 1410.40, 728.80]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [98, 49, 114, 60]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6377
  Bounding Box: [1913.60, 1864.00, 2022.40, 2048.00]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [154, 150, 161, 164]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6318
  Bounding Box: [1388.80, 409.60, 1612.80, 824.00]
  Mask Area: 344 pixels
  Mask Ratio: 0.0122
  Mask BBox: [113, 36, 129, 68]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6274
  Bounding Box: [496.80, 1756.80, 686.40, 1964.80]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [43, 142, 57, 157]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6235
  Bounding Box: [0.60, 1215.20, 144.80, 1365.60]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [5, 99, 13, 110]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6230
  Bounding Box: [897.60, 366.40, 1009.60, 450.40]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [75, 33, 82, 39]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6226
  Bounding Box: [1932.80, 819.20, 2035.20, 918.40]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [155, 68, 162, 75]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6201
  Bounding Box: [1304.00, 1833.60, 1433.60, 1920.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [106, 148, 114, 153]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6191
  Bounding Box: [1468.00, 1227.20, 1576.80, 1345.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [119, 100, 127, 109]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6167
  Bounding Box: [52.40, 594.40, 211.60, 794.40]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [9, 51, 20, 66]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6123
  Bounding Box: [5.30, 710.80, 82.10, 834.40]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [5, 60, 10, 69]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6113
  Bounding Box: [1.20, 1840.00, 76.60, 1968.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [5, 148, 9, 157]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6099
  Bounding Box: [1029.60, 775.20, 1143.20, 893.60]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [85, 65, 93, 72]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.5908
  Bounding Box: [1086.40, 1537.60, 1190.40, 1627.20]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [89, 125, 96, 131]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.5845
  Bounding Box: [1638.40, 872.00, 1801.60, 1040.00]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [132, 73, 144, 85]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.5781
  Bounding Box: [1085.60, 1627.20, 1245.60, 1796.80]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [89, 132, 101, 144]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.5752
  Bounding Box: [346.00, 985.60, 530.00, 1182.40]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [32, 81, 45, 95]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.5713
  Bounding Box: [381.20, 1392.80, 659.60, 1557.60]
  Mask Area: 244 pixels
  Mask Ratio: 0.0086
  Mask BBox: [34, 113, 55, 125]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.5684
  Bounding Box: [1066.40, 984.00, 1181.60, 1144.00]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [88, 81, 96, 93]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.5654
  Bounding Box: [1400.00, 1284.00, 1508.80, 1426.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [114, 105, 121, 115]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.5640
  Bounding Box: [18.25, 159.20, 156.00, 314.80]
  Mask Area: 112 pixels
  Mask Ratio: 0.0040
  Mask BBox: [6, 17, 16, 28]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.5591
  Bounding Box: [1515.20, 1856.00, 1678.40, 2041.60]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [123, 149, 135, 163]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.5562
  Bounding Box: [1535.20, 716.80, 1729.60, 876.80]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [124, 60, 139, 72]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5498
  Bounding Box: [21.85, 322.20, 171.00, 466.40]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [6, 30, 16, 40]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5459
  Bounding Box: [146.80, 481.20, 236.00, 598.80]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [16, 42, 22, 50]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5435
  Bounding Box: [934.40, 752.80, 1083.20, 912.80]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [77, 63, 88, 75]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5420
  Bounding Box: [529.60, 1111.20, 599.20, 1218.40]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [46, 91, 50, 97]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5420
  Bounding Box: [720.80, 1520.80, 895.20, 1737.60]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [61, 123, 73, 139]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5391
  Bounding Box: [142.50, 452.00, 248.00, 583.20]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [16, 40, 23, 49]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5391
  Bounding Box: [1659.20, 1998.40, 1784.00, 2043.20]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [134, 161, 142, 163]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5269
  Bounding Box: [1680.00, 1904.00, 1926.40, 2035.20]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [138, 153, 152, 162]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5244
  Bounding Box: [1180.00, 1543.20, 1279.20, 1668.80]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [97, 125, 103, 134]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5195
  Bounding Box: [748.00, 478.40, 879.20, 584.00]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [63, 42, 72, 49]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5098
  Bounding Box: [156.60, 418.40, 304.20, 561.60]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [17, 37, 27, 47]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5063
  Bounding Box: [543.60, 1112.80, 673.20, 1234.40]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [47, 91, 56, 100]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.4980
  Bounding Box: [1201.60, 861.60, 1465.60, 1194.40]
  Mask Area: 394 pixels
  Mask Ratio: 0.0140
  Mask BBox: [98, 72, 118, 97]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.4961
  Bounding Box: [1136.00, 830.40, 1249.60, 966.40]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [93, 69, 101, 78]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.4917
  Bounding Box: [520.80, 1094.40, 622.40, 1228.80]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [45, 90, 52, 99]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.4912
  Bounding Box: [1541.60, 546.00, 1672.00, 743.60]
  Mask Area: 112 pixels
  Mask Ratio: 0.0040
  Mask BBox: [125, 47, 134, 62]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.4717
  Bounding Box: [569.20, 1128.00, 696.40, 1281.60]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [49, 93, 58, 104]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.4692
  Bounding Box: [1942.40, 1880.00, 2048.00, 2043.20]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [156, 151, 163, 163]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.4678
  Bounding Box: [6.40, 1684.80, 184.60, 1902.40]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [5, 136, 18, 152]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.4639
  Bounding Box: [93.00, 843.20, 164.60, 924.80]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [12, 70, 16, 75]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.4629
  Bounding Box: [394.40, 1179.20, 644.00, 1385.60]
  Mask Area: 230 pixels
  Mask Ratio: 0.0081
  Mask BBox: [35, 97, 54, 112]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.4619
  Bounding Box: [411.20, 1349.60, 537.60, 1436.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [37, 110, 45, 116]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.4504
  Bounding Box: [346.40, 1776.00, 644.00, 1939.20]
  Mask Area: 245 pixels
  Mask Ratio: 0.0087
  Mask BBox: [32, 143, 54, 155]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.4490
  Bounding Box: [1188.80, 1985.60, 1315.20, 2046.40]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [97, 160, 105, 163]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.4485
  Bounding Box: [2001.60, 1300.80, 2046.40, 1424.00]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [161, 106, 163, 115]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.4431
  Bounding Box: [1947.20, 1440.00, 2048.00, 1584.00]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [157, 117, 164, 127]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.4419
  Bounding Box: [735.20, 370.80, 901.60, 538.00]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [62, 33, 74, 46]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.4377
  Bounding Box: [1267.20, 388.00, 1480.00, 605.60]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [103, 35, 119, 51]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.4333
  Bounding Box: [50.80, 573.60, 272.00, 822.40]
  Mask Area: 272 pixels
  Mask Ratio: 0.0096
  Mask BBox: [8, 49, 25, 68]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.4297
  Bounding Box: [1021.60, 1732.80, 1220.00, 1950.40]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [84, 140, 99, 156]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.4297
  Bounding Box: [329.40, 1784.00, 536.00, 2014.40]
  Mask Area: 213 pixels
  Mask Ratio: 0.0075
  Mask BBox: [30, 144, 45, 161]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.4263
  Bounding Box: [1737.60, 1004.80, 1811.20, 1088.00]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [140, 83, 144, 88]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.4192
  Bounding Box: [2.60, 410.40, 110.60, 560.80]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [5, 37, 12, 47]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.4097
  Bounding Box: [1342.40, 362.80, 1476.80, 442.00]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [109, 33, 119, 38]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.4070
  Bounding Box: [1692.80, 1820.80, 1929.60, 2025.60]
  Mask Area: 172 pixels
  Mask Ratio: 0.0061
  Mask BBox: [137, 147, 154, 162]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4026
  Bounding Box: [4.00, 865.60, 133.20, 1044.80]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [5, 72, 14, 85]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.3992
  Bounding Box: [1299.20, 1846.40, 1483.20, 2016.00]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [106, 149, 119, 161]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.3955
  Bounding Box: [977.60, 855.20, 1113.60, 976.80]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [81, 71, 90, 80]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.3953
  Bounding Box: [16.45, 1297.60, 214.80, 1550.40]
  Mask Area: 219 pixels
  Mask Ratio: 0.0078
  Mask BBox: [6, 106, 20, 125]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.3923
  Bounding Box: [382.00, 1342.40, 518.80, 1484.80]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [34, 109, 44, 119]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.3877
  Bounding Box: [1129.60, 906.40, 1483.20, 1352.80]
  Mask Area: 662 pixels
  Mask Ratio: 0.0235
  Mask BBox: [93, 75, 119, 109]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.3840
  Bounding Box: [10.90, 1971.20, 117.10, 2041.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [5, 158, 13, 163]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.3823
  Bounding Box: [1884.80, 1820.80, 2009.60, 1952.00]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [152, 147, 160, 156]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.3757
  Bounding Box: [1620.80, 9.15, 1768.00, 182.80]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [131, 5, 142, 18]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.3757
  Bounding Box: [0.00, 1108.80, 143.80, 1337.60]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [4, 91, 14, 108]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.3743
  Bounding Box: [223.00, 853.60, 288.60, 922.40]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [22, 71, 26, 76]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.3728
  Bounding Box: [10.70, 1603.20, 171.20, 1849.60]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [5, 130, 17, 148]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.3708
  Bounding Box: [1135.20, 786.40, 1282.40, 968.80]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [93, 66, 104, 78]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.3687
  Bounding Box: [465.60, 1812.80, 675.20, 2048.00]
  Mask Area: 247 pixels
  Mask Ratio: 0.0088
  Mask BBox: [41, 146, 56, 164]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.3674
  Bounding Box: [1281.60, 1900.80, 1574.40, 2022.40]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [105, 153, 126, 161]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.3667
  Bounding Box: [1183.20, 976.80, 1232.80, 1080.80]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [97, 81, 100, 87]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.3628
  Bounding Box: [1496.00, 1477.60, 1704.00, 1657.60]
  Mask Area: 181 pixels
  Mask Ratio: 0.0064
  Mask BBox: [121, 120, 137, 133]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.3606
  Bounding Box: [1699.20, 651.60, 1798.40, 799.20]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [137, 55, 144, 66]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.3550
  Bounding Box: [1090.40, 424.80, 1252.00, 567.20]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [90, 38, 101, 48]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.3547
  Bounding Box: [169.00, 1136.80, 352.60, 1362.40]
  Mask Area: 211 pixels
  Mask Ratio: 0.0075
  Mask BBox: [18, 93, 31, 110]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.3540
  Bounding Box: [1670.40, 1376.00, 1881.60, 1620.80]
  Mask Area: 206 pixels
  Mask Ratio: 0.0073
  Mask BBox: [135, 112, 150, 130]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.3535
  Bounding Box: [163.60, 1403.20, 350.80, 1627.20]
  Mask Area: 197 pixels
  Mask Ratio: 0.0070
  Mask BBox: [17, 114, 31, 131]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.3511
  Bounding Box: [1184.00, 1780.80, 1425.60, 1953.60]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [97, 144, 115, 156]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.3508
  Bounding Box: [1228.80, 392.00, 1342.40, 495.20]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [100, 35, 108, 42]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.3494
  Bounding Box: [1134.40, 729.60, 1305.60, 944.00]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [93, 61, 105, 77]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.3455
  Bounding Box: [1458.40, 1061.60, 1578.40, 1207.20]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [118, 88, 127, 98]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.3430
  Bounding Box: [661.60, 1912.00, 839.20, 2048.00]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [56, 154, 69, 165]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.3416
  Bounding Box: [799.20, 620.00, 1056.80, 914.40]
  Mask Area: 295 pixels
  Mask Ratio: 0.0105
  Mask BBox: [67, 53, 86, 74]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.3386
  Bounding Box: [1990.40, 1326.40, 2048.00, 1436.80]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [160, 108, 163, 116]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.3372
  Bounding Box: [1231.20, 364.00, 1338.40, 461.60]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [101, 33, 108, 40]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.3357
  Bounding Box: [1441.60, 1079.20, 1564.80, 1332.00]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [117, 89, 126, 108]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.3308
  Bounding Box: [1135.20, 358.00, 1322.40, 458.00]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [93, 32, 107, 39]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1857.60, 1795.20, 1960.00, 1916.80]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [151, 146, 157, 153]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1033.60, 1624.00, 1225.60, 1867.20]
  Mask Area: 187 pixels
  Mask Ratio: 0.0066
  Mask BBox: [85, 131, 99, 149]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.3293
  Bounding Box: [1415.20, 1873.60, 1506.40, 1934.40]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [115, 151, 121, 154]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.3286
  Bounding Box: [953.60, 780.80, 1115.20, 971.20]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [79, 65, 91, 79]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.3281
  Bounding Box: [1726.40, 975.20, 1809.60, 1071.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [139, 81, 144, 87]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [1774.40, 1144.00, 1848.00, 1260.80]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [143, 94, 147, 102]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.3259
  Bounding Box: [1224.80, 578.40, 1431.20, 864.80]
  Mask Area: 260 pixels
  Mask Ratio: 0.0092
  Mask BBox: [100, 50, 115, 70]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.3252
  Bounding Box: [11.15, 576.00, 162.80, 787.20]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [5, 49, 16, 65]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.3247
  Bounding Box: [680.00, 1455.20, 892.80, 1713.60]
  Mask Area: 258 pixels
  Mask Ratio: 0.0091
  Mask BBox: [58, 118, 73, 137]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.3242
  Bounding Box: [1950.40, 1832.00, 2043.20, 2017.60]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [157, 148, 163, 161]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.3240
  Bounding Box: [1649.60, 1572.00, 1825.60, 1776.00]
  Mask Area: 159 pixels
  Mask Ratio: 0.0056
  Mask BBox: [133, 127, 146, 142]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.3235
  Bounding Box: [1707.20, 1035.20, 1787.20, 1134.40]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [138, 85, 143, 92]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [1601.60, 472.80, 1710.40, 556.00]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [130, 41, 137, 47]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3208
  Bounding Box: [81.70, 793.60, 197.20, 926.40]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [11, 66, 19, 75]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3203
  Bounding Box: [1569.60, 1250.40, 1665.60, 1420.00]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [127, 104, 134, 114]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3196
  Bounding Box: [4.80, 1232.00, 216.80, 1452.80]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [5, 101, 20, 117]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3174
  Bounding Box: [6.50, 1201.60, 78.80, 1321.60]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [5, 98, 10, 107]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3169
  Bounding Box: [320.00, 1940.80, 427.20, 2040.00]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [29, 156, 37, 163]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3162
  Bounding Box: [1604.80, 1514.40, 1742.40, 1684.80]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [130, 123, 140, 135]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3123
  Bounding Box: [1635.20, 1974.40, 1760.00, 2048.00]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [132, 159, 141, 163]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3123
  Bounding Box: [1635.20, 2000.00, 1760.00, 2048.00]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [132, 161, 141, 164]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3118
  Bounding Box: [16.95, 335.20, 151.00, 524.00]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [6, 31, 15, 44]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3103
  Bounding Box: [1118.40, 1739.20, 1281.60, 1976.00]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [92, 140, 104, 158]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3098
  Bounding Box: [0.00, 1381.60, 153.10, 1517.60]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [3, 112, 15, 122]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [1979.20, 978.40, 2048.00, 1144.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [159, 81, 164, 93]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [109.80, 762.40, 226.60, 917.60]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [13, 64, 20, 75]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3086
  Bounding Box: [1276.00, 1820.80, 1415.20, 1904.00]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [104, 147, 114, 152]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3086
  Bounding Box: [1276.00, 1846.40, 1415.20, 1929.60]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [104, 149, 114, 154]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3086
  Bounding Box: [1305.60, 1816.00, 1443.20, 1899.20]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [106, 146, 116, 152]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3081
  Bounding Box: [1771.20, 1953.60, 1940.80, 2043.20]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [143, 157, 155, 163]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3057
  Bounding Box: [1006.40, 332.80, 1219.20, 493.60]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [83, 30, 99, 42]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3049
  Bounding Box: [127.40, 470.40, 213.40, 583.20]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [14, 41, 20, 49]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3032
  Bounding Box: [1526.40, 606.00, 1718.40, 888.00]
  Mask Area: 205 pixels
  Mask Ratio: 0.0073
  Mask BBox: [124, 52, 138, 73]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3022
  Bounding Box: [522.80, 397.60, 604.40, 502.40]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [45, 36, 51, 43]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3003
  Bounding Box: [1484.80, 1226.40, 1593.60, 1384.80]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [120, 100, 128, 112]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.2986
  Bounding Box: [1787.20, 1086.40, 1864.00, 1142.40]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [144, 89, 149, 93]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.2986
  Bounding Box: [603.20, 402.40, 763.20, 577.60]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [52, 36, 63, 49]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.2954
  Bounding Box: [1667.20, 1972.80, 1785.60, 2040.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [135, 159, 143, 163]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.2947
  Bounding Box: [129.40, 497.20, 211.00, 610.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [15, 43, 20, 50]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.2942
  Bounding Box: [1710.40, 1182.40, 1809.60, 1366.40]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [138, 97, 145, 110]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.2939
  Bounding Box: [55.20, 1984.00, 189.60, 2044.80]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [9, 159, 18, 163]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.2925
  Bounding Box: [20.00, 160.80, 178.00, 436.00]
  Mask Area: 213 pixels
  Mask Ratio: 0.0075
  Mask BBox: [6, 17, 16, 38]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.2925
  Bounding Box: [110.00, 376.80, 300.00, 546.40]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [13, 34, 27, 46]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.2925
  Bounding Box: [957.60, 341.80, 1143.20, 482.40]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [79, 31, 93, 41]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.2922
  Bounding Box: [371.20, 1328.80, 493.60, 1415.20]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [33, 108, 42, 114]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.2913
  Bounding Box: [0.00, 690.40, 107.50, 815.20]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [4, 58, 12, 67]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.2898
  Bounding Box: [1481.60, 1414.40, 1656.00, 1582.40]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [120, 115, 133, 127]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.2869
  Bounding Box: [1134.40, 1702.40, 1240.00, 1792.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [93, 137, 100, 143]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.2864
  Bounding Box: [6.75, 1888.00, 78.40, 2016.00]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [5, 152, 10, 161]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.2856
  Bounding Box: [1123.20, 1678.40, 1230.40, 1806.40]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [92, 136, 100, 145]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.2854
  Bounding Box: [1984.00, 885.60, 2048.00, 1016.80]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [159, 74, 163, 83]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.2849
  Bounding Box: [10.40, 776.80, 71.10, 890.40]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 65, 9, 73]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.2837
  Bounding Box: [1188.80, 746.40, 1305.60, 920.80]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [97, 63, 105, 75]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.2834
  Bounding Box: [1731.20, 1480.80, 1948.80, 1766.40]
  Mask Area: 246 pixels
  Mask Ratio: 0.0087
  Mask BBox: [140, 120, 156, 141]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [1179.20, 1742.40, 1342.40, 1969.60]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [97, 141, 108, 157]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [1055.20, 421.60, 1215.20, 593.60]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [87, 37, 98, 50]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.2817
  Bounding Box: [1686.40, 1614.40, 1827.20, 1822.40]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [136, 131, 146, 146]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.2795
  Bounding Box: [1624.00, 1285.60, 1716.80, 1436.00]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [131, 105, 138, 114]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.2776
  Bounding Box: [14.80, 1079.20, 159.40, 1213.60]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [6, 89, 14, 98]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.2756
  Bounding Box: [1876.80, 29.05, 2036.80, 133.40]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [151, 7, 163, 14]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.2756
  Bounding Box: [977.60, 1904.00, 1257.60, 2019.20]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [81, 153, 102, 161]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.2756
  Bounding Box: [0.00, 36.15, 87.80, 199.40]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [4, 7, 10, 19]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.2712
  Bounding Box: [125.70, 1438.40, 222.00, 1564.80]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [14, 117, 21, 126]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.2712
  Bounding Box: [151.30, 1438.40, 247.60, 1564.80]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [16, 117, 22, 126]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.2710
  Bounding Box: [1830.40, 851.20, 2032.00, 1096.00]
  Mask Area: 243 pixels
  Mask Ratio: 0.0086
  Mask BBox: [147, 71, 162, 89]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.2686
  Bounding Box: [72.80, 865.60, 155.40, 944.00]
  Mask Area: 19 pixels
  Mask Ratio: 0.0007
  Mask BBox: [11, 72, 16, 75]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.2686
  Bounding Box: [98.40, 865.60, 181.00, 944.00]
  Mask Area: 17 pixels
  Mask Ratio: 0.0006
  Mask BBox: [12, 72, 16, 75]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.2671
  Bounding Box: [1852.80, 1390.40, 1974.40, 1504.00]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [149, 114, 158, 121]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.2666
  Bounding Box: [1720.00, 1001.60, 1790.40, 1070.40]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [139, 83, 143, 87]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.2666
  Bounding Box: [1720.00, 1027.20, 1790.40, 1096.00]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [139, 85, 143, 89]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.2666
  Bounding Box: [1745.60, 1027.20, 1816.00, 1096.00]
  Mask Area: 15 pixels
  Mask Ratio: 0.0005
  Mask BBox: [141, 85, 144, 89]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.2664
  Bounding Box: [846.40, 357.60, 1006.40, 503.20]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [71, 32, 82, 43]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.2654
  Bounding Box: [1048.80, 798.40, 1164.00, 904.00]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [86, 67, 94, 74]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.2654
  Bounding Box: [1159.20, 816.00, 1266.40, 948.80]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [95, 68, 102, 78]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.2654
  Bounding Box: [3.30, 578.80, 116.30, 733.20]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [5, 50, 13, 61]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.2637
  Bounding Box: [131.50, 1462.40, 217.80, 1595.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [15, 119, 21, 128]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.2629
  Bounding Box: [0.00, 14.50, 76.70, 153.50]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [4, 6, 9, 15]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.2622
  Bounding Box: [548.00, 416.00, 760.00, 606.40]
  Mask Area: 249 pixels
  Mask Ratio: 0.0088
  Mask BBox: [47, 37, 63, 51]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.2612
  Bounding Box: [917.60, 769.60, 1056.80, 944.00]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [76, 65, 86, 77]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.2605
  Bounding Box: [179.80, 871.20, 302.60, 1103.20]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [19, 73, 27, 90]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.2603
  Bounding Box: [664.80, 1094.40, 904.80, 1417.60]
  Mask Area: 371 pixels
  Mask Ratio: 0.0131
  Mask BBox: [56, 90, 74, 114]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.2598
  Bounding Box: [1748.80, 1003.20, 1819.20, 1067.20]
  Mask Area: 19 pixels
  Mask Ratio: 0.0007
  Mask BBox: [141, 83, 144, 87]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.2595
  Bounding Box: [976.00, 378.40, 1108.80, 502.40]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [81, 34, 90, 43]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.2590
  Bounding Box: [765.20, 831.20, 916.00, 944.80]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [65, 69, 75, 77]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.2590
  Bounding Box: [1047.20, 775.20, 1172.00, 876.00]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [86, 65, 95, 72]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.2563
  Bounding Box: [1982.40, 1286.40, 2036.80, 1411.20]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [159, 105, 163, 114]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.2563
  Bounding Box: [2008.00, 1286.40, 2048.00, 1411.20]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [161, 105, 165, 114]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.2554
  Bounding Box: [1971.20, 1477.60, 2044.80, 1641.60]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [158, 120, 163, 132]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.2549
  Bounding Box: [1180.80, 1996.80, 1294.40, 2048.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [97, 160, 105, 164]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.2546
  Bounding Box: [1172.80, 1461.60, 1275.20, 1660.80]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [96, 119, 103, 133]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.2527
  Bounding Box: [1664.00, 1862.40, 1881.60, 2028.80]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [134, 150, 150, 162]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.2520
  Bounding Box: [12.05, 1046.40, 105.20, 1152.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [5, 86, 11, 93]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.2517
  Bounding Box: [1517.60, 1010.40, 1686.40, 1274.40]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [123, 83, 135, 103]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.2510
  Bounding Box: [2001.60, 1315.20, 2048.00, 1435.20]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [161, 107, 164, 116]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.2510
  Bounding Box: [1039.20, 754.40, 1236.00, 906.40]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [86, 63, 100, 74]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.2487
  Bounding Box: [1397.60, 1087.20, 1519.20, 1274.40]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [114, 89, 122, 103]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.2482
  Bounding Box: [658.40, 1304.00, 833.60, 1590.40]
  Mask Area: 290 pixels
  Mask Ratio: 0.0103
  Mask BBox: [56, 106, 69, 128]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.2469
  Bounding Box: [1133.60, 334.40, 1290.40, 430.40]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [93, 31, 104, 37]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.2458
  Bounding Box: [1553.60, 1259.20, 1691.20, 1443.20]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [126, 103, 136, 116]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.2456
  Bounding Box: [1889.60, 643.20, 2046.40, 841.60]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [152, 55, 163, 69]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.2448
  Bounding Box: [1200.00, 430.80, 1289.60, 556.40]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [98, 38, 104, 47]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.2418
  Bounding Box: [884.00, 409.60, 1080.80, 593.60]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [74, 36, 88, 49]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.2411
  Bounding Box: [0.00, 1843.20, 51.60, 1974.40]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [3, 148, 8, 158]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2410
  Bounding Box: [1065.60, 1504.80, 1203.20, 1638.40]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [88, 122, 97, 131]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2407
  Bounding Box: [1913.60, 787.20, 2016.00, 889.60]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [154, 66, 161, 73]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2407
  Bounding Box: [1939.20, 787.20, 2041.60, 889.60]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [156, 66, 163, 73]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2405
  Bounding Box: [1440.80, 1252.00, 1556.00, 1372.00]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [117, 102, 125, 111]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2394
  Bounding Box: [1207.20, 1990.40, 1320.80, 2048.00]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [99, 160, 104, 164]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2390
  Bounding Box: [1536.00, 1828.80, 1680.00, 1985.60]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [124, 147, 135, 159]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2379
  Bounding Box: [1126.40, 716.40, 1284.80, 888.00]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [92, 60, 104, 73]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2378
  Bounding Box: [10.15, 1040.00, 87.00, 1129.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 86, 10, 92]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2358
  Bounding Box: [1506.40, 1260.80, 1609.60, 1430.40]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [122, 103, 129, 115]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2356
  Bounding Box: [2001.60, 1549.60, 2048.00, 1670.40]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [161, 126, 164, 134]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2355
  Bounding Box: [1468.80, 1256.80, 1580.80, 1373.60]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [119, 103, 127, 111]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2334
  Bounding Box: [662.00, 1736.00, 911.20, 1921.60]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [56, 140, 73, 154]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2316
  Bounding Box: [445.60, 499.20, 764.80, 812.00]
  Mask Area: 518 pixels
  Mask Ratio: 0.0184
  Mask BBox: [39, 43, 63, 67]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2313
  Bounding Box: [1281.60, 691.20, 1446.40, 910.40]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [105, 58, 116, 75]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2306
  Bounding Box: [1083.20, 410.80, 1204.80, 546.80]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [89, 37, 98, 46]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2303
  Bounding Box: [1.60, 153.20, 77.70, 282.80]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [5, 16, 7, 26]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2295
  Bounding Box: [0.00, 1870.40, 55.50, 2001.60]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [3, 151, 8, 160]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2261
  Bounding Box: [887.20, 332.40, 989.60, 422.00]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [74, 30, 81, 36]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2261
  Bounding Box: [912.80, 332.40, 1015.20, 422.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [76, 30, 83, 36]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2260
  Bounding Box: [1282.40, 1774.40, 1479.20, 1950.40]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [105, 143, 119, 156]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2255
  Bounding Box: [550.80, 1076.80, 643.60, 1200.00]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [48, 91, 54, 97]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2244
  Bounding Box: [60.80, 712.80, 218.40, 908.00]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [9, 60, 20, 74]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2244
  Bounding Box: [1873.60, 1892.80, 2048.00, 2048.00]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [151, 152, 164, 164]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2240
  Bounding Box: [1343.20, 1439.20, 1604.80, 1581.60]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [109, 117, 129, 127]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2239
  Bounding Box: [1667.20, 489.60, 1833.60, 596.00]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [135, 43, 147, 50]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2220
  Bounding Box: [895.20, 472.40, 1012.00, 610.80]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [74, 41, 81, 51]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2220
  Bounding Box: [1214.40, 848.00, 1425.60, 1105.60]
  Mask Area: 202 pixels
  Mask Ratio: 0.0072
  Mask BBox: [99, 71, 115, 89]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2216
  Bounding Box: [1218.40, 485.20, 1285.60, 582.00]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [100, 42, 103, 49]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2203
  Bounding Box: [1137.60, 1544.00, 1284.80, 1720.00]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [93, 125, 103, 138]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2184
  Bounding Box: [0.00, 1474.40, 47.40, 1635.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [3, 120, 7, 131]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2173
  Bounding Box: [1611.20, 1833.60, 1723.20, 1968.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [130, 148, 136, 157]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2173
  Bounding Box: [1402.40, 1894.40, 1488.80, 1964.80]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [114, 152, 120, 154]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2173
  Bounding Box: [1428.00, 1894.40, 1514.40, 1964.80]
  Mask Area: 16 pixels
  Mask Ratio: 0.0006
  Mask BBox: [116, 152, 121, 154]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2167
  Bounding Box: [743.20, 684.80, 965.60, 963.20]
  Mask Area: 286 pixels
  Mask Ratio: 0.0101
  Mask BBox: [63, 58, 79, 79]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2157
  Bounding Box: [1452.00, 1049.60, 1641.60, 1249.60]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [118, 86, 132, 101]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2153
  Bounding Box: [1617.60, 1312.80, 1732.80, 1461.60]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [131, 107, 139, 118]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2140
  Bounding Box: [127.40, 1489.60, 215.80, 1654.40]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [14, 121, 20, 133]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2140
  Bounding Box: [153.00, 1489.60, 241.40, 1654.40]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [16, 121, 21, 133]

