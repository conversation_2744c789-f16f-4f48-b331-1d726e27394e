Image: tile_0026.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8828
  Bounding Box: [146.10, 1110.40, 394.80, 1324.80]
  Mask Area: 227 pixels
  Mask Ratio: 0.0080
  Mask BBox: [16, 91, 34, 107]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8516
  Bounding Box: [1619.20, 1672.00, 1881.60, 2036.80]
  Mask Area: 421 pixels
  Mask Ratio: 0.0149
  Mask BBox: [131, 135, 150, 163]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8452
  Bounding Box: [282.00, 892.00, 524.40, 1112.80]
  Mask Area: 261 pixels
  Mask Ratio: 0.0092
  Mask BBox: [27, 74, 44, 90]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8433
  Bounding Box: [0.00, 320.00, 260.80, 570.40]
  Mask Area: 263 pixels
  Mask Ratio: 0.0093
  Mask BBox: [4, 29, 24, 48]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8271
  Bounding Box: [2.15, 1652.80, 173.80, 1848.00]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [5, 134, 16, 148]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8188
  Bounding Box: [1884.80, 1761.60, 2032.00, 1950.40]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [152, 143, 162, 156]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8145
  Bounding Box: [1827.20, 757.60, 2044.80, 960.80]
  Mask Area: 213 pixels
  Mask Ratio: 0.0075
  Mask BBox: [147, 64, 163, 79]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8071
  Bounding Box: [1412.80, 1095.20, 1659.20, 1237.60]
  Mask Area: 159 pixels
  Mask Ratio: 0.0056
  Mask BBox: [115, 90, 133, 100]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.7993
  Bounding Box: [1764.80, 1359.20, 1908.80, 1644.80]
  Mask Area: 182 pixels
  Mask Ratio: 0.0064
  Mask BBox: [142, 111, 153, 132]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.7979
  Bounding Box: [484.80, 1564.80, 697.60, 1744.00]
  Mask Area: 165 pixels
  Mask Ratio: 0.0058
  Mask BBox: [42, 127, 58, 139]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.7959
  Bounding Box: [673.20, 1007.20, 829.60, 1197.60]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [57, 85, 68, 97]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.7954
  Bounding Box: [0.00, 1372.80, 79.80, 1531.20]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [4, 112, 10, 123]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.7817
  Bounding Box: [1064.80, 1808.00, 1205.60, 2038.40]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [88, 146, 98, 163]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.7803
  Bounding Box: [1875.20, 1376.00, 2038.40, 1552.00]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [151, 112, 163, 125]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7798
  Bounding Box: [768.80, 337.80, 914.40, 440.00]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [65, 31, 74, 38]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7783
  Bounding Box: [1572.00, 1401.60, 1704.00, 1536.00]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [127, 114, 137, 123]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7778
  Bounding Box: [869.60, 768.00, 1055.20, 1270.40]
  Mask Area: 457 pixels
  Mask Ratio: 0.0162
  Mask BBox: [72, 64, 86, 103]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7769
  Bounding Box: [669.60, 707.60, 865.60, 957.60]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [57, 60, 71, 78]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7754
  Bounding Box: [1256.80, 428.00, 1381.60, 568.00]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [103, 38, 110, 48]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7739
  Bounding Box: [1830.40, 1542.40, 2038.40, 1782.40]
  Mask Area: 184 pixels
  Mask Ratio: 0.0065
  Mask BBox: [147, 125, 163, 143]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7734
  Bounding Box: [66.80, 1327.20, 496.00, 1963.20]
  Mask Area: 923 pixels
  Mask Ratio: 0.0327
  Mask BBox: [10, 113, 42, 157]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7725
  Bounding Box: [1000.00, 1171.20, 1198.40, 1440.00]
  Mask Area: 210 pixels
  Mask Ratio: 0.0074
  Mask BBox: [83, 96, 97, 115]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7686
  Bounding Box: [1056.80, 302.80, 1335.20, 526.00]
  Mask Area: 313 pixels
  Mask Ratio: 0.0111
  Mask BBox: [87, 28, 108, 45]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7632
  Bounding Box: [204.40, 283.20, 469.20, 496.00]
  Mask Area: 267 pixels
  Mask Ratio: 0.0095
  Mask BBox: [20, 27, 40, 42]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7612
  Bounding Box: [1136.80, 488.00, 1295.20, 707.20]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [93, 43, 105, 59]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7568
  Bounding Box: [750.40, 1245.60, 1028.80, 1606.40]
  Mask Area: 328 pixels
  Mask Ratio: 0.0116
  Mask BBox: [63, 102, 84, 129]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7563
  Bounding Box: [1225.60, 1184.00, 1507.20, 1457.60]
  Mask Area: 329 pixels
  Mask Ratio: 0.0117
  Mask BBox: [100, 97, 121, 117]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7539
  Bounding Box: [1392.80, 894.40, 1557.60, 1068.80]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [113, 74, 125, 87]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7490
  Bounding Box: [78.00, 1292.80, 237.60, 1470.40]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [11, 105, 22, 118]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7471
  Bounding Box: [541.60, 1396.80, 729.60, 1537.60]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [47, 114, 60, 124]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7466
  Bounding Box: [1540.00, 1689.60, 1672.00, 1843.20]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [125, 136, 134, 147]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7441
  Bounding Box: [748.80, 1276.80, 824.00, 1427.20]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [63, 104, 68, 114]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7422
  Bounding Box: [424.00, 1383.20, 536.00, 1560.80]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [38, 113, 45, 125]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7407
  Bounding Box: [526.40, 824.80, 693.60, 1015.20]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [46, 69, 58, 83]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7402
  Bounding Box: [103.50, 1058.40, 224.80, 1175.20]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [13, 87, 20, 95]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7373
  Bounding Box: [1560.00, 1285.60, 1793.60, 1468.00]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [126, 105, 144, 118]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7373
  Bounding Box: [752.00, 489.60, 1052.80, 872.80]
  Mask Area: 508 pixels
  Mask Ratio: 0.0180
  Mask BBox: [63, 43, 86, 72]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7354
  Bounding Box: [471.60, 1819.20, 640.40, 2001.60]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [41, 147, 54, 159]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7324
  Bounding Box: [741.60, 302.20, 927.20, 445.60]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [62, 28, 76, 38]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7314
  Bounding Box: [576.80, 1729.60, 785.60, 1892.80]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [50, 141, 64, 151]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7300
  Bounding Box: [0.00, 1061.60, 136.30, 1245.60]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [4, 87, 14, 101]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7261
  Bounding Box: [1351.20, 1942.40, 1583.20, 2032.00]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [111, 156, 127, 162]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7246
  Bounding Box: [386.80, 1122.40, 494.80, 1282.40]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [35, 92, 42, 104]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7212
  Bounding Box: [1694.40, 916.80, 1803.20, 1126.40]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [137, 76, 144, 90]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7183
  Bounding Box: [0.00, 858.40, 184.00, 1018.40]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [4, 72, 18, 83]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7158
  Bounding Box: [0.00, 657.60, 110.80, 812.80]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [4, 56, 12, 67]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7158
  Bounding Box: [1501.60, 1489.60, 1616.00, 1657.60]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [122, 121, 130, 131]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7134
  Bounding Box: [1339.20, 1492.80, 1502.40, 1612.80]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [109, 121, 121, 129]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7104
  Bounding Box: [1110.40, 780.00, 1379.20, 1213.60]
  Mask Area: 511 pixels
  Mask Ratio: 0.0181
  Mask BBox: [91, 65, 111, 98]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7031
  Bounding Box: [178.80, 900.00, 280.40, 1063.20]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [18, 75, 25, 87]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.7002
  Bounding Box: [567.60, 1171.20, 742.80, 1403.20]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [49, 96, 62, 113]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.6924
  Bounding Box: [1016.80, 494.00, 1157.60, 705.20]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [84, 43, 94, 59]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.6914
  Bounding Box: [1516.80, 1846.40, 1608.00, 1971.20]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [123, 149, 129, 157]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.6865
  Bounding Box: [353.40, 1280.80, 504.40, 1442.40]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [32, 105, 43, 116]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.6860
  Bounding Box: [94.30, 534.00, 228.40, 650.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [12, 46, 21, 53]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.6860
  Bounding Box: [81.50, 569.20, 313.60, 914.40]
  Mask Area: 325 pixels
  Mask Ratio: 0.0115
  Mask BBox: [11, 49, 28, 75]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6807
  Bounding Box: [1161.60, 1673.60, 1272.00, 1964.80]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [95, 135, 103, 157]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6768
  Bounding Box: [1307.20, 1446.40, 1420.80, 1518.40]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [107, 117, 114, 122]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6758
  Bounding Box: [1844.80, 1910.40, 1976.00, 2032.00]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [149, 154, 158, 162]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6724
  Bounding Box: [1944.00, 603.20, 2046.40, 773.60]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [156, 52, 163, 64]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6719
  Bounding Box: [1109.60, 1499.20, 1220.00, 1638.40]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [91, 122, 97, 130]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6709
  Bounding Box: [11.90, 541.20, 95.70, 634.80]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [5, 47, 11, 53]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6665
  Bounding Box: [1969.60, 1928.00, 2043.20, 2036.80]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [158, 155, 163, 163]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6655
  Bounding Box: [534.40, 497.60, 697.60, 692.80]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [46, 43, 58, 58]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6650
  Bounding Box: [512.00, 1159.20, 573.60, 1258.40]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [44, 95, 48, 102]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6646
  Bounding Box: [474.40, 1713.60, 570.40, 1857.60]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [42, 138, 48, 148]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6626
  Bounding Box: [1796.80, 888.00, 1972.80, 1164.80]
  Mask Area: 201 pixels
  Mask Ratio: 0.0071
  Mask BBox: [145, 74, 158, 94]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6572
  Bounding Box: [1736.00, 502.00, 1940.80, 834.40]
  Mask Area: 303 pixels
  Mask Ratio: 0.0107
  Mask BBox: [140, 44, 155, 69]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6553
  Bounding Box: [1689.60, 1460.80, 1776.00, 1606.40]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [136, 119, 142, 129]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6523
  Bounding Box: [1532.00, 1220.80, 1681.60, 1371.20]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [124, 100, 135, 111]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6509
  Bounding Box: [4.50, 1226.40, 159.20, 1383.20]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [5, 100, 16, 112]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6499
  Bounding Box: [438.80, 631.60, 610.80, 994.40]
  Mask Area: 298 pixels
  Mask Ratio: 0.0106
  Mask BBox: [39, 54, 51, 81]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6484
  Bounding Box: [1587.20, 1523.20, 1721.60, 1792.00]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [128, 123, 138, 143]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6396
  Bounding Box: [1153.60, 737.60, 1315.20, 828.80]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [95, 62, 105, 68]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6377
  Bounding Box: [506.40, 1265.60, 631.20, 1409.60]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [44, 103, 53, 113]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6348
  Bounding Box: [1856.00, 373.20, 2048.00, 590.80]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [149, 34, 163, 50]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6304
  Bounding Box: [1875.20, 1005.60, 2048.00, 1183.20]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [151, 83, 163, 96]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6284
  Bounding Box: [446.80, 716.00, 646.80, 1029.60]
  Mask Area: 317 pixels
  Mask Ratio: 0.0112
  Mask BBox: [39, 60, 54, 84]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6265
  Bounding Box: [1464.00, 1364.00, 1568.00, 1487.20]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [119, 111, 126, 120]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6235
  Bounding Box: [285.60, 1892.80, 383.20, 2046.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [27, 152, 33, 163]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6230
  Bounding Box: [334.00, 529.60, 560.40, 697.60]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [31, 46, 47, 58]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6216
  Bounding Box: [451.20, 333.40, 565.60, 412.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [40, 31, 47, 36]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.5972
  Bounding Box: [920.00, 1410.40, 1051.20, 1612.80]
  Mask Area: 112 pixels
  Mask Ratio: 0.0040
  Mask BBox: [76, 115, 86, 129]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.5967
  Bounding Box: [1380.00, 1563.20, 1540.00, 1876.80]
  Mask Area: 205 pixels
  Mask Ratio: 0.0073
  Mask BBox: [112, 127, 124, 150]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.5947
  Bounding Box: [1576.00, 318.80, 1710.40, 466.00]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [128, 29, 136, 40]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.5923
  Bounding Box: [667.60, 1524.00, 796.00, 1763.20]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [57, 125, 66, 141]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.5913
  Bounding Box: [1107.20, 1152.80, 1238.40, 1247.20]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [91, 95, 100, 101]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.5835
  Bounding Box: [1227.20, 1522.40, 1385.60, 1664.00]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [101, 123, 112, 133]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.5776
  Bounding Box: [905.60, 352.00, 1049.60, 490.40]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [76, 32, 85, 42]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.5747
  Bounding Box: [1057.60, 732.80, 1140.80, 985.60]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [87, 62, 93, 80]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.5742
  Bounding Box: [1441.60, 1246.40, 1590.40, 1339.20]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [117, 102, 128, 108]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.5723
  Bounding Box: [921.60, 301.40, 1080.00, 386.40]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [76, 28, 88, 34]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5684
  Bounding Box: [1291.20, 577.20, 1419.20, 705.20]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [105, 50, 114, 59]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5654
  Bounding Box: [1027.20, 1431.20, 1108.80, 1606.40]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [85, 116, 90, 128]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5635
  Bounding Box: [72.70, 1760.00, 287.20, 2006.40]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [10, 142, 26, 160]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5620
  Bounding Box: [1274.40, 1660.80, 1370.40, 1824.00]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [104, 134, 111, 146]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5518
  Bounding Box: [1681.60, 481.20, 1771.20, 598.80]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [136, 42, 142, 50]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5479
  Bounding Box: [512.00, 1001.60, 695.20, 1169.60]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [44, 83, 58, 93]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5449
  Bounding Box: [1678.40, 451.60, 1784.00, 582.80]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [136, 40, 143, 49]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5381
  Bounding Box: [923.20, 1593.60, 1163.20, 1856.00]
  Mask Area: 271 pixels
  Mask Ratio: 0.0096
  Mask BBox: [77, 129, 94, 148]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5342
  Bounding Box: [938.40, 1838.40, 1088.80, 1988.80]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [78, 148, 89, 159]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5327
  Bounding Box: [996.00, 850.40, 1076.00, 1034.40]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [82, 71, 88, 84]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5269
  Bounding Box: [758.80, 1172.00, 896.80, 1319.20]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [64, 96, 73, 107]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5239
  Bounding Box: [1947.20, 1920.00, 2048.00, 2048.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [157, 154, 164, 163]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5225
  Bounding Box: [1692.80, 418.40, 1840.00, 560.80]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [137, 37, 147, 47]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5205
  Bounding Box: [1595.20, 599.20, 1752.00, 794.40]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [129, 51, 140, 66]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5200
  Bounding Box: [1096.00, 652.40, 1217.60, 780.80]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [90, 55, 99, 64]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5195
  Bounding Box: [1921.60, 1192.00, 2048.00, 1361.60]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [155, 98, 164, 110]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5117
  Bounding Box: [680.40, 934.40, 854.40, 1169.60]
  Mask Area: 159 pixels
  Mask Ratio: 0.0056
  Mask BBox: [58, 77, 70, 95]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5098
  Bounding Box: [580.00, 365.20, 668.80, 478.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [50, 33, 56, 41]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5083
  Bounding Box: [1205.60, 1974.40, 1338.40, 2035.20]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [99, 159, 108, 162]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5078
  Bounding Box: [1528.80, 168.00, 1692.80, 316.80]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [124, 18, 136, 28]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5034
  Bounding Box: [165.60, 1849.60, 327.20, 2028.80]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [17, 149, 29, 162]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5000
  Bounding Box: [242.20, 1304.00, 354.20, 1409.60]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [23, 106, 31, 113]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.4985
  Bounding Box: [1377.60, 546.40, 1644.80, 878.40]
  Mask Area: 399 pixels
  Mask Ratio: 0.0141
  Mask BBox: [112, 47, 132, 71]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.4980
  Bounding Box: [911.20, 320.00, 1069.60, 412.80]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [76, 29, 87, 36]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.4976
  Bounding Box: [651.20, 1912.00, 800.00, 2048.00]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [55, 154, 66, 164]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.4849
  Bounding Box: [1561.60, 1969.60, 1648.00, 2046.40]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [126, 158, 132, 163]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.4839
  Bounding Box: [883.20, 1744.00, 984.00, 1875.20]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [73, 141, 80, 150]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.4829
  Bounding Box: [1090.40, 1782.40, 1231.20, 2019.20]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [90, 144, 100, 161]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.4824
  Bounding Box: [1630.40, 841.60, 1700.80, 923.20]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [132, 70, 136, 76]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.4688
  Bounding Box: [631.60, 392.00, 771.20, 551.20]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [54, 35, 64, 47]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.4673
  Bounding Box: [1963.20, 70.20, 2046.40, 199.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [158, 10, 163, 19]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.4629
  Bounding Box: [724.00, 902.40, 876.00, 1100.80]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [61, 75, 71, 89]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.4619
  Bounding Box: [854.40, 1718.40, 1000.00, 1868.80]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [71, 139, 82, 149]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.4519
  Bounding Box: [750.00, 1188.80, 862.40, 1299.20]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [63, 97, 71, 105]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.4456
  Bounding Box: [1538.40, 1681.60, 1724.80, 1902.40]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [125, 136, 137, 152]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4417
  Bounding Box: [1956.80, 1355.20, 2043.20, 1440.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [157, 110, 163, 116]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.4414
  Bounding Box: [784.00, 1332.80, 1062.40, 1640.00]
  Mask Area: 343 pixels
  Mask Ratio: 0.0122
  Mask BBox: [66, 109, 86, 132]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4402
  Bounding Box: [1545.60, 866.40, 1673.60, 1050.40]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [125, 72, 134, 86]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4402
  Bounding Box: [174.80, 490.00, 383.20, 726.80]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [18, 43, 33, 60]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4341
  Bounding Box: [330.40, 100.60, 493.60, 174.00]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [30, 12, 42, 17]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4341
  Bounding Box: [15.50, 1664.00, 238.80, 1952.00]
  Mask Area: 227 pixels
  Mask Ratio: 0.0080
  Mask BBox: [6, 134, 22, 156]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4294
  Bounding Box: [1256.80, 1696.00, 1407.20, 1987.20]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [103, 137, 113, 159]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4277
  Bounding Box: [1867.20, 1772.80, 2048.00, 2019.20]
  Mask Area: 221 pixels
  Mask Ratio: 0.0078
  Mask BBox: [150, 143, 165, 161]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4272
  Bounding Box: [666.80, 906.40, 804.00, 1069.60]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [57, 75, 66, 85]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4238
  Bounding Box: [0.00, 1072.00, 156.80, 1363.20]
  Mask Area: 259 pixels
  Mask Ratio: 0.0092
  Mask BBox: [4, 88, 16, 110]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4216
  Bounding Box: [349.20, 738.80, 440.40, 864.80]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [32, 62, 38, 71]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4211
  Bounding Box: [315.40, 668.40, 428.80, 752.40]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [29, 57, 36, 62]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4177
  Bounding Box: [220.80, 1862.40, 357.20, 2038.40]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [22, 150, 31, 163]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4116
  Bounding Box: [662.80, 1960.00, 816.00, 2043.20]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [56, 158, 67, 163]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4106
  Bounding Box: [1921.60, 1334.40, 2048.00, 1470.40]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [155, 109, 163, 118]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4106
  Bounding Box: [682.00, 768.00, 866.40, 1091.20]
  Mask Area: 225 pixels
  Mask Ratio: 0.0080
  Mask BBox: [58, 64, 71, 89]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4080
  Bounding Box: [474.80, 1769.60, 606.80, 2025.60]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [42, 143, 51, 162]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4050
  Bounding Box: [960.00, 1964.80, 1139.20, 2035.20]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [79, 158, 92, 162]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.3994
  Bounding Box: [1758.40, 853.60, 1825.60, 922.40]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [142, 71, 146, 76]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.3994
  Bounding Box: [504.00, 1153.60, 601.60, 1257.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [44, 95, 48, 102]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.3989
  Bounding Box: [3.40, 647.20, 175.40, 821.60]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [5, 55, 17, 68]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.3975
  Bounding Box: [556.80, 462.40, 735.20, 680.80]
  Mask Area: 173 pixels
  Mask Ratio: 0.0061
  Mask BBox: [48, 41, 61, 57]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.3896
  Bounding Box: [839.20, 1984.00, 972.00, 2038.40]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [70, 159, 79, 163]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.3892
  Bounding Box: [6.65, 1886.40, 76.20, 2024.00]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 152, 8, 162]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.3877
  Bounding Box: [1368.00, 1905.60, 1545.60, 2048.00]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [111, 153, 124, 164]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.3838
  Bounding Box: [1292.80, 548.40, 1486.40, 738.80]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [105, 47, 120, 61]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.3838
  Bounding Box: [6.10, 1360.80, 100.30, 1516.00]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [5, 111, 10, 122]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.3821
  Bounding Box: [1590.40, 579.20, 1811.20, 824.80]
  Mask Area: 248 pixels
  Mask Ratio: 0.0088
  Mask BBox: [129, 50, 145, 68]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.3801
  Bounding Box: [1516.00, 1402.40, 1700.80, 1601.60]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [123, 114, 136, 129]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.3767
  Bounding Box: [13.90, 564.00, 116.50, 659.20]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [6, 49, 13, 55]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.3767
  Bounding Box: [1907.20, 1325.60, 2032.00, 1421.60]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [153, 108, 162, 115]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.3757
  Bounding Box: [1079.20, 618.00, 1221.60, 760.40]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [89, 53, 99, 63]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.3721
  Bounding Box: [1493.60, 1864.00, 1604.80, 1995.20]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [121, 150, 129, 159]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.3721
  Bounding Box: [491.20, 1142.40, 565.60, 1248.00]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [43, 94, 48, 101]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.3721
  Bounding Box: [516.80, 1168.00, 591.20, 1273.60]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [45, 96, 49, 103]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.3708
  Bounding Box: [528.80, 1211.20, 717.60, 1417.60]
  Mask Area: 201 pixels
  Mask Ratio: 0.0071
  Mask BBox: [46, 99, 60, 114]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.3701
  Bounding Box: [486.80, 1168.80, 564.40, 1268.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [43, 96, 48, 103]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.3701
  Bounding Box: [658.80, 1542.40, 865.60, 1747.20]
  Mask Area: 200 pixels
  Mask Ratio: 0.0071
  Mask BBox: [56, 125, 71, 140]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.3699
  Bounding Box: [1683.20, 406.80, 1907.20, 574.00]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [136, 36, 152, 48]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.3672
  Bounding Box: [742.80, 1180.80, 905.60, 1390.40]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [63, 97, 74, 112]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.3652
  Bounding Box: [117.50, 507.20, 380.80, 792.80]
  Mask Area: 278 pixels
  Mask Ratio: 0.0098
  Mask BBox: [14, 44, 33, 65]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.3650
  Bounding Box: [940.00, 1368.00, 1087.20, 1641.60]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [78, 111, 88, 132]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.3621
  Bounding Box: [754.00, 1255.20, 848.00, 1415.20]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [63, 103, 70, 114]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.3616
  Bounding Box: [845.60, 1620.80, 954.40, 1739.20]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [71, 131, 77, 139]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3589
  Bounding Box: [1319.20, 1470.40, 1490.40, 1579.20]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [108, 119, 120, 127]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3579
  Bounding Box: [1697.60, 1402.40, 1889.60, 1627.20]
  Mask Area: 197 pixels
  Mask Ratio: 0.0070
  Mask BBox: [137, 114, 151, 131]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3567
  Bounding Box: [1149.60, 1426.40, 1284.00, 1517.60]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [94, 116, 104, 122]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [1537.60, 1609.60, 1704.00, 1852.80]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [125, 130, 137, 148]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3535
  Bounding Box: [1108.00, 346.00, 1308.00, 706.80]
  Mask Area: 309 pixels
  Mask Ratio: 0.0109
  Mask BBox: [91, 32, 106, 58]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3508
  Bounding Box: [448.00, 1843.20, 610.40, 2016.00]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [39, 148, 51, 160]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3499
  Bounding Box: [1558.40, 1300.80, 1750.40, 1545.60]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [126, 107, 140, 124]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3474
  Bounding Box: [1261.60, 1644.80, 1391.20, 1900.80]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [103, 133, 112, 152]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3459
  Bounding Box: [1518.40, 1387.20, 1691.20, 1515.20]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [123, 113, 136, 122]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3416
  Bounding Box: [1425.60, 1809.60, 1524.80, 1944.00]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [116, 146, 123, 155]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3376
  Bounding Box: [1200.00, 1945.60, 1321.60, 2038.40]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [98, 156, 107, 163]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3376
  Bounding Box: [812.00, 1704.00, 964.00, 1870.40]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [68, 138, 79, 150]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3367
  Bounding Box: [1707.20, 1136.80, 1889.60, 1362.40]
  Mask Area: 218 pixels
  Mask Ratio: 0.0077
  Mask BBox: [138, 93, 151, 110]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3364
  Bounding Box: [1241.60, 1788.80, 1401.60, 2003.20]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [101, 144, 113, 160]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3359
  Bounding Box: [546.40, 340.20, 659.20, 482.40]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [47, 31, 55, 41]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3354
  Bounding Box: [466.00, 380.00, 560.40, 456.00]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [41, 34, 47, 39]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3350
  Bounding Box: [1092.00, 1473.60, 1196.00, 1630.40]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [90, 120, 97, 130]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3342
  Bounding Box: [446.00, 1360.00, 551.60, 1542.40]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [39, 111, 47, 124]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3342
  Bounding Box: [595.20, 1641.60, 803.20, 1897.60]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [51, 133, 66, 152]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3342
  Bounding Box: [1398.40, 1622.40, 1664.00, 1862.40]
  Mask Area: 292 pixels
  Mask Ratio: 0.0103
  Mask BBox: [114, 131, 133, 149]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3333
  Bounding Box: [796.00, 1712.00, 981.60, 1932.80]
  Mask Area: 199 pixels
  Mask Ratio: 0.0071
  Mask BBox: [67, 138, 80, 154]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3306
  Bounding Box: [41.00, 1019.20, 119.40, 1081.60]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [8, 84, 13, 87]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3279
  Bounding Box: [95.00, 1800.00, 321.00, 2036.80]
  Mask Area: 209 pixels
  Mask Ratio: 0.0074
  Mask BBox: [12, 145, 29, 163]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3271
  Bounding Box: [125.00, 513.20, 247.40, 626.80]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [14, 46, 22, 52]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [859.20, 1988.80, 996.80, 2048.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [72, 160, 81, 163]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3264
  Bounding Box: [1458.40, 163.20, 1632.00, 314.80]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [118, 17, 131, 28]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3252
  Bounding Box: [1534.40, 1237.60, 1755.20, 1444.00]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [124, 101, 141, 116]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3235
  Bounding Box: [258.60, 736.00, 376.00, 860.80]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [25, 62, 33, 71]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3235
  Bounding Box: [269.60, 736.00, 412.80, 899.20]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [26, 62, 36, 74]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [7.00, 1908.80, 107.40, 2040.00]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [5, 154, 12, 163]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3218
  Bounding Box: [1140.80, 1617.60, 1244.80, 1694.40]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [94, 131, 101, 135]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3218
  Bounding Box: [376.40, 1272.00, 635.60, 1425.60]
  Mask Area: 187 pixels
  Mask Ratio: 0.0066
  Mask BBox: [34, 104, 53, 115]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3208
  Bounding Box: [1616.00, 793.60, 1731.20, 924.80]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [131, 66, 139, 76]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3191
  Bounding Box: [1226.40, 1520.00, 1320.80, 1644.80]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [100, 124, 107, 132]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3186
  Bounding Box: [1961.60, 1215.20, 2048.00, 1376.80]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [158, 99, 164, 111]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3167
  Bounding Box: [1644.80, 762.40, 1763.20, 917.60]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [133, 64, 140, 75]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3140
  Bounding Box: [167.60, 928.00, 265.20, 1089.60]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [18, 77, 24, 89]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3140
  Bounding Box: [193.20, 928.00, 290.80, 1089.60]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [20, 77, 25, 89]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3123
  Bounding Box: [1889.60, 1137.60, 2040.00, 1364.80]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [152, 93, 163, 110]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3108
  Bounding Box: [1664.00, 470.40, 1750.40, 583.20]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [134, 41, 140, 49]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3108
  Bounding Box: [721.20, 1268.00, 834.40, 1412.00]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [61, 104, 68, 114]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3108
  Bounding Box: [746.80, 1293.60, 860.00, 1437.60]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [63, 106, 71, 114]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3101
  Bounding Box: [6.85, 1537.60, 82.60, 1656.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [5, 125, 10, 133]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [9.15, 1614.40, 75.60, 1710.40]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [5, 131, 9, 137]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [272.80, 744.00, 353.60, 851.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [26, 63, 31, 70]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3052
  Bounding Box: [35.40, 520.80, 129.40, 632.80]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [7, 45, 14, 53]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3040
  Bounding Box: [5.45, 1385.60, 92.00, 1569.60]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [5, 113, 10, 126]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3035
  Bounding Box: [1590.40, 1980.80, 1718.40, 2038.40]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [129, 159, 138, 163]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3025
  Bounding Box: [1968.00, 573.60, 2041.60, 754.40]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [158, 49, 163, 62]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3020
  Bounding Box: [1135.20, 1676.80, 1255.20, 1923.20]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [93, 135, 102, 154]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.2986
  Bounding Box: [1175.20, 1418.40, 1314.40, 1511.20]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [96, 115, 106, 122]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.2983
  Bounding Box: [219.80, 1296.80, 325.00, 1392.80]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [22, 106, 29, 112]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.2983
  Bounding Box: [219.80, 1322.40, 325.00, 1418.40]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [22, 108, 29, 113]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.2961
  Bounding Box: [34.75, 1320.00, 209.40, 1504.00]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [7, 108, 20, 121]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.2954
  Bounding Box: [350.80, 502.40, 614.80, 680.00]
  Mask Area: 221 pixels
  Mask Ratio: 0.0078
  Mask BBox: [32, 44, 52, 57]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.2952
  Bounding Box: [1953.60, 938.40, 2033.60, 1028.00]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [157, 78, 162, 84]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.2944
  Bounding Box: [139.40, 1995.20, 256.60, 2043.20]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [15, 160, 24, 163]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.2939
  Bounding Box: [999.20, 1974.40, 1165.60, 2048.00]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [83, 159, 95, 163]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.2930
  Bounding Box: [6.70, 525.20, 111.30, 613.20]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [5, 46, 12, 51]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.2927
  Bounding Box: [1392.80, 306.80, 1646.40, 620.40]
  Mask Area: 407 pixels
  Mask Ratio: 0.0144
  Mask BBox: [113, 28, 132, 52]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.2903
  Bounding Box: [1665.60, 500.80, 1748.80, 611.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [135, 44, 140, 50]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.2891
  Bounding Box: [1134.40, 1448.00, 1262.40, 1558.40]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [93, 118, 102, 125]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.2876
  Bounding Box: [779.20, 1692.80, 928.00, 1910.40]
  Mask Area: 188 pixels
  Mask Ratio: 0.0067
  Mask BBox: [65, 137, 76, 153]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.2839
  Bounding Box: [1293.60, 1660.80, 1386.40, 1795.20]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [106, 134, 112, 144]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [165.00, 860.00, 275.00, 1015.20]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [18, 72, 25, 83]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.2810
  Bounding Box: [321.60, 741.60, 450.40, 900.00]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [30, 62, 39, 74]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.2798
  Bounding Box: [1431.20, 1064.80, 1721.60, 1224.80]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [116, 88, 138, 99]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.2786
  Bounding Box: [1894.40, 413.60, 2044.80, 613.60]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [152, 37, 163, 51]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.2781
  Bounding Box: [1533.60, 1971.20, 1640.00, 2025.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [124, 158, 132, 162]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.2761
  Bounding Box: [1979.20, 1664.00, 2036.80, 1801.60]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [159, 134, 163, 144]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.2739
  Bounding Box: [1609.60, 864.80, 1692.80, 944.80]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [130, 72, 136, 75]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.2739
  Bounding Box: [1635.20, 864.80, 1718.40, 944.80]
  Mask Area: 17 pixels
  Mask Ratio: 0.0006
  Mask BBox: [132, 72, 136, 75]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.2732
  Bounding Box: [1643.20, 378.80, 1835.20, 544.40]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [133, 34, 147, 46]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.2698
  Bounding Box: [1662.40, 1438.40, 1758.40, 1564.80]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [134, 117, 141, 126]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.2698
  Bounding Box: [1688.00, 1438.40, 1784.00, 1564.80]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [136, 117, 142, 126]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.2698
  Bounding Box: [1165.60, 716.80, 1351.20, 824.00]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [96, 60, 109, 68]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.2693
  Bounding Box: [503.20, 1676.80, 594.40, 1833.60]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [44, 135, 50, 147]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.2681
  Bounding Box: [1482.40, 1219.20, 1662.40, 1392.00]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [120, 100, 133, 112]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.2671
  Bounding Box: [1221.60, 1940.80, 1354.40, 2033.60]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [100, 156, 109, 162]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.2666
  Bounding Box: [995.20, 1403.20, 1091.20, 1598.40]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [82, 114, 89, 128]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.2666
  Bounding Box: [1127.20, 656.00, 1295.20, 841.60]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [93, 56, 105, 69]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.2664
  Bounding Box: [1996.80, 1900.80, 2048.00, 2022.40]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [160, 153, 165, 161]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.2659
  Bounding Box: [349.20, 500.00, 429.20, 576.80]
  Mask Area: 21 pixels
  Mask Ratio: 0.0007
  Mask BBox: [32, 44, 37, 48]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.2625
  Bounding Box: [1929.60, 907.20, 2048.00, 1003.20]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [155, 75, 163, 82]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.2617
  Bounding Box: [1608.00, 708.80, 1764.80, 908.80]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [130, 60, 140, 74]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.2610
  Bounding Box: [1715.20, 872.00, 1840.00, 1102.40]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [138, 73, 147, 90]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.2607
  Bounding Box: [1548.00, 168.00, 1708.80, 453.60]
  Mask Area: 214 pixels
  Mask Ratio: 0.0076
  Mask BBox: [125, 18, 136, 39]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.2595
  Bounding Box: [911.20, 1964.80, 1095.20, 2044.80]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [76, 158, 89, 163]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.2576
  Bounding Box: [1667.20, 1467.20, 1756.80, 1596.80]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [135, 119, 141, 128]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.2573
  Bounding Box: [1036.80, 1123.20, 1094.40, 1195.20]
  Mask Area: 21 pixels
  Mask Ratio: 0.0007
  Mask BBox: [85, 92, 89, 96]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.2573
  Bounding Box: [1236.80, 144.40, 1356.80, 238.00]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [101, 16, 108, 22]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2544
  Bounding Box: [930.40, 1824.00, 1146.40, 2009.60]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [77, 147, 93, 160]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2537
  Bounding Box: [1848.00, 522.80, 2027.20, 784.80]
  Mask Area: 235 pixels
  Mask Ratio: 0.0083
  Mask BBox: [149, 45, 162, 65]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2527
  Bounding Box: [1040.80, 722.40, 1130.40, 936.80]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [86, 61, 92, 77]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2520
  Bounding Box: [468.00, 386.80, 577.60, 478.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [41, 35, 49, 41]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2512
  Bounding Box: [1453.60, 1329.60, 1554.40, 1476.80]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [118, 108, 125, 119]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2512
  Bounding Box: [1479.20, 1329.60, 1580.00, 1476.80]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [120, 108, 127, 119]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2496
  Bounding Box: [651.20, 440.00, 766.40, 567.20]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [55, 39, 63, 48]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2477
  Bounding Box: [996.00, 488.40, 1242.40, 721.20]
  Mask Area: 281 pixels
  Mask Ratio: 0.0100
  Mask BBox: [82, 43, 101, 60]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2473
  Bounding Box: [26.00, 1005.60, 105.80, 1068.00]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [7, 84, 12, 87]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2473
  Bounding Box: [51.60, 1005.60, 131.40, 1068.00]
  Mask Area: 21 pixels
  Mask Ratio: 0.0007
  Mask BBox: [9, 83, 14, 87]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2473
  Bounding Box: [26.00, 1031.20, 105.80, 1093.60]
  Mask Area: 17 pixels
  Mask Ratio: 0.0006
  Mask BBox: [7, 85, 12, 87]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2473
  Bounding Box: [51.60, 1031.20, 131.40, 1093.60]
  Mask Area: 14 pixels
  Mask Ratio: 0.0005
  Mask BBox: [9, 85, 14, 87]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2467
  Bounding Box: [756.80, 1716.80, 955.20, 1976.00]
  Mask Area: 247 pixels
  Mask Ratio: 0.0088
  Mask BBox: [64, 139, 78, 157]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2455
  Bounding Box: [61.40, 1052.80, 201.80, 1219.20]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [9, 87, 19, 99]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2440
  Bounding Box: [1284.80, 1475.20, 1400.00, 1547.20]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [105, 120, 113, 123]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2437
  Bounding Box: [1908.80, 594.00, 2024.00, 768.00]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [154, 51, 162, 63]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2422
  Bounding Box: [424.40, 319.80, 536.40, 417.60]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [38, 29, 45, 36]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2422
  Bounding Box: [424.40, 345.40, 536.40, 443.20]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [39, 31, 45, 38]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2415
  Bounding Box: [905.60, 281.40, 1060.80, 376.40]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [75, 26, 86, 33]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2411
  Bounding Box: [122.40, 1030.40, 259.20, 1158.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [14, 85, 24, 94]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2407
  Bounding Box: [330.00, 559.20, 489.20, 708.00]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [30, 48, 42, 59]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2401
  Bounding Box: [832.00, 1825.60, 1067.20, 2001.60]
  Mask Area: 198 pixels
  Mask Ratio: 0.0070
  Mask BBox: [69, 147, 87, 160]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2397
  Bounding Box: [0.00, 1034.40, 123.00, 1186.40]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [4, 85, 13, 96]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2391
  Bounding Box: [1948.80, 1317.60, 2048.00, 1432.80]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [157, 107, 163, 115]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2375
  Bounding Box: [450.00, 350.80, 561.20, 444.40]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [40, 32, 47, 36]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2372
  Bounding Box: [1364.00, 1491.20, 1535.20, 1801.60]
  Mask Area: 204 pixels
  Mask Ratio: 0.0072
  Mask BBox: [111, 121, 123, 144]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2366
  Bounding Box: [0.00, 1395.20, 60.50, 1553.60]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [3, 113, 8, 125]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2351
  Bounding Box: [1014.40, 833.60, 1097.60, 1027.20]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [84, 70, 89, 84]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2347
  Bounding Box: [23.45, 643.60, 136.20, 801.60]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [6, 55, 11, 66]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2338
  Bounding Box: [1174.40, 742.40, 1340.80, 864.00]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [96, 62, 105, 68]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2338
  Bounding Box: [536.40, 1292.00, 656.40, 1432.80]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [46, 105, 55, 115]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2335
  Bounding Box: [1068.80, 1500.80, 1222.40, 1696.00]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [88, 122, 99, 136]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2334
  Bounding Box: [1402.40, 1884.80, 1606.40, 2028.80]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [114, 152, 129, 162]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2330
  Bounding Box: [193.60, 866.40, 298.80, 1004.00]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [20, 72, 27, 82]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2323
  Bounding Box: [1311.20, 1472.80, 1426.40, 1549.60]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [107, 120, 115, 125]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2323
  Bounding Box: [912.80, 1854.40, 1060.00, 2001.60]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [76, 149, 86, 160]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2295
  Bounding Box: [2.90, 1792.00, 66.40, 1926.40]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [5, 144, 9, 154]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2292
  Bounding Box: [685.60, 1827.20, 908.00, 2032.00]
  Mask Area: 228 pixels
  Mask Ratio: 0.0081
  Mask BBox: [58, 147, 74, 162]

