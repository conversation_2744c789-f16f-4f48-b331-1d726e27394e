#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化调试模块
专门用于分析YOLOv5分割模型验证过程中的可视化问题
特别是val_batch0_pred输出中标签显示异常的问题

主要功能:
1. 调试output_to_target函数的转换过程
2. 分析plot_images_and_masks函数的可视化逻辑
3. 检查多模态数据处理的兼容性
4. 提供详细的数据流分析
"""

import torch
import numpy as np
import cv2
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
import sys

# 添加项目根目录到Python路径
FILE = Path(__file__).resolve()
ROOT = FILE.parents[0]
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

from utils.segment.general import LOGGER, colorstr
from utils.segment.plots import Colors, xywh2xyxy
from utils.segment.general import scale_image


class ValidationDebugger:
    """
    验证过程调试器
    用于分析验证过程中的数据流和可视化问题
    """
    
    def __init__(self, save_dir=None):
        """
        初始化调试器
        
        Args:
            save_dir: 调试信息保存目录
        """
        self.save_dir = Path(save_dir) if save_dir else Path('debug_output')
        self.save_dir.mkdir(parents=True, exist_ok=True)
        self.colors = Colors()
        self.debug_info = {}
        
    def debug_model_output(self, output, batch_idx=0, save_details=True):
        """
        调试模型原始输出
        
        Args:
            output: 模型输出
            batch_idx: 批次索引
            save_details: 是否保存详细信息
        
        Returns:
            dict: 调试信息
        """
        debug_info = {
            'batch_idx': batch_idx,
            'output_type': type(output),
            'output_length': len(output) if hasattr(output, '__len__') else None
        }
        
        print(f"\n{'='*50}")
        print(f"调试模型输出 - 批次 {batch_idx}")
        print(f"{'='*50}")
        print(f"输出类型: {debug_info['output_type']}")
        print(f"输出长度: {debug_info['output_length']}")
        
        if hasattr(output, '__iter__'):
            for i, o in enumerate(output):
                item_info = self._analyze_output_item(o, i)
                debug_info[f'item_{i}'] = item_info
                
        if save_details:
            self._save_debug_info(debug_info, f'model_output_batch_{batch_idx}.txt')
            
        return debug_info
    
    def _analyze_output_item(self, item, item_idx):
        """
        分析单个输出项
        
        Args:
            item: 输出项
            item_idx: 项索引
        
        Returns:
            dict: 分析结果
        """
        info = {
            'item_idx': item_idx,
            'type': type(item),
            'shape': getattr(item, 'shape', None),
            'dtype': getattr(item, 'dtype', None),
            'device': getattr(item, 'device', None)
        }
        
        print(f"\n  项 {item_idx}:")
        print(f"    类型: {info['type']}")
        print(f"    形状: {info['shape']}")
        print(f"    数据类型: {info['dtype']}")
        print(f"    设备: {info['device']}")
        
        if hasattr(item, 'shape') and len(item.shape) >= 2:
            # 分析检测结果
            num_detections = item.shape[0]
            num_features = item.shape[1]
            
            info['num_detections'] = num_detections
            info['num_features'] = num_features
            
            print(f"    检测数量: {num_detections}")
            print(f"    特征维度: {num_features}")
            
            if num_detections > 0 and num_features >= 6:
                # 分析前几个检测结果
                sample_size = min(3, num_detections)
                sample_data = item[:sample_size].cpu().numpy() if hasattr(item, 'cpu') else item[:sample_size]
                
                info['sample_data'] = sample_data.tolist()
                
                print(f"    样本数据 (前{sample_size}个):")
                for j, detection in enumerate(sample_data):
                    print(f"      检测 {j}: {detection[:6]}...")
                    if num_features >= 6:
                        print(f"        边界框: [{detection[0]:.3f}, {detection[1]:.3f}, {detection[2]:.3f}, {detection[3]:.3f}]")
                        print(f"        置信度: {detection[4]:.3f}")
                        print(f"        类别: {detection[5]:.0f}")
                        
                # 统计信息
                if hasattr(item, 'cpu'):
                    item_np = item.cpu().numpy()
                else:
                    item_np = np.array(item)
                    
                info['stats'] = {
                    'conf_range': [float(item_np[:, 4].min()), float(item_np[:, 4].max())] if num_features > 4 else None,
                    'class_range': [float(item_np[:, 5].min()), float(item_np[:, 5].max())] if num_features > 5 else None,
                    'bbox_range': {
                        'x_min': float(item_np[:, 0].min()),
                        'x_max': float(item_np[:, 0].max()),
                        'y_min': float(item_np[:, 1].min()),
                        'y_max': float(item_np[:, 1].max())
                    } if num_features >= 4 else None
                }
                
                print(f"    统计信息:")
                if info['stats']['conf_range']:
                    print(f"      置信度范围: {info['stats']['conf_range']}")
                if info['stats']['class_range']:
                    print(f"      类别范围: {info['stats']['class_range']}")
                if info['stats']['bbox_range']:
                    bbox_stats = info['stats']['bbox_range']
                    print(f"      边界框范围: x[{bbox_stats['x_min']:.3f}, {bbox_stats['x_max']:.3f}], y[{bbox_stats['y_min']:.3f}, {bbox_stats['y_max']:.3f}]")
        
        return info
    
    def debug_output_to_target_conversion(self, output, max_det=300):
        """
        调试output_to_target转换过程
        
        Args:
            output: 模型输出
            max_det: 最大检测数量
        
        Returns:
            tuple: (转换结果, 调试信息)
        """
        print(f"\n{'='*50}")
        print(f"调试 output_to_target 转换过程")
        print(f"{'='*50}")
        print(f"最大检测数量: {max_det}")
        
        debug_info = {
            'input_type': type(output),
            'input_length': len(output) if hasattr(output, '__len__') else None,
            'max_det': max_det,
            'conversion_steps': []
        }
        
        targets = []
        
        for i, o in enumerate(output):
            step_info = {
                'batch_idx': i,
                'input_shape': getattr(o, 'shape', None),
                'input_dtype': getattr(o, 'dtype', None)
            }
            
            print(f"\n批次 {i}:")
            print(f"  输入形状: {step_info['input_shape']}")
            print(f"  输入数据类型: {step_info['input_dtype']}")
            
            if hasattr(o, 'shape') and len(o.shape) >= 2 and o.shape[1] >= 6:
                # 限制检测数量
                o_limited = o[:max_det]
                step_info['limited_shape'] = o_limited.shape
                print(f"  限制后形状: {step_info['limited_shape']}")
                
                # 分离组件
                try:
                    # 假设输入格式为 [x1, y1, x2, y2, conf, cls, ...]
                    box = o_limited[:, :4].cpu()  # xyxy格式
                    conf = o_limited[:, 4:5].cpu()
                    cls = o_limited[:, 5:6].cpu()
                    
                    step_info['box_shape'] = box.shape
                    step_info['conf_shape'] = conf.shape
                    step_info['cls_shape'] = cls.shape
                    
                    print(f"  边界框形状: {step_info['box_shape']}")
                    print(f"  置信度形状: {step_info['conf_shape']}")
                    print(f"  类别形状: {step_info['cls_shape']}")
                    
                    # 检查边界框格式和值
                    if box.shape[0] > 0:
                        box_sample = box[:min(3, box.shape[0])]
                        print(f"  边界框样本 (xyxy): {box_sample.tolist()}")
                        
                        # 检查是否为归一化坐标
                        is_normalized = (box.max() <= 1.0).item()
                        step_info['is_normalized'] = is_normalized
                        print(f"  是否归一化: {is_normalized}")
                        
                        # 转换为xywh格式
                        box_xywh = torch.zeros_like(box)
                        box_xywh[:, 0] = (box[:, 0] + box[:, 2]) / 2  # x_center
                        box_xywh[:, 1] = (box[:, 1] + box[:, 3]) / 2  # y_center
                        box_xywh[:, 2] = box[:, 2] - box[:, 0]        # width
                        box_xywh[:, 3] = box[:, 3] - box[:, 1]        # height
                        
                        box_xywh_sample = box_xywh[:min(3, box_xywh.shape[0])]
                        print(f"  转换后边界框样本 (xywh): {box_xywh_sample.tolist()}")
                        
                        step_info['box_xywh_sample'] = box_xywh_sample.tolist()
                    
                    # 创建批次索引
                    j = torch.full((conf.shape[0], 1), i)
                    step_info['batch_idx_shape'] = j.shape
                    
                    # 拼接最终结果 [batch_id, class_id, x, y, w, h, conf]
                    target = torch.cat((j, cls, box_xywh, conf), 1)
                    step_info['target_shape'] = target.shape
                    
                    print(f"  最终目标形状: {step_info['target_shape']}")
                    
                    if target.shape[0] > 0:
                        target_sample = target[:min(3, target.shape[0])]
                        print(f"  最终目标样本: {target_sample.tolist()}")
                        step_info['target_sample'] = target_sample.tolist()
                    
                    targets.append(target)
                    step_info['success'] = True
                    
                except Exception as e:
                    print(f"  转换出错: {e}")
                    step_info['error'] = str(e)
                    step_info['success'] = False
            else:
                print(f"  跳过: 形状不符合要求")
                step_info['skipped'] = True
                step_info['success'] = False
            
            debug_info['conversion_steps'].append(step_info)
        
        # 合并结果
        if targets:
            try:
                result = torch.cat(targets, 0).numpy()
                debug_info['final_shape'] = result.shape
                debug_info['final_dtype'] = result.dtype
                
                print(f"\n最终合并结果:")
                print(f"  形状: {debug_info['final_shape']}")
                print(f"  数据类型: {debug_info['final_dtype']}")
                
                if result.shape[0] > 0:
                    print(f"  总检测数量: {result.shape[0]}")
                    print(f"  特征维度: {result.shape[1]}")
                    
                    # 统计每个批次的检测数量
                    batch_counts = {}
                    for batch_id in np.unique(result[:, 0]):
                        count = np.sum(result[:, 0] == batch_id)
                        batch_counts[int(batch_id)] = count
                    
                    debug_info['batch_counts'] = batch_counts
                    print(f"  各批次检测数量: {batch_counts}")
                    
                    # 统计类别分布
                    class_counts = {}
                    for class_id in np.unique(result[:, 1]):
                        count = np.sum(result[:, 1] == class_id)
                        class_counts[int(class_id)] = count
                    
                    debug_info['class_counts'] = class_counts
                    print(f"  类别分布: {class_counts}")
                
                debug_info['success'] = True
                return result, debug_info
                
            except Exception as e:
                print(f"\n合并结果时出错: {e}")
                debug_info['merge_error'] = str(e)
                debug_info['success'] = False
                return np.array([]).reshape(0, 7), debug_info
        else:
            print(f"\n没有有效的转换结果")
            debug_info['success'] = False
            return np.array([]).reshape(0, 7), debug_info
    
    def debug_plot_masks_data(self, plot_masks, batch_idx=0):
        """
        调试plot_masks数据
        
        Args:
            plot_masks: 掩码数据
            batch_idx: 批次索引
        
        Returns:
            dict: 调试信息
        """
        print(f"\n{'='*50}")
        print(f"调试 plot_masks 数据 - 批次 {batch_idx}")
        print(f"{'='*50}")
        
        debug_info = {
            'batch_idx': batch_idx,
            'type': type(plot_masks),
            'shape': getattr(plot_masks, 'shape', None),
            'dtype': getattr(plot_masks, 'dtype', None)
        }
        
        print(f"掩码类型: {debug_info['type']}")
        print(f"掩码形状: {debug_info['shape']}")
        print(f"掩码数据类型: {debug_info['dtype']}")
        
        if hasattr(plot_masks, 'shape') and len(plot_masks.shape) >= 3:
            num_masks = plot_masks.shape[0]
            mask_height = plot_masks.shape[1]
            mask_width = plot_masks.shape[2]
            
            debug_info.update({
                'num_masks': num_masks,
                'mask_height': mask_height,
                'mask_width': mask_width
            })
            
            print(f"掩码数量: {num_masks}")
            print(f"掩码尺寸: {mask_height} x {mask_width}")
            
            if hasattr(plot_masks, 'cpu'):
                masks_np = plot_masks.cpu().numpy()
            else:
                masks_np = np.array(plot_masks)
            
            # 分析掩码值分布
            unique_values = np.unique(masks_np)
            debug_info['unique_values'] = unique_values.tolist()
            debug_info['value_range'] = [float(masks_np.min()), float(masks_np.max())]
            
            print(f"掩码值范围: {debug_info['value_range']}")
            print(f"唯一值: {debug_info['unique_values']}")
            
            # 分析每个掩码
            mask_stats = []
            for i in range(min(5, num_masks)):  # 只分析前5个掩码
                mask = masks_np[i]
                stats = {
                    'mask_idx': i,
                    'non_zero_pixels': int(np.count_nonzero(mask)),
                    'total_pixels': int(mask.size),
                    'coverage_ratio': float(np.count_nonzero(mask) / mask.size)
                }
                mask_stats.append(stats)
                
                print(f"  掩码 {i}: 非零像素={stats['non_zero_pixels']}, 覆盖率={stats['coverage_ratio']:.3f}")
            
            debug_info['mask_stats'] = mask_stats
        
        return debug_info
    
    def compare_targets_and_masks(self, targets, plot_masks, image_shape=None):
        """
        比较目标和掩码的一致性
        
        Args:
            targets: 目标数据 [batch_id, class_id, x, y, w, h, conf]
            plot_masks: 掩码数据
            image_shape: 图像形状
        
        Returns:
            dict: 比较结果
        """
        print(f"\n{'='*50}")
        print(f"比较目标和掩码的一致性")
        print(f"{'='*50}")
        
        debug_info = {
            'targets_shape': getattr(targets, 'shape', None),
            'masks_shape': getattr(plot_masks, 'shape', None),
            'image_shape': image_shape
        }
        
        print(f"目标形状: {debug_info['targets_shape']}")
        print(f"掩码形状: {debug_info['masks_shape']}")
        print(f"图像形状: {debug_info['image_shape']}")
        
        # 检查数量一致性
        if hasattr(targets, 'shape') and hasattr(plot_masks, 'shape'):
            if len(targets.shape) >= 2 and len(plot_masks.shape) >= 3:
                num_targets = targets.shape[0]
                num_masks = plot_masks.shape[0]
                
                debug_info['num_targets'] = num_targets
                debug_info['num_masks'] = num_masks
                debug_info['count_match'] = num_targets == num_masks
                
                print(f"目标数量: {num_targets}")
                print(f"掩码数量: {num_masks}")
                print(f"数量匹配: {debug_info['count_match']}")
                
                if not debug_info['count_match']:
                    print(f"警告: 目标和掩码数量不匹配!")
                    debug_info['warning'] = "目标和掩码数量不匹配"
        
        return debug_info
    
    def _save_debug_info(self, info, filename):
        """
        保存调试信息到文件
        
        Args:
            info: 调试信息字典
            filename: 文件名
        """
        try:
            import json
            filepath = self.save_dir / filename
            
            # 转换不可序列化的对象
            def convert_for_json(obj):
                if isinstance(obj, (np.ndarray, torch.Tensor)):
                    return obj.tolist() if hasattr(obj, 'tolist') else str(obj)
                elif isinstance(obj, (np.integer, np.floating)):
                    return obj.item()
                elif hasattr(obj, '__dict__'):
                    return str(obj)
                return obj
            
            def recursive_convert(obj):
                if isinstance(obj, dict):
                    return {k: recursive_convert(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [recursive_convert(item) for item in obj]
                else:
                    return convert_for_json(obj)
            
            converted_info = recursive_convert(info)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(converted_info, f, indent=2, ensure_ascii=False)
            
            print(f"调试信息已保存到: {filepath}")
            
        except Exception as e:
            print(f"保存调试信息时出错: {e}")
    
    def generate_debug_report(self, output, targets, plot_masks, image_shape=None, batch_idx=0):
        """
        生成完整的调试报告
        
        Args:
            output: 模型原始输出
            targets: 转换后的目标
            plot_masks: 掩码数据
            image_shape: 图像形状
            batch_idx: 批次索引
        
        Returns:
            dict: 完整的调试报告
        """
        print(f"\n{colorstr('bright_blue', 'bold', '生成调试报告')}")
        
        report = {
            'batch_idx': batch_idx,
            'timestamp': str(Path().cwd()),
            'model_output': self.debug_model_output(output, batch_idx, save_details=False),
            'conversion_debug': None,
            'masks_debug': None,
            'consistency_check': None
        }
        
        # 调试转换过程
        if output is not None:
            _, conversion_debug = self.debug_output_to_target_conversion(output)
            report['conversion_debug'] = conversion_debug
        
        # 调试掩码数据
        if plot_masks is not None:
            masks_debug = self.debug_plot_masks_data(plot_masks, batch_idx)
            report['masks_debug'] = masks_debug
        
        # 一致性检查
        if targets is not None and plot_masks is not None:
            consistency_check = self.compare_targets_and_masks(targets, plot_masks, image_shape)
            report['consistency_check'] = consistency_check
        
        # 保存完整报告
        self._save_debug_info(report, f'debug_report_batch_{batch_idx}.json')
        
        return report


def create_debug_visualization(targets, plot_masks, image, save_path, names=None):
    """
    创建调试可视化图像
    
    Args:
        targets: 目标数据
        plot_masks: 掩码数据
        image: 原始图像
        save_path: 保存路径
        names: 类别名称
    """
    try:
        print(f"\n创建调试可视化图像: {save_path}")
        
        # 确保图像是numpy数组
        if isinstance(image, torch.Tensor):
            if image.dim() == 4:  # [B, C, H, W]
                image = image[0]  # 取第一个批次
            if image.dim() == 3 and image.shape[0] in [1, 3]:  # [C, H, W]
                image = image.permute(1, 2, 0)  # [H, W, C]
            image = image.cpu().numpy()
        
        # 归一化到0-255
        if image.dtype != np.uint8:
            if image.max() <= 1.0:
                image = (image * 255).astype(np.uint8)
            else:
                image = image.astype(np.uint8)
        
        # 确保是RGB格式
        if len(image.shape) == 3 and image.shape[2] == 3:
            # RGB图像
            vis_image = image.copy()
        elif len(image.shape) == 2:
            # 灰度图像转RGB
            vis_image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
        else:
            print(f"警告: 不支持的图像格式: {image.shape}")
            return
        
        height, width = vis_image.shape[:2]
        colors = Colors()
        
        # 绘制边界框和标签
        if targets is not None and len(targets) > 0:
            for target in targets:
                if len(target) >= 7:
                    batch_id, class_id, x_center, y_center, w, h, conf = target[:7]
                    
                    # 转换为像素坐标
                    x_center *= width
                    y_center *= height
                    w *= width
                    h *= height
                    
                    # 计算边界框坐标
                    x1 = int(x_center - w/2)
                    y1 = int(y_center - h/2)
                    x2 = int(x_center + w/2)
                    y2 = int(y_center + h/2)
                    
                    # 绘制边界框
                    color = colors(int(class_id), True)  # BGR格式
                    cv2.rectangle(vis_image, (x1, y1), (x2, y2), color, 2)
                    
                    # 绘制标签
                    label = f"{names[int(class_id)] if names and int(class_id) < len(names) else int(class_id)}: {conf:.2f}"
                    label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
                    cv2.rectangle(vis_image, (x1, y1 - label_size[1] - 10), (x1 + label_size[0], y1), color, -1)
                    cv2.putText(vis_image, label, (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 保存图像
        save_path = Path(save_path)
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 转换为RGB格式保存
        vis_image_rgb = cv2.cvtColor(vis_image, cv2.COLOR_BGR2RGB)
        Image.fromarray(vis_image_rgb).save(save_path)
        
        print(f"调试可视化图像已保存: {save_path}")
        
    except Exception as e:
        print(f"创建调试可视化时出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    # 测试调试器
    debugger = ValidationDebugger('debug_test')
    print("调试器创建成功")
    
    # 创建模拟数据进行测试
    mock_output = [
        torch.randn(10, 7),  # 模拟检测结果
        torch.randn(5, 7)
    ]
    
    # 测试调试功能
    debugger.debug_model_output(mock_output)
    result, debug_info = debugger.debug_output_to_target_conversion(mock_output)
    
    print(f"\n测试完成，结果形状: {result.shape}")