import os

def count_yolo_masks_in_files(folder_path):
    """
    统计每个 YOLO 掩码 txt 文件中的掩码数量（每行代表一个掩码）
    :param folder_path: 标签文件夹路径
    :return: {文件名: 掩码数量} 的字典
    """
    if not os.path.exists(folder_path):
        print(f"❌ 路径不存在: {folder_path}")
        return {}

    results = {}
    total_masks = 0

    for file in os.listdir(folder_path):
        if file.endswith(".txt"):
            file_path = os.path.join(folder_path, file)
            with open(file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()
                mask_count = len([line for line in lines if line.strip()])  # 有效行数
                results[file] = mask_count
                total_masks += mask_count

    # 输出统计结果
    print(f"📂 标签文件夹: {folder_path}")
    for k, v in results.items():
        print(f"📝 {k}: {v} 个掩码")

    print(f"\n✅ 总文件数: {len(results)}")
    print(f"✅ 总掩码数: {total_masks}")

    return results


if __name__ == "__main__":
    # 修改为你的标签文件夹路径
    folder = r"D:/yolov5/dataset-seg/labels/train"
    count_yolo_masks_in_files(folder)
