Image: tile_0165.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8711
  Bounding Box: [1712.00, 1250.40, 1884.80, 1480.80]
  Mask Area: 181 pixels
  Mask Ratio: 0.0064
  Mask BBox: [138, 102, 151, 119]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8481
  Bounding Box: [1414.40, 672.00, 1641.60, 1033.60]
  Mask Area: 354 pixels
  Mask Ratio: 0.0125
  Mask BBox: [115, 57, 132, 84]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8291
  Bounding Box: [1870.40, 860.00, 2048.00, 1048.80]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [151, 72, 164, 85]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8203
  Bounding Box: [1302.40, 1292.80, 1528.00, 1611.20]
  Mask Area: 306 pixels
  Mask Ratio: 0.0108
  Mask BBox: [106, 106, 122, 128]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8130
  Bounding Box: [246.80, 1686.40, 443.60, 2035.20]
  Mask Area: 267 pixels
  Mask Ratio: 0.0095
  Mask BBox: [24, 136, 38, 162]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8101
  Bounding Box: [844.80, 196.00, 1027.20, 406.40]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [70, 20, 84, 35]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8062
  Bounding Box: [0.00, 960.80, 134.00, 1237.60]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [4, 80, 14, 100]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8047
  Bounding Box: [1660.80, 643.20, 1961.60, 944.00]
  Mask Area: 376 pixels
  Mask Ratio: 0.0133
  Mask BBox: [134, 55, 157, 77]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8042
  Bounding Box: [1385.60, 1652.80, 1536.00, 1848.00]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [113, 134, 123, 147]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8042
  Bounding Box: [984.00, 1769.60, 1187.20, 2006.40]
  Mask Area: 200 pixels
  Mask Ratio: 0.0071
  Mask BBox: [81, 143, 96, 160]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8008
  Bounding Box: [1172.80, 500.40, 1344.00, 747.60]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [96, 44, 108, 62]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.7974
  Bounding Box: [172.80, 836.80, 384.80, 1281.60]
  Mask Area: 433 pixels
  Mask Ratio: 0.0153
  Mask BBox: [18, 70, 34, 104]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.7959
  Bounding Box: [1160.00, 1592.00, 1360.00, 1723.20]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [95, 129, 110, 138]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.7920
  Bounding Box: [793.60, 395.60, 977.60, 560.40]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [66, 35, 80, 46]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7915
  Bounding Box: [676.80, 1255.20, 809.60, 1372.00]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [57, 103, 66, 109]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7876
  Bounding Box: [359.20, 672.80, 599.20, 944.80]
  Mask Area: 240 pixels
  Mask Ratio: 0.0085
  Mask BBox: [33, 57, 50, 77]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7861
  Bounding Box: [1244.80, 1792.00, 1347.20, 1942.40]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [102, 144, 109, 155]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7842
  Bounding Box: [1904.00, 194.60, 2022.40, 395.20]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [153, 20, 161, 34]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7837
  Bounding Box: [1319.20, 128.80, 1548.00, 338.00]
  Mask Area: 187 pixels
  Mask Ratio: 0.0066
  Mask BBox: [108, 15, 124, 30]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7817
  Bounding Box: [780.00, 1580.00, 1042.40, 1953.60]
  Mask Area: 454 pixels
  Mask Ratio: 0.0161
  Mask BBox: [65, 128, 85, 156]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7788
  Bounding Box: [282.80, 298.40, 493.20, 568.00]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [27, 28, 42, 48]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7769
  Bounding Box: [271.20, 1225.60, 409.60, 1305.60]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [26, 100, 34, 105]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7769
  Bounding Box: [635.60, 1036.00, 832.80, 1178.40]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [54, 85, 69, 96]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7764
  Bounding Box: [250.80, 562.40, 431.60, 793.60]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [24, 48, 37, 65]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7754
  Bounding Box: [155.60, 1635.20, 281.20, 1785.60]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [17, 132, 25, 143]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7715
  Bounding Box: [1260.80, 338.80, 1368.00, 450.80]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [103, 31, 110, 38]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7715
  Bounding Box: [436.40, 535.20, 713.20, 763.20]
  Mask Area: 265 pixels
  Mask Ratio: 0.0094
  Mask BBox: [39, 46, 59, 63]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7695
  Bounding Box: [1739.20, 1820.80, 2048.00, 2041.60]
  Mask Area: 390 pixels
  Mask Ratio: 0.0138
  Mask BBox: [140, 147, 164, 163]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7656
  Bounding Box: [1905.60, 16.20, 2033.60, 228.20]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [153, 6, 162, 21]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7622
  Bounding Box: [712.40, 1159.20, 785.60, 1253.60]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [60, 95, 65, 101]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7612
  Bounding Box: [183.00, 1309.60, 549.60, 1651.20]
  Mask Area: 561 pixels
  Mask Ratio: 0.0199
  Mask BBox: [19, 107, 46, 132]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7612
  Bounding Box: [418.00, 1731.20, 593.20, 2041.60]
  Mask Area: 205 pixels
  Mask Ratio: 0.0073
  Mask BBox: [37, 140, 48, 163]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7603
  Bounding Box: [1392.00, 1896.00, 1502.40, 2043.20]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [113, 153, 120, 162]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7603
  Bounding Box: [15.40, 17.20, 235.60, 352.80]
  Mask Area: 298 pixels
  Mask Ratio: 0.0106
  Mask BBox: [6, 6, 22, 31]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7588
  Bounding Box: [624.00, 849.60, 736.80, 1038.40]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [53, 71, 61, 84]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7583
  Bounding Box: [556.00, 1864.00, 846.40, 2048.00]
  Mask Area: 232 pixels
  Mask Ratio: 0.0082
  Mask BBox: [48, 150, 70, 164]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7578
  Bounding Box: [912.80, 604.00, 1090.40, 882.40]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [77, 52, 89, 71]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7495
  Bounding Box: [536.00, 1042.40, 675.20, 1215.20]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [46, 86, 56, 97]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7441
  Bounding Box: [851.20, 721.20, 964.80, 851.20]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [71, 61, 77, 70]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7437
  Bounding Box: [1276.80, 760.00, 1422.40, 1028.80]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [104, 64, 114, 84]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7427
  Bounding Box: [521.20, 1220.80, 630.80, 1356.80]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [45, 100, 53, 109]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7407
  Bounding Box: [109.20, 1812.80, 256.40, 2004.80]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [13, 146, 24, 160]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7383
  Bounding Box: [1118.40, 1748.80, 1203.20, 1873.60]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [92, 141, 97, 150]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7373
  Bounding Box: [992.00, 357.00, 1166.40, 608.00]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [82, 32, 95, 50]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7363
  Bounding Box: [12.00, 585.60, 240.00, 900.80]
  Mask Area: 340 pixels
  Mask Ratio: 0.0120
  Mask BBox: [5, 50, 22, 74]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7358
  Bounding Box: [1167.20, 19.10, 1356.00, 317.20]
  Mask Area: 250 pixels
  Mask Ratio: 0.0089
  Mask BBox: [96, 6, 109, 28]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7354
  Bounding Box: [212.00, 73.30, 351.20, 277.60]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [21, 12, 31, 25]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7339
  Bounding Box: [1530.40, 1811.20, 1731.20, 2009.60]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [124, 146, 139, 158]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7329
  Bounding Box: [1937.60, 459.20, 2040.00, 592.00]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [156, 41, 163, 50]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7305
  Bounding Box: [1667.20, 51.70, 1907.20, 293.20]
  Mask Area: 242 pixels
  Mask Ratio: 0.0086
  Mask BBox: [135, 9, 152, 26]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.7271
  Bounding Box: [1444.00, 396.80, 1604.80, 737.60]
  Mask Area: 205 pixels
  Mask Ratio: 0.0073
  Mask BBox: [117, 35, 129, 61]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.7212
  Bounding Box: [812.00, 1426.40, 917.60, 1554.40]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [68, 116, 75, 124]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.7139
  Bounding Box: [891.20, 0.00, 1204.80, 279.00]
  Mask Area: 412 pixels
  Mask Ratio: 0.0146
  Mask BBox: [74, 3, 98, 25]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.7124
  Bounding Box: [1196.80, 1849.60, 1280.00, 1987.20]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [98, 149, 103, 159]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.7056
  Bounding Box: [11.00, 421.60, 255.20, 671.20]
  Mask Area: 268 pixels
  Mask Ratio: 0.0095
  Mask BBox: [5, 37, 23, 56]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.7041
  Bounding Box: [1524.80, 221.60, 1793.60, 498.40]
  Mask Area: 262 pixels
  Mask Ratio: 0.0093
  Mask BBox: [124, 22, 144, 42]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.7031
  Bounding Box: [1084.00, 480.00, 1200.80, 705.60]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [89, 42, 96, 59]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6982
  Bounding Box: [1.40, 1798.40, 103.20, 1932.80]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [5, 145, 11, 154]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6973
  Bounding Box: [804.00, 1114.40, 941.60, 1373.60]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [67, 92, 77, 111]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6929
  Bounding Box: [666.40, 1681.60, 821.60, 1892.80]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [57, 136, 66, 151]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6870
  Bounding Box: [1809.60, 548.40, 1979.20, 686.80]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [146, 47, 158, 55]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6865
  Bounding Box: [1573.60, 530.00, 1702.40, 824.00]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [127, 46, 136, 67]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6777
  Bounding Box: [15.00, 1245.60, 224.80, 1524.00]
  Mask Area: 251 pixels
  Mask Ratio: 0.0089
  Mask BBox: [6, 102, 21, 123]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6768
  Bounding Box: [1562.40, 1660.80, 1696.00, 1766.40]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [127, 134, 135, 141]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6768
  Bounding Box: [1596.80, 1670.40, 1814.40, 1913.60]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [129, 135, 145, 153]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6738
  Bounding Box: [1814.40, 324.00, 1990.40, 550.40]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [146, 30, 159, 44]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6733
  Bounding Box: [0.00, 1464.00, 83.40, 1571.20]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [4, 119, 10, 126]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6675
  Bounding Box: [387.20, 1123.20, 460.00, 1224.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [35, 92, 39, 99]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6665
  Bounding Box: [903.20, 875.20, 1164.00, 1233.60]
  Mask Area: 411 pixels
  Mask Ratio: 0.0146
  Mask BBox: [75, 73, 94, 100]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6587
  Bounding Box: [66.60, 308.40, 157.80, 441.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [10, 29, 15, 36]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6558
  Bounding Box: [1180.00, 0.00, 1256.80, 74.40]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [97, 4, 101, 9]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6528
  Bounding Box: [591.60, 339.00, 802.40, 445.20]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [51, 31, 65, 38]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6489
  Bounding Box: [1276.00, 1944.00, 1416.80, 2036.80]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [104, 156, 114, 163]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6484
  Bounding Box: [61.20, 1609.60, 151.20, 1776.00]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [9, 130, 15, 141]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6367
  Bounding Box: [1694.40, 1492.80, 1867.20, 1707.20]
  Mask Area: 203 pixels
  Mask Ratio: 0.0072
  Mask BBox: [137, 121, 149, 137]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6318
  Bounding Box: [1263.20, 440.00, 1453.60, 724.00]
  Mask Area: 233 pixels
  Mask Ratio: 0.0083
  Mask BBox: [103, 39, 117, 60]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6245
  Bounding Box: [1370.40, 301.80, 1519.20, 537.60]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [112, 28, 122, 45]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6240
  Bounding Box: [29.40, 1907.20, 184.40, 2038.40]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [7, 153, 18, 163]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6216
  Bounding Box: [1798.40, 1203.20, 1875.20, 1286.40]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [145, 98, 150, 103]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6196
  Bounding Box: [618.80, 412.80, 777.60, 638.40]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [53, 37, 64, 53]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6182
  Bounding Box: [1771.20, 1192.00, 1883.20, 1291.20]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [143, 98, 151, 103]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6133
  Bounding Box: [1531.20, 1436.80, 1694.40, 1654.40]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [124, 117, 136, 133]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6118
  Bounding Box: [904.80, 1421.60, 1021.60, 1572.00]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [75, 116, 83, 126]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6074
  Bounding Box: [1447.20, 1000.00, 1875.20, 1358.40]
  Mask Area: 723 pixels
  Mask Ratio: 0.0256
  Mask BBox: [118, 83, 150, 110]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6069
  Bounding Box: [628.40, 1525.60, 749.20, 1684.80]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [54, 124, 62, 135]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6016
  Bounding Box: [1835.20, 1.30, 1944.00, 121.10]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [148, 5, 155, 13]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.5991
  Bounding Box: [1081.60, 701.20, 1297.60, 984.00]
  Mask Area: 273 pixels
  Mask Ratio: 0.0097
  Mask BBox: [89, 59, 105, 80]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.5952
  Bounding Box: [7.20, 305.00, 68.00, 428.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [5, 28, 8, 37]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.5952
  Bounding Box: [1473.60, 1296.80, 1614.40, 1421.60]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [120, 106, 130, 115]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.5942
  Bounding Box: [1452.80, 0.00, 1734.40, 205.20]
  Mask Area: 239 pixels
  Mask Ratio: 0.0085
  Mask BBox: [118, 4, 139, 20]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.5854
  Bounding Box: [1976.00, 564.00, 2046.40, 692.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [159, 49, 163, 58]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.5840
  Bounding Box: [1228.80, 1184.80, 1358.40, 1421.60]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [100, 97, 110, 115]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5830
  Bounding Box: [1926.40, 1028.80, 2044.80, 1139.20]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [155, 85, 163, 92]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5757
  Bounding Box: [1848.00, 1633.60, 1953.60, 1764.80]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [149, 132, 156, 140]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5718
  Bounding Box: [855.20, 1976.00, 1015.20, 2036.80]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [71, 159, 83, 163]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5703
  Bounding Box: [769.60, 517.60, 960.00, 639.20]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [65, 45, 78, 53]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5688
  Bounding Box: [159.20, 1.70, 341.20, 102.50]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [17, 5, 30, 10]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5620
  Bounding Box: [166.40, 316.00, 328.40, 472.80]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [17, 29, 28, 39]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5615
  Bounding Box: [1771.20, 1710.40, 1860.80, 1777.60]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [143, 138, 148, 142]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5615
  Bounding Box: [1865.60, 1141.60, 1968.00, 1285.60]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [150, 94, 156, 102]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5591
  Bounding Box: [1380.80, 381.60, 1512.00, 552.80]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [112, 34, 121, 46]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5591
  Bounding Box: [992.00, 1280.80, 1163.20, 1485.60]
  Mask Area: 157 pixels
  Mask Ratio: 0.0056
  Mask BBox: [82, 105, 94, 119]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5576
  Bounding Box: [1117.60, 747.20, 1317.60, 1051.20]
  Mask Area: 268 pixels
  Mask Ratio: 0.0095
  Mask BBox: [92, 63, 106, 86]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5566
  Bounding Box: [776.80, 210.20, 855.20, 333.80]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [65, 21, 70, 30]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5566
  Bounding Box: [218.00, 751.20, 377.20, 965.60]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [22, 63, 32, 78]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5566
  Bounding Box: [542.40, 1381.60, 712.80, 1517.60]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [47, 112, 58, 122]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5537
  Bounding Box: [0.00, 1560.00, 84.50, 1800.00]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [4, 126, 10, 144]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5532
  Bounding Box: [0.00, 580.80, 82.60, 676.80]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [4, 50, 10, 56]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5522
  Bounding Box: [802.40, 1053.60, 978.40, 1399.20]
  Mask Area: 254 pixels
  Mask Ratio: 0.0090
  Mask BBox: [67, 87, 80, 113]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5498
  Bounding Box: [1336.80, 1753.60, 1442.40, 1868.80]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [109, 141, 116, 149]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5474
  Bounding Box: [1029.60, 609.60, 1165.60, 767.20]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [85, 52, 95, 63]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5396
  Bounding Box: [1328.80, 1000.80, 1421.60, 1296.80]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [108, 83, 115, 105]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5317
  Bounding Box: [1063.20, 279.20, 1221.60, 441.60]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [88, 26, 99, 36]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5259
  Bounding Box: [725.60, 780.80, 884.00, 1072.00]
  Mask Area: 230 pixels
  Mask Ratio: 0.0081
  Mask BBox: [61, 65, 73, 87]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5254
  Bounding Box: [1659.20, 1330.40, 1729.60, 1408.80]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [134, 108, 138, 113]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5234
  Bounding Box: [1665.60, 1960.00, 1748.80, 2048.00]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [135, 158, 140, 163]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5229
  Bounding Box: [1870.40, 1134.40, 2001.60, 1312.00]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [151, 94, 160, 106]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5225
  Bounding Box: [1039.20, 1971.20, 1140.00, 2035.20]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [86, 158, 91, 162]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5210
  Bounding Box: [1340.00, 80.90, 1396.00, 169.60]
  Mask Area: 21 pixels
  Mask Ratio: 0.0007
  Mask BBox: [109, 11, 112, 16]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.5195
  Bounding Box: [1029.60, 1153.60, 1125.60, 1296.00]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [85, 95, 91, 104]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.5181
  Bounding Box: [675.20, 1312.00, 844.80, 1510.40]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [57, 107, 69, 121]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.5176
  Bounding Box: [1.20, 881.60, 123.20, 987.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [5, 73, 13, 79]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.5171
  Bounding Box: [6.00, 192.60, 93.10, 329.80]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [5, 20, 11, 29]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.5171
  Bounding Box: [411.60, 420.80, 616.40, 543.20]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [37, 37, 52, 46]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.5103
  Bounding Box: [1.90, 1426.40, 65.70, 1549.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [5, 116, 9, 125]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.5098
  Bounding Box: [1120.00, 1102.40, 1184.00, 1308.80]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [92, 91, 96, 106]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.5083
  Bounding Box: [793.60, 657.60, 942.40, 772.80]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [66, 56, 77, 64]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.5049
  Bounding Box: [1904.00, 53.80, 2048.00, 342.20]
  Mask Area: 239 pixels
  Mask Ratio: 0.0085
  Mask BBox: [153, 9, 164, 30]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.5029
  Bounding Box: [1646.40, 1318.40, 1716.80, 1412.80]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [133, 107, 138, 114]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.5010
  Bounding Box: [1892.80, 1142.40, 2048.00, 1387.20]
  Mask Area: 206 pixels
  Mask Ratio: 0.0073
  Mask BBox: [152, 94, 164, 112]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4980
  Bounding Box: [21.50, 63.90, 345.60, 313.20]
  Mask Area: 393 pixels
  Mask Ratio: 0.0139
  Mask BBox: [6, 9, 30, 28]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4980
  Bounding Box: [536.40, 1636.80, 674.80, 1828.80]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [46, 132, 56, 146]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4946
  Bounding Box: [830.40, 694.40, 956.80, 834.40]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [69, 59, 77, 69]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4902
  Bounding Box: [788.80, 12.80, 932.80, 218.80]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [66, 5, 76, 21]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4854
  Bounding Box: [1918.40, 1525.60, 2014.40, 1704.00]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [154, 124, 161, 137]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4834
  Bounding Box: [1731.20, 456.40, 1872.00, 580.40]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [140, 40, 150, 49]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4824
  Bounding Box: [1376.00, 0.00, 1528.00, 88.80]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [112, 4, 123, 9]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4814
  Bounding Box: [1499.20, 1896.00, 1697.60, 2040.00]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [122, 153, 136, 163]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4790
  Bounding Box: [745.20, 767.20, 878.40, 927.20]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [63, 64, 72, 76]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4761
  Bounding Box: [677.60, 656.80, 792.00, 831.20]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [57, 56, 65, 68]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4761
  Bounding Box: [458.40, 1128.00, 576.00, 1256.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [40, 93, 48, 102]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4746
  Bounding Box: [1138.40, 275.20, 1271.20, 440.00]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [93, 26, 103, 38]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4736
  Bounding Box: [330.60, 0.00, 665.60, 400.40]
  Mask Area: 696 pixels
  Mask Ratio: 0.0247
  Mask BBox: [30, 3, 55, 35]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4727
  Bounding Box: [1191.20, 506.40, 1436.00, 737.60]
  Mask Area: 295 pixels
  Mask Ratio: 0.0105
  Mask BBox: [98, 44, 116, 61]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4609
  Bounding Box: [1697.60, 256.80, 1832.00, 340.40]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [137, 25, 147, 30]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4604
  Bounding Box: [1328.80, 83.80, 1416.80, 158.60]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [108, 11, 114, 16]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4556
  Bounding Box: [8.95, 1587.20, 127.00, 1782.40]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [5, 128, 13, 143]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4531
  Bounding Box: [1255.20, 333.20, 1420.00, 468.40]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [103, 31, 114, 40]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4490
  Bounding Box: [1047.20, 1551.20, 1146.40, 1672.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [86, 126, 93, 134]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4490
  Bounding Box: [536.00, 870.40, 614.40, 1032.00]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [46, 72, 51, 84]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4475
  Bounding Box: [1945.60, 679.60, 2048.00, 871.20]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [156, 58, 163, 71]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4460
  Bounding Box: [252.60, 1206.40, 408.00, 1331.20]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [24, 99, 35, 107]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4456
  Bounding Box: [41.05, 326.80, 150.00, 429.20]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [8, 30, 15, 37]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4436
  Bounding Box: [1894.40, 1747.20, 2009.60, 1824.00]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [152, 141, 160, 145]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4351
  Bounding Box: [1504.00, 1960.00, 1686.40, 2046.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [122, 158, 135, 163]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4346
  Bounding Box: [381.60, 962.40, 463.20, 1047.20]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [34, 80, 40, 85]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4346
  Bounding Box: [220.00, 1966.40, 352.00, 2036.80]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [22, 158, 31, 163]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4243
  Bounding Box: [313.60, 7.20, 568.00, 318.80]
  Mask Area: 407 pixels
  Mask Ratio: 0.0144
  Mask BBox: [29, 5, 48, 28]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4238
  Bounding Box: [14.95, 878.40, 107.00, 966.40]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [6, 73, 12, 79]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4238
  Bounding Box: [448.80, 50.60, 763.20, 418.40]
  Mask Area: 484 pixels
  Mask Ratio: 0.0171
  Mask BBox: [40, 8, 63, 36]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4216
  Bounding Box: [1160.00, 993.60, 1268.80, 1225.60]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [95, 82, 103, 99]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.4216
  Bounding Box: [1896.00, 1339.20, 2027.20, 1524.80]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [153, 109, 162, 123]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.4192
  Bounding Box: [1252.00, 1784.00, 1381.60, 1921.60]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [102, 144, 111, 154]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.4187
  Bounding Box: [1584.80, 912.80, 1649.60, 1010.40]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [128, 76, 132, 82]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.4172
  Bounding Box: [1562.40, 1386.40, 1670.40, 1471.20]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [127, 113, 134, 118]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.4136
  Bounding Box: [1178.40, 1070.40, 1261.60, 1219.20]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [97, 88, 102, 97]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.4136
  Bounding Box: [1883.20, 1352.00, 2001.60, 1473.60]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [152, 110, 160, 119]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.4121
  Bounding Box: [1924.80, 1368.80, 2043.20, 1556.00]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [155, 111, 163, 125]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.4102
  Bounding Box: [1105.60, 454.40, 1219.20, 688.00]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [92, 40, 99, 57]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.4065
  Bounding Box: [1.65, 545.60, 81.60, 666.40]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [5, 47, 10, 56]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.4065
  Bounding Box: [25.30, 1516.00, 133.70, 1606.40]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [6, 123, 14, 128]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.4041
  Bounding Box: [608.00, 1169.60, 696.80, 1286.40]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [52, 96, 58, 104]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.4026
  Bounding Box: [858.40, 1420.80, 1007.20, 1588.80]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [72, 115, 82, 127]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.4026
  Bounding Box: [53.60, 1833.60, 243.60, 2028.80]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [9, 148, 23, 162]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.4011
  Bounding Box: [332.40, 952.00, 419.60, 1033.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [30, 79, 36, 84]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3999
  Bounding Box: [1697.60, 535.20, 1777.60, 656.80]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [137, 46, 142, 55]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3979
  Bounding Box: [739.20, 33.40, 830.40, 215.00]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [62, 7, 68, 20]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3970
  Bounding Box: [1290.40, 425.20, 1520.80, 749.20]
  Mask Area: 303 pixels
  Mask Ratio: 0.0107
  Mask BBox: [105, 38, 122, 62]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3967
  Bounding Box: [679.20, 1627.20, 988.00, 1905.60]
  Mask Area: 403 pixels
  Mask Ratio: 0.0143
  Mask BBox: [58, 132, 81, 152]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3965
  Bounding Box: [1091.20, 700.00, 1267.20, 909.60]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [90, 59, 102, 75]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3928
  Bounding Box: [1550.40, 1662.40, 1732.80, 1812.80]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [126, 134, 139, 145]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3909
  Bounding Box: [1752.00, 322.40, 1844.80, 458.40]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [141, 30, 148, 39]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3909
  Bounding Box: [1982.40, 1435.20, 2046.40, 1700.80]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [159, 117, 163, 136]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3909
  Bounding Box: [401.20, 385.20, 581.20, 554.00]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [36, 35, 49, 47]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3892
  Bounding Box: [731.20, 862.40, 865.60, 1100.80]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [62, 72, 71, 89]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3850
  Bounding Box: [1364.80, 355.20, 1528.00, 604.80]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [111, 32, 123, 51]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3845
  Bounding Box: [375.20, 1678.40, 484.00, 1777.60]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [34, 136, 41, 142]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3845
  Bounding Box: [1894.40, 1519.20, 1996.80, 1720.00]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [152, 123, 159, 138]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3835
  Bounding Box: [618.00, 1334.40, 848.00, 1537.60]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [53, 109, 70, 124]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3821
  Bounding Box: [1170.40, 0.00, 1295.20, 84.50]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [96, 4, 105, 9]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3816
  Bounding Box: [1171.20, 58.40, 1457.60, 314.00]
  Mask Area: 343 pixels
  Mask Ratio: 0.0122
  Mask BBox: [96, 9, 117, 28]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3799
  Bounding Box: [572.40, 1449.60, 714.80, 1529.60]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [49, 118, 59, 123]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3794
  Bounding Box: [1570.40, 154.00, 1684.80, 253.60]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [127, 17, 135, 23]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3782
  Bounding Box: [519.20, 1064.00, 662.40, 1323.20]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [45, 88, 55, 107]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3779
  Bounding Box: [944.00, 1488.00, 1059.20, 1595.20]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [78, 121, 86, 128]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3738
  Bounding Box: [278.00, 1649.60, 379.20, 1768.00]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [26, 133, 33, 140]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3738
  Bounding Box: [652.00, 1536.00, 779.20, 1696.00]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [55, 124, 64, 136]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3735
  Bounding Box: [1534.40, 1489.60, 1684.80, 1764.80]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [124, 121, 135, 141]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3721
  Bounding Box: [1328.00, 1088.00, 1409.60, 1316.80]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [108, 89, 114, 106]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3721
  Bounding Box: [581.20, 908.00, 689.20, 1068.00]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [50, 75, 57, 87]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3706
  Bounding Box: [672.00, 1248.80, 817.60, 1431.20]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [57, 102, 66, 115]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3704
  Bounding Box: [1865.60, 1644.80, 1980.80, 1788.80]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [150, 133, 158, 143]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3701
  Bounding Box: [1492.80, 1931.20, 1656.00, 2043.20]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [122, 155, 133, 163]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3684
  Bounding Box: [1913.60, 177.40, 2048.00, 370.40]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [154, 18, 164, 32]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3684
  Bounding Box: [1724.80, 445.20, 1859.20, 546.00]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [139, 39, 148, 46]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3679
  Bounding Box: [1061.60, 1681.60, 1146.40, 1809.60]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [87, 136, 93, 145]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3669
  Bounding Box: [1758.40, 375.60, 1822.40, 466.00]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [142, 34, 146, 40]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3647
  Bounding Box: [801.60, 0.00, 1008.00, 229.40]
  Mask Area: 253 pixels
  Mask Ratio: 0.0090
  Mask BBox: [67, 4, 82, 21]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3621
  Bounding Box: [515.60, 874.40, 627.60, 1004.00]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [45, 73, 53, 82]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3611
  Bounding Box: [1356.80, 1702.40, 1478.40, 1884.80]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [110, 137, 119, 151]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3606
  Bounding Box: [318.60, 948.00, 394.40, 1039.20]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [29, 79, 34, 85]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3606
  Bounding Box: [1019.20, 289.60, 1198.40, 528.00]
  Mask Area: 206 pixels
  Mask Ratio: 0.0073
  Mask BBox: [84, 27, 97, 45]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3579
  Bounding Box: [1147.20, 1204.00, 1275.20, 1604.80]
  Mask Area: 227 pixels
  Mask Ratio: 0.0080
  Mask BBox: [94, 99, 103, 129]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3579
  Bounding Box: [1844.80, 352.60, 2020.80, 603.20]
  Mask Area: 182 pixels
  Mask Ratio: 0.0064
  Mask BBox: [149, 32, 161, 51]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3579
  Bounding Box: [683.20, 1219.20, 868.80, 1395.20]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [58, 100, 71, 112]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3577
  Bounding Box: [201.20, 306.60, 466.80, 539.20]
  Mask Area: 247 pixels
  Mask Ratio: 0.0088
  Mask BBox: [20, 28, 40, 46]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3562
  Bounding Box: [1412.80, 1878.40, 1515.20, 2025.60]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [115, 151, 122, 162]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [1987.20, 1680.00, 2041.60, 1769.60]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [160, 136, 163, 142]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3555
  Bounding Box: [1801.60, 1171.20, 1952.00, 1297.60]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [145, 96, 156, 103]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3552
  Bounding Box: [832.80, 949.60, 1036.00, 1352.80]
  Mask Area: 421 pixels
  Mask Ratio: 0.0149
  Mask BBox: [70, 79, 84, 109]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3550
  Bounding Box: [882.40, 1947.20, 1020.00, 2036.80]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [73, 157, 83, 163]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3547
  Bounding Box: [172.80, 1649.60, 341.20, 1812.80]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [18, 133, 30, 145]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3540
  Bounding Box: [1222.40, 1438.40, 1297.60, 1564.80]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [100, 117, 105, 126]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3535
  Bounding Box: [1020.80, 1662.40, 1084.80, 1764.80]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [84, 134, 88, 141]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3533
  Bounding Box: [662.40, 1200.80, 803.20, 1364.00]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [56, 98, 66, 109]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3508
  Bounding Box: [1992.00, 1512.80, 2043.20, 1699.20]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [160, 123, 163, 136]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3501
  Bounding Box: [1907.20, 1028.80, 2038.40, 1187.20]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [153, 85, 163, 96]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3486
  Bounding Box: [1464.80, 283.40, 1536.80, 366.40]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [119, 27, 123, 32]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3481
  Bounding Box: [1240.80, 312.80, 1359.20, 436.00]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [101, 29, 110, 38]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3477
  Bounding Box: [1164.80, 1210.40, 1254.40, 1527.20]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [95, 99, 101, 123]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3474
  Bounding Box: [643.20, 1176.00, 728.00, 1283.20]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [55, 96, 60, 104]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3474
  Bounding Box: [283.40, 267.80, 376.00, 337.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [27, 25, 33, 30]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3469
  Bounding Box: [1979.20, 386.40, 2043.20, 519.20]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [159, 35, 163, 44]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3455
  Bounding Box: [1980.80, 664.00, 2041.60, 821.60]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [159, 56, 163, 68]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3420
  Bounding Box: [1268.00, 319.80, 1388.00, 436.00]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [104, 29, 112, 38]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3398
  Bounding Box: [1897.60, 82.20, 2048.00, 215.40]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [153, 11, 164, 20]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3398
  Bounding Box: [1198.40, 438.40, 1275.20, 489.60]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [98, 39, 103, 42]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3394
  Bounding Box: [1536.80, 1651.20, 1672.00, 1756.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [126, 134, 134, 141]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.3389
  Bounding Box: [1340.00, 73.70, 1627.20, 305.60]
  Mask Area: 312 pixels
  Mask Ratio: 0.0111
  Mask BBox: [109, 10, 131, 27]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.3376
  Bounding Box: [1918.40, 647.20, 2036.80, 852.00]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [154, 55, 163, 70]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.3364
  Bounding Box: [1093.60, 1766.40, 1194.40, 1888.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [90, 142, 97, 151]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.3359
  Bounding Box: [388.40, 1031.20, 492.40, 1146.40]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [35, 85, 42, 93]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.3337
  Bounding Box: [1366.40, 5.45, 1536.00, 144.20]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [111, 5, 123, 15]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.3325
  Bounding Box: [619.20, 24.15, 757.60, 129.80]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [53, 6, 63, 14]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.3311
  Bounding Box: [706.40, 143.10, 777.60, 226.60]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [60, 16, 64, 21]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [541.20, 1241.60, 658.80, 1382.40]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [47, 101, 55, 109]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.3293
  Bounding Box: [832.00, 737.60, 944.00, 875.20]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [69, 62, 77, 72]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.3286
  Bounding Box: [690.80, 1144.00, 780.00, 1246.40]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [58, 94, 64, 101]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.3286
  Bounding Box: [690.80, 1169.60, 780.00, 1272.00]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [58, 96, 64, 103]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.3286
  Bounding Box: [716.40, 1169.60, 805.60, 1272.00]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [60, 96, 66, 103]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [1504.00, 1326.40, 1624.00, 1440.00]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [122, 108, 130, 116]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.3259
  Bounding Box: [1562.40, 928.00, 1636.80, 1011.20]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [127, 77, 131, 82]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.3235
  Bounding Box: [935.20, 1213.60, 1010.40, 1404.00]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [78, 99, 82, 113]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.3235
  Bounding Box: [278.20, 1192.00, 444.40, 1313.60]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [26, 98, 38, 106]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [1904.00, 1060.80, 2022.40, 1168.00]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [153, 87, 161, 95]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [1309.60, 70.60, 1381.60, 165.60]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [108, 10, 111, 16]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [1309.60, 96.20, 1381.60, 191.20]
  Mask Area: 18 pixels
  Mask Ratio: 0.0006
  Mask BBox: [108, 12, 111, 16]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [1335.20, 96.20, 1407.20, 191.20]
  Mask Area: 17 pixels
  Mask Ratio: 0.0006
  Mask BBox: [109, 12, 112, 16]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [1236.00, 1.85, 1372.00, 104.20]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [101, 5, 111, 12]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [1961.60, 407.20, 2048.00, 570.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [158, 36, 163, 48]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.3218
  Bounding Box: [257.60, 1200.00, 397.20, 1291.20]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [25, 98, 35, 104]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.3218
  Bounding Box: [609.60, 52.00, 752.80, 165.60]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [52, 9, 62, 16]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.3213
  Bounding Box: [8.80, 342.40, 75.70, 472.00]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [5, 31, 7, 39]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.3208
  Bounding Box: [622.40, 8.20, 740.00, 75.80]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [53, 5, 61, 9]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.3203
  Bounding Box: [571.20, 324.00, 791.20, 417.60]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [50, 30, 65, 36]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.3201
  Bounding Box: [680.40, 675.20, 823.20, 868.80]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [58, 57, 68, 71]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.3176
  Bounding Box: [1574.40, 230.80, 1836.80, 434.00]
  Mask Area: 212 pixels
  Mask Ratio: 0.0075
  Mask BBox: [127, 23, 147, 37]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.3174
  Bounding Box: [617.60, 841.60, 797.60, 1044.80]
  Mask Area: 165 pixels
  Mask Ratio: 0.0058
  Mask BBox: [53, 70, 66, 84]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.3169
  Bounding Box: [447.60, 1284.80, 572.40, 1396.80]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [39, 105, 48, 112]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.3167
  Bounding Box: [682.00, 1163.20, 801.60, 1292.80]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [58, 95, 66, 104]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.3154
  Bounding Box: [1971.20, 1384.80, 2041.60, 1651.20]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [158, 113, 163, 132]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.3127
  Bounding Box: [437.20, 969.60, 552.40, 1123.20]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [39, 80, 47, 91]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.3120
  Bounding Box: [538.40, 1368.00, 687.20, 1476.80]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [47, 111, 57, 119]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.3108
  Bounding Box: [716.00, 1140.80, 805.60, 1243.20]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [60, 94, 66, 101]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.3103
  Bounding Box: [1736.00, 9.30, 1937.60, 232.80]
  Mask Area: 199 pixels
  Mask Ratio: 0.0071
  Mask BBox: [140, 5, 155, 22]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [1129.60, 288.80, 1347.20, 450.40]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [93, 27, 109, 39]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.3071
  Bounding Box: [1995.20, 1715.20, 2048.00, 1814.40]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [160, 138, 163, 145]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [618.40, 1472.00, 771.20, 1686.40]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [53, 119, 64, 135]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.3057
  Bounding Box: [1878.40, 1126.40, 1984.00, 1257.60]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [151, 94, 156, 102]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.3027
  Bounding Box: [652.80, 395.60, 812.80, 634.80]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [55, 35, 67, 53]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.3018
  Bounding Box: [455.20, 1243.20, 619.20, 1393.60]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [40, 102, 52, 112]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.3010
  Bounding Box: [438.00, 944.00, 548.40, 1062.40]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [39, 78, 46, 86]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.3003
  Bounding Box: [1273.60, 1915.20, 1412.80, 2017.60]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [104, 154, 114, 161]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2991
  Bounding Box: [1165.60, 834.40, 1311.20, 1066.40]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [96, 70, 106, 87]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2988
  Bounding Box: [1600.00, 1443.20, 1705.60, 1572.80]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [129, 117, 137, 126]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2981
  Bounding Box: [254.80, 1976.00, 369.20, 2040.00]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [24, 159, 31, 163]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2969
  Bounding Box: [1478.40, 1310.40, 1689.60, 1441.60]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [120, 107, 135, 116]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2966
  Bounding Box: [836.80, 1955.20, 972.80, 2032.00]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [70, 157, 79, 162]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2964
  Bounding Box: [993.60, 1108.00, 1145.60, 1324.00]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [82, 91, 93, 107]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2937
  Bounding Box: [591.20, 254.40, 795.20, 446.40]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [51, 24, 66, 38]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2932
  Bounding Box: [1640.00, 1511.20, 1707.20, 1619.20]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [133, 123, 136, 130]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2915
  Bounding Box: [884.00, 855.20, 941.60, 927.20]
  Mask Area: 21 pixels
  Mask Ratio: 0.0007
  Mask BBox: [74, 71, 77, 76]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2874
  Bounding Box: [1910.40, 1369.60, 2025.60, 1686.40]
  Mask Area: 186 pixels
  Mask Ratio: 0.0066
  Mask BBox: [154, 111, 162, 135]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2861
  Bounding Box: [108.00, 953.60, 180.00, 1108.80]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [13, 79, 17, 90]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2854
  Bounding Box: [0.00, 504.00, 268.40, 926.40]
  Mask Area: 561 pixels
  Mask Ratio: 0.0199
  Mask BBox: [2, 44, 24, 76]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2849
  Bounding Box: [1678.40, 1225.60, 1844.80, 1465.60]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [136, 103, 148, 118]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2847
  Bounding Box: [466.80, 980.00, 553.20, 1103.20]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [41, 81, 47, 90]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2842
  Bounding Box: [1157.60, 871.20, 1300.00, 1152.80]
  Mask Area: 203 pixels
  Mask Ratio: 0.0072
  Mask BBox: [95, 73, 105, 94]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2842
  Bounding Box: [1244.00, 1108.80, 1391.20, 1419.20]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [102, 91, 112, 114]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2830
  Bounding Box: [1756.80, 1214.40, 1862.40, 1313.60]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [142, 99, 149, 103]

