# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license
"""数据加载器模块 - 用于YOLOv5分割模型的数据加载和预处理。"""

import contextlib
import glob
import math
import os
import random
from pathlib import Path
import hashlib
import json
import time
from itertools import repeat
from multiprocessing.pool import Pool, ThreadPool
from threading import Thread
from zipfile import ZipFile

import cv2
import numpy as np
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset, dataloader, distributed
from PIL import ExifTags, Image, ImageOps  # 图像处理
from tqdm import tqdm
# 导入数据增强相关模块
IMG_FORMATS = "bmp", "dng", "jpeg", "jpg", "mpo", "png", "tif", "tiff", "webp", "pfm"  # 支持的图像格式
# 已迁移的类和函数现在在本文件中定义：InfiniteDataLoader, LoadImagesAndLabels, SmartDistributedSampler, seed_worker
from .general import (
    DATASETS_DIR,
    LOGGER,
    NUM_THREADS,
    TQDM_BAR_FORMAT,
    check_dataset,
    check_requirements,
    check_yaml,
    clean_str,
    cv2,
    is_colab,
    is_kaggle,
    segments2boxes,
    unzip_file,
    xyn2xy,
    xywh2xyxy,
    xywhn2xyxy,
    xyxy2xywhn,
)
from ..torch_utils import torch_distributed_zero_first
from .augmentations import Albumentations, mixup, random_perspective, augment_hsv, copy_paste, letterbox

# 获取分布式训练的进程排名和世界大小
RANK = int(os.getenv("RANK", -1))
WORLD_SIZE = int(os.getenv("WORLD_SIZE", 1))
LOCAL_RANK = int(os.getenv("LOCAL_RANK", -1))  # 本地进程排名（分布式训练）

# 帮助URL
HELP_URL = "See https://docs.ultralytics.com/yolov5/tutorials/train_custom_data"

# 获取方向标签
for orientation in ExifTags.TAGS.keys():
    if ExifTags.TAGS[orientation] == "Orientation":
        break


def get_hash(paths):
    """为文件或目录路径列表生成单个SHA256哈希值。
    
    通过组合文件大小和路径来生成唯一的哈希标识，用于缓存验证。
    
    Args:
        paths: 文件或目录路径列表
        
    Returns:
        str: SHA256哈希值的十六进制字符串
    """
    size = sum(os.path.getsize(p) for p in paths if os.path.exists(p))  # 计算所有文件的总大小
    h = hashlib.sha256(str(size).encode())  # 对大小进行哈希
    h.update("".join(paths).encode())  # 对路径进行哈希
    return h.hexdigest()  # 返回十六进制哈希值


def exif_size(img):
    """返回考虑EXIF方向信息的PIL图像尺寸。
    
    根据图像的EXIF方向标签调整图像尺寸，确保返回正确的宽高。
    
    Args:
        img: PIL图像对象
        
    Returns:
        tuple: 修正后的图像尺寸(width, height)
    """
    s = img.size  # 获取原始尺寸(width, height)
    with contextlib.suppress(Exception):
        rotation = dict(img._getexif().items())[orientation]
        if rotation in [6, 8]:  # 旋转270度或90度时需要交换宽高
            s = (s[1], s[0])
    return s

def exif_transpose(image):
    """根据EXIF方向标签转置PIL图像。
    
    这是PIL ImageOps.exif_transpose()的就地版本，用于根据图像的EXIF方向信息
    自动旋转图像到正确的方向。
    
    Args:
        image: 要转置的PIL图像对象
        
    Returns:
        Image: 转置后的图像对象
    """
    exif = image.getexif()  # 获取EXIF数据
    orientation = exif.get(0x0112, 1)  # 获取方向标签，默认为1（正常）
    if orientation > 1:
        # 根据EXIF方向标签选择相应的变换方法
        method = {
            2: Image.FLIP_LEFT_RIGHT,    # 水平翻转
            3: Image.ROTATE_180,         # 旋转180度
            4: Image.FLIP_TOP_BOTTOM,    # 垂直翻转
            5: Image.TRANSPOSE,          # 转置
            6: Image.ROTATE_270,         # 旋转270度
            7: Image.TRANSVERSE,         # 反转置
            8: Image.ROTATE_90,          # 旋转90度
        }.get(orientation)
        if method is not None:
            image = image.transpose(method)  # 应用变换
            del exif[0x0112]  # 删除方向标签
            image.info["exif"] = exif.tobytes()  # 更新EXIF信息
    return image

def seed_worker(worker_id):
    """为数据加载器工作进程设置随机种子以确保可重现性。"""
    worker_seed = torch.initial_seed() % 2**32
    np.random.seed(worker_seed)
    random.seed(worker_seed)


class SmartDistributedSampler(distributed.DistributedSampler):
    """智能分布式采样器，确保分布式训练中跨GPU的确定性洗牌和平衡数据分布。"""

    def __init__(self, dataset, num_replicas=None, rank=None, shuffle=True, seed=0):
        super().__init__(dataset, num_replicas, rank, shuffle, seed)

    def __iter__(self):
        if self.shuffle:
            g = torch.Generator()
            g.manual_seed(self.seed + self.epoch)
            indices = torch.randperm(len(self.dataset), generator=g).tolist()
        else:
            indices = list(range(len(self.dataset)))

        # 添加额外的样本以使其可被num_replicas整除
        indices += indices[: (self.total_size - len(indices))]
        assert len(indices) == self.total_size

        # 子集
        indices = indices[self.rank : self.total_size : self.num_replicas]
        assert len(indices) == self.num_samples

        return iter(indices)


class InfiniteDataLoader(dataloader.DataLoader):
    """无限数据加载器，通过重复使用工作进程实现无限循环地提供数据批次。"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        object.__setattr__(self, "batch_sampler", _RepeatSampler(self.batch_sampler))
        self.iterator = super().__iter__()

    def __len__(self):
        return len(self.batch_sampler.sampler)

    def __iter__(self):
        for _ in range(len(self)):
            yield next(self.iterator)


class _RepeatSampler:
    """采样器包装器，永久重复采样。"""

    def __init__(self, sampler):
        self.sampler = sampler

    def __iter__(self):
        while True:
            yield from iter(self.sampler)
            
def verify_image_label(args):
    """Verifies a single image-label pair, ensuring image format, size, and legal label values."""
    im_file, lb_file, prefix = args
    nm, nf, ne, nc, msg, segments = 0, 0, 0, 0, "", []  # number (missing, found, empty, corrupt), message, segments
    try:
        # verify images
        im = Image.open(im_file)
        im.verify()  # PIL verify
        shape = exif_size(im)  # image size
        assert (shape[0] > 9) & (shape[1] > 9), f"image size {shape} <10 pixels"
        assert im.format.lower() in IMG_FORMATS, f"invalid image format {im.format}"
        if im.format.lower() in ("jpg", "jpeg"):
            with open(im_file, "rb") as f:
                f.seek(-2, 2)
                if f.read() != b"\xff\xd9":  # corrupt JPEG
                    ImageOps.exif_transpose(Image.open(im_file)).save(im_file, "JPEG", subsampling=0, quality=100)
                    msg = f"{prefix}WARNING ⚠️ {im_file}: corrupt JPEG restored and saved"

        # verify labels
        if os.path.isfile(lb_file):
            nf = 1  # label found
            with open(lb_file) as f:
                lb = [x.split() for x in f.read().strip().splitlines() if len(x)]
                if any(len(x) > 6 for x in lb):  # is segment
                    classes = np.array([x[0] for x in lb], dtype=np.float32)
                    segments = [np.array(x[1:], dtype=np.float32).reshape(-1, 2) for x in lb]  # (cls, xy1...)
                    lb = np.concatenate((classes.reshape(-1, 1), segments2boxes(segments)), 1)  # (cls, xywh)
                lb = np.array(lb, dtype=np.float32)
            if nl := len(lb):
                assert lb.shape[1] == 5, f"labels require 5 columns, {lb.shape[1]} columns detected"
                assert (lb >= 0).all(), f"negative label values {lb[lb < 0]}"
                assert (lb[:, 1:] <= 1).all(), f"non-normalized or out of bounds coordinates {lb[:, 1:][lb[:, 1:] > 1]}"
                _, i = np.unique(lb, axis=0, return_index=True)
                if len(i) < nl:  # duplicate row check
                    lb = lb[i]  # remove duplicates
                    if segments:
                        segments = [segments[x] for x in i]
                    msg = f"{prefix}WARNING ⚠️ {im_file}: {nl - len(i)} duplicate labels removed"
            else:
                ne = 1  # label empty
                lb = np.zeros((0, 5), dtype=np.float32)
        else:
            nm = 1  # label missing
            lb = np.zeros((0, 5), dtype=np.float32)
        return im_file, lb, shape, segments, nm, nf, ne, nc, msg
    except Exception as e:
        nc = 1
        msg = f"{prefix}WARNING ⚠️ {im_file}: ignoring corrupt image/label: {e}"
        return [None, None, None, None, nm, nf, ne, nc, msg]

def img2label_paths(img_paths):
    """从对应的图像文件路径生成标签文件路径，通过将`/images/`替换为`/labels/`并将扩展名替换为`.txt`。
    
    Args:
        img_paths: 图像文件路径列表
        
    Returns:
        list: 对应的标签文件路径列表
    """
    sa, sb = f"{os.sep}images{os.sep}", f"{os.sep}labels{os.sep}"  # /images/, /labels/ 子字符串
    return [sb.join(x.rsplit(sa, 1)).rsplit(".", 1)[0] + ".txt" for x in img_paths]  # 路径转换



def polygon2mask(img_size, polygons, color=1, downsample_ratio=1):
    """
    将多边形转换为掩码。
    
    Args:
        img_size (tuple): 图像尺寸。
        polygons (np.ndarray): [N, M], N是多边形数量，
            M是点的数量(需要被2整除)。
        color (int): 填充颜色。
        downsample_ratio (int): 下采样比例。
    """
    mask = np.zeros(img_size, dtype=np.uint8)
    polygons = np.asarray(polygons)
    polygons = polygons.astype(np.int32)
    shape = polygons.shape
    polygons = polygons.reshape(shape[0], -1, 2)
    cv2.fillPoly(mask, polygons, color=color)  # 填充多边形
    nh, nw = (img_size[0] // downsample_ratio, img_size[1] // downsample_ratio)
    # 注意：先fillPoly再resize是为了保持与mask-ratio=1时
    # 损失计算的一致性
    mask = cv2.resize(mask, (nw, nh))
    return mask


def polygons2masks(img_size, polygons, color, downsample_ratio=1):
    """
    将多个多边形转换为掩码数组。
    
    Args:
        img_size (tuple): 图像尺寸。
        polygons (list[np.ndarray]): 每个多边形是[N, M]，
            N是多边形数量，
            M是点的数量(需要被2整除)。
        color (int): 填充颜色。
        downsample_ratio (int): 下采样比例。
    """
    masks = []
    for si in range(len(polygons)):
        mask = polygon2mask(img_size, [polygons[si].reshape(-1)], color, downsample_ratio)
        masks.append(mask)
    return np.array(masks)


def polygons2masks_overlap(img_size, segments, downsample_ratio=1):
    """返回一个重叠掩码，按面积大小排序处理重叠区域。"""
    masks = np.zeros(
        (img_size[0] // downsample_ratio, img_size[1] // downsample_ratio),
        dtype=np.int32 if len(segments) > 255 else np.uint8,
    )
    areas = []  # 存储每个分割区域的面积
    ms = []     # 存储每个掩码
    
    # 为每个分割生成掩码并计算面积
    for si in range(len(segments)):
        mask = polygon2mask(
            img_size,
            [segments[si].reshape(-1)],
            downsample_ratio=downsample_ratio,
            color=1,
        )
        ms.append(mask)
        areas.append(mask.sum())  # 计算掩码面积
    
    # 按面积从大到小排序
    areas = np.asarray(areas)
    index = np.argsort(-areas)  # 降序排列索引
    ms = np.array(ms)[index]
    
    # 按面积大小顺序叠加掩码，处理重叠
    for i in range(len(segments)):
        mask = ms[i] * (i + 1)  # 给每个掩码分配唯一ID
        masks = masks + mask
        masks = np.clip(masks, a_min=0, a_max=i + 1)  # 限制值范围
    
    return masks, index

class LoadImagesAndLabelsAndMasks(Dataset):
    """
    统一数据集类：
      - 单模态图像/标签/分割
      - 双模态（PPL/XPL）成对图像、同步增强、掩码/标签一致
    返回：
      - 单模态: (img, labels, path, shapes, masks)
      - 双模态: (ppl_img, xpl_img, labels, path, shapes, masks)
    """
    cache_version = 0.6
    rand_interp_methods = [
        cv2.INTER_NEAREST, cv2.INTER_LINEAR, cv2.INTER_CUBIC,
        cv2.INTER_AREA, cv2.INTER_LANCZOS4
    ]

    def __init__(
        self,
        path,
        img_size=640,
        batch_size=16,
        augment=False,
        hyp=None,
        rect=False,
        image_weights=False,
        cache_images=False,
        single_cls=False,
        stride=32,
        pad=0.0,
        min_items=0,
        prefix="",
        rank=-1,
        seed=0,
        # 分割
        downsample_ratio=1,
        overlap=False,
        # 双模态
        xpl_path=None,
    ):
        """
        Args:
            path: PPL（或单模态）图像路径/清单
            xpl_path: XPL 图像目录（提供则启用双模态）
        """
        # ---- 基础配置 ----
        self.img_size = img_size
        self.augment = augment
        self.hyp = hyp or {}
        self.image_weights = image_weights
        self.rect = False if image_weights else rect
        self.mosaic = self.augment and not self.rect
        self.mosaic_border = [-img_size // 2, -img_size // 2]
        self.stride = stride
        self.path = path
        self.single_cls = single_cls
        self.prefix = prefix
        self.seed = seed
        self.rank = rank

        # 分割相关
        self.downsample_ratio = max(1, int(downsample_ratio))
        self.overlap = bool(overlap)

        # Albumentations
        self.albumentations = Albumentations(size=img_size) if augment else None

        # ---- 扫描 PPL 图像 ----
        try:
            f = []
            for p in path if isinstance(path, list) else [path]:
                p = Path(p)
                if p.is_dir():
                    f += glob.glob(str(p / "**" / "*.*"), recursive=True)
                elif p.is_file():
                    with open(p) as t:
                        t = t.read().strip().splitlines()
                        parent = str(p.parent) + os.sep
                        f += [x.replace("./", parent, 1) if x.startswith("./") else x for x in t]
                else:
                    raise FileNotFoundError(f"{prefix}{p} does not exist")
            self.im_files = sorted(
                x.replace("/", os.sep) for x in f if x.split(".")[-1].lower() in IMG_FORMATS
            )
            assert self.im_files, f"{prefix}No images found"
        except Exception as e:
            raise Exception(f"{prefix}Error loading data from {path}: {e}\n{HELP_URL}") from e

        # ---- 标签缓存/读取 ----
        self.label_files = img2label_paths(self.im_files)
        p = Path(path) if isinstance(path, (str, Path)) else Path(self.label_files[0]).parent
        cache_path = (p if p.is_file() else Path(self.label_files[0]).parent).with_suffix(".cache")

        try:
            cache, exists = np.load(cache_path, allow_pickle=True).item(), True
            assert cache["version"] == self.cache_version
            assert cache["hash"] == get_hash(self.label_files + self.im_files)
        except Exception:
            cache, exists = self.cache_labels(cache_path, prefix), False

        nf, nm, ne, nc, n = cache.pop("results")
        if exists and LOCAL_RANK in {-1, 0}:
            d = f"Scanning {cache_path}... {nf} images, {nm + ne} backgrounds, {nc} corrupt"
            tqdm(None, desc=prefix + d, total=n, initial=n, bar_format=TQDM_BAR_FORMAT)
            if cache.get("msgs"):
                LOGGER.info("\n".join(cache["msgs"]))
        assert nf > 0 or not augment, f"{prefix}No labels found in {cache_path}, can not start training. {HELP_URL}"

        [cache.pop(k) for k in ("hash", "version", "msgs")]
        labels, shapes, self.segments = zip(*cache.values())
        nl = len(np.concatenate(labels, 0))
        assert nl > 0 or not augment, f"{prefix}All labels empty in {cache_path}, can not start training. {HELP_URL}"
        self.labels = list(labels)
        self.shapes = np.array(shapes)
        self.im_files = list(cache.keys())
        self.label_files = img2label_paths(cache.keys())

        # ---- 过滤最小标注数量 ----
        if min_items:
            include = np.array([len(x) >= min_items for x in self.labels]).nonzero()[0].astype(int)
            LOGGER.info(f"{prefix}{n - len(include)}/{n} images filtered from dataset")
            self.im_files = [self.im_files[i] for i in include]
            self.label_files = [self.label_files[i] for i in include]
            self.labels = [self.labels[i] for i in include]
            self.segments = [self.segments[i] for i in include]
            self.shapes = self.shapes[include]

        # ---- 构建索引/批形状 ----
        n = len(self.shapes)
        bi = np.floor(np.arange(n) / batch_size).astype(int)
        nb = bi[-1] + 1
        self.batch = bi
        self.n = n
        self.indices = np.arange(n)
        if rank > -1:
            self.indices = self.indices[np.random.RandomState(seed=seed).permutation(n) % WORLD_SIZE == RANK]

        # 单类训练
        self.segments = list(self.segments)
        if self.single_cls:
            for i in range(len(self.labels)):
                if len(self.labels[i]):
                    self.labels[i][:, 0] = 0

        # 矩形训练
        if self.rect:
            s = self.shapes  # wh
            ar = s[:, 1] / s[:, 0]
            irect = ar.argsort()
            self.im_files = [self.im_files[i] for i in irect]
            self.label_files = [self.label_files[i] for i in irect]
            self.labels = [self.labels[i] for i in irect]
            self.segments = [self.segments[i] for i in irect]
            self.shapes = s[irect]
            ar = ar[irect]
            shapes = [[1, 1]] * nb
            for i in range(nb):
                ari = ar[bi == i]
                mini, maxi = ari.min(), ari.max()
                if maxi < 1:
                    shapes[i] = [maxi, 1]
                elif mini > 1:
                    shapes[i] = [1, 1 / mini]
            self.batch_shapes = np.ceil(np.array(shapes) * img_size / stride + pad).astype(int) * stride

        # ---- 图像缓存 ----
        if cache_images == "ram" and not self.check_cache_ram(prefix=prefix):
            cache_images = False
        self.ims = [None] * n
        self.npy_files = [Path(f).with_suffix(".npy") for f in self.im_files]
        self.im_hw0, self.im_hw = None, None
        if cache_images:
            b, gb = 0, 1 << 30
            self.im_hw0, self.im_hw = [None] * n, [None] * n
            fcn = self.cache_images_to_disk if cache_images == "disk" else self.load_image
            with ThreadPool(NUM_THREADS) as pool:
                results = pool.imap(lambda i: (i, fcn(i)), self.indices)
                pbar = tqdm(results, total=len(self.indices), bar_format=TQDM_BAR_FORMAT, disable=LOCAL_RANK > 0)
                for i, x in pbar:
                    if cache_images == "disk":
                        b += self.npy_files[i].stat().st_size
                    else:
                        self.ims[i], self.im_hw0[i], self.im_hw[i] = x
                        b += self.ims[i].nbytes * WORLD_SIZE
                    pbar.desc = f"{prefix}Caching images ({b / gb:.1f}GB {cache_images})"
                pbar.close()

        # ---- 双模态映射（提供 xpl_path 才启用）----
        self.xpl_path = xpl_path
        self.dual = False
        self.ppl_files = self.im_files
        self.xpl_files = []
        self.modal_pairs = {}
        self.missing_xpl = []
        if xpl_path is not None:
            self._build_modal_pairs()
            self.dual = True if self.xpl_files else False
            LOGGER.info(f"{prefix}双模态初始化: {len(self.ppl_files)} PPL, {len(self.xpl_files)} XPL, 缺失 {len(self.missing_xpl)}")

    # --------------------------- 工具方法 ---------------------------
    def _build_modal_pairs(self):
        """基于文件名匹配 PPL 与 XPL 文件对；缺失时使用 PPL 兜底"""
        xpl_dir = Path(self.xpl_path)
        if not xpl_dir.exists():
            LOGGER.warning(f"XPL directory {xpl_dir} does not exist, using PPL images for both modalities")
            self.xpl_files = self.ppl_files.copy()
            self.modal_pairs = {ppl: ppl for ppl in self.ppl_files}
            return

        xpl_file_map = {}
        for ext in ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.tif', '*.tiff']:
            for xpl_file in xpl_dir.rglob(ext):
                xpl_file_map[xpl_file.stem] = str(xpl_file)

        for ppl_file in self.ppl_files:
            stem = Path(ppl_file).stem
            if stem in xpl_file_map:
                xf = xpl_file_map[stem]
                self.xpl_files.append(xf)
                self.modal_pairs[ppl_file] = xf
            else:
                self.xpl_files.append(ppl_file)
                self.modal_pairs[ppl_file] = ppl_file
                self.missing_xpl.append(stem)

    def check_cache_ram(self, safety_margin=0.1, prefix=""):
        """估算 RAM 是否足以缓存图像"""
        b, gb = 0, 1 << 30
        n = min(self.n, 30)
        for _ in range(n):
            im = cv2.imread(random.choice(self.im_files))
            if im is None:
                continue
            ratio = self.img_size / max(im.shape[0], im.shape[1])
            b += im.nbytes * ratio**2
        mem_required = b * self.n / max(1, n)
        mem = psutil.virtual_memory()
        cache = mem_required * (1 + safety_margin) < mem.available
        if not cache:
            LOGGER.info(
                f"{prefix}{mem_required / gb:.1f}GB RAM required, "
                f"{mem.available / gb:.1f}/{mem.total / gb:.1f}GB available, "
                f"{'✅ ' if cache else '❌ '}{'caching images' if cache else 'not caching images'}"
            )
        return cache

    def cache_labels(self, path=Path("./labels.cache"), prefix=""):
        """扫描校验图像/标签并缓存（与原版一致，增加日志）"""
        x = {}
        nm, nf, ne, nc, msgs = 0, 0, 0, 0, []
        desc = f"{prefix}Scanning {path.parent / path.stem}..."
        with Pool(NUM_THREADS) as pool:
            pbar = tqdm(
                pool.imap(verify_image_label, zip(self.im_files, self.label_files, repeat(prefix))),
                desc=desc, total=len(self.im_files), bar_format=TQDM_BAR_FORMAT
            )
            for im_file, lb, shape, segments, nm_f, nf_f, ne_f, nc_f, msg in pbar:
                nm += nm_f; nf += nf_f; ne += ne_f; nc += nc_f
                if im_file:
                    x[im_file] = [lb, shape, segments]
                if msg:
                    msgs.append(msg)
                pbar.desc = f"{desc} {nf} images, {nm + ne} backgrounds, {nc} corrupt"
        pbar.close()

        if msgs:
            LOGGER.info("\n".join(msgs))
        if nf == 0:
            LOGGER.warning(f"{prefix}WARNING ⚠️ No labels found in {path}. {HELP_URL}")
        x["hash"] = get_hash(self.label_files + self.im_files)
        x["results"] = nf, nm, ne, nc, len(self.im_files)
        x["msgs"] = msgs
        x["version"] = self.cache_version
        try:
            np.save(path, x)
            path.with_suffix(".cache.npy").rename(path)
            LOGGER.info(f"{prefix}New cache created: {path}")
        except Exception as e:
            LOGGER.warning(f"{prefix}WARNING ⚠️ Cache directory {path.parent} is not writeable: {e}")
        return x

    def __len__(self):
        return len(self.im_files)

    # --------------------------- 读图 ---------------------------
    def load_image(self, i):
        """单模态按索引加载图像（BGR）"""
        im, f, fn = self.ims[i], self.im_files[i], self.npy_files[i]
        if im is None:
            if fn.exists():
                im = np.load(fn)
            else:
                im = cv2.imread(f)
                assert im is not None, f"Image Not Found {f}"
            h0, w0 = im.shape[:2]
            r = self.img_size / max(h0, w0)
            if r != 1:
                interp = cv2.INTER_LINEAR if (self.augment or r > 1) else cv2.INTER_AREA
                im = cv2.resize(im, (math.ceil(w0 * r), math.ceil(h0 * r)), interpolation=interp)
            return im, (h0, w0), im.shape[:2]
        return self.ims[i], self.im_hw0[i], self.im_hw[i]

    def cache_images_to_disk(self, i):
        f = self.npy_files[i]
        if not f.exists():
            np.save(f.as_posix(), cv2.imread(self.im_files[i]))

    def load_image_dual(self, i):
        """双模态：返回 ((ppl_img, xpl_img), (h0,w0), (h,w))"""
        ppl_img, (h0, w0), (h, w) = self.load_image(i)
        xpl_path = self.modal_pairs.get(self.im_files[i], self.im_files[i])
        xpl_img = cv2.imread(xpl_path)
        assert xpl_img is not None, f"XPL Image Not Found {xpl_path}"
        if xpl_img.shape[:2] != (h, w):
            xpl_img = cv2.resize(xpl_img, (w, h), interpolation=cv2.INTER_LINEAR)
        return (ppl_img, xpl_img), (h0, w0), (h, w)

    # --------------------------- Mosaic ---------------------------
    def load_mosaic_single(self, index):
        """单模态 4-图像马赛克，返回 (img4, labels4, segments4)"""
        labels4, segments4 = [], []
        s = self.img_size
        yc, xc = (int(random.uniform(-x, 2 * s + x)) for x in self.mosaic_border)
        indices = [index] + random.choices(self.indices, k=3)
        random.shuffle(indices)

        for i, index in enumerate(indices):
            img, _, (h, w) = self.load_image(index)
            if i == 0:
                img4 = np.full((s * 2, s * 2, img.shape[2]), 114, dtype=np.uint8)
                x1a, y1a, x2a, y2a = max(xc - w, 0), max(yc - h, 0), xc, yc
                x1b, y1b, x2b, y2b = w - (x2a - x1a), h - (y2a - y1a), w, h
            elif i == 1:
                x1a, y1a, x2a, y2a = xc, max(yc - h, 0), min(xc + w, s * 2), yc
                x1b, y1b, x2b, y2b = 0, h - (y2a - y1a), min(w, x2a - x1a), h
            elif i == 2:
                x1a, y1a, x2a, y2a = max(xc - w, 0), yc, xc, min(s * 2, yc + h)
                x1b, y1b, x2b, y2b = w - (x2a - x1a), 0, w, min(y2a - y1a, h)
            else:
                x1a, y1a, x2a, y2a = xc, yc, min(xc + w, s * 2), min(s * 2, yc + h)
                x1b, y1b, x2b, y2b = 0, 0, min(w, x2a - x1a), min(y2a - y1a, h)

            img4[y1a:y2a, x1a:x2a] = img[y1b:y2b, x1b:x2b]
            padw, padh = x1a - x1b, y1a - y1b

            labels = self.labels[index].copy()
            segments = self.segments[index].copy()
            if labels.size:
                labels[:, 1:] = xywhn2xyxy(labels[:, 1:], w, h, padw, padh)
                segments = [xyn2xy(x, w, h, padw, padh) for x in segments]
            labels4.append(labels)
            segments4.extend(segments)

        labels4 = np.concatenate(labels4, 0) if len(labels4) else np.zeros((0, 5), dtype=np.float32)
        for x in (labels4[:, 1:], *segments4) if len(segments4) else (labels4[:, 1:],):
            np.clip(x, 0, 2 * s, out=x)

        # copy-paste + 透视
        img4, labels4, segments4 = copy_paste(img4, labels4, segments4, p=self.hyp.get("copy_paste", 0.0))
        img4, labels4, segments4 = random_perspective(
            img4, labels4, segments=segments4,
            degrees=self.hyp.get("degrees", 0.0),
            translate=self.hyp.get("translate", 0.0),
            scale=self.hyp.get("scale", 0.5),
            shear=self.hyp.get("shear", 0.0),
            perspective=self.hyp.get("perspective", 0.0),
            border=self.mosaic_border,
        )
        return img4, labels4, segments4

    def load_mosaic_dual(self, index):
        """双模态 4-图像马赛克，返回 ((ppl_img4, xpl_img4), labels4, segments4)"""
        labels4, segments4 = [], []
        s = self.img_size
        yc, xc = (int(random.uniform(-x, 2 * s + x)) for x in self.mosaic_border)
        indices = [index] + random.choices(self.indices, k=3)
        random.shuffle(indices)

        ppl_img4 = xpl_img4 = None
        for i, index in enumerate(indices):
            (ppl_img, xpl_img), _, (h, w) = self.load_image_dual(index)
            if i == 0:
                ppl_img4 = np.full((s * 2, s * 2, ppl_img.shape[2]), 114, dtype=np.uint8)
                xpl_img4 = np.full((s * 2, s * 2, xpl_img.shape[2]), 114, dtype=np.uint8)
                x1a, y1a, x2a, y2a = max(xc - w, 0), max(yc - h, 0), xc, yc
                x1b, y1b, x2b, y2b = w - (x2a - x1a), h - (y2a - y1a), w, h
            elif i == 1:
                x1a, y1a, x2a, y2a = xc, max(yc - h, 0), min(xc + w, s * 2), yc
                x1b, y1b, x2b, y2b = 0, h - (y2a - y1a), min(w, x2a - x1a), h
            elif i == 2:
                x1a, y1a, x2a, y2a = max(xc - w, 0), yc, xc, min(s * 2, yc + h)
                x1b, y1b, x2b, y2b = w - (x2a - x1a), 0, w, min(y2a - y1a, h)
            else:
                x1a, y1a, x2a, y2a = xc, yc, min(xc + w, s * 2), min(s * 2, yc + h)
                x1b, y1b, x2b, y2b = 0, 0, min(w, x2a - x1a), min(y2a - y1a, h)

            ppl_img4[y1a:y2a, x1a:x2a] = ppl_img[y1b:y2b, x1b:x2b]
            xpl_img4[y1a:y2a, x1a:x2a] = xpl_img[y1b:y2b, x1b:x2b]
            padw, padh = x1a - x1b, y1a - y1b

            labels = self.labels[index].copy()
            segments = self.segments[index].copy()
            if labels.size:
                labels[:, 1:] = xywhn2xyxy(labels[:, 1:], w, h, padw, padh)
                segments = [xyn2xy(x, w, h, padw, padh) for x in segments]
            labels4.append(labels)
            segments4.extend(segments)

        labels4 = np.concatenate(labels4, 0) if len(labels4) else np.zeros((0, 5), dtype=np.float32)
        for x in (labels4[:, 1:], *segments4) if len(segments4) else (labels4[:, 1:],):
            np.clip(x, 0, 2 * s, out=x)

        ppl_img4, labels4, segments4, xpl_img4 = copy_paste(
            ppl_img4, labels4, segments4, p=self.hyp.get("copy_paste", 0.0), im_xpl=xpl_img4
        )
        ppl_img4, labels4, segments4, xpl_img4 = random_perspective(
            ppl_img4, labels4, segments4,
            degrees=self.hyp.get("degrees", 0.0),
            translate=self.hyp.get("translate", 0.0),
            scale=self.hyp.get("scale", 0.5),
            shear=self.hyp.get("shear", 0.0),
            perspective=self.hyp.get("perspective", 0.0),
            border=self.mosaic_border,
            im_xpl=xpl_img4,
        )
        return (ppl_img4, xpl_img4), labels4, segments4

    # --------------------------- __getitem__ ---------------------------
    def __getitem__(self, index):
        """根据是否双模态返回对应 tuple（见类注释）"""
        index = self.indices[index]
        hyp = self.hyp

        # ========== 1) 加载图 ==========
        if self.dual:
            if self.mosaic and random.random() < hyp.get("mosaic", 0.0):
                (ppl_img, xpl_img), labels, segments = self.load_mosaic_dual(index)
                shapes = None
                if random.random() < hyp.get("mixup", 0.0):
                    (ppl2, xpl2), labels2, seg2 = self.load_mosaic_dual(random.randint(0, self.n - 1))
                    ppl_img, labels, segments, xpl_img = mixup(
                        ppl_img, labels, segments, ppl2, labels2, seg2,
                        im_xpl=xpl_img, im2_xpl=xpl2
                    )
            else:
                (ppl_img, xpl_img), (h0, w0), (h, w) = self.load_image_dual(index)
                shape = self.batch_shapes[self.batch[index]] if self.rect else self.img_size
                ppl_img, ratio, pad = letterbox(ppl_img, shape, auto=False, scaleup=self.augment)
                xpl_img, _, _ = letterbox(xpl_img, shape, auto=False, scaleup=self.augment)
                shapes = (h0, w0), ((h / h0, w / w0), pad)

                labels = self.labels[index].copy()
                segments = self.segments[index].copy()
                if len(segments):
                    for i_s in range(len(segments)):
                        segments[i_s] = xyn2xy(
                            segments[i_s], ratio[0] * w, ratio[1] * h, padw=pad[0], padh=pad[1]
                        )
                if labels.size:
                    labels[:, 1:] = xywhn2xyxy(labels[:, 1:], ratio[0] * w, ratio[1] * h, padw=pad[0], padh=pad[1])

                if self.augment:
                    ppl_img, labels, segments, xpl_img = random_perspective(
                        ppl_img, labels, segments=segments,
                        degrees=hyp.get("degrees", 0.0),
                        translate=hyp.get("translate", 0.0),
                        scale=hyp.get("scale", 0.5),
                        shear=hyp.get("shear", 0.0),
                        perspective=hyp.get("perspective", 0.0),
                        im_xpl=xpl_img,
                    )
        else:
            if self.mosaic and random.random() < hyp.get("mosaic", 0.0):
                ppl_img, labels, segments = self.load_mosaic_single(index)
                shapes = None
                if random.random() < hyp.get("mixup", 0.0):
                    ppl2, labels2, seg2 = self.load_mosaic_single(random.randint(0, self.n - 1))
                    ppl_img, labels = mixup(ppl_img, labels, ppl2, labels2)  # 单模态分割 seg2 可不传
            else:
                ppl_img, (h0, w0), (h, w) = self.load_image(index)
                shape = self.batch_shapes[self.batch[index]] if self.rect else self.img_size
                ppl_img, ratio, pad = letterbox(ppl_img, shape, auto=False, scaleup=self.augment)
                shapes = (h0, w0), ((h / h0, w / w0), pad)

                labels = self.labels[index].copy()
                segments = self.segments[index].copy()
                if len(segments):
                    for i_s in range(len(segments)):
                        segments[i_s] = xyn2xy(
                            segments[i_s], ratio[0] * w, ratio[1] * h, padw=pad[0], padh=pad[1]
                        )
                if labels.size:
                    labels[:, 1:] = xywhn2xyxy(labels[:, 1:], ratio[0] * w, ratio[1] * h, padw=pad[0], padh=pad[1])

                if self.augment:
                    ppl_img, labels, segments = random_perspective(
                        ppl_img, labels, segments=segments,
                        degrees=hyp.get("degrees", 0.0),
                        translate=hyp.get("translate", 0.0),
                        scale=hyp.get("scale", 0.5),
                        shear=hyp.get("shear", 0.0),
                        perspective=hyp.get("perspective", 0.0),
                    )

        # ========== 2) 生成/对齐掩码 ==========
        nl = len(labels)
        masks = []
        if nl:
            # 转回归一化 xywhn（注意此时 ppl_img 已是 letterbox 后尺寸）
            labels[:, 1:5] = xyxy2xywhn(
                labels[:, 1:5], w=ppl_img.shape[1], h=ppl_img.shape[0], clip=True, eps=1e-3
            )
            if self.overlap:
                masks, sorted_idx = polygons2masks_overlap(
                    ppl_img.shape[:2], segments, downsample_ratio=self.downsample_ratio
                )
                masks = masks[None]  # (H,W) -> (1,H,W)
                labels = labels[sorted_idx]
            else:
                masks = polygons2masks(
                    ppl_img.shape[:2], segments, color=1, downsample_ratio=self.downsample_ratio
                )

        masks = (
            torch.from_numpy(masks)
            if len(masks)
            else torch.zeros(
                1 if self.overlap else nl,
                ppl_img.shape[0] // self.downsample_ratio,
                ppl_img.shape[1] // self.downsample_ratio,
            )
        )

        # ========== 3) Albumentations/色域/翻转（双模态尽量同步） ==========
        if self.augment:
            if self.dual:
                ppl_img, labels = self.albumentations(ppl_img, labels)
                xpl_img, _ = self.albumentations(xpl_img, labels)
            else:
                ppl_img, labels = self.albumentations(ppl_img, labels)
            nl = len(labels)

            augment_hsv(ppl_img, hgain=hyp.get("hsv_h", 0.0), sgain=hyp.get("hsv_s", 0.0), vgain=hyp.get("hsv_v", 0.0))
            if self.dual:
                augment_hsv(xpl_img, hgain=hyp.get("hsv_h", 0.0), sgain=hyp.get("hsv_s", 0.0), vgain=hyp.get("hsv_v", 0.0))

            if random.random() < hyp.get("flipud", 0.0):
                ppl_img = np.flipud(ppl_img)
                if self.dual:
                    xpl_img = np.flipud(xpl_img)
                if nl:
                    labels[:, 2] = 1 - labels[:, 2]
                    masks = torch.flip(masks, dims=[1])

            if random.random() < hyp.get("fliplr", 0.0):
                ppl_img = np.fliplr(ppl_img)
                if self.dual:
                    xpl_img = np.fliplr(xpl_img)
                if nl:
                    labels[:, 1] = 1 - labels[:, 1]
                    masks = torch.flip(masks, dims=[2])

        # ========== 4) 打包输出 ==========
        labels_out = torch.zeros((nl, 6))
        if nl:
            labels_out[:, 1:] = torch.from_numpy(labels)

        # HWC->CHW，BGR->RGB
        def _to_chw_rgb(im):
            im = im.transpose((2, 0, 1))[::-1]
            return np.ascontiguousarray(im)

        ppl_img = _to_chw_rgb(ppl_img)
        if self.dual:
            xpl_img = _to_chw_rgb(xpl_img)

        if self.dual:
            return (
                torch.from_numpy(ppl_img), torch.from_numpy(xpl_img),
                labels_out, self.im_files[index], shapes, masks
            )
        else:
            return (torch.from_numpy(ppl_img), labels_out, self.im_files[index], shapes, masks)

    # --------------------------- collate ---------------------------
    @staticmethod
    def collate_fn(batch):
        """
        自适配整理：
          - 单模态: (img, labels, path, shapes, masks)
          - 双模态: (ppl_img, xpl_img, labels, path, shapes, masks)
        """
        first = batch[0]
        if len(first) == 5:  # 单模态
            im, label, path, shapes, masks = zip(*batch)
            batched_masks = torch.cat(masks, 0)
            for i, lb in enumerate(label):
                lb[:, 0] = i
            return torch.stack(im, 0), torch.cat(label, 0), path, shapes, batched_masks
        elif len(first) == 6:  # 双模态
            ppl_img, xpl_img, label, path, shapes, masks = zip(*batch)
            batched_masks = torch.cat(masks, 0)
            for i, lb in enumerate(label):
                lb[:, 0] = i
            return torch.stack(ppl_img, 0), torch.stack(xpl_img, 0), torch.cat(label, 0), path, shapes, batched_masks
        else:
            raise ValueError("Unexpected batch tuple length.")

    @staticmethod
    def collate_fn4(batch):
        """
        4图合并版整理函数（与原版逻辑一致），自适配单/双模态：
          - 单模态返回: (img4, labels4, path4, shapes4)
          - 双模态返回: (ppl_img4, xpl_img4, labels4, path4, shapes4)
        """
        first = batch[0]
        if len(first) == 5:  # 单模态
            im, label, path, shapes, masks = zip(*batch)
            n = len(shapes) // 4
            im4, label4, path4, shapes4 = [], [], path[:n], shapes[:n]
            ho = torch.tensor([[0.0, 0, 0, 1, 0, 0]])
            s = torch.tensor([[1, 1, 0.5, 0.5, 0.5, 0.5]])
            for i in range(n):
                i *= 4
                if random.random() < 0.5:
                    im1 = F.interpolate(
                        im[i].unsqueeze(0).float(), scale_factor=2.0, mode="bilinear", align_corners=False
                    )[0].type(im[i].type())
                    lb = label[i]
                else:
                    im1 = torch.cat((torch.cat((im[i], im[i + 1]), 1), torch.cat((im[i + 2], im[i + 3]), 1)), 2)
                    lb = torch.cat((label[i], label[i + 1] + ho, label[i + 2] + ho, label[i + 3] + ho), 0) * s
                im4.append(im1); label4.append(lb)
            for i, lb in enumerate(label4):
                lb[:, 0] = i
            return torch.stack(im4, 0), torch.cat(label4, 0), path4, shapes4

        elif len(first) == 6:  # 双模态
            ppl_img, xpl_img, label, path, shapes, masks = zip(*batch)
            n = len(shapes) // 4
            ppl_img4, xpl_img4, label4, path4, shapes4 = [], [], [], path[:n], shapes[:n]
            ho = torch.tensor([[0.0, 0, 0, 1, 0, 0]])
            for i in range(n):
                i *= 4
                if random.random() < 0.5:
                    im1 = F.interpolate(
                        ppl_img[i].unsqueeze(0).float(), scale_factor=2.0, mode="bilinear", align_corners=False
                    )[0].type(ppl_img[i].type())
                    im2 = F.interpolate(
                        xpl_img[i].unsqueeze(0).float(), scale_factor=2.0, mode="bilinear", align_corners=False
                    )[0].type(xpl_img[i].type())
                    l = label[i]
                else:
                    im1 = torch.cat(
                        (torch.cat((ppl_img[i], ppl_img[i + 1]), 1), torch.cat((ppl_img[i + 2], ppl_img[i + 3]), 1)), 2
                    )
                    im2 = torch.cat(
                        (torch.cat((xpl_img[i], xpl_img[i + 1]), 1), torch.cat((xpl_img[i + 2], xpl_img[i + 3]), 1)), 2
                    )
                    l = torch.cat((label[i], label[i + 1] + ho, label[i + 2] + ho, label[i + 3] + ho), 0)
                ppl_img4.append(im1); xpl_img4.append(im2); label4.append(l)
            for i, lb in enumerate(label4):
                lb[:, 0] = i
            return torch.stack(ppl_img4, 0), torch.stack(xpl_img4, 0), torch.cat(label4, 0), path4, shapes4

        else:
            raise ValueError("Unexpected batch tuple length.")


def create_multimodal_dataloader(
    path,
    imgsz,
    batch_size,
    stride,
    single_cls=False,
    hyp=None,
    augment=False,
    cache=False,
    pad=0.0,
    rect=False,
    rank=-1,
    workers=8,
    image_weights=False,
    quad=False,
    prefix="",
    shuffle=False,
    mask_downsample_ratio=1,
    overlap_mask=False,
    seed=0,
    xpl_path=None,
    multimodal=False,
):
    """创建支持双模态的多模态训练/测试数据加载器。"""
    if rect and shuffle:
        LOGGER.warning("WARNING ⚠️ --rect is incompatible with DataLoader shuffle, setting shuffle=False")
        shuffle = False
        
    with torch_distributed_zero_first(rank):
        if multimodal and xpl_path:
            # 创建双模态数据集
            dataset = LoadImagesAndLabelsAndMasks(
                path,
                imgsz,
                batch_size,
                augment=augment,
                hyp=hyp,
                rect=rect,
                cache_images=cache,
                single_cls=single_cls,
                stride=int(stride),
                pad=pad,
                image_weights=image_weights,
                prefix=prefix,
                downsample_ratio=mask_downsample_ratio,
                overlap=overlap_mask,
                rank=rank,
                xpl_path=xpl_path,
            )
        else:
            # 创建单模态数据集
            dataset = LoadImagesAndLabelsAndMasks(
                path,
                imgsz,
                batch_size,
                augment=augment,
                hyp=hyp,
                rect=rect,
                cache_images=cache,
                single_cls=single_cls,
                stride=int(stride),
                pad=pad,
                image_weights=image_weights,
                prefix=prefix,
                downsample_ratio=mask_downsample_ratio,
                overlap=overlap_mask,
                rank=rank,
            )

    batch_size = min(batch_size, len(dataset))
    nd = torch.cuda.device_count()
    nw = min([os.cpu_count() // max(nd, 1), batch_size if batch_size > 1 else 0, workers])
    sampler = None if rank == -1 else SmartDistributedSampler(dataset, shuffle=shuffle)
    loader = DataLoader if image_weights else InfiniteDataLoader
    generator = torch.Generator()
    generator.manual_seed(6148914691236517205 + seed + RANK)
    
    # 使用适当的整理函数
    if multimodal and xpl_path:
        collate_fn = LoadImagesAndLabelsAndMasks.collate_fn4 if quad else LoadImagesAndLabelsAndMasks.collate_fn
    else:
        collate_fn = LoadImagesAndLabelsAndMasks.collate_fn4 if quad else LoadImagesAndLabelsAndMasks.collate_fn
        
    return loader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle and sampler is None,
        num_workers=nw,
        sampler=sampler,
        drop_last=quad,
        pin_memory=True,
        collate_fn=collate_fn,
        worker_init_fn=seed_worker,
        generator=generator,
    ), dataset
    
class LoadImages:
    """YOLOv5图像数据加载器。
    
    用于加载图像文件，支持单个文件、目录、glob模式和文件列表。
    例如：`python detect.py --source image.jpg`
    """

    def __init__(self, path, img_size=640, stride=32, auto=True, transforms=None, xpl_path=None, multimodal=False):
        """初始化YOLOv5图像加载器。
        
        支持glob模式、目录和路径列表。
        
        Args:
            path: 文件路径、目录路径、glob模式或路径列表
            img_size: 图像尺寸
            stride: 模型步长
            auto: 是否自动调整
            transforms: 图像变换
            xpl_path: XPL图像路径（双模态模式下使用）
            multimodal: 是否启用双模态模式
        """
        if isinstance(path, str) and Path(path).suffix == ".txt":  # txt文件，每行一个图像路径
            path = Path(path).read_text().rsplit()
        files = []  # 文件列表
        for p in sorted(path) if isinstance(path, (list, tuple)) else [path]:
            p = str(Path(p).resolve())  # 解析为绝对路径
            if "*" in p:
                files.extend(sorted(glob.glob(p, recursive=True)))  # glob模式匹配
            elif os.path.isdir(p):
                files.extend(sorted(glob.glob(os.path.join(p, "*.*"))))  # 目录中的所有文件
            elif os.path.isfile(p):
                files.append(p)  # 单个文件
            else:
                raise FileNotFoundError(f"{p} does not exist")  # 路径不存在

        # 只保留图像文件
        self.files = [x for x in files if x.split(".")[-1].lower() in IMG_FORMATS]  # 图像文件
        self.nf = len(self.files)  # 文件总数

        self.img_size = img_size  # 图像尺寸
        self.stride = stride  # 步长
        self.mode = "image"  # 模式
        self.auto = auto  # 自动调整
        self.transforms = transforms  # 变换（可选）
        self.multimodal = multimodal  # 双模态模式
        
        # 处理XPL图像路径（双模态模式）
        self.xpl_files = []
        if multimodal and xpl_path:
            if isinstance(xpl_path, str) and Path(xpl_path).suffix == ".txt":
                xpl_path = Path(xpl_path).read_text().rsplit()
            xpl_files = []
            for p in sorted(xpl_path) if isinstance(xpl_path, (list, tuple)) else [xpl_path]:
                p = str(Path(p).resolve())
                if "*" in p:
                    xpl_files.extend(sorted(glob.glob(p, recursive=True)))
                elif os.path.isdir(p):
                    xpl_files.extend(sorted(glob.glob(os.path.join(p, "*.*"))))
                elif os.path.isfile(p):
                    xpl_files.append(p)
                else:
                    raise FileNotFoundError(f"XPL path {p} does not exist")
            
            self.xpl_files = [x for x in xpl_files if x.split(".")[-1].lower() in IMG_FORMATS]
            
            # 确保XPL图像数量与RGB图像数量匹配
            if len(self.xpl_files) != self.nf:
                raise ValueError(f"XPL images count ({len(self.xpl_files)}) does not match RGB images count ({self.nf})")
        
        assert self.nf > 0, (
            f"No images found in {p}. Supported formats are:\nimages: {IMG_FORMATS}"
        )  # 确保找到了文件

    def __iter__(self):
        """通过重置计数初始化迭代器并返回迭代器对象本身。
        
        Returns:
            self: 返回自身实例
        """
        self.count = 0  # 重置计数
        return self

    def __next__(self):
        """前进到数据集中的下一个文件，如果到达末尾则引发StopIteration。
        
        Returns:
            tuple: 单模态模式: (文件路径, 处理后图像, 原始图像, None, 描述字符串)
                  双模态模式: (ppl_img_tensor, xpl_img_tensor, empty_labels, path, shapes, empty_masks)
            
        Raises:
            StopIteration: 当到达数据集末尾时
        """
        if self.count == self.nf:
            raise StopIteration  # 到达末尾
        path = self.files[self.count]  # 当前文件路径

        # 读取RGB图像
        self.count += 1  # 计数递增
        im0 = cv2.imread(path)  # 读取BGR图像
        assert im0 is not None, f"RGB Image Not Found {path}"  # 确保图像存在
        s = f"image {self.count}/{self.nf} {path}: "  # 图像描述

        # 保存原始图像尺寸
        h0, w0 = im0.shape[:2]  # 原始高度和宽度
        
        if self.transforms:
            im_rgb = self.transforms(im0)  # 应用变换
            # 如果使用自定义变换，假设输出尺寸与输入相同
            r = 1.0  # 缩放比例
            shapes = (h0, w0), ((h0, w0), (r, r))  # 原始尺寸和缩放信息
        else:
            im_rgb, ratio, pad = letterbox(im0, self.img_size, stride=self.stride, auto=self.auto)  # 填充调整大小
            h, w = im_rgb.shape[:2]  # 处理后的高度和宽度
            shapes = (h0, w0), ((h, w), ratio)  # 原始尺寸和缩放信息
            im_rgb = im_rgb.transpose((2, 0, 1))[::-1]  # HWC转CHW，BGR转RGB
            im_rgb = np.ascontiguousarray(im_rgb)  # 确保内存连续

        if self.multimodal and self.xpl_files:
            # 双模态模式：同时加载XPL图像，返回与predict.py期望的格式匹配
            xpl_path = self.xpl_files[self.count - 1]  # 对应的XPL图像路径
            im0_xpl = cv2.imread(xpl_path)  # 读取XPL图像
            assert im0_xpl is not None, f"XPL Image Not Found {xpl_path}"  # 确保XPL图像存在
            
            if self.transforms:
                im_xpl = self.transforms(im0_xpl)  # 应用变换
            else:
                im_xpl, _, _ = letterbox(im0_xpl, self.img_size, stride=self.stride, auto=self.auto)  # 填充调整大小
                im_xpl = im_xpl.transpose((2, 0, 1))[::-1]  # HWC转CHW，BGR转RGB
                im_xpl = np.ascontiguousarray(im_xpl)  # 确保内存连续
            
            # 返回与predict.py期望的格式匹配：(path, ppl_im, xpl_im, im0s, vid_cap, s)
            return path, im_rgb, im_xpl, im0, None, s
        else:
            # 单模态模式：保持原有功能
            return path, im_rgb, im0, None, s  # 返回路径、处理图像、原图、None、描述

    def __len__(self):
        """返回数据集中的文件数量。
        
        Returns:
            int: 文件总数
        """
        return self.nf  # 文件数量