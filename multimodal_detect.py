#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv5 多模态检测脚本
基于原detect.py修改，支持RGB+X双模态输入

运行示例:
    python multimodal_detect.py --weights yolov5n-mid-seg.pt --source data/images --xpl-source data/xpl_images
    python multimodal_detect.py --weights yolov5n-mid-seg.pt --source data/images --xpl-source data/xpl_images --save-txt --save-conf
"""

import argparse
import os
import platform
import sys
from pathlib import Path

import numpy as np
import torch
import cv2

FILE = Path(__file__).resolve()
ROOT = FILE.parents[0]  # YOLOv5 root directory
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))  # add ROOT to PATH
ROOT = Path(os.path.relpath(ROOT, Path.cwd()))  # relative

from models.common import DetectMultiBackend
from ultralytics.utils.plotting import Annotator, colors, save_one_box
from utils.segment.dataloaders import IMG_FORMATS, LoadImages

# 定义视频格式
VID_FORMATS = "asf", "avi", "gif", "m4v", "mkv", "mov", "mp4", "mpeg", "mpg", "ts", "wmv", "webm"
from utils.segment.general import (LOGGER, Profile, check_file, check_img_size, check_imshow, check_requirements, colorstr,
                           increment_path, non_max_suppression, print_args, scale_boxes, strip_optimizer, xyxy2xywh)
from utils.torch_utils import select_device, smart_inference_mode

# 导入多模态相关模块
# from utils.segment.dataloaders import LoadDualModalImages
# from utils.segment.multimodal_plots import plot_multimodal_images_and_masks


class LoadDualModalImages:
    """
    简化的双模态图像加载器，用于推理
    支持RGB和XPL图像的配对加载
    """
    def __init__(self, rgb_path, xpl_path, img_size=640, stride=32, auto=True, transforms=None, vid_stride=1):
        self.rgb_path = rgb_path
        self.xpl_path = xpl_path
        self.img_size = img_size
        self.stride = stride
        self.auto = auto
        self.transforms = transforms
        self.vid_stride = vid_stride
        
        # 获取RGB和XPL文件列表
        self.rgb_files = self._get_files(rgb_path)
        self.xpl_files = self._get_files(xpl_path)
        
        # 构建配对
        self.pairs = self._build_pairs()
        self.nf = len(self.pairs)
        
        self.count = 0
        
    def _get_files(self, path):
        """获取路径下的图像文件"""
        if os.path.isdir(path):
            files = []
            for ext in IMG_FORMATS:
                files.extend(Path(path).glob(f'*.{ext}'))
            return sorted(files)
        elif os.path.isfile(path):
            return [Path(path)]
        else:
            raise FileNotFoundError(f'Path not found: {path}')
    
    def _build_pairs(self):
        """构建RGB和XPL的文件配对"""
        pairs = []
        rgb_stems = {f.stem: f for f in self.rgb_files}
        xpl_stems = {f.stem: f for f in self.xpl_files}
        
        # 找到共同的文件名
        common_stems = set(rgb_stems.keys()) & set(xpl_stems.keys())
        
        for stem in sorted(common_stems):
            pairs.append((rgb_stems[stem], xpl_stems[stem]))
            
        if not pairs:
            raise ValueError(f'No matching pairs found between {self.rgb_path} and {self.xpl_path}')
            
        return pairs
    
    def __iter__(self):
        self.count = 0
        return self
    
    def __next__(self):
        if self.count >= self.nf:
            raise StopIteration
            
        rgb_path, xpl_path = self.pairs[self.count]
        self.count += 1
        
        # 加载RGB图像
        rgb_img = cv2.imread(str(rgb_path))
        assert rgb_img is not None, f'Image Not Found {rgb_path}'
        
        # 加载XPL图像
        xpl_img = cv2.imread(str(xpl_path))
        assert xpl_img is not None, f'Image Not Found {xpl_path}'
        
        # 预处理
        rgb_img = self._letterbox(rgb_img)
        xpl_img = self._letterbox(xpl_img)
        
        # HWC to CHW, BGR to RGB
        rgb_img = rgb_img.transpose((2, 0, 1))[::-1]
        xpl_img = xpl_img.transpose((2, 0, 1))[::-1]
        
        # 转换为连续数组
        rgb_img = torch.from_numpy(rgb_img.copy()).float() / 255.0
        xpl_img = torch.from_numpy(xpl_img.copy()).float() / 255.0
        
        return (str(rgb_path), str(xpl_path)), (rgb_img, xpl_img), rgb_img.shape[1:], ''
    
    def _letterbox(self, im, new_shape=(640, 640), color=(114, 114, 114), auto=True, scaleFill=False, scaleup=True, stride=32):
        """调整图像大小并填充"""
        shape = im.shape[:2]  # current shape [height, width]
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)
        
        # Scale ratio (new / old)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        if not scaleup:  # only scale down, do not scale up (for better val mAP)
            r = min(r, 1.0)
        
        # Compute padding
        ratio = r, r  # width, height ratios
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
        
        if auto:  # minimum rectangle
            dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
        elif scaleFill:  # stretch
            dw, dh = 0.0, 0.0
            new_unpad = (new_shape[1], new_shape[0])
            ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # width, height ratios
        
        dw /= 2  # divide padding into 2 sides
        dh /= 2
        
        if shape[::-1] != new_unpad:  # resize
            im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # add border
        return im
    
    def __len__(self):
        return self.nf


@smart_inference_mode()
def run(
        weights=ROOT / 'yolov5s.pt',  # model path or triton URL
        source=ROOT / 'data/images',  # RGB images source
        xpl_source=None,  # XPL images source
        data=ROOT / 'data/coco128.yaml',  # dataset.yaml path
        imgsz=(640, 640),  # inference size (height, width)
        conf_thres=0.25,  # confidence threshold
        iou_thres=0.45,  # NMS IOU threshold
        max_det=1000,  # maximum detections per image
        device='',  # cuda device, i.e. 0 or 0,1,2,3 or cpu
        view_img=False,  # show results
        save_txt=False,  # save results to *.txt
        save_conf=False,  # save confidences in --save-txt labels
        save_crop=False,  # save cropped prediction boxes
        nosave=False,  # do not save images/videos
        classes=None,  # filter by class: --class 0, or --class 0 2 3
        agnostic_nms=False,  # class-agnostic NMS
        augment=False,  # augmented inference
        visualize=False,  # visualize features
        update=False,  # update all models
        project=ROOT / 'runs/detect',  # save results to project/name
        name='exp',  # save results to project/name
        exist_ok=False,  # existing project/name ok, do not increment
        line_thickness=3,  # bounding box thickness (pixels)
        hide_labels=False,  # hide labels
        hide_conf=False,  # hide confidences
        half=False,  # use FP16 half-precision inference
        dnn=False,  # use OpenCV DNN for ONNX inference
        vid_stride=1,  # video frame-rate stride
):
    # 检查XPL源路径
    if xpl_source is None:
        raise ValueError('XPL source path is required for multimodal detection')
    
    source = str(source)
    xpl_source = str(xpl_source)
    save_img = not nosave and not source.endswith('.txt')  # save inference images
    is_file = Path(source).suffix[1:] in (IMG_FORMATS + VID_FORMATS)
    is_url = source.lower().startswith(('rtsp://', 'rtmp://', 'http://', 'https://'))
    webcam = source.isnumeric() or source.endswith('.txt') or (is_url and not is_file)
    screenshot = source.lower().startswith('screen')
    if is_url and is_file:
        source = check_file(source)  # download
    
    # 创建保存目录
    save_dir = increment_path(Path(project) / name, exist_ok=exist_ok)  # increment run
    (save_dir / 'labels' if save_txt else save_dir).mkdir(parents=True, exist_ok=True)  # make dir
    
    # 加载模型
    device = select_device(device)
    model = DetectMultiBackend(weights, device=device, dnn=dnn, data=data, fp16=half)
    stride, names, pt = model.stride, model.names, model.pt
    imgsz = check_img_size(imgsz, s=stride)  # check image size
    
    # 数据加载器
    bs = 1  # batch_size
    if webcam:
        raise NotImplementedError('Webcam not supported for multimodal detection')
    else:
        dataset = LoadDualModalImages(source, xpl_source, img_size=imgsz, stride=stride, auto=pt, vid_stride=vid_stride)
    vid_path, vid_writer = [None] * bs, [None] * bs
    
    # 运行推理
    model.warmup(imgsz=(1 if pt or model.triton else bs, 3, *imgsz))  # warmup
    seen, windows, dt = 0, [], (Profile(), Profile(), Profile())
    
    for path, im, im0_shape, vid_cap in dataset:
        rgb_path, xpl_path = path
        rgb_im, xpl_im = im
        
        with dt[0]:
            # 准备输入
            rgb_im = rgb_im.to(device)
            xpl_im = xpl_im.to(device)
            
            # 添加batch维度
            if len(rgb_im.shape) == 3:
                rgb_im = rgb_im[None]  # expand for batch dim
            if len(xpl_im.shape) == 3:
                xpl_im = xpl_im[None]  # expand for batch dim
            
            # 多模态输入
            multimodal_input = (rgb_im, xpl_im)
        
        # 推理
        with dt[1]:
            visualize = increment_path(save_dir / Path(rgb_path).stem, mkdir=True) if visualize else False
            pred = model(multimodal_input, augment=augment, visualize=visualize)
        
        # NMS
        with dt[2]:
            pred = non_max_suppression(pred, conf_thres, iou_thres, classes, agnostic_nms, max_det=max_det)
        
        # 处理检测结果
        for i, det in enumerate(pred):  # per image
            seen += 1
            p, im0, frame = Path(rgb_path), cv2.imread(rgb_path), getattr(dataset, 'frame', 0)
            
            p = Path(p)  # to Path
            save_path = str(save_dir / p.name)  # im.jpg
            txt_path = str(save_dir / 'labels' / p.stem) + ('' if dataset.mode == 'image' else f'_{frame}')  # im.txt
            s = f'{i}: {im0.shape[0]}x{im0.shape[1]} '  # print string
            gn = torch.tensor(im0.shape)[[1, 0, 1, 0]]  # normalization gain whwh
            imc = im0.copy() if save_crop else im0  # for save_crop
            annotator = Annotator(im0, line_width=line_thickness, example=str(names))
            
            if len(det):
                # 重新缩放框从img_size到im0大小
                det[:, :4] = scale_boxes(rgb_im.shape[2:], det[:, :4], im0.shape).round()
                
                # 打印结果
                for c in det[:, 5].unique():
                    n = (det[:, 5] == c).sum()  # detections per class
                    s += f"{n} {names[int(c)]}{'s' * (n > 1)}, "  # add to string
                
                # 写入结果
                for *xyxy, conf, cls in reversed(det):
                    if save_txt:  # Write to file
                        xywh = (xyxy2xywh(torch.tensor(xyxy).view(1, 4)) / gn).view(-1).tolist()  # normalized xywh
                        line = (cls, *xywh, conf) if save_conf else (cls, *xywh)  # label format
                        with open(f'{txt_path}.txt', 'a') as f:
                            f.write(('%g ' * len(line)).rstrip() % line + '\n')
                    
                    if save_img or save_crop or view_img:  # Add bbox to image
                        c = int(cls)  # integer class
                        label = None if hide_labels else (names[c] if hide_conf else f'{names[c]} {conf:.2f}')
                        annotator.box_label(xyxy, label, color=colors(c, True))
                    if save_crop:
                        save_one_box(xyxy, imc, file=save_dir / 'crops' / names[c] / f'{p.stem}.jpg', BGR=True)
            
            # 流式结果
            im0 = annotator.result()
            if view_img:
                if platform.system() == 'Linux' and p not in windows:
                    windows.append(p)
                    cv2.namedWindow(str(p), cv2.WINDOW_NORMAL | cv2.WINDOW_KEEPRATIO)  # allow window resize (Linux)
                    cv2.resizeWindow(str(p), im0.shape[1], im0.shape[0])
                cv2.imshow(str(p), im0)
                cv2.waitKey(1)  # 1 millisecond
            
            # 保存结果（图像带检测）
            if save_img:
                if dataset.mode == 'image':
                    cv2.imwrite(save_path, im0)
                else:  # 'video' or 'stream'
                    if vid_path[i] != save_path:  # new video
                        vid_path[i] = save_path
                        if isinstance(vid_writer[i], cv2.VideoWriter):
                            vid_writer[i].release()  # release previous video writer
                        if vid_cap:  # video
                            fps, w, h = 30, im0.shape[1], im0.shape[0]
                        else:  # stream
                            fps, w, h = 30, im0.shape[1], im0.shape[0]
                        save_path = str(Path(save_path).with_suffix('.mp4'))  # force *.mp4 suffix on results videos
                        vid_writer[i] = cv2.VideoWriter(save_path, cv2.VideoWriter_fourcc(*'mp4v'), fps, (w, h))
                    vid_writer[i].write(im0)
        
        # 打印时间（仅推理）
        LOGGER.info(f"{s}{'' if len(det) else '(no detections), '}{dt[1].dt * 1E3:.1f}ms")
    
    # 打印结果
    t = tuple(x.t / seen * 1E3 for x in dt)  # speeds per image
    LOGGER.info(f'Speed: %.1fms pre-process, %.1fms inference, %.1fms NMS per image at shape {(1, 3, *imgsz)}' % t)
    if save_txt or save_img:
        s = f"\n{len(list(save_dir.glob('labels/*.txt')))} labels saved to {save_dir / 'labels'}" if save_txt else ''
        LOGGER.info(f"Results saved to {colorstr('bold', save_dir)}{s}")
    if update:
        strip_optimizer(weights[0])  # update model (to fix SourceChangeWarning)


def parse_opt():
    parser = argparse.ArgumentParser()
    parser.add_argument('--weights', nargs='+', type=str, default=ROOT / 'yolov5s.pt', help='model path or triton URL')
    parser.add_argument('--source', type=str, default=ROOT / 'data/images', help='RGB images source')
    parser.add_argument('--xpl-source', type=str, required=True, help='XPL images source')
    parser.add_argument('--data', type=str, default=ROOT / 'data/coco128.yaml', help='(optional) dataset.yaml path')
    parser.add_argument('--imgsz', '--img', '--img-size', nargs='+', type=int, default=[640], help='inference size h,w')
    parser.add_argument('--conf-thres', type=float, default=0.25, help='confidence threshold')
    parser.add_argument('--iou-thres', type=float, default=0.45, help='NMS IoU threshold')
    parser.add_argument('--max-det', type=int, default=1000, help='maximum detections per image')
    parser.add_argument('--device', default='', help='cuda device, i.e. 0 or 0,1,2,3 or cpu')
    parser.add_argument('--view-img', action='store_true', help='show results')
    parser.add_argument('--save-txt', action='store_true', help='save results to *.txt')
    parser.add_argument('--save-conf', action='store_true', help='save confidences in --save-txt labels')
    parser.add_argument('--save-crop', action='store_true', help='save cropped prediction boxes')
    parser.add_argument('--nosave', action='store_true', help='do not save images/videos')
    parser.add_argument('--classes', nargs='+', type=int, help='filter by class: --class 0, or --class 0 2 3')
    parser.add_argument('--agnostic-nms', action='store_true', help='class-agnostic NMS')
    parser.add_argument('--augment', action='store_true', help='augmented inference')
    parser.add_argument('--visualize', action='store_true', help='visualize features')
    parser.add_argument('--update', action='store_true', help='update all models')
    parser.add_argument('--project', default=ROOT / 'runs/detect', help='save results to project/name')
    parser.add_argument('--name', default='exp', help='save results to project/name')
    parser.add_argument('--exist-ok', action='store_true', help='existing project/name ok, do not increment')
    parser.add_argument('--line-thickness', default=3, type=int, help='bounding box thickness (pixels)')
    parser.add_argument('--hide-labels', default=False, action='store_true', help='hide labels')
    parser.add_argument('--hide-conf', default=False, action='store_true', help='hide confidences')
    parser.add_argument('--half', action='store_true', help='use FP16 half-precision inference')
    parser.add_argument('--dnn', action='store_true', help='use OpenCV DNN for ONNX inference')
    parser.add_argument('--vid-stride', type=int, default=1, help='video frame-rate stride')
    opt = parser.parse_args()
    opt.imgsz *= 2 if len(opt.imgsz) == 1 else 1  # expand
    print_args(vars(opt))
    return opt


def main(opt):
    check_requirements(exclude=('tensorboard', 'thop'))
    run(**vars(opt))


if __name__ == '__main__':
    opt = parse_opt()
    main(opt)