Image: tile_0103.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8452
  Bounding Box: [960.00, 1379.20, 1326.40, 1795.20]
  Mask Area: 854 pixels
  Mask Ratio: 0.0303
  Mask BBox: [79, 112, 107, 144]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8433
  Bounding Box: [478.80, 1428.00, 646.80, 1595.20]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [42, 116, 54, 127]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8379
  Bounding Box: [1118.40, 56.30, 1360.00, 278.40]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [92, 9, 110, 25]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8359
  Bounding Box: [304.60, 17.80, 604.00, 431.20]
  Mask Area: 690 pixels
  Mask Ratio: 0.0244
  Mask BBox: [28, 6, 51, 37]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8286
  Bounding Box: [725.20, 850.40, 870.40, 1037.60]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [61, 71, 71, 85]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8174
  Bounding Box: [232.20, 664.40, 410.00, 832.00]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [23, 56, 34, 68]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8145
  Bounding Box: [385.60, 1592.00, 496.00, 1752.00]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [35, 129, 42, 140]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8042
  Bounding Box: [124.40, 182.00, 328.40, 466.80]
  Mask Area: 263 pixels
  Mask Ratio: 0.0093
  Mask BBox: [14, 19, 29, 40]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.7925
  Bounding Box: [574.80, 2.50, 729.20, 137.10]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [49, 5, 60, 14]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.7832
  Bounding Box: [1086.40, 266.40, 1238.40, 528.00]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [89, 26, 100, 45]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.7832
  Bounding Box: [0.70, 0.00, 240.00, 184.80]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [5, 4, 21, 18]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.7793
  Bounding Box: [492.80, 496.80, 719.20, 856.80]
  Mask Area: 325 pixels
  Mask Ratio: 0.0115
  Mask BBox: [43, 43, 60, 70]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.7788
  Bounding Box: [1784.00, 1915.20, 2020.80, 2040.00]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [144, 154, 161, 163]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.7788
  Bounding Box: [227.60, 1405.60, 482.80, 1567.20]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [22, 114, 41, 126]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7778
  Bounding Box: [92.20, 1092.00, 276.60, 1330.40]
  Mask Area: 202 pixels
  Mask Ratio: 0.0072
  Mask BBox: [12, 90, 25, 107]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7754
  Bounding Box: [620.00, 414.00, 819.20, 586.00]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [53, 37, 67, 49]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7734
  Bounding Box: [21.30, 1800.00, 342.00, 2048.00]
  Mask Area: 460 pixels
  Mask Ratio: 0.0163
  Mask BBox: [6, 145, 30, 165]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7720
  Bounding Box: [762.40, 1780.80, 980.00, 1956.80]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [64, 144, 80, 155]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7695
  Bounding Box: [732.80, 1513.60, 908.80, 1769.60]
  Mask Area: 172 pixels
  Mask Ratio: 0.0061
  Mask BBox: [62, 123, 74, 142]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7666
  Bounding Box: [1246.40, 173.60, 1548.80, 434.40]
  Mask Area: 330 pixels
  Mask Ratio: 0.0117
  Mask BBox: [102, 18, 124, 37]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7651
  Bounding Box: [160.40, 808.00, 333.20, 926.40]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [17, 68, 30, 76]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7646
  Bounding Box: [899.20, 994.40, 1128.00, 1396.00]
  Mask Area: 423 pixels
  Mask Ratio: 0.0150
  Mask BBox: [75, 82, 92, 113]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7622
  Bounding Box: [377.60, 456.00, 488.80, 600.00]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [34, 40, 42, 49]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7524
  Bounding Box: [811.20, 480.40, 952.00, 633.20]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [68, 42, 77, 53]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7524
  Bounding Box: [87.20, 1460.80, 264.80, 1689.60]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [11, 119, 24, 135]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7520
  Bounding Box: [1423.20, 1012.00, 1622.40, 1274.40]
  Mask Area: 209 pixels
  Mask Ratio: 0.0074
  Mask BBox: [116, 84, 130, 103]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7515
  Bounding Box: [803.20, 1038.40, 968.00, 1185.60]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [67, 86, 78, 96]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7456
  Bounding Box: [809.60, 265.20, 1140.80, 488.40]
  Mask Area: 350 pixels
  Mask Ratio: 0.0124
  Mask BBox: [68, 25, 93, 42]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7446
  Bounding Box: [1550.40, 75.50, 1761.60, 407.60]
  Mask Area: 271 pixels
  Mask Ratio: 0.0096
  Mask BBox: [126, 10, 141, 35]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7407
  Bounding Box: [1590.40, 1235.20, 1721.60, 1368.00]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [129, 101, 136, 110]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7363
  Bounding Box: [398.40, 674.00, 506.40, 825.60]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [36, 57, 43, 68]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7339
  Bounding Box: [1854.40, 1571.20, 2024.00, 1776.00]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [149, 127, 162, 142]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7231
  Bounding Box: [0.00, 1187.20, 223.60, 1468.80]
  Mask Area: 260 pixels
  Mask Ratio: 0.0092
  Mask BBox: [4, 97, 21, 118]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7183
  Bounding Box: [500.80, 835.20, 800.80, 1315.20]
  Mask Area: 649 pixels
  Mask Ratio: 0.0230
  Mask BBox: [44, 70, 66, 106]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7153
  Bounding Box: [1867.20, 780.80, 2043.20, 1084.80]
  Mask Area: 248 pixels
  Mask Ratio: 0.0088
  Mask BBox: [150, 65, 163, 88]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7148
  Bounding Box: [1331.20, 1700.80, 1502.40, 2004.80]
  Mask Area: 236 pixels
  Mask Ratio: 0.0084
  Mask BBox: [108, 137, 121, 159]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7134
  Bounding Box: [1402.40, 1206.40, 1567.20, 1438.40]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [114, 99, 126, 116]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7080
  Bounding Box: [1126.40, 529.20, 1252.80, 768.00]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [92, 46, 101, 63]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7070
  Bounding Box: [1963.20, 482.40, 2046.40, 618.40]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [158, 42, 163, 51]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7070
  Bounding Box: [1325.60, 740.40, 1508.00, 934.40]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [108, 62, 121, 75]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7046
  Bounding Box: [1374.40, 358.40, 1736.00, 684.00]
  Mask Area: 509 pixels
  Mask Ratio: 0.0180
  Mask BBox: [112, 32, 139, 57]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7046
  Bounding Box: [941.60, 659.60, 1152.80, 907.20]
  Mask Area: 237 pixels
  Mask Ratio: 0.0084
  Mask BBox: [78, 56, 94, 74]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7007
  Bounding Box: [11.70, 180.60, 137.70, 374.40]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [5, 19, 14, 33]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.6948
  Bounding Box: [1686.40, 1079.20, 1862.40, 1250.40]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [136, 89, 149, 101]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.6934
  Bounding Box: [1600.00, 1563.20, 1843.20, 1864.00]
  Mask Area: 289 pixels
  Mask Ratio: 0.0102
  Mask BBox: [129, 127, 147, 149]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.6929
  Bounding Box: [952.00, 572.00, 1134.40, 677.60]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [79, 49, 92, 56]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.6924
  Bounding Box: [761.60, 1167.20, 920.00, 1512.80]
  Mask Area: 248 pixels
  Mask Ratio: 0.0088
  Mask BBox: [64, 96, 75, 122]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.6904
  Bounding Box: [996.80, 5.75, 1177.60, 149.20]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [82, 5, 94, 15]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.6870
  Bounding Box: [15.25, 1697.60, 128.40, 1816.00]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [6, 137, 14, 144]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.6836
  Bounding Box: [1769.60, 1331.20, 1894.40, 1560.00]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [143, 108, 151, 124]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.6753
  Bounding Box: [1039.20, 1628.80, 1407.20, 2003.20]
  Mask Area: 639 pixels
  Mask Ratio: 0.0226
  Mask BBox: [86, 132, 113, 160]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.6704
  Bounding Box: [1357.60, 102.80, 1455.20, 236.40]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [111, 13, 117, 22]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.6660
  Bounding Box: [1235.20, 558.00, 1364.80, 736.40]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [101, 48, 110, 61]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.6626
  Bounding Box: [1313.60, 935.20, 1459.20, 1055.20]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [107, 78, 116, 86]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.6621
  Bounding Box: [1404.80, 72.60, 1598.40, 310.60]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [114, 10, 128, 28]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.6602
  Bounding Box: [1732.80, 144.70, 1931.20, 468.80]
  Mask Area: 305 pixels
  Mask Ratio: 0.0108
  Mask BBox: [140, 16, 154, 40]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6582
  Bounding Box: [1583.20, 844.80, 1712.00, 1182.40]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [128, 70, 137, 96]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6538
  Bounding Box: [270.00, 388.00, 372.80, 518.40]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [26, 35, 33, 44]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6499
  Bounding Box: [255.20, 1564.00, 403.20, 1856.00]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [24, 127, 35, 147]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6494
  Bounding Box: [1923.20, 284.40, 2048.00, 453.20]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [155, 27, 164, 39]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6484
  Bounding Box: [1737.60, 12.00, 1971.20, 135.80]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [140, 5, 157, 14]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6475
  Bounding Box: [1505.60, 1681.60, 1633.60, 1940.80]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [122, 136, 130, 155]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6470
  Bounding Box: [1262.40, 552.40, 1406.40, 734.00]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [103, 48, 113, 61]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6440
  Bounding Box: [1964.80, 1840.00, 2048.00, 1955.20]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [158, 148, 163, 156]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6411
  Bounding Box: [192.80, 945.60, 364.80, 1100.80]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [20, 78, 31, 87]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6406
  Bounding Box: [938.40, 457.60, 1154.40, 596.80]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [79, 40, 94, 50]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6377
  Bounding Box: [266.60, 986.40, 558.40, 1368.80]
  Mask Area: 434 pixels
  Mask Ratio: 0.0154
  Mask BBox: [25, 82, 47, 110]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6304
  Bounding Box: [1312.80, 566.00, 1442.40, 742.00]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [107, 49, 115, 61]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6279
  Bounding Box: [10.20, 881.60, 183.00, 1040.00]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [5, 73, 18, 85]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6265
  Bounding Box: [1830.40, 522.80, 1996.80, 678.00]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [147, 45, 159, 56]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6230
  Bounding Box: [47.30, 650.00, 224.80, 843.20]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [8, 55, 21, 69]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6221
  Bounding Box: [1865.60, 1067.20, 2038.40, 1408.00]
  Mask Area: 322 pixels
  Mask Ratio: 0.0114
  Mask BBox: [150, 88, 163, 113]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6216
  Bounding Box: [682.40, 656.00, 809.60, 815.20]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [58, 56, 66, 67]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6069
  Bounding Box: [366.80, 1872.00, 547.60, 2041.60]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [33, 151, 46, 163]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6011
  Bounding Box: [1253.60, 412.80, 1375.20, 540.80]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [102, 37, 110, 46]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.5918
  Bounding Box: [3.60, 433.60, 89.90, 612.80]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [5, 38, 9, 51]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.5913
  Bounding Box: [1723.20, 1792.00, 1873.60, 2006.40]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [139, 144, 150, 160]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.5859
  Bounding Box: [1358.40, 1501.60, 1555.20, 1707.20]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [111, 122, 125, 137]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.5801
  Bounding Box: [986.40, 148.80, 1159.20, 255.60]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [82, 16, 92, 23]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.5801
  Bounding Box: [1771.20, 20.10, 1928.00, 144.50]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [143, 6, 154, 14]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.5791
  Bounding Box: [1726.40, 638.80, 1915.20, 1005.60]
  Mask Area: 384 pixels
  Mask Ratio: 0.0136
  Mask BBox: [139, 54, 153, 82]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.5771
  Bounding Box: [896.00, 139.40, 1048.00, 244.20]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [74, 15, 85, 22]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.5767
  Bounding Box: [486.80, 1729.60, 627.60, 1918.40]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [43, 140, 52, 153]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.5762
  Bounding Box: [0.00, 742.40, 107.40, 872.00]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [4, 62, 12, 72]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.5757
  Bounding Box: [584.00, 1881.60, 804.80, 2048.00]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [50, 151, 66, 164]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.5752
  Bounding Box: [1921.60, 1415.20, 2024.00, 1638.40]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [155, 115, 162, 131]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.5747
  Bounding Box: [1268.80, 716.80, 1336.00, 846.40]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [104, 60, 108, 70]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.5718
  Bounding Box: [346.40, 868.00, 510.40, 967.20]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [32, 72, 43, 79]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.5669
  Bounding Box: [726.40, 0.30, 825.60, 80.50]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [61, 5, 68, 9]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.5640
  Bounding Box: [1351.20, 0.00, 1455.20, 90.50]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [110, 4, 117, 11]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.5615
  Bounding Box: [820.80, 1979.20, 953.60, 2036.80]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [69, 159, 78, 163]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.5552
  Bounding Box: [1624.00, 1808.00, 1742.40, 1961.60]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [131, 146, 140, 156]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5532
  Bounding Box: [47.70, 1112.00, 286.40, 1379.20]
  Mask Area: 283 pixels
  Mask Ratio: 0.0100
  Mask BBox: [8, 91, 26, 111]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5527
  Bounding Box: [1942.40, 1443.20, 2048.00, 1633.60]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [156, 117, 164, 131]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5498
  Bounding Box: [0.00, 1581.60, 87.60, 1678.40]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [4, 128, 10, 135]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5454
  Bounding Box: [153.50, 477.20, 332.80, 706.00]
  Mask Area: 212 pixels
  Mask Ratio: 0.0075
  Mask BBox: [16, 42, 29, 59]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5449
  Bounding Box: [293.20, 1245.60, 457.20, 1407.20]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [27, 102, 39, 113]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5381
  Bounding Box: [1274.40, 1659.20, 1488.80, 2014.40]
  Mask Area: 344 pixels
  Mask Ratio: 0.0122
  Mask BBox: [104, 134, 120, 159]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5347
  Bounding Box: [1417.60, 643.60, 1529.60, 728.40]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [115, 55, 123, 60]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5337
  Bounding Box: [1723.20, 1292.00, 1841.60, 1448.80]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [139, 105, 147, 117]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5269
  Bounding Box: [0.00, 1447.20, 102.60, 1573.60]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [4, 118, 12, 126]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5234
  Bounding Box: [1372.00, 1416.00, 1544.80, 1553.60]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [112, 115, 124, 125]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5234
  Bounding Box: [1876.80, 1391.20, 2014.40, 1554.40]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [151, 113, 161, 125]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5176
  Bounding Box: [200.40, 656.80, 395.60, 900.00]
  Mask Area: 211 pixels
  Mask Ratio: 0.0075
  Mask BBox: [20, 56, 34, 74]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5068
  Bounding Box: [1564.00, 1318.40, 1668.80, 1419.20]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [127, 107, 134, 114]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5044
  Bounding Box: [1942.40, 114.70, 2038.40, 271.60]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [156, 13, 163, 25]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5029
  Bounding Box: [479.20, 436.80, 581.60, 569.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [42, 39, 49, 48]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.4956
  Bounding Box: [6.15, 603.20, 74.20, 734.40]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [5, 52, 8, 61]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.4932
  Bounding Box: [1892.80, 152.70, 1956.80, 233.60]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [152, 16, 156, 22]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.4888
  Bounding Box: [328.20, 844.00, 506.80, 946.40]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [30, 70, 43, 77]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.4878
  Bounding Box: [853.60, 634.80, 951.20, 727.60]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [71, 54, 78, 60]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.4878
  Bounding Box: [1034.40, 900.00, 1117.60, 1004.00]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [85, 75, 91, 81]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.4878
  Bounding Box: [806.40, 3.15, 990.40, 167.40]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [67, 5, 81, 17]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.4849
  Bounding Box: [1528.80, 0.00, 1691.20, 102.20]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [124, 4, 136, 11]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.4824
  Bounding Box: [1528.00, 1968.00, 1681.60, 2028.80]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [124, 158, 135, 162]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.4753
  Bounding Box: [292.20, 1584.80, 481.60, 1798.40]
  Mask Area: 206 pixels
  Mask Ratio: 0.0073
  Mask BBox: [27, 128, 41, 144]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.4727
  Bounding Box: [1691.20, 283.00, 1752.00, 411.20]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [137, 27, 140, 35]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.4702
  Bounding Box: [1841.60, 1525.60, 1905.60, 1612.80]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [148, 124, 152, 129]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.4702
  Bounding Box: [1737.60, 1283.20, 1868.80, 1512.00]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [140, 105, 149, 122]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.4692
  Bounding Box: [1684.80, 1189.60, 1867.20, 1300.00]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [136, 97, 148, 105]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.4609
  Bounding Box: [644.40, 616.00, 815.20, 826.40]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [55, 53, 67, 68]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.4595
  Bounding Box: [1672.00, 1908.80, 1755.20, 2011.20]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [135, 154, 140, 161]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.4585
  Bounding Box: [1536.80, 643.60, 1763.20, 833.60]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [125, 55, 141, 69]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.4541
  Bounding Box: [1233.60, 370.40, 1401.60, 550.40]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [101, 33, 113, 46]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.4529
  Bounding Box: [1514.40, 1444.80, 1656.00, 1596.80]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [123, 117, 132, 128]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.4485
  Bounding Box: [448.00, 1716.80, 540.80, 1860.80]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [39, 139, 45, 149]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.4426
  Bounding Box: [832.80, 1745.60, 936.80, 1809.60]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [70, 141, 76, 144]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4421
  Bounding Box: [1312.00, 10.60, 1438.40, 99.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [107, 5, 116, 11]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.4412
  Bounding Box: [1576.00, 1560.80, 1646.40, 1660.80]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [128, 126, 132, 133]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4407
  Bounding Box: [51.40, 451.60, 317.00, 699.60]
  Mask Area: 321 pixels
  Mask Ratio: 0.0114
  Mask BBox: [9, 40, 28, 58]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4387
  Bounding Box: [1434.40, 844.80, 1560.80, 979.20]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [117, 70, 125, 80]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4341
  Bounding Box: [940.80, 502.40, 1144.00, 677.60]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [79, 44, 93, 56]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4338
  Bounding Box: [362.40, 440.00, 507.20, 669.60]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [33, 39, 43, 56]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4297
  Bounding Box: [382.40, 438.80, 548.80, 590.00]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [34, 39, 46, 50]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4285
  Bounding Box: [1166.40, 1032.00, 1430.40, 1416.00]
  Mask Area: 416 pixels
  Mask Ratio: 0.0147
  Mask BBox: [96, 85, 115, 114]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4282
  Bounding Box: [1320.00, 748.00, 1529.60, 1013.60]
  Mask Area: 242 pixels
  Mask Ratio: 0.0086
  Mask BBox: [108, 63, 123, 83]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4268
  Bounding Box: [1560.00, 1272.00, 1707.20, 1408.00]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [126, 104, 137, 113]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4231
  Bounding Box: [1972.80, 617.60, 2046.40, 778.40]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [159, 53, 163, 64]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4211
  Bounding Box: [358.40, 821.60, 493.60, 896.80]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [32, 69, 42, 74]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4211
  Bounding Box: [1819.20, 1728.00, 1969.60, 1904.00]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [147, 139, 157, 152]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4182
  Bounding Box: [1929.60, 59.30, 2035.20, 156.80]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [155, 9, 162, 16]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4148
  Bounding Box: [1705.60, 720.80, 1907.20, 1076.00]
  Mask Area: 331 pixels
  Mask Ratio: 0.0117
  Mask BBox: [138, 61, 152, 88]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4136
  Bounding Box: [1509.60, 1705.60, 1704.00, 1964.80]
  Mask Area: 217 pixels
  Mask Ratio: 0.0077
  Mask BBox: [122, 138, 137, 157]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4116
  Bounding Box: [1479.20, 553.60, 1652.80, 695.20]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [120, 48, 133, 58]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4106
  Bounding Box: [1636.80, 1848.00, 1755.20, 1998.40]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [132, 149, 141, 160]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4087
  Bounding Box: [1600.00, 809.60, 1686.40, 896.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [129, 68, 135, 73]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4072
  Bounding Box: [1984.00, 1651.20, 2041.60, 1785.60]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [159, 133, 163, 143]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4031
  Bounding Box: [760.80, 0.00, 944.80, 171.00]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [64, 4, 77, 17]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.3979
  Bounding Box: [391.20, 624.40, 582.40, 822.40]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [35, 53, 49, 68]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.3923
  Bounding Box: [1224.80, 715.20, 1317.60, 833.60]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [100, 60, 106, 69]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.3896
  Bounding Box: [1524.80, 670.80, 1729.60, 799.20]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [124, 57, 137, 66]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.3892
  Bounding Box: [1348.80, 1980.80, 1481.60, 2041.60]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [110, 159, 119, 162]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.3887
  Bounding Box: [602.40, 1862.40, 764.80, 2022.40]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [52, 150, 63, 161]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.3882
  Bounding Box: [1910.40, 23.50, 2041.60, 146.90]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [154, 6, 163, 15]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.3872
  Bounding Box: [1606.40, 820.80, 1667.20, 888.00]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [130, 69, 134, 73]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.3831
  Bounding Box: [1147.20, 532.00, 1308.80, 746.40]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [94, 46, 106, 62]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.3767
  Bounding Box: [1484.80, 3.80, 1643.20, 97.20]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [120, 5, 132, 11]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.3762
  Bounding Box: [1442.40, 0.40, 1578.40, 94.50]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [117, 5, 127, 11]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.3752
  Bounding Box: [245.60, 389.20, 365.20, 578.00]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [24, 35, 32, 49]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.3743
  Bounding Box: [1664.00, 1066.40, 1840.00, 1216.80]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [134, 88, 147, 99]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.3728
  Bounding Box: [915.20, 136.20, 1147.20, 248.60]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [76, 15, 92, 23]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.3726
  Bounding Box: [0.00, 1014.40, 86.90, 1184.00]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [4, 84, 10, 96]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.3706
  Bounding Box: [944.80, 1976.00, 1055.20, 2033.60]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [78, 159, 86, 162]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.3689
  Bounding Box: [1718.40, 216.40, 1964.80, 580.40]
  Mask Area: 398 pixels
  Mask Ratio: 0.0141
  Mask BBox: [139, 21, 157, 49]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.3684
  Bounding Box: [1348.00, 101.00, 1606.40, 360.80]
  Mask Area: 244 pixels
  Mask Ratio: 0.0086
  Mask BBox: [110, 12, 129, 32]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.3635
  Bounding Box: [877.60, 2.55, 1013.60, 139.40]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [73, 5, 83, 14]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.3625
  Bounding Box: [1697.60, 824.00, 1870.40, 1048.00]
  Mask Area: 198 pixels
  Mask Ratio: 0.0070
  Mask BBox: [137, 69, 150, 85]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.3604
  Bounding Box: [1913.60, 230.40, 1977.60, 344.80]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [154, 22, 158, 30]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.3594
  Bounding Box: [974.40, 1984.00, 1100.80, 2044.80]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [81, 159, 89, 163]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.3584
  Bounding Box: [1435.20, 526.40, 1681.60, 688.00]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [117, 46, 135, 57]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.3562
  Bounding Box: [824.00, 1952.00, 939.20, 2025.60]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [69, 157, 77, 162]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [1408.80, 462.80, 1694.40, 704.40]
  Mask Area: 307 pixels
  Mask Ratio: 0.0109
  Mask BBox: [115, 41, 136, 59]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3552
  Bounding Box: [1667.20, 984.00, 1747.20, 1096.00]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [135, 81, 140, 89]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3552
  Bounding Box: [1566.40, 625.60, 1803.20, 871.20]
  Mask Area: 261 pixels
  Mask Ratio: 0.0092
  Mask BBox: [127, 53, 144, 72]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3550
  Bounding Box: [70.60, 836.00, 164.00, 914.40]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [10, 70, 15, 74]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3550
  Bounding Box: [1468.80, 1451.20, 1656.00, 1649.60]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [119, 118, 133, 132]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3545
  Bounding Box: [627.60, 1485.60, 892.00, 1801.60]
  Mask Area: 379 pixels
  Mask Ratio: 0.0134
  Mask BBox: [54, 121, 73, 144]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3523
  Bounding Box: [753.60, 214.40, 873.60, 341.20]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [63, 21, 72, 30]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3523
  Bounding Box: [13.65, 419.20, 195.20, 618.40]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [6, 37, 19, 52]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3518
  Bounding Box: [1750.40, 1760.00, 1920.00, 1971.20]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [141, 142, 153, 157]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3477
  Bounding Box: [1481.60, 1944.00, 1630.40, 2030.40]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [120, 156, 131, 162]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3469
  Bounding Box: [1833.60, 1537.60, 1920.00, 1622.40]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [148, 125, 153, 130]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3459
  Bounding Box: [549.20, 869.60, 775.20, 1248.80]
  Mask Area: 462 pixels
  Mask Ratio: 0.0164
  Mask BBox: [47, 72, 64, 101]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3433
  Bounding Box: [1900.80, 1606.40, 2048.00, 1788.80]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [153, 130, 165, 143]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3396
  Bounding Box: [616.00, 125.90, 835.20, 374.00]
  Mask Area: 304 pixels
  Mask Ratio: 0.0108
  Mask BBox: [53, 14, 69, 33]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3389
  Bounding Box: [1705.60, 1265.60, 1852.80, 1478.40]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [138, 103, 148, 119]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3386
  Bounding Box: [1971.20, 969.60, 2041.60, 1104.00]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [158, 80, 163, 90]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3369
  Bounding Box: [1817.60, 464.00, 2022.40, 670.40]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [146, 41, 161, 56]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3367
  Bounding Box: [693.60, 445.20, 972.00, 609.20]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [59, 39, 79, 51]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3364
  Bounding Box: [1316.00, 1060.00, 1426.40, 1164.00]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [107, 87, 115, 94]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3345
  Bounding Box: [1249.60, 1985.60, 1355.20, 2043.20]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [102, 160, 109, 163]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3345
  Bounding Box: [1284.80, 1988.80, 1400.00, 2043.20]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [105, 160, 113, 163]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3333
  Bounding Box: [742.80, 0.00, 860.80, 113.50]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [63, 4, 71, 12]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3289
  Bounding Box: [818.40, 971.20, 994.40, 1184.00]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [68, 80, 81, 96]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3254
  Bounding Box: [1961.60, 436.80, 2032.00, 516.80]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [158, 39, 162, 44]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [370.40, 654.00, 491.20, 809.60]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [33, 56, 42, 67]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [1343.20, 121.00, 1440.80, 253.80]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [109, 14, 116, 22]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [1827.20, 1548.80, 1990.40, 1756.80]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [147, 125, 159, 141]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [625.60, 171.20, 786.40, 382.00]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [53, 18, 65, 33]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3218
  Bounding Box: [1953.60, 1800.00, 2040.00, 1937.60]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [157, 145, 163, 155]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3213
  Bounding Box: [860.80, 1411.20, 961.60, 1584.00]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [72, 115, 79, 127]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3184
  Bounding Box: [608.00, 1355.20, 792.80, 1660.80]
  Mask Area: 246 pixels
  Mask Ratio: 0.0087
  Mask BBox: [52, 110, 65, 133]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3179
  Bounding Box: [1429.60, 882.40, 1533.60, 1005.60]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [116, 73, 123, 82]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3169
  Bounding Box: [1349.60, 1420.00, 1551.20, 1675.20]
  Mask Area: 241 pixels
  Mask Ratio: 0.0085
  Mask BBox: [110, 115, 125, 134]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3167
  Bounding Box: [449.60, 1443.20, 619.20, 1614.40]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [41, 117, 52, 127]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3154
  Bounding Box: [29.00, 1688.00, 186.20, 1812.80]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [7, 136, 18, 144]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3149
  Bounding Box: [1244.00, 749.60, 1336.80, 853.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [102, 63, 108, 70]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3149
  Bounding Box: [1269.60, 749.60, 1362.40, 853.60]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [104, 63, 108, 70]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3137
  Bounding Box: [1036.80, 546.40, 1264.00, 729.60]
  Mask Area: 187 pixels
  Mask Ratio: 0.0066
  Mask BBox: [85, 47, 102, 60]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3123
  Bounding Box: [1322.40, 1075.20, 1439.20, 1192.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [108, 88, 116, 97]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [274.00, 652.00, 515.60, 824.80]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [26, 55, 43, 68]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3081
  Bounding Box: [1364.80, 123.60, 1473.60, 254.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [111, 14, 116, 23]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3079
  Bounding Box: [1499.20, 310.20, 1584.00, 379.20]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [122, 29, 127, 33]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [1551.20, 1296.00, 1656.00, 1403.20]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [126, 106, 133, 113]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [1803.20, 8.10, 2011.20, 141.90]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [145, 5, 161, 15]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3062
  Bounding Box: [1119.20, 756.80, 1199.20, 912.00]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [92, 64, 97, 74]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3057
  Bounding Box: [370.80, 699.60, 506.80, 952.00]
  Mask Area: 172 pixels
  Mask Ratio: 0.0061
  Mask BBox: [33, 59, 43, 78]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [1238.40, 435.20, 1355.20, 555.20]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [101, 38, 109, 45]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3010
  Bounding Box: [709.20, 178.40, 877.60, 356.00]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [60, 18, 72, 31]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3010
  Bounding Box: [1728.00, 1841.60, 1968.00, 2043.20]
  Mask Area: 228 pixels
  Mask Ratio: 0.0081
  Mask BBox: [139, 148, 157, 163]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.2988
  Bounding Box: [1468.80, 1945.60, 1577.60, 2035.20]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [119, 156, 127, 162]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.2983
  Bounding Box: [1278.40, 722.40, 1355.20, 824.80]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [104, 61, 108, 68]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.2966
  Bounding Box: [1104.00, 884.80, 1316.80, 1144.00]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [91, 74, 106, 93]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.2961
  Bounding Box: [819.20, 783.20, 1044.80, 1050.40]
  Mask Area: 322 pixels
  Mask Ratio: 0.0114
  Mask BBox: [68, 66, 85, 86]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.2930
  Bounding Box: [705.20, 91.90, 910.40, 352.00]
  Mask Area: 259 pixels
  Mask Ratio: 0.0092
  Mask BBox: [60, 12, 75, 31]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.2927
  Bounding Box: [1218.40, 836.00, 1352.80, 959.20]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [100, 70, 109, 78]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.2925
  Bounding Box: [1518.40, 1526.40, 1662.40, 1657.60]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [123, 124, 132, 133]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.2910
  Bounding Box: [868.00, 118.20, 1012.00, 230.60]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [72, 14, 83, 22]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.2905
  Bounding Box: [1250.40, 726.00, 1330.40, 823.20]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [102, 61, 107, 68]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.2898
  Bounding Box: [431.60, 1700.80, 600.40, 1896.00]
  Mask Area: 159 pixels
  Mask Ratio: 0.0056
  Mask BBox: [38, 137, 50, 152]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.2891
  Bounding Box: [588.00, 1565.60, 826.40, 1902.40]
  Mask Area: 368 pixels
  Mask Ratio: 0.0130
  Mask BBox: [50, 127, 68, 152]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.2886
  Bounding Box: [1119.20, 926.40, 1389.60, 1337.60]
  Mask Area: 455 pixels
  Mask Ratio: 0.0161
  Mask BBox: [92, 77, 112, 108]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.2871
  Bounding Box: [1333.60, 585.20, 1490.40, 759.60]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [109, 50, 120, 63]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.2866
  Bounding Box: [844.80, 1971.20, 964.80, 2048.00]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [70, 158, 79, 163]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.2866
  Bounding Box: [819.20, 1996.80, 939.20, 2048.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [68, 160, 77, 163]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.2866
  Bounding Box: [1595.20, 882.40, 1816.00, 1106.40]
  Mask Area: 254 pixels
  Mask Ratio: 0.0090
  Mask BBox: [129, 73, 145, 90]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.2847
  Bounding Box: [140.60, 783.20, 307.80, 900.00]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [15, 66, 28, 74]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.2842
  Bounding Box: [2.85, 421.60, 133.60, 590.40]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [5, 37, 14, 50]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [0.00, 588.80, 92.00, 699.20]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [4, 50, 8, 58]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.2805
  Bounding Box: [1544.00, 0.65, 1704.00, 159.20]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [125, 5, 137, 16]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.2793
  Bounding Box: [9.90, 169.20, 66.50, 278.80]
  Mask Area: 16 pixels
  Mask Ratio: 0.0006
  Mask BBox: [5, 18, 8, 25]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.2791
  Bounding Box: [1578.40, 1289.60, 1678.40, 1404.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [128, 105, 135, 113]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.2783
  Bounding Box: [388.80, 1312.00, 508.00, 1430.40]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [35, 107, 43, 115]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.2778
  Bounding Box: [1659.20, 1936.00, 1742.40, 2025.60]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [134, 156, 140, 162]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.2778
  Bounding Box: [1684.80, 1936.00, 1768.00, 2025.60]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [136, 156, 142, 162]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.2778
  Bounding Box: [200.80, 14.90, 271.20, 105.90]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [20, 6, 25, 12]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.2764
  Bounding Box: [832.00, 451.60, 980.80, 603.60]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [69, 41, 80, 51]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.2751
  Bounding Box: [1940.80, 987.20, 2048.00, 1104.00]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [158, 82, 164, 90]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.2751
  Bounding Box: [1636.80, 1772.80, 1835.20, 1990.40]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [132, 143, 147, 159]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.2747
  Bounding Box: [1696.00, 16.65, 1881.60, 142.40]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [137, 6, 150, 15]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.2734
  Bounding Box: [532.00, 1939.20, 609.60, 2032.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [46, 156, 51, 162]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.2727
  Bounding Box: [6.45, 1184.00, 130.80, 1337.60]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [5, 97, 13, 106]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.2725
  Bounding Box: [1752.00, 1348.80, 1870.40, 1593.60]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [141, 110, 150, 124]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.2717
  Bounding Box: [1524.80, 575.20, 1662.40, 688.00]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [124, 49, 133, 57]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.2700
  Bounding Box: [884.80, 1976.00, 1022.40, 2027.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [74, 159, 83, 162]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.2690
  Bounding Box: [1972.80, 1680.00, 2043.20, 1833.60]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [159, 136, 163, 147]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.2673
  Bounding Box: [294.40, 366.00, 398.40, 487.60]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [27, 33, 35, 42]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.2673
  Bounding Box: [1960.00, 1760.00, 2036.80, 1856.00]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [158, 142, 163, 148]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.2671
  Bounding Box: [178.00, 911.20, 357.60, 1066.40]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [18, 76, 31, 87]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.2654
  Bounding Box: [13.35, 1075.20, 73.60, 1198.40]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [6, 88, 9, 97]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.2651
  Bounding Box: [1384.80, 1448.80, 1656.00, 1686.40]
  Mask Area: 253 pixels
  Mask Ratio: 0.0090
  Mask BBox: [113, 118, 133, 135]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.2639
  Bounding Box: [1684.80, 1132.80, 1912.00, 1316.80]
  Mask Area: 204 pixels
  Mask Ratio: 0.0072
  Mask BBox: [136, 93, 153, 106]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2637
  Bounding Box: [1519.20, 812.00, 1600.00, 988.00]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [123, 68, 128, 81]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2632
  Bounding Box: [936.80, 586.00, 1124.00, 710.00]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [79, 50, 91, 56]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2629
  Bounding Box: [1561.60, 1552.80, 1635.20, 1648.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [126, 126, 131, 132]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2629
  Bounding Box: [1587.20, 1552.80, 1660.80, 1648.00]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [128, 126, 132, 132]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2629
  Bounding Box: [1561.60, 1578.40, 1635.20, 1673.60]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [126, 128, 131, 134]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2629
  Bounding Box: [1587.20, 1578.40, 1660.80, 1673.60]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [128, 128, 132, 134]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2622
  Bounding Box: [1587.20, 1828.80, 1750.40, 2011.20]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [128, 147, 140, 161]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2620
  Bounding Box: [1283.20, 1310.40, 1414.40, 1486.40]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [105, 107, 114, 120]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2617
  Bounding Box: [582.40, 392.00, 785.60, 569.60]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [50, 35, 65, 48]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2603
  Bounding Box: [1323.20, 959.20, 1460.80, 1098.40]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [108, 79, 118, 89]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2593
  Bounding Box: [1739.20, 374.80, 1960.00, 609.20]
  Mask Area: 238 pixels
  Mask Ratio: 0.0084
  Mask BBox: [140, 34, 157, 51]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2588
  Bounding Box: [1547.20, 1312.80, 1672.00, 1508.00]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [125, 107, 134, 121]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2571
  Bounding Box: [627.20, 282.40, 764.00, 388.00]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [53, 27, 63, 34]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2563
  Bounding Box: [29.75, 1712.00, 145.00, 1833.60]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [7, 138, 15, 147]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2563
  Bounding Box: [1246.40, 885.60, 1347.20, 1004.00]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [102, 74, 109, 82]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2561
  Bounding Box: [1873.60, 129.00, 1947.20, 214.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [151, 15, 156, 20]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2561
  Bounding Box: [1899.20, 129.00, 1972.80, 214.20]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [153, 15, 156, 20]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2561
  Bounding Box: [732.00, 582.40, 839.20, 684.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [62, 50, 69, 57]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2554
  Bounding Box: [1876.80, 151.20, 1940.80, 236.00]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [151, 16, 155, 22]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2549
  Bounding Box: [845.60, 1992.00, 964.00, 2048.00]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [71, 160, 79, 163]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2546
  Bounding Box: [177.60, 834.40, 353.60, 1012.00]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [18, 70, 31, 83]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2529
  Bounding Box: [1646.40, 0.60, 1758.40, 122.20]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [133, 5, 141, 13]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2520
  Bounding Box: [501.20, 1404.00, 682.80, 1580.00]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [44, 114, 57, 127]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2520
  Bounding Box: [1668.80, 262.40, 1742.40, 385.60]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [135, 25, 140, 34]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2520
  Bounding Box: [1694.40, 262.40, 1768.00, 385.60]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [137, 25, 141, 34]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2520
  Bounding Box: [772.80, 1915.20, 889.60, 2014.40]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [65, 154, 73, 161]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2515
  Bounding Box: [402.80, 1321.60, 526.00, 1456.00]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [36, 108, 45, 117]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2505
  Bounding Box: [1768.00, 662.40, 1905.60, 921.60]
  Mask Area: 200 pixels
  Mask Ratio: 0.0071
  Mask BBox: [143, 56, 152, 75]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2469
  Bounding Box: [0.00, 859.20, 156.00, 1017.60]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [3, 72, 16, 83]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2469
  Bounding Box: [1704.00, 1272.00, 1816.00, 1428.80]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [138, 104, 145, 115]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2452
  Bounding Box: [1155.20, 777.60, 1236.80, 902.40]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [95, 65, 100, 74]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2448
  Bounding Box: [765.20, 1900.80, 858.40, 2028.80]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [64, 153, 71, 162]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2444
  Bounding Box: [207.20, 929.60, 425.60, 1238.40]
  Mask Area: 237 pixels
  Mask Ratio: 0.0084
  Mask BBox: [21, 77, 37, 100]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2440
  Bounding Box: [1769.60, 1003.20, 1852.80, 1083.20]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [143, 83, 148, 88]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2415
  Bounding Box: [1673.60, 284.40, 1737.60, 415.60]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [135, 27, 139, 36]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2413
  Bounding Box: [1188.00, 0.00, 1301.60, 60.00]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [97, 4, 105, 8]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2413
  Bounding Box: [1217.60, 1158.40, 1435.20, 1510.40]
  Mask Area: 331 pixels
  Mask Ratio: 0.0117
  Mask BBox: [100, 95, 116, 121]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2405
  Bounding Box: [233.80, 904.80, 337.80, 978.40]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [24, 75, 30, 78]

