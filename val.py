# -*- coding: utf-8 -*-

from pathlib import Path
import sys
import yaml

# 保证可以导入 segment/val.py
FILE = Path(__file__).resolve()
ROOT = FILE.parent
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

from segment.val import run as val_run  # noqa: E402


def load_yaml(yaml_path: Path) -> dict:
    with open(yaml_path, "r", encoding="utf-8") as f:
        return yaml.safe_load(f)


def pick_xpl_path(ds_cfg: dict, split: str) -> str:
    # 多模态时，从yaml选择XPL路径：path + train_xpl/val_xpl
    base = Path(ds_cfg.get("path", "."))
    key = "train_xpl" if split == "train" else "val_xpl"
    rel = ds_cfg.get(key, None)
    if not rel:
        return ""
    return str((base / rel).resolve())


def main():
    # --- 配置参数 ---
    weights = "runs/train-seg/seg_experiment3/weights/best.pt"  # 模型权重路径
    data = "dataset-seg/datasets.yaml"  # 数据集YAML路径
    task = "val"  # 'train' 或 'val'
    imgsz = 640  # 推理尺寸
    batch_size = 1  # 批处理大小
    device = ""  # CUDA设备, e.g., 0 or 0,1,2,3 or cpu
    workers = 4  # Dataloader工作进程数
    single_cls = False  # 是否为单类别数据集
    save_txt = True  # 保存结果到 *.txt
    save_conf = True  # 在 --save-txt 中保存置信度
    project = "runs/val-seg"  # 结果保存的项目目录
    name = "simple_val"  # 结果保存的目录名
    exist_ok = False  # 是否覆盖已存在的 project/name 目录
    # --- 配置结束 ---

    data_path = Path(data).resolve()
    assert data_path.exists(), f"dataset yaml not found: {data_path}"

    ds_cfg = load_yaml(data_path)
    multimodal = bool(ds_cfg.get("multimodal", False))
    xpl_path = pick_xpl_path(ds_cfg, task) if multimodal else None

    # 调用 segment/val.py 的 run
    results = val_run(
        data=str(data_path),
        weights=str(Path(weights).resolve()),
        batch_size=batch_size,
        imgsz=imgsz,
        conf_thres=0.001,
        iou_thres=0.6,
        max_det=300,
        task=task,
        device=device,
        workers=workers,
        single_cls=single_cls,
        augment=False,
        verbose=False,
        save_txt=save_txt,
        save_hybrid=False,
        save_conf=save_conf,
        save_json=False,
        project=str(Path(project)),
        name=name,
        exist_ok=exist_ok,
        half=True,
        dnn=False,
        model=None,
        dataloader=None,
        plots=True,
        overlap=False,
        mask_downsample_ratio=1,
        compute_loss=None,
        multimodal=multimodal,
        xpl_path=xpl_path,
    )

    # 打印关键指标和保存路径信息
    if results:
        (mp_bbox, mr_bbox, map50_bbox, map_bbox,
         mp_mask, mr_mask, map50_mask, map_mask, *rest) = results[0]
        print("Validation done. Key metrics:")
        print(f"- Box:  P={mp_bbox:.3f}, R={mr_bbox:.3f}, mAP50={map50_bbox:.3f}, mAP50-95={map_bbox:.3f}")
        print(f"- Mask: P={mp_mask:.3f}, R={mr_mask:.3f}, mAP50={map50_mask:.3f}, mAP50-95={map_mask:.3f}")
        
        # 打印保存路径信息
        save_dir = Path(project) / name
        print(f"\nResults saved to: {save_dir}")
        if save_txt:
            print(f"- TXT results: {save_dir / 'labels'}")
        print(f"- Visualization images: {save_dir}")
        if multimodal:
            print(f"- Multimodal mode: RGB + XPL from {xpl_path}")


if __name__ == "__main__":
    main()

