# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license
"""回调工具模块。"""

import threading


class Callbacks:
    """处理YOLOv5钩子的所有已注册回调函数。"""

    def __init__(self):
        """初始化Callbacks对象以管理已注册的YOLOv5训练事件钩子。"""
        self._callbacks = {
            "on_pretrain_routine_start": [],  # 预训练例程开始
            "on_pretrain_routine_end": [],   # 预训练例程结束
            "on_train_start": [],            # 训练开始
            "on_train_epoch_start": [],      # 训练轮次开始
            "on_train_batch_start": [],      # 训练批次开始
            "optimizer_step": [],            # 优化器步骤
            "on_before_zero_grad": [],       # 梯度清零前
            "on_train_batch_end": [],        # 训练批次结束
            "on_train_epoch_end": [],        # 训练轮次结束
            "on_val_start": [],              # 验证开始
            "on_val_batch_start": [],        # 验证批次开始
            "on_val_image_end": [],          # 验证图像结束
            "on_val_batch_end": [],          # 验证批次结束
            "on_val_end": [],                # 验证结束
            "on_fit_epoch_end": [],          # 拟合轮次结束（fit = train + val）
            "on_model_save": [],             # 模型保存
            "on_train_end": [],              # 训练结束
            "on_params_update": [],          # 参数更新
            "teardown": [],                  # 清理
        }
        self.stop_training = False  # 设置为True以中断训练

    def register_action(self, hook, name="", callback=None):
        """
        向回调钩子注册新的动作。

        参数:
            hook: 要注册动作的回调钩子名称
            name: 动作的名称，用于后续引用
            callback: 要触发的回调函数
        """
        assert hook in self._callbacks, f"钩子 '{hook}' 在回调中未找到 {self._callbacks}"
        assert callable(callback), f"回调 '{callback}' 不可调用"
        self._callbacks[hook].append({"name": name, "callback": callback})

    def get_registered_actions(self, hook=None):
        """
        返回按回调钩子分类的所有已注册动作。

        参数:
            hook: 要检查的钩子名称，默认为全部
        """
        return self._callbacks[hook] if hook else self._callbacks

    def run(self, hook, *args, thread=False, **kwargs):
        """
        遍历已注册的动作并在主线程上触发所有回调。

        参数:
            hook: 要检查的钩子名称，默认为全部
            args: 从YOLOv5接收的参数
            thread: (布尔值) 在守护线程中运行回调
            kwargs: 从YOLOv5接收的关键字参数
        """
        assert hook in self._callbacks, f"钩子 '{hook}' 在回调中未找到 {self._callbacks}"
        for logger in self._callbacks[hook]:
            if thread:
                threading.Thread(target=logger["callback"], args=args, kwargs=kwargs, daemon=True).start()
            else:
                logger["callback"](*args, **kwargs)
