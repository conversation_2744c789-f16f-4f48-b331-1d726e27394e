Image: tile_0056.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8667
  Bounding Box: [1235.20, 1368.80, 1438.40, 1660.80]
  Mask Area: 338 pixels
  Mask Ratio: 0.0120
  Mask BBox: [101, 111, 116, 133]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8604
  Bounding Box: [1319.20, 1095.20, 1546.40, 1314.40]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [108, 90, 124, 106]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8584
  Bounding Box: [436.40, 20.50, 796.80, 339.60]
  Mask Area: 501 pixels
  Mask Ratio: 0.0178
  Mask BBox: [39, 6, 66, 30]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8467
  Bounding Box: [915.20, 1440.00, 1113.60, 1664.00]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [76, 117, 90, 133]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8389
  Bounding Box: [1412.00, 1560.00, 1586.40, 1777.60]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [115, 126, 127, 142]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8369
  Bounding Box: [1680.00, 1.20, 2048.00, 366.00]
  Mask Area: 724 pixels
  Mask Ratio: 0.0257
  Mask BBox: [136, 5, 164, 32]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8354
  Bounding Box: [115.80, 662.80, 393.60, 910.40]
  Mask Area: 318 pixels
  Mask Ratio: 0.0113
  Mask BBox: [14, 56, 34, 74]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8345
  Bounding Box: [967.20, 821.60, 1104.80, 1000.80]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [80, 69, 90, 82]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8345
  Bounding Box: [1384.80, 1830.40, 1517.60, 1984.00]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [113, 147, 122, 157]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8296
  Bounding Box: [1673.60, 1114.40, 1862.40, 1391.20]
  Mask Area: 206 pixels
  Mask Ratio: 0.0073
  Mask BBox: [135, 92, 148, 111]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8296
  Bounding Box: [39.00, 1712.00, 371.20, 2044.80]
  Mask Area: 450 pixels
  Mask Ratio: 0.0159
  Mask BBox: [8, 138, 32, 163]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8223
  Bounding Box: [1026.40, 1664.00, 1229.60, 1891.20]
  Mask Area: 203 pixels
  Mask Ratio: 0.0072
  Mask BBox: [85, 134, 100, 151]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.8179
  Bounding Box: [1366.40, 585.60, 1536.00, 780.00]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [111, 50, 122, 64]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.8159
  Bounding Box: [1358.40, 284.80, 1560.00, 562.40]
  Mask Area: 251 pixels
  Mask Ratio: 0.0089
  Mask BBox: [111, 27, 125, 46]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.8120
  Bounding Box: [1756.80, 1768.00, 1913.60, 1976.00]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [142, 143, 153, 158]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.8086
  Bounding Box: [1556.80, 1737.60, 1761.60, 1980.80]
  Mask Area: 235 pixels
  Mask Ratio: 0.0083
  Mask BBox: [126, 140, 141, 158]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.8076
  Bounding Box: [1339.20, 941.60, 1537.60, 1128.80]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [109, 78, 124, 92]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.8071
  Bounding Box: [763.20, 1715.20, 1025.60, 2016.00]
  Mask Area: 327 pixels
  Mask Ratio: 0.0116
  Mask BBox: [64, 138, 84, 161]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.8042
  Bounding Box: [1407.20, 1312.00, 1624.00, 1528.00]
  Mask Area: 244 pixels
  Mask Ratio: 0.0086
  Mask BBox: [114, 107, 130, 123]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7993
  Bounding Box: [1.95, 1625.60, 132.20, 1795.20]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [5, 131, 13, 144]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7959
  Bounding Box: [787.20, 1104.00, 1028.80, 1315.20]
  Mask Area: 176 pixels
  Mask Ratio: 0.0062
  Mask BBox: [66, 91, 84, 106]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7930
  Bounding Box: [80.00, 11.20, 253.60, 168.40]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [11, 5, 23, 17]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7920
  Bounding Box: [1113.60, 470.80, 1260.80, 684.40]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [91, 41, 102, 56]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7920
  Bounding Box: [637.60, 1405.60, 840.80, 1592.00]
  Mask Area: 187 pixels
  Mask Ratio: 0.0066
  Mask BBox: [55, 114, 69, 127]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7900
  Bounding Box: [176.60, 147.80, 292.60, 322.20]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [18, 16, 26, 29]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7852
  Bounding Box: [689.60, 1883.20, 812.00, 2036.80]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [58, 152, 67, 162]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7808
  Bounding Box: [0.00, 497.60, 134.80, 725.60]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [4, 44, 14, 60]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7803
  Bounding Box: [615.20, 281.80, 894.40, 469.60]
  Mask Area: 217 pixels
  Mask Ratio: 0.0077
  Mask BBox: [53, 27, 72, 40]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7778
  Bounding Box: [1864.00, 1105.60, 2048.00, 1376.00]
  Mask Area: 227 pixels
  Mask Ratio: 0.0080
  Mask BBox: [150, 91, 164, 111]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7749
  Bounding Box: [1659.20, 604.80, 1768.00, 702.40]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [134, 52, 142, 58]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7744
  Bounding Box: [483.20, 1649.60, 630.40, 1860.80]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [42, 133, 52, 149]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7720
  Bounding Box: [1088.80, 712.00, 1255.20, 913.60]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [90, 60, 102, 75]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7632
  Bounding Box: [1153.60, 1891.20, 1360.00, 2048.00]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [95, 152, 110, 164]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7617
  Bounding Box: [972.00, 301.20, 1103.20, 443.60]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [80, 28, 89, 37]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7568
  Bounding Box: [229.40, 0.00, 357.80, 193.40]
  Mask Area: 112 pixels
  Mask Ratio: 0.0040
  Mask BBox: [22, 4, 31, 19]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7510
  Bounding Box: [1023.20, 1015.20, 1184.80, 1175.20]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [84, 84, 95, 95]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7495
  Bounding Box: [178.20, 332.00, 283.40, 601.60]
  Mask Area: 112 pixels
  Mask Ratio: 0.0040
  Mask BBox: [18, 31, 26, 50]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7485
  Bounding Box: [826.40, 817.60, 959.20, 961.60]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [69, 68, 78, 79]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7466
  Bounding Box: [281.20, 391.20, 458.00, 623.20]
  Mask Area: 184 pixels
  Mask Ratio: 0.0065
  Mask BBox: [26, 35, 39, 51]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7437
  Bounding Box: [325.40, 65.00, 454.40, 297.40]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [30, 10, 39, 27]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7422
  Bounding Box: [1528.80, 87.30, 1683.20, 270.00]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [124, 11, 134, 24]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7412
  Bounding Box: [580.80, 1040.80, 779.20, 1346.40]
  Mask Area: 293 pixels
  Mask Ratio: 0.0104
  Mask BBox: [50, 86, 64, 109]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7349
  Bounding Box: [904.00, 473.20, 1121.60, 707.60]
  Mask Area: 203 pixels
  Mask Ratio: 0.0072
  Mask BBox: [75, 41, 91, 59]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7212
  Bounding Box: [2.25, 1065.60, 129.40, 1209.60]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [5, 88, 13, 98]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7212
  Bounding Box: [432.80, 1388.00, 639.20, 1565.60]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [39, 113, 52, 126]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7207
  Bounding Box: [0.00, 157.40, 81.00, 297.40]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [4, 17, 10, 27]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7197
  Bounding Box: [786.40, 1611.20, 959.20, 1710.40]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [66, 130, 78, 136]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7197
  Bounding Box: [1220.00, 757.20, 1416.80, 956.00]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [100, 64, 114, 78]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7183
  Bounding Box: [1187.20, 1160.00, 1347.20, 1369.60]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [97, 95, 108, 110]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7095
  Bounding Box: [95.70, 322.20, 183.00, 422.80]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [12, 30, 18, 37]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.7085
  Bounding Box: [2.75, 1256.80, 138.00, 1376.80]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [5, 103, 14, 111]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.6987
  Bounding Box: [487.20, 1200.80, 656.80, 1400.80]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [43, 98, 55, 113]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.6953
  Bounding Box: [555.20, 868.00, 649.60, 999.20]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [48, 72, 54, 82]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.6953
  Bounding Box: [223.60, 1460.00, 343.60, 1681.60]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [22, 119, 30, 133]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.6929
  Bounding Box: [1235.20, 392.80, 1379.20, 601.60]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [101, 35, 111, 50]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.6929
  Bounding Box: [218.80, 0.95, 329.20, 159.80]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [22, 5, 29, 16]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6787
  Bounding Box: [984.80, 1846.40, 1168.80, 2038.40]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [82, 149, 95, 163]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6777
  Bounding Box: [9.60, 1360.80, 215.60, 1584.80]
  Mask Area: 229 pixels
  Mask Ratio: 0.0081
  Mask BBox: [5, 111, 20, 127]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6724
  Bounding Box: [333.60, 1285.60, 456.80, 1392.80]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [31, 105, 39, 111]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6675
  Bounding Box: [1046.40, 1302.40, 1212.80, 1564.80]
  Mask Area: 228 pixels
  Mask Ratio: 0.0081
  Mask BBox: [86, 106, 98, 126]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6636
  Bounding Box: [1766.40, 775.20, 1971.20, 1015.20]
  Mask Area: 212 pixels
  Mask Ratio: 0.0075
  Mask BBox: [142, 65, 157, 83]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6597
  Bounding Box: [616.80, 1668.80, 767.20, 2001.60]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [53, 135, 63, 160]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6582
  Bounding Box: [1760.00, 629.20, 1868.80, 728.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [142, 54, 148, 60]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6572
  Bounding Box: [696.40, 805.60, 844.00, 887.20]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [59, 67, 69, 73]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6528
  Bounding Box: [1152.80, 16.20, 1381.60, 428.00]
  Mask Area: 375 pixels
  Mask Ratio: 0.0133
  Mask BBox: [95, 6, 111, 37]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6504
  Bounding Box: [203.80, 908.00, 376.80, 1018.40]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [20, 75, 33, 83]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6499
  Bounding Box: [1585.60, 979.20, 1745.60, 1200.00]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [128, 81, 140, 97]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6479
  Bounding Box: [0.00, 303.60, 126.20, 510.80]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [4, 30, 13, 42]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6450
  Bounding Box: [1744.00, 1950.40, 1833.60, 2033.60]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [141, 157, 147, 162]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6436
  Bounding Box: [1841.60, 569.60, 2040.00, 795.20]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [148, 49, 163, 66]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6426
  Bounding Box: [444.80, 1900.80, 608.00, 2025.60]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [39, 153, 49, 162]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6362
  Bounding Box: [540.40, 381.60, 622.80, 476.80]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [47, 34, 51, 40]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6353
  Bounding Box: [723.60, 912.00, 908.80, 1086.40]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [61, 76, 74, 88]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6348
  Bounding Box: [1824.00, 1950.40, 2009.60, 2036.80]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [147, 157, 160, 163]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6333
  Bounding Box: [1894.40, 1521.60, 2048.00, 1774.40]
  Mask Area: 171 pixels
  Mask Ratio: 0.0061
  Mask BBox: [152, 123, 163, 142]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6274
  Bounding Box: [10.80, 1547.20, 88.90, 1622.40]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [5, 125, 10, 130]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6240
  Bounding Box: [137.20, 1050.40, 348.40, 1319.20]
  Mask Area: 262 pixels
  Mask Ratio: 0.0093
  Mask BBox: [15, 87, 31, 106]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6235
  Bounding Box: [277.20, 261.60, 386.80, 385.60]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [26, 25, 32, 32]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6211
  Bounding Box: [685.20, 1584.80, 788.00, 1675.20]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [58, 128, 65, 134]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6138
  Bounding Box: [184.20, 545.60, 316.60, 665.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [19, 47, 28, 55]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6118
  Bounding Box: [704.80, 878.40, 783.20, 966.40]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [60, 73, 65, 79]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6108
  Bounding Box: [495.20, 1040.00, 585.60, 1185.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [43, 86, 49, 96]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6021
  Bounding Box: [0.00, 34.30, 94.00, 150.50]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [4, 7, 11, 15]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6006
  Bounding Box: [166.20, 1327.20, 415.20, 1562.40]
  Mask Area: 214 pixels
  Mask Ratio: 0.0076
  Mask BBox: [17, 109, 36, 126]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.5991
  Bounding Box: [528.40, 956.80, 607.60, 1078.40]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [46, 79, 51, 88]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.5981
  Bounding Box: [109.40, 1564.80, 237.40, 1670.40]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [13, 127, 22, 132]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.5977
  Bounding Box: [372.80, 346.40, 520.00, 537.60]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [34, 32, 44, 45]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.5967
  Bounding Box: [1940.80, 416.00, 2033.60, 560.00]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [156, 37, 162, 47]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.5967
  Bounding Box: [748.00, 650.00, 940.00, 809.60]
  Mask Area: 159 pixels
  Mask Ratio: 0.0056
  Mask BBox: [63, 55, 77, 67]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.5962
  Bounding Box: [1580.80, 1199.20, 1667.20, 1320.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [128, 98, 134, 107]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.5942
  Bounding Box: [280.40, 1480.00, 492.40, 1819.20]
  Mask Area: 329 pixels
  Mask Ratio: 0.0117
  Mask BBox: [26, 120, 42, 146]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.5898
  Bounding Box: [1584.80, 1641.60, 1745.60, 1734.40]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [128, 133, 139, 139]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5874
  Bounding Box: [472.40, 1547.20, 611.60, 1665.60]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [41, 125, 51, 133]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5835
  Bounding Box: [1564.80, 0.00, 1654.40, 82.30]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [127, 4, 132, 10]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5825
  Bounding Box: [1196.80, 1665.60, 1392.00, 1953.60]
  Mask Area: 215 pixels
  Mask Ratio: 0.0076
  Mask BBox: [98, 135, 111, 156]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5801
  Bounding Box: [782.40, 1232.00, 880.00, 1377.60]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [66, 101, 72, 111]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5767
  Bounding Box: [1945.60, 822.40, 2038.40, 1052.80]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [156, 69, 163, 86]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5752
  Bounding Box: [1627.20, 772.00, 1876.80, 1114.40]
  Mask Area: 367 pixels
  Mask Ratio: 0.0130
  Mask BBox: [132, 65, 150, 91]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5737
  Bounding Box: [586.40, 1588.80, 693.60, 1697.60]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [50, 129, 58, 136]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5718
  Bounding Box: [1556.80, 291.40, 1771.20, 528.80]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [126, 27, 142, 45]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5708
  Bounding Box: [1520.80, 1766.40, 1766.40, 2041.60]
  Mask Area: 317 pixels
  Mask Ratio: 0.0112
  Mask BBox: [123, 142, 141, 163]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5688
  Bounding Box: [965.60, 690.80, 1040.80, 792.80]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [80, 58, 85, 65]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5674
  Bounding Box: [543.20, 656.00, 703.20, 808.80]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [47, 56, 58, 67]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5669
  Bounding Box: [1154.40, 907.20, 1247.20, 998.40]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [95, 75, 101, 80]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5610
  Bounding Box: [776.00, 35.30, 1016.00, 239.20]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [65, 7, 83, 22]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5547
  Bounding Box: [956.80, 1664.00, 1067.20, 1747.20]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [79, 134, 86, 140]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5493
  Bounding Box: [904.00, 1692.80, 1008.00, 1756.80]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [75, 137, 82, 141]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5474
  Bounding Box: [1384.80, 201.40, 1461.60, 289.80]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [113, 20, 117, 26]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5464
  Bounding Box: [1638.40, 352.80, 1868.80, 613.60]
  Mask Area: 258 pixels
  Mask Ratio: 0.0091
  Mask BBox: [132, 32, 149, 51]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5464
  Bounding Box: [1077.60, 732.80, 1343.20, 913.60]
  Mask Area: 232 pixels
  Mask Ratio: 0.0082
  Mask BBox: [89, 62, 108, 75]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5425
  Bounding Box: [385.60, 844.80, 556.80, 1065.60]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [35, 70, 47, 87]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5337
  Bounding Box: [10.35, 928.80, 81.80, 1028.00]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [5, 77, 9, 84]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5337
  Bounding Box: [977.60, 68.30, 1168.00, 325.20]
  Mask Area: 250 pixels
  Mask Ratio: 0.0089
  Mask BBox: [81, 10, 95, 29]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5308
  Bounding Box: [1473.60, 769.60, 1617.60, 1009.60]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [120, 65, 130, 82]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5288
  Bounding Box: [1579.20, 1753.60, 1883.20, 1964.80]
  Mask Area: 330 pixels
  Mask Ratio: 0.0117
  Mask BBox: [128, 141, 151, 157]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5205
  Bounding Box: [929.60, 15.30, 1084.80, 123.10]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [77, 6, 88, 11]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5156
  Bounding Box: [845.60, 1299.20, 1032.80, 1531.20]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [71, 106, 84, 123]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5093
  Bounding Box: [1429.60, 1522.40, 1517.60, 1596.80]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [116, 123, 122, 128]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5063
  Bounding Box: [1579.20, 1382.40, 1883.20, 1691.20]
  Mask Area: 424 pixels
  Mask Ratio: 0.0150
  Mask BBox: [128, 112, 151, 136]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.5034
  Bounding Box: [1569.60, 1614.40, 1729.60, 1748.80]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [127, 131, 139, 139]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.5010
  Bounding Box: [342.80, 1945.60, 486.00, 2048.00]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [31, 156, 41, 163]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.4990
  Bounding Box: [1844.80, 367.60, 1950.40, 449.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [149, 33, 156, 39]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.4878
  Bounding Box: [1565.60, 292.40, 1872.00, 574.80]
  Mask Area: 375 pixels
  Mask Ratio: 0.0133
  Mask BBox: [127, 27, 150, 48]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.4849
  Bounding Box: [1900.80, 1715.20, 2035.20, 1977.60]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [153, 138, 162, 158]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.4844
  Bounding Box: [845.60, 1499.20, 952.80, 1608.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [71, 122, 77, 129]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.4829
  Bounding Box: [1149.60, 919.20, 1229.60, 1015.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [94, 76, 100, 83]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.4829
  Bounding Box: [891.20, 1283.20, 1054.40, 1484.80]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [74, 105, 86, 119]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4800
  Bounding Box: [506.80, 677.20, 559.60, 770.40]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [44, 57, 47, 64]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.4785
  Bounding Box: [390.40, 0.00, 519.20, 102.20]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [35, 4, 43, 11]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4766
  Bounding Box: [1987.20, 1960.00, 2041.60, 2036.80]
  Mask Area: 21 pixels
  Mask Ratio: 0.0007
  Mask BBox: [160, 158, 163, 163]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4736
  Bounding Box: [1680.00, 460.00, 1865.60, 608.00]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [136, 40, 148, 51]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4727
  Bounding Box: [968.80, 712.80, 1053.60, 808.00]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [80, 60, 86, 67]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4702
  Bounding Box: [9.85, 740.40, 66.40, 837.60]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [5, 62, 9, 69]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4697
  Bounding Box: [133.20, 392.00, 200.00, 513.60]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [15, 35, 18, 44]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4648
  Bounding Box: [1998.40, 1501.60, 2046.40, 1596.80]
  Mask Area: 18 pixels
  Mask Ratio: 0.0006
  Mask BBox: [161, 122, 163, 127]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4639
  Bounding Box: [609.20, 440.00, 899.20, 681.60]
  Mask Area: 320 pixels
  Mask Ratio: 0.0113
  Mask BBox: [52, 39, 74, 57]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4600
  Bounding Box: [871.20, 284.40, 1015.20, 549.20]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [73, 27, 83, 46]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4595
  Bounding Box: [0.45, 1966.40, 115.80, 2048.00]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [5, 158, 13, 164]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4565
  Bounding Box: [580.80, 465.60, 832.00, 754.40]
  Mask Area: 311 pixels
  Mask Ratio: 0.0110
  Mask BBox: [50, 41, 68, 62]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4548
  Bounding Box: [1764.80, 1280.80, 1889.60, 1410.40]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [142, 105, 151, 114]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4534
  Bounding Box: [91.20, 1578.40, 236.40, 1702.40]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [12, 128, 22, 136]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4514
  Bounding Box: [1139.20, 424.80, 1340.80, 659.20]
  Mask Area: 249 pixels
  Mask Ratio: 0.0088
  Mask BBox: [93, 38, 108, 55]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4500
  Bounding Box: [205.60, 1398.40, 364.80, 1657.60]
  Mask Area: 187 pixels
  Mask Ratio: 0.0066
  Mask BBox: [21, 114, 32, 133]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4495
  Bounding Box: [851.20, 1649.60, 955.20, 1726.40]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [71, 133, 78, 138]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4490
  Bounding Box: [284.80, 1536.00, 529.60, 1891.20]
  Mask Area: 350 pixels
  Mask Ratio: 0.0124
  Mask BBox: [27, 124, 45, 151]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4470
  Bounding Box: [1481.60, 540.40, 1574.40, 635.60]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [120, 47, 126, 53]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4460
  Bounding Box: [7.25, 900.80, 75.40, 1004.80]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [5, 75, 9, 82]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4397
  Bounding Box: [1568.00, 1374.40, 1817.60, 1611.20]
  Mask Area: 200 pixels
  Mask Ratio: 0.0071
  Mask BBox: [127, 112, 145, 129]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4373
  Bounding Box: [422.00, 1646.40, 622.00, 1912.00]
  Mask Area: 242 pixels
  Mask Ratio: 0.0086
  Mask BBox: [37, 133, 52, 153]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4358
  Bounding Box: [929.60, 1668.80, 1048.00, 1758.40]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [77, 135, 85, 141]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4358
  Bounding Box: [1921.60, 1776.00, 2048.00, 1964.80]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [155, 143, 164, 157]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4316
  Bounding Box: [1883.20, 1576.80, 2040.00, 1904.00]
  Mask Area: 237 pixels
  Mask Ratio: 0.0084
  Mask BBox: [152, 128, 163, 152]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4312
  Bounding Box: [861.60, 469.60, 962.40, 642.40]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [72, 41, 79, 54]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4282
  Bounding Box: [1860.80, 1688.00, 2030.40, 1921.60]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [150, 136, 162, 154]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4216
  Bounding Box: [1391.20, 1976.00, 1520.80, 2036.80]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [113, 159, 122, 163]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4207
  Bounding Box: [1979.20, 987.20, 2043.20, 1112.00]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [159, 82, 163, 90]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4192
  Bounding Box: [996.00, 1146.40, 1197.60, 1340.00]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [82, 94, 97, 108]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4148
  Bounding Box: [1054.40, 1657.60, 1316.80, 1900.80]
  Mask Area: 306 pixels
  Mask Ratio: 0.0108
  Mask BBox: [87, 134, 106, 152]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4131
  Bounding Box: [1204.80, 1104.80, 1456.00, 1348.00]
  Mask Area: 283 pixels
  Mask Ratio: 0.0100
  Mask BBox: [99, 91, 117, 109]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4106
  Bounding Box: [1923.20, 1592.00, 2048.00, 1812.80]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [155, 129, 165, 145]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4092
  Bounding Box: [1330.40, 1272.00, 1396.00, 1355.20]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [108, 104, 113, 109]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.4087
  Bounding Box: [694.40, 438.00, 894.40, 622.80]
  Mask Area: 159 pixels
  Mask Ratio: 0.0056
  Mask BBox: [59, 39, 73, 52]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.4087
  Bounding Box: [480.00, 1567.20, 680.00, 1676.80]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [42, 127, 57, 134]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.4011
  Bounding Box: [525.60, 1094.40, 775.20, 1376.00]
  Mask Area: 313 pixels
  Mask Ratio: 0.0111
  Mask BBox: [46, 90, 64, 111]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.3994
  Bounding Box: [549.60, 575.60, 765.60, 806.40]
  Mask Area: 260 pixels
  Mask Ratio: 0.0092
  Mask BBox: [47, 49, 63, 66]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.3914
  Bounding Box: [943.20, 681.60, 1032.80, 779.20]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [78, 58, 84, 64]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.3914
  Bounding Box: [943.20, 707.20, 1032.80, 804.80]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [78, 60, 84, 66]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.3909
  Bounding Box: [1289.60, 710.80, 1396.80, 802.40]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [105, 60, 113, 65]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.3899
  Bounding Box: [986.40, 775.20, 1180.00, 983.20]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [82, 65, 96, 80]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.3867
  Bounding Box: [1369.60, 186.40, 1488.00, 294.40]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [111, 19, 117, 26]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.3857
  Bounding Box: [604.40, 942.40, 724.40, 1060.80]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [52, 78, 60, 85]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3850
  Bounding Box: [106.70, 0.00, 329.60, 177.20]
  Mask Area: 184 pixels
  Mask Ratio: 0.0065
  Mask BBox: [13, 4, 29, 17]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3840
  Bounding Box: [1492.80, 1897.60, 1736.00, 2041.60]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [121, 153, 137, 163]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3831
  Bounding Box: [77.00, 355.20, 207.40, 516.80]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [11, 32, 20, 44]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3821
  Bounding Box: [1966.40, 888.80, 2043.20, 1125.60]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [158, 74, 163, 91]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3809
  Bounding Box: [1979.20, 1475.20, 2043.20, 1579.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [159, 120, 163, 127]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3794
  Bounding Box: [0.00, 1792.00, 73.60, 1939.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [4, 144, 9, 155]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3794
  Bounding Box: [10.40, 749.60, 162.00, 944.80]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [5, 63, 16, 77]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3782
  Bounding Box: [614.40, 1593.60, 771.20, 1686.40]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [52, 129, 64, 135]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3774
  Bounding Box: [52.80, 156.60, 194.80, 337.80]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [9, 17, 19, 30]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3772
  Bounding Box: [1130.40, 505.60, 1295.20, 695.20]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [93, 44, 105, 56]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3689
  Bounding Box: [384.40, 1019.20, 482.80, 1190.40]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [35, 84, 41, 96]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3674
  Bounding Box: [368.40, 300.80, 452.40, 365.60]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [33, 28, 39, 32]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3674
  Bounding Box: [0.00, 1804.80, 74.80, 2009.60]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [4, 145, 9, 160]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3625
  Bounding Box: [1533.60, 471.20, 1678.40, 800.80]
  Mask Area: 224 pixels
  Mask Ratio: 0.0079
  Mask BBox: [124, 41, 135, 66]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3625
  Bounding Box: [752.80, 930.40, 1002.40, 1132.00]
  Mask Area: 237 pixels
  Mask Ratio: 0.0084
  Mask BBox: [63, 77, 82, 92]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3611
  Bounding Box: [1953.60, 1619.20, 2043.20, 1827.20]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [157, 131, 163, 146]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3594
  Bounding Box: [242.80, 1460.00, 442.40, 1750.40]
  Mask Area: 250 pixels
  Mask Ratio: 0.0089
  Mask BBox: [23, 119, 38, 140]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [1040.00, 416.00, 1131.20, 512.80]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [86, 37, 92, 43]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [1153.60, 968.80, 1307.20, 1264.80]
  Mask Area: 226 pixels
  Mask Ratio: 0.0080
  Mask BBox: [95, 80, 106, 102]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3545
  Bounding Box: [0.00, 765.60, 78.50, 880.80]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [4, 64, 8, 72]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3535
  Bounding Box: [2.65, 343.00, 154.20, 670.40]
  Mask Area: 187 pixels
  Mask Ratio: 0.0066
  Mask BBox: [5, 31, 16, 56]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3530
  Bounding Box: [1370.40, 957.60, 1565.60, 1160.80]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [112, 79, 126, 94]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3503
  Bounding Box: [1424.00, 57.20, 1689.60, 321.60]
  Mask Area: 257 pixels
  Mask Ratio: 0.0091
  Mask BBox: [116, 9, 135, 29]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3501
  Bounding Box: [338.80, 286.40, 445.20, 371.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [31, 27, 38, 32]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3491
  Bounding Box: [544.00, 1860.80, 620.00, 1940.80]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [47, 150, 52, 155]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3486
  Bounding Box: [1956.80, 830.40, 2046.40, 992.00]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [157, 69, 163, 81]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3464
  Bounding Box: [329.60, 986.40, 485.60, 1284.00]
  Mask Area: 233 pixels
  Mask Ratio: 0.0083
  Mask BBox: [30, 82, 41, 104]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3455
  Bounding Box: [1368.00, 20.10, 1630.40, 340.40]
  Mask Area: 321 pixels
  Mask Ratio: 0.0114
  Mask BBox: [111, 6, 131, 30]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3452
  Bounding Box: [1747.20, 640.00, 1859.20, 758.40]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [141, 54, 148, 63]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3430
  Bounding Box: [16.70, 1240.80, 160.40, 1357.60]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [6, 101, 16, 110]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3425
  Bounding Box: [130.80, 1584.00, 257.20, 1683.20]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [15, 128, 24, 135]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3425
  Bounding Box: [2.70, 1595.20, 184.40, 1806.40]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [5, 129, 18, 144]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3420
  Bounding Box: [189.80, 1076.00, 333.00, 1298.40]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [19, 89, 30, 105]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3411
  Bounding Box: [471.20, 443.20, 605.60, 670.40]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [41, 39, 51, 56]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3408
  Bounding Box: [141.90, 384.80, 229.00, 540.80]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [16, 35, 21, 46]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3394
  Bounding Box: [1788.80, 1271.20, 1958.40, 1440.80]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [144, 104, 156, 116]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3376
  Bounding Box: [1771.20, 643.20, 1883.20, 749.60]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [143, 55, 148, 60]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3364
  Bounding Box: [356.80, 4.60, 457.60, 79.00]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [32, 5, 39, 10]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3364
  Bounding Box: [1319.20, 1646.40, 1439.20, 1793.60]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [108, 133, 116, 144]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3350
  Bounding Box: [132.40, 1553.60, 256.40, 1652.80]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [15, 126, 24, 133]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3333
  Bounding Box: [1002.40, 305.00, 1116.00, 472.00]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [83, 28, 90, 40]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3328
  Bounding Box: [554.80, 372.80, 642.80, 468.00]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [48, 34, 51, 40]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3328
  Bounding Box: [529.20, 398.40, 617.20, 493.60]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [46, 36, 51, 42]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3328
  Bounding Box: [554.80, 398.40, 642.80, 493.60]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [48, 36, 51, 42]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3306
  Bounding Box: [1664.00, 1479.20, 1904.00, 1713.60]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [134, 120, 152, 137]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1142.40, 895.20, 1240.00, 978.40]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [94, 74, 100, 80]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1168.00, 895.20, 1265.60, 978.40]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [96, 74, 101, 80]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1168.00, 920.80, 1265.60, 1004.00]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [96, 76, 101, 81]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1483.20, 534.00, 1651.20, 777.60]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [120, 46, 132, 64]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3274
  Bounding Box: [924.00, 285.20, 1112.80, 462.00]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [77, 27, 90, 40]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [170.80, 899.20, 279.60, 971.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [18, 75, 25, 79]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3259
  Bounding Box: [1307.20, 1284.00, 1408.00, 1372.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [107, 105, 113, 110]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3259
  Bounding Box: [930.40, 0.00, 1128.80, 207.00]
  Mask Area: 231 pixels
  Mask Ratio: 0.0082
  Mask BBox: [77, 4, 92, 20]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3259
  Bounding Box: [740.40, 860.80, 939.20, 1081.60]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [62, 72, 77, 88]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3254
  Bounding Box: [984.80, 1339.20, 1188.00, 1641.60]
  Mask Area: 307 pixels
  Mask Ratio: 0.0109
  Mask BBox: [81, 109, 96, 132]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3230
  Bounding Box: [804.00, 1219.20, 908.00, 1363.20]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [67, 101, 74, 110]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3203
  Bounding Box: [398.40, 747.60, 536.80, 856.00]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [36, 63, 45, 70]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3196
  Bounding Box: [0.00, 272.60, 108.10, 459.20]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [4, 26, 12, 39]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3184
  Bounding Box: [0.00, 910.40, 109.80, 1017.60]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [4, 76, 12, 83]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3184
  Bounding Box: [1139.20, 0.00, 1222.40, 120.00]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [93, 4, 99, 13]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3179
  Bounding Box: [357.20, 1897.60, 510.00, 2048.00]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [32, 153, 43, 164]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3164
  Bounding Box: [657.20, 1820.80, 812.80, 2048.00]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [56, 147, 67, 164]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3157
  Bounding Box: [1673.60, 585.20, 1785.60, 686.80]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [135, 50, 143, 57]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3149
  Bounding Box: [1817.60, 1397.60, 1993.60, 1503.20]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [146, 114, 159, 120]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3140
  Bounding Box: [1976.00, 1529.60, 2043.20, 1620.80]
  Mask Area: 16 pixels
  Mask Ratio: 0.0006
  Mask BBox: [159, 124, 163, 127]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3140
  Bounding Box: [2001.60, 1529.60, 2048.00, 1620.80]
  Mask Area: 11 pixels
  Mask Ratio: 0.0004
  Mask BBox: [161, 124, 163, 127]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3127
  Bounding Box: [976.80, 1635.20, 1205.60, 1875.20]
  Mask Area: 227 pixels
  Mask Ratio: 0.0080
  Mask BBox: [81, 132, 98, 150]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.3120
  Bounding Box: [94.60, 1643.20, 220.60, 1755.20]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [12, 133, 21, 141]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.3120
  Bounding Box: [0.00, 1910.40, 97.60, 2044.80]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [4, 154, 11, 163]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [964.00, 1458.40, 1146.40, 1681.60]
  Mask Area: 170 pixels
  Mask Ratio: 0.0060
  Mask BBox: [80, 118, 93, 135]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.3086
  Bounding Box: [7.75, 1332.00, 181.00, 1506.40]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [5, 109, 18, 121]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.3079
  Bounding Box: [1332.00, 1870.40, 1397.60, 1969.60]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [109, 151, 112, 157]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [794.40, 1256.00, 911.20, 1390.40]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [67, 103, 75, 111]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [1848.00, 1276.00, 1982.40, 1424.80]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [149, 104, 158, 115]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.3064
  Bounding Box: [870.40, 1288.80, 1110.40, 1536.80]
  Mask Area: 292 pixels
  Mask Ratio: 0.0103
  Mask BBox: [72, 105, 90, 124]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.3054
  Bounding Box: [421.20, 4.15, 538.00, 112.20]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [37, 5, 43, 10]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.3052
  Bounding Box: [1910.40, 404.80, 2012.80, 552.00]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [154, 36, 161, 47]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.3052
  Bounding Box: [56.80, 1625.60, 196.40, 1769.60]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [9, 131, 19, 142]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.3052
  Bounding Box: [728.00, 0.95, 857.60, 78.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [61, 5, 70, 8]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.3042
  Bounding Box: [707.20, 706.80, 916.80, 884.00]
  Mask Area: 186 pixels
  Mask Ratio: 0.0066
  Mask BBox: [60, 60, 75, 73]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.3037
  Bounding Box: [0.00, 737.20, 47.70, 825.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [3, 62, 7, 68]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.3037
  Bounding Box: [0.00, 762.80, 47.70, 851.20]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [3, 64, 7, 70]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.3035
  Bounding Box: [3.65, 936.80, 119.60, 1058.40]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [5, 78, 13, 86]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.3008
  Bounding Box: [1651.20, 571.60, 1760.00, 691.60]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [133, 49, 141, 58]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.3003
  Bounding Box: [21.70, 1090.40, 150.70, 1234.40]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [6, 90, 15, 100]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.2988
  Bounding Box: [22.10, 179.40, 145.90, 357.80]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [6, 19, 15, 31]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.2986
  Bounding Box: [1865.60, 1870.40, 2032.00, 2040.00]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [150, 151, 162, 163]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.2976
  Bounding Box: [847.20, 342.00, 1012.00, 624.40]
  Mask Area: 218 pixels
  Mask Ratio: 0.0077
  Mask BBox: [71, 31, 83, 52]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.2974
  Bounding Box: [6.20, 770.40, 68.40, 845.60]
  Mask Area: 15 pixels
  Mask Ratio: 0.0005
  Mask BBox: [5, 65, 7, 70]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.2954
  Bounding Box: [210.40, 85.30, 431.20, 287.60]
  Mask Area: 187 pixels
  Mask Ratio: 0.0066
  Mask BBox: [21, 11, 37, 26]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.2930
  Bounding Box: [254.80, 279.00, 374.00, 401.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [24, 26, 33, 35]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2917
  Bounding Box: [892.00, 1664.00, 983.20, 1744.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [74, 134, 80, 140]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2917
  Bounding Box: [917.60, 1664.00, 1008.80, 1744.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [76, 134, 82, 140]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2917
  Bounding Box: [892.00, 1689.60, 983.20, 1769.60]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [74, 136, 80, 141]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2915
  Bounding Box: [808.80, 1593.60, 994.40, 1699.20]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [68, 129, 81, 136]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2893
  Bounding Box: [1948.80, 988.80, 2048.00, 1102.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [157, 82, 164, 90]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2866
  Bounding Box: [22.90, 464.00, 157.60, 694.40]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [6, 41, 14, 58]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2847
  Bounding Box: [349.40, 330.40, 494.00, 512.80]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [32, 31, 42, 44]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2830
  Bounding Box: [206.40, 1302.40, 452.40, 1456.00]
  Mask Area: 138 pixels
  Mask Ratio: 0.0049
  Mask BBox: [21, 106, 39, 117]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [725.20, 785.60, 871.20, 883.20]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [61, 66, 72, 72]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [725.20, 811.20, 871.20, 908.80]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [61, 68, 72, 74]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [1590.40, 1364.80, 1772.80, 1571.20]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [129, 111, 142, 126]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2822
  Bounding Box: [585.20, 1034.40, 727.60, 1172.00]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [50, 85, 60, 93]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2817
  Bounding Box: [1974.40, 1502.40, 2041.60, 1595.20]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [159, 122, 163, 127]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2817
  Bounding Box: [1760.00, 598.00, 1980.80, 780.00]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [142, 51, 158, 64]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2817
  Bounding Box: [1016.80, 1184.00, 1212.00, 1505.60]
  Mask Area: 309 pixels
  Mask Ratio: 0.0109
  Mask BBox: [84, 97, 98, 121]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2805
  Bounding Box: [480.40, 1017.60, 572.40, 1171.20]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [42, 84, 48, 95]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2788
  Bounding Box: [1675.20, 612.80, 1835.20, 741.60]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [135, 52, 147, 61]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2786
  Bounding Box: [702.40, 828.00, 835.20, 922.40]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [59, 69, 69, 76]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2761
  Bounding Box: [1867.20, 372.40, 2001.60, 508.40]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [150, 34, 160, 43]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2759
  Bounding Box: [31.35, 158.40, 172.00, 417.20]
  Mask Area: 188 pixels
  Mask Ratio: 0.0067
  Mask BBox: [7, 17, 17, 36]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2756
  Bounding Box: [1266.40, 595.20, 1376.80, 708.00]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [103, 51, 111, 59]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2739
  Bounding Box: [1300.80, 1918.40, 1395.20, 2024.00]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [106, 154, 112, 162]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2739
  Bounding Box: [1241.60, 940.80, 1350.40, 1124.80]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [101, 78, 108, 91]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2737
  Bounding Box: [1320.80, 1279.20, 1436.00, 1351.20]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [108, 104, 116, 109]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2727
  Bounding Box: [765.20, 1044.80, 940.00, 1147.20]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [64, 86, 77, 93]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2717
  Bounding Box: [0.00, 147.80, 59.20, 291.40]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [3, 16, 8, 26]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2715
  Bounding Box: [328.20, 1707.20, 532.00, 1931.20]
  Mask Area: 220 pixels
  Mask Ratio: 0.0078
  Mask BBox: [30, 138, 45, 154]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2712
  Bounding Box: [10.80, 1448.80, 234.20, 1616.00]
  Mask Area: 157 pixels
  Mask Ratio: 0.0056
  Mask BBox: [5, 118, 22, 130]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2708
  Bounding Box: [354.00, 1283.20, 471.60, 1368.00]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [32, 105, 40, 110]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2661
  Bounding Box: [295.20, 416.00, 509.60, 695.20]
  Mask Area: 253 pixels
  Mask Ratio: 0.0090
  Mask BBox: [28, 37, 43, 58]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2659
  Bounding Box: [361.20, 1017.60, 479.60, 1246.40]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [33, 84, 41, 101]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2644
  Bounding Box: [1378.40, 1840.00, 1646.40, 2009.60]
  Mask Area: 171 pixels
  Mask Ratio: 0.0061
  Mask BBox: [112, 148, 132, 160]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2634
  Bounding Box: [708.40, 896.80, 877.60, 1036.00]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [60, 75, 72, 84]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2625
  Bounding Box: [812.00, 1619.20, 989.60, 1734.40]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [68, 131, 81, 136]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2617
  Bounding Box: [553.20, 899.20, 694.80, 1081.60]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [48, 75, 58, 88]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2610
  Bounding Box: [1659.20, 524.00, 1816.00, 702.40]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [134, 45, 145, 58]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2595
  Bounding Box: [1564.80, 1155.20, 1699.20, 1328.00]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [127, 95, 134, 107]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2588
  Bounding Box: [18.95, 298.80, 155.20, 542.00]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [6, 28, 16, 46]

