这个模型的输出头没在c3对应位置

# Parameters
nc: 80
depth_multiple: 0.33
width_multiple: 0.25
anchors:
  - [10,13, 16,30, 33,23]
  - [30,61, 62,45, 59,119]
  - [116,90, 156,198, 373,326]

# Backbone
backbone:
  # ====== 显式取两路输入 ======
  - [-1, 1, Input<PERSON>outer, ['RGB']]                # 0: 取 RGB 输入
  - [-1, 1, Input<PERSON><PERSON><PERSON>, ['X']]                  # 1: 取 X   输入

  # --- RGB Path ---
  - [0, 1, Conv, [64, 3, 2]]                     # 2  P1/2 (RGB)
  - [-1, 1, Conv, [128, 3, 2]]                   # 3  P2/4
  - [-1, 3, C3,  [128]]
  - [-1, 1, Conv, [256, 3, 2]]                   # 5  P3/8
  - [-1, 6, C3,  [256]]                          # 6  RGB P3

  # --- X Path ---
  - [1, 1, Conv, [64, 3, 2]]                     # 7  P1/2 (X)   —— 注意：from = 1（X 输入）
  - [-1, 1, Conv, [128, 3, 2]]                   # 8  P2/4
  - [-1, 3, C3,  [128]]
  - [-1, 1, Conv, [256, 3, 2]]                   # 10 P3/8
  - [-1, 6, C3,  [256]]                          # 11 X  P3

  # --- Mid Fusion 1 (P3) ---
  - [[6, 11], 1, Concat, [1]]                    # 12: 融合 RGB+X 的 P3
  - [-1, 3, C3, [256]]                           # 13 fused P3 (等价你原先的 11)

  # 继续各自下采样到 P4
  - [6,  1, Conv, [512, 3, 2]]                   # 14 RGB -> P4/16
  - [-1, 6, C3,  [512]]                          # 15 RGB P4
  - [11, 1, Conv, [512, 3, 2]]                   # 16 X   -> P4/16
  - [-1, 6, C3,  [512]]                          # 17 X   P4

  # --- Mid Fusion 2 (P4) ---
  - [[15, 17], 1, Concat, [1]]                   # 18: 融合 RGB+X 的 P4
  - [-1, 3, C3, [512]]                           # 19 fused P4

  # P5 from fused P4
  - [-1, 1, Conv, [1024, 3, 2]]                  # 20 P5/32
  - [-1, 3, C3,  [1024]]
  - [-1, 1, SPPF, [1024, 5]]                     # 22 fused P5

# Head 
head:
  - [22, 1, Conv, [512, 1, 1]]
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 19], 1, Concat, [1]]
  - [-1, 3, C3, [512, False]]                    # 对齐新的 fused P4 索引

  - [-1, 1, Conv, [256, 1, 1]]
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 13], 1, Concat, [1]]
  - [-1, 3, C3, [256, False]]                    # (P3/8)30

  - [-1, 1, Conv, [256, 3, 2]]
  - [[-1, 24], 1, Concat, [1]]
  - [-1, 3, C3, [512, False]]                    # (P4/16)33

  - [-1, 1, Conv, [512, 3, 2]]
  - [[-1, 22], 1, Concat, [1]]
  - [-1, 3, C3, [1024, False]]                   # (P5/32)36

  - [[30, 33, 36], 1, Segment, [nc, anchors, 32, 256]]
