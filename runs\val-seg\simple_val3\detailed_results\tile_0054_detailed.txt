Image: tile_0054.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8438
  Bounding Box: [91.80, 1600.00, 321.80, 1897.60]
  Mask Area: 326 pixels
  Mask Ratio: 0.0116
  Mask BBox: [12, 129, 29, 152]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8296
  Bounding Box: [1374.40, 1284.80, 1595.20, 1649.60]
  Mask Area: 311 pixels
  Mask Ratio: 0.0110
  Mask BBox: [112, 105, 128, 130]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8291
  Bounding Box: [1145.60, 90.10, 1371.20, 372.00]
  Mask Area: 324 pixels
  Mask Ratio: 0.0115
  Mask BBox: [94, 12, 111, 33]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8286
  Bounding Box: [992.80, 501.20, 1260.00, 776.80]
  Mask Area: 313 pixels
  Mask Ratio: 0.0111
  Mask BBox: [82, 44, 102, 64]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8281
  Bounding Box: [244.40, 1415.20, 380.40, 1616.00]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [24, 115, 33, 129]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8267
  Bounding Box: [358.40, 90.50, 715.20, 392.80]
  Mask Area: 465 pixels
  Mask Ratio: 0.0165
  Mask BBox: [32, 12, 59, 34]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8179
  Bounding Box: [1554.40, 582.40, 1769.60, 804.80]
  Mask Area: 206 pixels
  Mask Ratio: 0.0073
  Mask BBox: [126, 50, 142, 65]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8159
  Bounding Box: [960.80, 1422.40, 1245.60, 1686.40]
  Mask Area: 296 pixels
  Mask Ratio: 0.0105
  Mask BBox: [80, 116, 101, 135]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8154
  Bounding Box: [646.80, 549.20, 840.80, 808.00]
  Mask Area: 219 pixels
  Mask Ratio: 0.0078
  Mask BBox: [55, 47, 69, 67]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8086
  Bounding Box: [962.40, 721.60, 1220.00, 1076.80]
  Mask Area: 445 pixels
  Mask Ratio: 0.0158
  Mask BBox: [80, 61, 99, 88]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8008
  Bounding Box: [1782.40, 967.20, 1977.60, 1212.00]
  Mask Area: 192 pixels
  Mask Ratio: 0.0068
  Mask BBox: [144, 80, 158, 98]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.7979
  Bounding Box: [858.40, 1327.20, 1063.20, 1516.00]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [72, 108, 87, 122]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.7954
  Bounding Box: [440.00, 550.00, 644.00, 932.80]
  Mask Area: 396 pixels
  Mask Ratio: 0.0140
  Mask BBox: [39, 47, 54, 76]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.7891
  Bounding Box: [1704.00, 0.00, 1860.80, 182.00]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [138, 4, 149, 17]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7871
  Bounding Box: [1889.60, 159.00, 2036.80, 333.00]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [152, 17, 162, 30]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7871
  Bounding Box: [170.40, 1129.60, 463.20, 1420.80]
  Mask Area: 349 pixels
  Mask Ratio: 0.0124
  Mask BBox: [18, 93, 40, 114]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7861
  Bounding Box: [1584.80, 1379.20, 1737.60, 1603.20]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [128, 112, 139, 128]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7832
  Bounding Box: [16.50, 1752.00, 224.80, 1982.40]
  Mask Area: 204 pixels
  Mask Ratio: 0.0072
  Mask BBox: [6, 141, 21, 158]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7808
  Bounding Box: [1635.20, 404.80, 1894.40, 603.20]
  Mask Area: 210 pixels
  Mask Ratio: 0.0074
  Mask BBox: [132, 36, 151, 51]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7808
  Bounding Box: [196.80, 973.60, 334.00, 1125.60]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [20, 81, 29, 91]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7798
  Bounding Box: [458.80, 1635.20, 630.00, 1792.00]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [40, 132, 53, 143]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7798
  Bounding Box: [712.40, 824.00, 883.20, 980.80]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [60, 69, 72, 80]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7778
  Bounding Box: [1365.60, 9.60, 1732.80, 330.40]
  Mask Area: 609 pixels
  Mask Ratio: 0.0216
  Mask BBox: [111, 5, 139, 29]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7754
  Bounding Box: [317.80, 802.40, 484.00, 1079.20]
  Mask Area: 196 pixels
  Mask Ratio: 0.0069
  Mask BBox: [29, 67, 41, 88]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7695
  Bounding Box: [976.80, 1054.40, 1293.60, 1404.80]
  Mask Area: 431 pixels
  Mask Ratio: 0.0153
  Mask BBox: [81, 87, 105, 113]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7666
  Bounding Box: [1756.80, 1384.00, 1904.00, 1619.20]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [142, 113, 151, 130]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7612
  Bounding Box: [1835.20, 2.70, 1982.40, 174.00]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [148, 5, 157, 17]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7588
  Bounding Box: [852.80, 804.00, 977.60, 983.20]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [71, 67, 79, 80]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7559
  Bounding Box: [358.40, 1787.20, 641.60, 2024.00]
  Mask Area: 273 pixels
  Mask Ratio: 0.0097
  Mask BBox: [32, 144, 54, 161]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7520
  Bounding Box: [862.40, 257.60, 1160.00, 505.60]
  Mask Area: 374 pixels
  Mask Ratio: 0.0133
  Mask BBox: [72, 25, 94, 43]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7500
  Bounding Box: [1595.20, 840.80, 1780.80, 1274.40]
  Mask Area: 387 pixels
  Mask Ratio: 0.0137
  Mask BBox: [129, 70, 143, 103]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7495
  Bounding Box: [187.80, 806.40, 345.40, 987.20]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [19, 67, 30, 81]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7466
  Bounding Box: [652.40, 1886.40, 849.60, 2040.00]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [55, 152, 70, 163]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7451
  Bounding Box: [287.20, 565.60, 460.80, 823.20]
  Mask Area: 211 pixels
  Mask Ratio: 0.0075
  Mask BBox: [27, 49, 39, 68]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7441
  Bounding Box: [316.00, 38.70, 440.80, 204.40]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [29, 8, 38, 19]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7422
  Bounding Box: [1440.80, 608.80, 1572.00, 753.60]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [117, 52, 126, 62]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7407
  Bounding Box: [687.20, 315.80, 853.60, 548.00]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [58, 30, 70, 46]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7407
  Bounding Box: [1184.00, 665.60, 1416.00, 1120.00]
  Mask Area: 444 pixels
  Mask Ratio: 0.0157
  Mask BBox: [97, 56, 113, 91]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7383
  Bounding Box: [329.20, 341.40, 588.40, 529.20]
  Mask Area: 186 pixels
  Mask Ratio: 0.0066
  Mask BBox: [30, 31, 49, 45]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7368
  Bounding Box: [1214.40, 387.20, 1384.00, 593.60]
  Mask Area: 131 pixels
  Mask Ratio: 0.0046
  Mask BBox: [99, 35, 112, 50]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7354
  Bounding Box: [742.80, 508.00, 1020.80, 834.40]
  Mask Area: 423 pixels
  Mask Ratio: 0.0150
  Mask BBox: [63, 44, 83, 69]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7354
  Bounding Box: [1766.40, 567.20, 1952.00, 742.40]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [142, 49, 156, 61]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7275
  Bounding Box: [2.00, 208.20, 118.80, 333.40]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [5, 21, 13, 30]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7183
  Bounding Box: [903.20, 987.20, 997.60, 1129.60]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [75, 82, 81, 92]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7134
  Bounding Box: [732.80, 6.75, 928.00, 126.80]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [62, 5, 75, 13]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7119
  Bounding Box: [613.60, 772.80, 743.20, 948.80]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [52, 65, 62, 78]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7104
  Bounding Box: [280.40, 1931.20, 412.80, 2036.80]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [26, 155, 36, 163]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7100
  Bounding Box: [1915.20, 1390.40, 2043.20, 1553.60]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [154, 113, 163, 125]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7100
  Bounding Box: [1718.40, 192.40, 1952.00, 402.80]
  Mask Area: 205 pixels
  Mask Ratio: 0.0073
  Mask BBox: [139, 20, 156, 35]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7061
  Bounding Box: [1031.20, 1793.60, 1164.00, 1931.20]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [85, 145, 94, 153]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.7036
  Bounding Box: [198.20, 188.60, 339.40, 293.00]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [20, 19, 30, 26]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.7007
  Bounding Box: [1940.80, 484.80, 2033.60, 612.00]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [156, 42, 162, 51]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.6968
  Bounding Box: [1279.20, 568.80, 1428.00, 704.00]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [104, 49, 114, 57]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.6953
  Bounding Box: [580.00, 43.60, 783.20, 296.80]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [50, 8, 65, 27]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.6899
  Bounding Box: [477.60, 921.60, 560.80, 1046.40]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [42, 76, 47, 85]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.6880
  Bounding Box: [1408.00, 1039.20, 1555.20, 1180.00]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [114, 86, 125, 96]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6870
  Bounding Box: [749.20, 117.20, 880.00, 350.40]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [63, 14, 71, 31]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6855
  Bounding Box: [1388.80, 904.00, 1505.60, 1048.00]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [113, 75, 120, 85]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6821
  Bounding Box: [1979.20, 866.40, 2043.20, 1031.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [159, 72, 163, 84]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6792
  Bounding Box: [1753.60, 1326.40, 1859.20, 1448.00]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [141, 108, 149, 117]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6748
  Bounding Box: [1333.60, 1630.40, 1469.60, 1771.20]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [110, 132, 118, 142]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6743
  Bounding Box: [686.40, 952.80, 929.60, 1325.60]
  Mask Area: 410 pixels
  Mask Ratio: 0.0145
  Mask BBox: [58, 79, 76, 107]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6714
  Bounding Box: [1571.20, 504.00, 1660.80, 596.00]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [127, 44, 133, 50]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6704
  Bounding Box: [1041.60, 0.00, 1185.60, 80.50]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [86, 4, 96, 10]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6689
  Bounding Box: [878.40, 1654.40, 1059.20, 1827.20]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [73, 134, 86, 146]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6685
  Bounding Box: [1688.00, 1536.80, 1768.00, 1619.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [136, 125, 142, 130]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6680
  Bounding Box: [27.90, 1322.40, 230.40, 1588.80]
  Mask Area: 251 pixels
  Mask Ratio: 0.0089
  Mask BBox: [7, 108, 21, 128]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6670
  Bounding Box: [1208.00, 1364.80, 1385.60, 1673.60]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [99, 111, 112, 134]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6665
  Bounding Box: [1240.00, 1148.80, 1379.20, 1342.40]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [102, 94, 111, 107]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6655
  Bounding Box: [131.00, 428.80, 353.00, 699.20]
  Mask Area: 250 pixels
  Mask Ratio: 0.0089
  Mask BBox: [15, 38, 31, 58]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6650
  Bounding Box: [1388.80, 1191.20, 1515.20, 1317.60]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [113, 98, 122, 106]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6646
  Bounding Box: [1174.40, 1673.60, 1403.20, 1926.40]
  Mask Area: 230 pixels
  Mask Ratio: 0.0081
  Mask BBox: [96, 135, 113, 154]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6602
  Bounding Box: [1960.00, 286.80, 2040.00, 414.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [158, 27, 163, 35]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6597
  Bounding Box: [850.40, 1844.80, 1088.80, 2011.20]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [71, 149, 88, 161]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6504
  Bounding Box: [42.15, 986.40, 201.40, 1109.60]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [8, 82, 19, 90]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6475
  Bounding Box: [735.60, 1487.20, 891.20, 1816.00]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [62, 121, 73, 145]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6431
  Bounding Box: [1472.80, 328.20, 1736.00, 550.40]
  Mask Area: 218 pixels
  Mask Ratio: 0.0077
  Mask BBox: [120, 30, 139, 46]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6426
  Bounding Box: [1982.40, 616.40, 2033.60, 738.00]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [159, 53, 162, 61]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6416
  Bounding Box: [1720.00, 762.80, 1864.00, 908.80]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [139, 64, 149, 74]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6348
  Bounding Box: [1611.20, 1828.80, 1873.60, 2033.60]
  Mask Area: 233 pixels
  Mask Ratio: 0.0083
  Mask BBox: [130, 147, 149, 162]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6333
  Bounding Box: [760.80, 28.40, 908.00, 132.00]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [64, 7, 74, 14]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6328
  Bounding Box: [366.00, 1055.20, 644.40, 1285.60]
  Mask Area: 267 pixels
  Mask Ratio: 0.0095
  Mask BBox: [33, 87, 54, 104]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6323
  Bounding Box: [1463.20, 1763.20, 1543.20, 1865.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [119, 142, 124, 148]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6323
  Bounding Box: [1275.20, 1112.00, 1406.40, 1228.80]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [104, 91, 113, 99]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6304
  Bounding Box: [125.20, 571.20, 233.20, 715.20]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [14, 50, 22, 59]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6294
  Bounding Box: [72.80, 1224.00, 181.40, 1339.20]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [10, 100, 16, 108]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.6240
  Bounding Box: [4.00, 0.20, 242.40, 216.00]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [5, 5, 22, 19]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.6206
  Bounding Box: [1083.20, 1934.40, 1208.00, 2033.60]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [89, 156, 98, 162]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.6196
  Bounding Box: [363.20, 1376.00, 633.60, 1688.00]
  Mask Area: 349 pixels
  Mask Ratio: 0.0124
  Mask BBox: [33, 112, 52, 135]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.6152
  Bounding Box: [552.00, 916.00, 704.80, 1087.20]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [48, 76, 59, 87]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.6138
  Bounding Box: [1056.80, 1710.40, 1183.20, 1844.80]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [87, 138, 95, 148]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.6006
  Bounding Box: [1702.40, 1537.60, 1788.80, 1611.20]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [137, 125, 143, 129]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.6001
  Bounding Box: [1558.40, 1697.60, 1648.00, 1819.20]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [126, 137, 132, 146]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5981
  Bounding Box: [43.00, 1632.00, 294.60, 1945.60]
  Mask Area: 345 pixels
  Mask Ratio: 0.0122
  Mask BBox: [8, 132, 27, 155]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5972
  Bounding Box: [197.20, 282.60, 342.00, 440.80]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [20, 27, 30, 38]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5952
  Bounding Box: [1263.20, 3.65, 1381.60, 105.40]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [103, 5, 110, 10]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5938
  Bounding Box: [1568.80, 1234.40, 1779.20, 1448.80]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [127, 101, 142, 117]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5938
  Bounding Box: [1712.00, 1756.80, 1875.20, 1936.00]
  Mask Area: 96 pixels
  Mask Ratio: 0.0034
  Mask BBox: [138, 142, 149, 155]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5894
  Bounding Box: [1476.00, 754.40, 1644.80, 917.60]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [120, 63, 132, 75]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5869
  Bounding Box: [1880.00, 1565.60, 2040.00, 1664.00]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [151, 127, 162, 133]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5864
  Bounding Box: [187.20, 8.10, 343.20, 94.30]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [19, 5, 30, 11]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5830
  Bounding Box: [1368.00, 1315.20, 1460.80, 1401.60]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [111, 107, 118, 113]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5801
  Bounding Box: [1865.60, 389.60, 2016.00, 543.20]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [150, 35, 161, 46]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5757
  Bounding Box: [202.00, 1125.60, 300.80, 1186.40]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [20, 92, 27, 96]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5747
  Bounding Box: [1681.60, 1608.00, 1774.40, 1755.20]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [136, 130, 142, 140]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5601
  Bounding Box: [1633.60, 790.40, 1707.20, 856.00]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [132, 66, 137, 70]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5591
  Bounding Box: [1780.80, 955.20, 2014.40, 1300.80]
  Mask Area: 306 pixels
  Mask Ratio: 0.0108
  Mask BBox: [144, 79, 161, 105]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5566
  Bounding Box: [1990.40, 1364.80, 2041.60, 1480.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [160, 111, 163, 119]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5532
  Bounding Box: [1206.40, 4.00, 1302.40, 86.70]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [99, 5, 105, 10]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5522
  Bounding Box: [80.90, 731.20, 270.80, 884.80]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [11, 62, 25, 73]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5483
  Bounding Box: [728.00, 1596.80, 862.40, 1814.40]
  Mask Area: 141 pixels
  Mask Ratio: 0.0050
  Mask BBox: [61, 129, 71, 145]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5483
  Bounding Box: [1418.40, 1843.20, 1601.60, 2044.80]
  Mask Area: 188 pixels
  Mask Ratio: 0.0067
  Mask BBox: [115, 148, 129, 163]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5396
  Bounding Box: [177.20, 18.60, 317.60, 115.20]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [18, 6, 28, 12]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5366
  Bounding Box: [85.80, 287.00, 208.60, 408.00]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [11, 27, 19, 35]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5361
  Bounding Box: [428.00, 13.60, 552.00, 159.60]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [38, 6, 47, 15]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5312
  Bounding Box: [1896.00, 697.20, 2027.20, 905.60]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [153, 59, 162, 74]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5273
  Bounding Box: [458.80, 50.10, 758.00, 368.80]
  Mask Area: 453 pixels
  Mask Ratio: 0.0161
  Mask BBox: [40, 8, 63, 32]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5259
  Bounding Box: [742.00, 1819.20, 852.80, 1896.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [62, 147, 70, 152]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5249
  Bounding Box: [941.60, 172.60, 1064.80, 281.80]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [78, 18, 87, 24]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.5181
  Bounding Box: [218.80, 0.00, 374.40, 74.00]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [22, 4, 33, 9]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.5132
  Bounding Box: [892.80, 1514.40, 972.80, 1649.60]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [74, 123, 79, 132]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.5112
  Bounding Box: [1766.40, 1620.80, 2000.00, 1924.80]
  Mask Area: 249 pixels
  Mask Ratio: 0.0088
  Mask BBox: [142, 131, 160, 154]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.5103
  Bounding Box: [1528.00, 1124.80, 1624.00, 1219.20]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [124, 92, 130, 99]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.5024
  Bounding Box: [948.80, 489.60, 1040.00, 602.40]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [79, 43, 84, 50]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.4985
  Bounding Box: [902.40, 25.25, 1012.80, 138.40]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [75, 6, 83, 14]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.4971
  Bounding Box: [1076.80, 203.40, 1158.40, 309.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [89, 20, 93, 28]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.4961
  Bounding Box: [1493.60, 844.80, 1593.60, 1017.60]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [121, 70, 128, 83]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4946
  Bounding Box: [1616.00, 1588.80, 1712.00, 1742.40]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [131, 129, 137, 140]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.4917
  Bounding Box: [846.40, 128.20, 977.60, 315.40]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [71, 15, 79, 26]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4912
  Bounding Box: [198.00, 802.40, 418.00, 1024.80]
  Mask Area: 217 pixels
  Mask Ratio: 0.0077
  Mask BBox: [20, 67, 36, 84]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4907
  Bounding Box: [597.20, 1382.40, 765.20, 1596.80]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [51, 112, 63, 127]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4780
  Bounding Box: [355.20, 1259.20, 659.20, 1638.40]
  Mask Area: 484 pixels
  Mask Ratio: 0.0171
  Mask BBox: [32, 103, 55, 131]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4771
  Bounding Box: [886.40, 1974.40, 1035.20, 2035.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [74, 159, 84, 162]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4756
  Bounding Box: [1364.00, 374.00, 1528.80, 592.40]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [111, 34, 123, 50]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4736
  Bounding Box: [1963.20, 1670.40, 2048.00, 2012.80]
  Mask Area: 184 pixels
  Mask Ratio: 0.0065
  Mask BBox: [158, 135, 166, 161]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4688
  Bounding Box: [1121.60, 1916.80, 1233.60, 2044.80]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [92, 154, 100, 162]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4683
  Bounding Box: [1891.20, 1389.60, 2006.40, 1556.00]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [152, 113, 160, 125]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4673
  Bounding Box: [19.30, 992.00, 202.00, 1172.80]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [6, 82, 19, 95]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4673
  Bounding Box: [1630.40, 1402.40, 1883.20, 1586.40]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [132, 114, 151, 127]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4644
  Bounding Box: [657.20, 1278.40, 838.40, 1419.20]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [56, 104, 69, 114]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4641
  Bounding Box: [1832.00, 1053.60, 2048.00, 1373.60]
  Mask Area: 363 pixels
  Mask Ratio: 0.0129
  Mask BBox: [148, 87, 164, 111]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4639
  Bounding Box: [1194.40, 1884.80, 1343.20, 2032.00]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [98, 152, 108, 162]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4629
  Bounding Box: [1475.20, 796.80, 1608.00, 1012.80]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [120, 67, 129, 83]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4590
  Bounding Box: [1966.40, 1356.00, 2040.00, 1532.00]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [158, 110, 163, 123]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4548
  Bounding Box: [904.80, 1126.40, 1024.80, 1296.00]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [75, 92, 84, 105]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4514
  Bounding Box: [1660.80, 0.00, 1740.80, 85.80]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [134, 4, 139, 10]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4509
  Bounding Box: [1278.40, 0.00, 1364.80, 92.70]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [104, 4, 110, 11]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4509
  Bounding Box: [1512.80, 1573.60, 1635.20, 1707.20]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [123, 127, 131, 137]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4485
  Bounding Box: [1651.20, 1768.00, 1881.60, 1992.00]
  Mask Area: 177 pixels
  Mask Ratio: 0.0063
  Mask BBox: [133, 143, 149, 159]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4441
  Bounding Box: [1052.00, 1123.20, 1380.00, 1380.80]
  Mask Area: 364 pixels
  Mask Ratio: 0.0129
  Mask BBox: [87, 92, 111, 111]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4431
  Bounding Box: [0.00, 877.60, 117.10, 1023.20]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [4, 73, 13, 83]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4321
  Bounding Box: [564.00, 1432.00, 622.40, 1500.80]
  Mask Area: 17 pixels
  Mask Ratio: 0.0006
  Mask BBox: [49, 116, 51, 121]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4312
  Bounding Box: [1203.20, 1742.40, 1393.60, 1937.60]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [98, 141, 112, 154]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4258
  Bounding Box: [1174.40, 507.60, 1254.40, 601.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [97, 44, 101, 50]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4258
  Bounding Box: [815.20, 1385.60, 874.40, 1449.60]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [68, 113, 71, 117]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4243
  Bounding Box: [1705.60, 0.70, 1971.20, 188.00]
  Mask Area: 229 pixels
  Mask Ratio: 0.0081
  Mask BBox: [138, 5, 157, 18]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4148
  Bounding Box: [1904.00, 1024.00, 2044.80, 1356.80]
  Mask Area: 239 pixels
  Mask Ratio: 0.0085
  Mask BBox: [153, 84, 163, 109]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4143
  Bounding Box: [1459.20, 1648.00, 1544.00, 1760.00]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [118, 133, 124, 141]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4141
  Bounding Box: [888.80, 964.00, 981.60, 1116.00]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [74, 80, 80, 91]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4111
  Bounding Box: [7.90, 332.80, 91.20, 502.40]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [5, 30, 11, 43]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4099
  Bounding Box: [1019.20, 0.00, 1176.00, 132.00]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [84, 4, 95, 14]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.4072
  Bounding Box: [817.60, 1454.40, 912.00, 1571.20]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [69, 118, 75, 126]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.4072
  Bounding Box: [367.60, 1628.80, 464.40, 1744.00]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [33, 132, 39, 140]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.4065
  Bounding Box: [910.40, 967.20, 1017.60, 1109.60]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [76, 80, 81, 90]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.4011
  Bounding Box: [1502.40, 345.40, 1688.00, 580.80]
  Mask Area: 160 pixels
  Mask Ratio: 0.0057
  Mask BBox: [122, 31, 135, 49]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.4006
  Bounding Box: [1966.40, 1157.60, 2043.20, 1352.80]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [158, 95, 163, 109]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.3979
  Bounding Box: [1972.80, 46.40, 2043.20, 237.00]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [159, 8, 163, 20]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.3979
  Bounding Box: [1227.20, 1376.80, 1403.20, 1573.60]
  Mask Area: 166 pixels
  Mask Ratio: 0.0059
  Mask BBox: [100, 112, 113, 126]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.3970
  Bounding Box: [5.40, 1232.00, 87.50, 1470.40]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [5, 101, 10, 116]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.3948
  Bounding Box: [560.00, 1086.40, 693.60, 1232.00]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [48, 89, 58, 100]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.3909
  Bounding Box: [8.25, 1134.40, 66.40, 1228.80]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [5, 93, 9, 99]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3867
  Bounding Box: [1740.80, 1144.80, 1833.60, 1266.40]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [140, 94, 147, 102]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3850
  Bounding Box: [1471.20, 1745.60, 1572.00, 1857.60]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [119, 141, 126, 148]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3843
  Bounding Box: [255.40, 1905.60, 408.40, 2048.00]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [24, 153, 35, 164]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3831
  Bounding Box: [1132.80, 1652.80, 1358.40, 1892.80]
  Mask Area: 223 pixels
  Mask Ratio: 0.0079
  Mask BBox: [93, 134, 110, 151]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3792
  Bounding Box: [1037.60, 1740.80, 1172.00, 1916.80]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [86, 140, 95, 153]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3752
  Bounding Box: [167.60, 1974.40, 281.20, 2041.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [18, 159, 25, 163]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3701
  Bounding Box: [1852.80, 0.00, 2006.40, 142.10]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [149, 4, 158, 15]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3674
  Bounding Box: [1345.60, 1321.60, 1452.80, 1425.60]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [110, 108, 117, 113]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3638
  Bounding Box: [1740.80, 1550.40, 1792.00, 1640.00]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [140, 126, 143, 132]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3635
  Bounding Box: [1472.80, 1769.60, 1565.60, 1884.80]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [120, 143, 126, 148]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3606
  Bounding Box: [1345.60, 701.60, 1472.00, 874.40]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [110, 59, 117, 72]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3594
  Bounding Box: [1960.00, 1061.60, 2040.00, 1360.80]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [158, 87, 163, 110]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3572
  Bounding Box: [1348.00, 1298.40, 1450.40, 1397.60]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [110, 106, 117, 113]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3572
  Bounding Box: [1373.60, 1324.00, 1476.00, 1423.20]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [112, 108, 117, 113]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3560
  Bounding Box: [1234.40, 0.00, 1362.40, 97.40]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [101, 4, 110, 10]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3550
  Bounding Box: [611.60, 1587.20, 742.00, 1852.80]
  Mask Area: 180 pixels
  Mask Ratio: 0.0064
  Mask BBox: [52, 128, 61, 148]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3535
  Bounding Box: [166.40, 958.40, 344.80, 1153.60]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [17, 79, 30, 93]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3530
  Bounding Box: [1542.40, 1040.00, 1609.60, 1150.40]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [125, 86, 129, 93]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3528
  Bounding Box: [5.70, 69.40, 188.00, 333.80]
  Mask Area: 252 pixels
  Mask Ratio: 0.0089
  Mask BBox: [5, 10, 18, 30]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3513
  Bounding Box: [1715.20, 700.00, 1846.40, 898.40]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [138, 59, 148, 74]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3513
  Bounding Box: [42.90, 1656.00, 108.70, 1761.60]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [8, 134, 11, 141]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3513
  Bounding Box: [1375.20, 801.60, 1456.80, 894.40]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [112, 67, 117, 73]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3501
  Bounding Box: [1920.00, 1614.40, 2038.40, 1716.80]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [154, 131, 162, 137]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3501
  Bounding Box: [632.80, 780.80, 847.20, 963.20]
  Mask Area: 177 pixels
  Mask Ratio: 0.0063
  Mask BBox: [54, 65, 70, 79]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3481
  Bounding Box: [498.40, 1075.20, 686.40, 1244.80]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [43, 88, 57, 101]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3477
  Bounding Box: [1894.40, 1926.40, 2019.20, 2048.00]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [152, 155, 161, 163]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3464
  Bounding Box: [892.00, 1798.40, 1023.20, 1881.60]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [74, 145, 83, 150]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3445
  Bounding Box: [789.60, 16.60, 973.60, 131.80]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [66, 6, 80, 14]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3442
  Bounding Box: [1963.20, 607.20, 2048.00, 742.40]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [158, 52, 164, 61]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3433
  Bounding Box: [0.00, 552.40, 75.30, 715.60]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [4, 48, 9, 59]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3433
  Bounding Box: [1761.60, 897.60, 1857.60, 995.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [143, 75, 149, 81]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3420
  Bounding Box: [37.15, 1979.20, 194.40, 2048.00]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [7, 159, 19, 164]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3420
  Bounding Box: [314.00, 42.00, 554.00, 278.00]
  Mask Area: 225 pixels
  Mask Ratio: 0.0080
  Mask BBox: [29, 8, 47, 25]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3413
  Bounding Box: [749.20, 1451.20, 911.20, 1707.20]
  Mask Area: 194 pixels
  Mask Ratio: 0.0069
  Mask BBox: [63, 118, 75, 137]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3389
  Bounding Box: [563.20, 916.00, 764.00, 1111.20]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [48, 76, 63, 90]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3386
  Bounding Box: [47.20, 1222.40, 177.20, 1408.00]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [8, 100, 17, 113]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3374
  Bounding Box: [158.40, 1401.60, 360.80, 1598.40]
  Mask Area: 179 pixels
  Mask Ratio: 0.0063
  Mask BBox: [17, 114, 32, 128]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3369
  Bounding Box: [1902.40, 426.40, 2040.00, 618.40]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [153, 38, 163, 52]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3359
  Bounding Box: [396.00, 1969.60, 479.20, 2030.40]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [35, 158, 41, 162]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3320
  Bounding Box: [217.40, 976.80, 364.00, 1152.80]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [21, 81, 32, 92]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3308
  Bounding Box: [412.00, 1518.40, 637.60, 1796.80]
  Mask Area: 240 pixels
  Mask Ratio: 0.0085
  Mask BBox: [37, 123, 53, 144]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3306
  Bounding Box: [1753.60, 1260.00, 1891.20, 1440.80]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [141, 103, 151, 116]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3293
  Bounding Box: [780.80, 1964.80, 912.00, 2035.20]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [65, 158, 75, 162]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3276
  Bounding Box: [6.30, 1110.40, 71.50, 1198.40]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [5, 91, 8, 97]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3276
  Bounding Box: [6.45, 1524.00, 76.80, 1673.60]
  Mask Area: 53 pixels
  Mask Ratio: 0.0019
  Mask BBox: [5, 124, 9, 134]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3252
  Bounding Box: [1396.80, 1774.40, 1448.00, 1883.20]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [114, 143, 117, 151]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3247
  Bounding Box: [1934.40, 1630.40, 2033.60, 1742.40]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [156, 132, 162, 139]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3237
  Bounding Box: [6.15, 1493.60, 69.80, 1620.80]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [5, 121, 9, 130]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3235
  Bounding Box: [20.70, 220.40, 167.20, 371.60]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [6, 22, 17, 33]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3230
  Bounding Box: [208.80, 170.00, 368.80, 278.00]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [21, 18, 32, 25]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [474.00, 1611.20, 689.20, 1825.60]
  Mask Area: 165 pixels
  Mask Ratio: 0.0058
  Mask BBox: [42, 130, 57, 146]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [1899.20, 131.90, 2048.00, 308.40]
  Mask Area: 120 pixels
  Mask Ratio: 0.0043
  Mask BBox: [153, 15, 165, 28]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3213
  Bounding Box: [189.40, 1137.60, 273.80, 1212.80]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [19, 93, 25, 98]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3193
  Bounding Box: [143.80, 296.40, 351.40, 458.80]
  Mask Area: 157 pixels
  Mask Ratio: 0.0056
  Mask BBox: [16, 28, 31, 39]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3188
  Bounding Box: [630.00, 337.20, 822.40, 558.80]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [54, 31, 68, 47]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3184
  Bounding Box: [1440.00, 1780.80, 1585.60, 1992.00]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [117, 144, 127, 159]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3179
  Bounding Box: [1327.20, 268.00, 1485.60, 410.40]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [108, 25, 120, 35]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3154
  Bounding Box: [1389.60, 623.20, 1556.00, 820.00]
  Mask Area: 152 pixels
  Mask Ratio: 0.0054
  Mask BBox: [113, 53, 125, 68]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3154
  Bounding Box: [1720.00, 1710.40, 1928.00, 1960.00]
  Mask Area: 181 pixels
  Mask Ratio: 0.0064
  Mask BBox: [139, 138, 154, 157]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3152
  Bounding Box: [59.50, 1172.00, 175.60, 1341.60]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [9, 96, 16, 108]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3149
  Bounding Box: [1403.20, 1638.40, 1555.20, 1766.40]
  Mask Area: 110 pixels
  Mask Ratio: 0.0039
  Mask BBox: [114, 132, 125, 141]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3145
  Bounding Box: [6.30, 1913.60, 65.90, 2016.00]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [5, 154, 9, 161]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3120
  Bounding Box: [913.60, 482.80, 1056.00, 584.40]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [76, 42, 84, 49]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3083
  Bounding Box: [273.00, 1389.60, 586.40, 1668.80]
  Mask Area: 395 pixels
  Mask Ratio: 0.0140
  Mask BBox: [26, 113, 49, 134]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3076
  Bounding Box: [392.80, 1225.60, 653.60, 1472.00]
  Mask Area: 305 pixels
  Mask Ratio: 0.0108
  Mask BBox: [35, 100, 55, 118]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [108.50, 372.40, 218.40, 470.00]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [13, 34, 21, 40]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [231.80, 1598.40, 340.20, 1675.20]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [23, 129, 30, 134]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [4.70, 857.60, 154.00, 1035.20]
  Mask Area: 143 pixels
  Mask Ratio: 0.0051
  Mask BBox: [5, 71, 16, 84]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.3049
  Bounding Box: [1736.00, 813.60, 1860.80, 946.40]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [140, 68, 149, 77]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.3047
  Bounding Box: [32.20, 1598.40, 109.40, 1742.40]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [7, 129, 11, 140]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.3040
  Bounding Box: [0.00, 1937.60, 118.50, 2033.60]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [4, 156, 13, 162]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.3037
  Bounding Box: [899.20, 1089.60, 1172.80, 1350.40]
  Mask Area: 236 pixels
  Mask Ratio: 0.0084
  Mask BBox: [75, 90, 95, 109]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.3035
  Bounding Box: [1856.00, 651.20, 2041.60, 879.20]
  Mask Area: 192 pixels
  Mask Ratio: 0.0068
  Mask BBox: [149, 55, 163, 72]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.3027
  Bounding Box: [423.60, 1984.00, 545.20, 2041.60]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [38, 159, 46, 162]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.3025
  Bounding Box: [1184.00, 0.30, 1275.20, 65.80]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [97, 5, 103, 9]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.3025
  Bounding Box: [292.80, 1849.60, 592.80, 2041.60]
  Mask Area: 301 pixels
  Mask Ratio: 0.0107
  Mask BBox: [27, 149, 50, 163]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.3020
  Bounding Box: [1348.80, 1651.20, 1488.00, 1798.40]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [110, 133, 120, 144]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.3008
  Bounding Box: [1993.60, 844.80, 2048.00, 1019.20]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [160, 70, 163, 83]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.3000
  Bounding Box: [1247.20, 1600.00, 1359.20, 1699.20]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [102, 129, 110, 136]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.2996
  Bounding Box: [584.80, 1748.80, 693.60, 1867.20]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [50, 141, 58, 149]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.2986
  Bounding Box: [96.00, 314.40, 220.00, 451.20]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [12, 29, 21, 39]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.2969
  Bounding Box: [1993.60, 416.80, 2038.40, 557.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [160, 37, 163, 47]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.2966
  Bounding Box: [1543.20, 997.60, 1603.20, 1120.80]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [125, 82, 129, 91]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.2959
  Bounding Box: [1782.40, 1100.80, 1987.20, 1358.40]
  Mask Area: 282 pixels
  Mask Ratio: 0.0100
  Mask BBox: [144, 90, 159, 110]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.2947
  Bounding Box: [1055.20, 1803.20, 1200.80, 2030.40]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [87, 145, 97, 162]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.2937
  Bounding Box: [581.20, 856.00, 722.80, 1089.60]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [50, 71, 60, 89]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.2922
  Bounding Box: [806.40, 1388.00, 891.20, 1504.80]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [67, 113, 73, 121]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.2910
  Bounding Box: [51.70, 1161.60, 160.80, 1235.20]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [9, 95, 16, 100]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.2910
  Bounding Box: [570.80, 1684.80, 714.00, 1873.60]
  Mask Area: 135 pixels
  Mask Ratio: 0.0048
  Mask BBox: [49, 136, 59, 150]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.2893
  Bounding Box: [1508.80, 307.20, 1694.40, 389.20]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [122, 28, 136, 33]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.2891
  Bounding Box: [124.70, 536.00, 288.80, 724.00]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [14, 46, 26, 60]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2883
  Bounding Box: [66.40, 468.00, 142.00, 594.40]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [10, 41, 15, 50]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2866
  Bounding Box: [890.40, 16.50, 988.00, 123.50]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [74, 6, 81, 13]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2866
  Bounding Box: [539.20, 1412.80, 609.60, 1480.00]
  Mask Area: 25 pixels
  Mask Ratio: 0.0009
  Mask BBox: [47, 115, 51, 119]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2866
  Bounding Box: [564.80, 1412.80, 635.20, 1480.00]
  Mask Area: 16 pixels
  Mask Ratio: 0.0006
  Mask BBox: [49, 115, 52, 119]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2866
  Bounding Box: [539.20, 1438.40, 609.60, 1505.60]
  Mask Area: 19 pixels
  Mask Ratio: 0.0007
  Mask BBox: [47, 117, 51, 120]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2859
  Bounding Box: [162.60, 1931.20, 279.80, 2043.20]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [17, 155, 25, 163]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2856
  Bounding Box: [621.60, 1094.40, 860.00, 1366.40]
  Mask Area: 376 pixels
  Mask Ratio: 0.0133
  Mask BBox: [53, 90, 71, 110]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2847
  Bounding Box: [712.80, 971.20, 916.00, 1233.60]
  Mask Area: 259 pixels
  Mask Ratio: 0.0092
  Mask BBox: [60, 80, 75, 100]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2839
  Bounding Box: [1937.60, 1134.40, 2048.00, 1366.40]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [156, 93, 164, 110]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2837
  Bounding Box: [894.40, 40.50, 987.20, 150.50]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [74, 8, 81, 14]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2834
  Bounding Box: [1053.60, 1662.40, 1170.40, 1748.80]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [87, 134, 93, 140]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2834
  Bounding Box: [836.80, 135.60, 1028.80, 373.20]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [70, 15, 84, 33]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2810
  Bounding Box: [1937.60, 39.30, 2048.00, 210.80]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [156, 8, 164, 20]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2808
  Bounding Box: [853.60, 1764.80, 1053.60, 2001.60]
  Mask Area: 241 pixels
  Mask Ratio: 0.0085
  Mask BBox: [71, 142, 86, 160]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2795
  Bounding Box: [264.80, 1392.80, 413.20, 1600.00]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [25, 113, 36, 128]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2795
  Bounding Box: [0.00, 316.20, 101.90, 467.60]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [4, 29, 11, 40]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2793
  Bounding Box: [111.50, 1971.20, 264.00, 2035.20]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [13, 158, 24, 162]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2791
  Bounding Box: [833.60, 794.40, 948.80, 970.40]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [70, 67, 78, 79]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2788
  Bounding Box: [1390.40, 1158.40, 1563.20, 1305.60]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [113, 95, 126, 105]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2761
  Bounding Box: [1164.80, 1764.80, 1280.00, 1867.20]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [95, 142, 103, 149]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2754
  Bounding Box: [118.90, 168.40, 207.40, 288.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [14, 18, 20, 26]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2751
  Bounding Box: [640.80, 1013.60, 959.20, 1367.20]
  Mask Area: 424 pixels
  Mask Ratio: 0.0150
  Mask BBox: [55, 84, 78, 110]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2717
  Bounding Box: [346.80, 1638.40, 433.20, 1766.40]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [32, 132, 37, 141]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2712
  Bounding Box: [634.40, 749.60, 765.60, 919.20]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [54, 64, 63, 75]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2705
  Bounding Box: [1568.00, 1683.20, 1673.60, 1811.20]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [127, 136, 134, 145]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2705
  Bounding Box: [25.75, 202.20, 143.00, 316.60]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [7, 20, 15, 28]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2705
  Bounding Box: [1646.40, 438.00, 1931.20, 651.60]
  Mask Area: 263 pixels
  Mask Ratio: 0.0093
  Mask BBox: [133, 39, 154, 54]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2705
  Bounding Box: [1531.20, 776.80, 1659.20, 909.60]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [124, 65, 133, 75]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2703
  Bounding Box: [13.60, 1971.20, 160.80, 2044.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [6, 158, 16, 163]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2693
  Bounding Box: [229.80, 306.00, 359.20, 485.20]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [22, 28, 32, 39]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2681
  Bounding Box: [16.60, 1555.20, 98.00, 1676.80]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [6, 126, 11, 134]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2671
  Bounding Box: [1193.60, 0.00, 1289.60, 60.10]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [98, 3, 104, 8]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2671
  Bounding Box: [1219.20, 0.00, 1315.20, 60.10]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [100, 3, 106, 8]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2666
  Bounding Box: [58.20, 1254.40, 166.40, 1363.20]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [9, 102, 16, 108]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2666
  Bounding Box: [1390.40, 375.20, 1667.20, 597.60]
  Mask Area: 271 pixels
  Mask Ratio: 0.0096
  Mask BBox: [113, 34, 134, 50]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2659
  Bounding Box: [491.20, 913.60, 600.00, 1044.80]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [43, 76, 50, 85]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2659
  Bounding Box: [465.60, 939.20, 574.40, 1070.40]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [41, 78, 48, 87]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2656
  Bounding Box: [1480.80, 317.60, 1731.20, 465.60]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [120, 29, 138, 40]

