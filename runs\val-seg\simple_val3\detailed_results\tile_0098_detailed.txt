Image: tile_0098.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8687
  Bounding Box: [19.70, 751.20, 250.00, 1005.60]
  Mask Area: 283 pixels
  Mask Ratio: 0.0100
  Mask BBox: [6, 63, 23, 82]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8623
  Bounding Box: [1357.60, 24.40, 1584.80, 274.00]
  Mask Area: 234 pixels
  Mask Ratio: 0.0083
  Mask BBox: [111, 6, 126, 25]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8618
  Bounding Box: [115.30, 8.00, 326.80, 230.80]
  Mask Area: 206 pixels
  Mask Ratio: 0.0073
  Mask BBox: [14, 5, 29, 22]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8516
  Bounding Box: [1511.20, 1345.60, 1705.60, 1561.60]
  Mask Area: 165 pixels
  Mask Ratio: 0.0058
  Mask BBox: [123, 110, 137, 125]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8516
  Bounding Box: [23.60, 554.00, 204.00, 754.80]
  Mask Area: 181 pixels
  Mask Ratio: 0.0064
  Mask BBox: [6, 48, 19, 62]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8267
  Bounding Box: [187.40, 1556.00, 376.40, 1795.20]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [19, 126, 33, 144]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8062
  Bounding Box: [337.40, 573.20, 466.40, 710.00]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [31, 49, 40, 59]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.7974
  Bounding Box: [308.00, 872.00, 471.20, 1091.20]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [29, 73, 38, 89]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.7964
  Bounding Box: [1284.80, 1035.20, 1547.20, 1300.80]
  Mask Area: 349 pixels
  Mask Ratio: 0.0124
  Mask BBox: [105, 85, 124, 105]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.7954
  Bounding Box: [443.20, 1926.40, 559.20, 2028.80]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [39, 155, 47, 162]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.7949
  Bounding Box: [1053.60, 2.60, 1263.20, 290.80]
  Mask Area: 235 pixels
  Mask Ratio: 0.0083
  Mask BBox: [87, 5, 101, 26]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.7935
  Bounding Box: [592.80, 1420.80, 748.00, 1643.20]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [51, 115, 62, 132]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.7900
  Bounding Box: [1608.00, 1948.80, 1752.00, 2041.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [130, 157, 139, 163]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.7881
  Bounding Box: [706.00, 189.20, 872.80, 470.40]
  Mask Area: 229 pixels
  Mask Ratio: 0.0081
  Mask BBox: [60, 19, 72, 40]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.7856
  Bounding Box: [760.00, 464.40, 889.60, 702.00]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [64, 41, 73, 58]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.7852
  Bounding Box: [1649.60, 1547.20, 1758.40, 1681.60]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [133, 125, 141, 134]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.7822
  Bounding Box: [889.60, 1019.20, 1012.80, 1163.20]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [74, 84, 83, 94]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.7769
  Bounding Box: [1335.20, 230.80, 1493.60, 524.40]
  Mask Area: 205 pixels
  Mask Ratio: 0.0073
  Mask BBox: [109, 23, 120, 44]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7769
  Bounding Box: [1656.00, 38.30, 1864.00, 377.20]
  Mask Area: 321 pixels
  Mask Ratio: 0.0114
  Mask BBox: [134, 7, 148, 33]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7749
  Bounding Box: [576.00, 1809.60, 736.80, 2001.60]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [49, 146, 61, 160]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7734
  Bounding Box: [567.20, 963.20, 740.00, 1224.00]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [49, 80, 61, 99]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7729
  Bounding Box: [154.40, 1851.20, 366.00, 2046.40]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [17, 149, 32, 163]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7729
  Bounding Box: [1160.80, 660.80, 1344.80, 897.60]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [95, 56, 108, 74]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7720
  Bounding Box: [792.00, 1232.00, 1073.60, 1459.20]
  Mask Area: 228 pixels
  Mask Ratio: 0.0081
  Mask BBox: [67, 101, 87, 117]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7715
  Bounding Box: [659.20, 1564.00, 902.40, 1939.20]
  Mask Area: 381 pixels
  Mask Ratio: 0.0135
  Mask BBox: [56, 127, 74, 155]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7686
  Bounding Box: [844.80, 139.50, 1016.00, 545.60]
  Mask Area: 318 pixels
  Mask Ratio: 0.0113
  Mask BBox: [70, 15, 83, 46]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7676
  Bounding Box: [363.20, 1320.00, 495.20, 1481.60]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [33, 108, 42, 118]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7661
  Bounding Box: [985.60, 693.20, 1156.80, 814.40]
  Mask Area: 86 pixels
  Mask Ratio: 0.0030
  Mask BBox: [81, 59, 93, 66]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7651
  Bounding Box: [1796.80, 513.60, 2024.00, 747.20]
  Mask Area: 201 pixels
  Mask Ratio: 0.0071
  Mask BBox: [145, 45, 162, 62]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7646
  Bounding Box: [670.00, 39.20, 805.60, 193.80]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [57, 8, 65, 18]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7642
  Bounding Box: [333.20, 1780.80, 452.40, 2014.40]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [31, 144, 38, 161]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7568
  Bounding Box: [204.80, 1274.40, 371.20, 1560.80]
  Mask Area: 212 pixels
  Mask Ratio: 0.0075
  Mask BBox: [20, 104, 32, 125]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7529
  Bounding Box: [1811.20, 1563.20, 1897.60, 1752.00]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [146, 127, 152, 140]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7515
  Bounding Box: [1092.80, 910.40, 1286.40, 1105.60]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [90, 76, 104, 89]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7383
  Bounding Box: [1697.60, 1115.20, 1832.00, 1291.20]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [137, 92, 147, 103]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7373
  Bounding Box: [49.00, 1256.00, 235.20, 1476.80]
  Mask Area: 188 pixels
  Mask Ratio: 0.0067
  Mask BBox: [8, 103, 22, 119]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7329
  Bounding Box: [543.60, 635.20, 689.20, 835.20]
  Mask Area: 138 pixels
  Mask Ratio: 0.0049
  Mask BBox: [47, 54, 57, 69]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7324
  Bounding Box: [898.40, 1748.80, 1138.40, 2033.60]
  Mask Area: 242 pixels
  Mask Ratio: 0.0086
  Mask BBox: [75, 141, 91, 162]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7300
  Bounding Box: [0.00, 1881.60, 162.20, 2044.80]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [4, 151, 16, 163]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7290
  Bounding Box: [1248.80, 893.60, 1396.00, 1061.60]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [102, 74, 113, 85]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7285
  Bounding Box: [1812.80, 1412.00, 1944.00, 1528.80]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [146, 115, 154, 123]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7275
  Bounding Box: [772.80, 1902.40, 907.20, 2046.40]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [65, 153, 74, 162]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7251
  Bounding Box: [1501.60, 214.00, 1633.60, 352.80]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [122, 21, 131, 31]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7236
  Bounding Box: [1113.60, 1720.00, 1257.60, 1921.60]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [91, 139, 102, 154]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.7231
  Bounding Box: [1987.20, 1916.80, 2041.60, 2041.60]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [160, 154, 163, 163]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.7178
  Bounding Box: [1576.80, 691.60, 1792.00, 1187.20]
  Mask Area: 498 pixels
  Mask Ratio: 0.0176
  Mask BBox: [128, 59, 143, 96]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.7168
  Bounding Box: [1222.40, 1712.00, 1291.20, 1788.80]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [100, 138, 104, 143]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.7163
  Bounding Box: [704.00, 780.00, 900.80, 1104.80]
  Mask Area: 317 pixels
  Mask Ratio: 0.0112
  Mask BBox: [59, 65, 73, 90]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.7144
  Bounding Box: [11.05, 141.70, 105.80, 274.40]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [5, 16, 11, 24]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.7129
  Bounding Box: [1508.80, 0.00, 1638.40, 84.40]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [122, 4, 131, 10]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.7109
  Bounding Box: [547.20, 1598.40, 648.80, 1748.80]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [47, 129, 54, 140]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.7080
  Bounding Box: [373.20, 1568.80, 561.20, 1840.00]
  Mask Area: 220 pixels
  Mask Ratio: 0.0078
  Mask BBox: [34, 127, 47, 147]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.7065
  Bounding Box: [798.40, 1137.60, 894.40, 1249.60]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [67, 93, 72, 100]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.7021
  Bounding Box: [291.60, 127.40, 503.60, 344.20]
  Mask Area: 149 pixels
  Mask Ratio: 0.0053
  Mask BBox: [27, 14, 41, 30]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.7007
  Bounding Box: [1140.80, 391.60, 1225.60, 592.40]
  Mask Area: 61 pixels
  Mask Ratio: 0.0022
  Mask BBox: [94, 35, 98, 50]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.7002
  Bounding Box: [325.60, 8.55, 528.80, 120.80]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [30, 5, 45, 13]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6987
  Bounding Box: [631.20, 444.80, 766.40, 656.00]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [54, 39, 63, 53]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6987
  Bounding Box: [875.20, 26.70, 1104.00, 362.80]
  Mask Area: 345 pixels
  Mask Ratio: 0.0122
  Mask BBox: [73, 7, 90, 32]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6982
  Bounding Box: [1322.40, 726.00, 1426.40, 852.00]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [108, 61, 115, 68]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6924
  Bounding Box: [448.40, 1520.00, 561.20, 1601.60]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [40, 123, 47, 129]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6904
  Bounding Box: [1575.20, 304.80, 1811.20, 511.20]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [129, 28, 145, 43]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6870
  Bounding Box: [769.60, 0.00, 916.80, 78.50]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [65, 4, 75, 10]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6865
  Bounding Box: [1174.40, 1486.40, 1281.60, 1619.20]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [96, 121, 104, 130]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6836
  Bounding Box: [355.80, 31.30, 492.80, 130.10]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [32, 7, 42, 13]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6831
  Bounding Box: [864.80, 1492.80, 1015.20, 1771.20]
  Mask Area: 176 pixels
  Mask Ratio: 0.0062
  Mask BBox: [72, 121, 83, 142]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6797
  Bounding Box: [1974.40, 248.00, 2044.80, 365.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [159, 24, 163, 32]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6763
  Bounding Box: [1902.40, 840.00, 2046.40, 1057.60]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [153, 70, 163, 85]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6738
  Bounding Box: [1203.20, 1716.80, 1288.00, 1803.20]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [98, 139, 104, 143]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6709
  Bounding Box: [468.40, 1291.20, 639.60, 1508.80]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [41, 105, 53, 121]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6680
  Bounding Box: [1094.40, 1139.20, 1241.60, 1457.60]
  Mask Area: 191 pixels
  Mask Ratio: 0.0068
  Mask BBox: [90, 93, 100, 117]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6675
  Bounding Box: [976.00, 806.40, 1168.00, 908.80]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [81, 67, 94, 74]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6655
  Bounding Box: [505.60, 759.60, 692.00, 975.20]
  Mask Area: 182 pixels
  Mask Ratio: 0.0064
  Mask BBox: [44, 64, 58, 80]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6626
  Bounding Box: [1168.00, 1596.80, 1283.20, 1712.00]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [96, 129, 104, 137]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6611
  Bounding Box: [1860.80, 1546.40, 2043.20, 1934.40]
  Mask Area: 311 pixels
  Mask Ratio: 0.0110
  Mask BBox: [150, 125, 163, 155]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6606
  Bounding Box: [974.40, 451.60, 1161.60, 693.20]
  Mask Area: 186 pixels
  Mask Ratio: 0.0066
  Mask BBox: [81, 40, 93, 58]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6558
  Bounding Box: [173.20, 545.60, 310.00, 745.60]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [18, 47, 28, 61]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6558
  Bounding Box: [1016.00, 1019.20, 1137.60, 1187.20]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [84, 84, 91, 96]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.6484
  Bounding Box: [352.00, 1470.40, 462.40, 1548.80]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [32, 119, 40, 124]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.6470
  Bounding Box: [9.65, 1801.60, 89.60, 1875.20]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [5, 145, 10, 150]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.6445
  Bounding Box: [166.60, 1130.40, 326.20, 1274.40]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [18, 93, 28, 102]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.6436
  Bounding Box: [703.20, 1083.20, 804.00, 1252.80]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [59, 89, 66, 100]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.6426
  Bounding Box: [1256.80, 36.50, 1381.60, 297.20]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [103, 7, 111, 27]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.6411
  Bounding Box: [216.40, 1811.20, 350.80, 1926.40]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [22, 146, 31, 153]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.6348
  Bounding Box: [161.60, 1472.80, 243.20, 1575.20]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [17, 120, 22, 126]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.6250
  Bounding Box: [491.60, 1112.80, 595.60, 1266.40]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [43, 91, 50, 102]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.6216
  Bounding Box: [437.60, 1449.60, 525.60, 1521.60]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [39, 118, 45, 122]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.6211
  Bounding Box: [882.40, 1734.40, 1020.00, 1904.00]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [73, 140, 82, 152]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.6206
  Bounding Box: [516.80, 0.00, 619.20, 64.80]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [45, 4, 52, 9]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.6172
  Bounding Box: [1472.00, 339.20, 1568.00, 528.80]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [119, 31, 126, 45]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.6060
  Bounding Box: [680.80, 656.40, 887.20, 832.80]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [58, 56, 71, 69]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.6030
  Bounding Box: [1700.80, 1649.60, 1934.40, 2048.00]
  Mask Area: 483 pixels
  Mask Ratio: 0.0171
  Mask BBox: [137, 133, 155, 165]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.6016
  Bounding Box: [1131.20, 1888.00, 1273.60, 2035.20]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [93, 153, 102, 162]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5981
  Bounding Box: [0.35, 264.00, 111.60, 486.40]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [5, 25, 12, 41]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5903
  Bounding Box: [1356.80, 524.00, 1494.40, 680.00]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [110, 45, 120, 56]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5894
  Bounding Box: [573.60, 23.25, 696.80, 175.00]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [49, 6, 58, 17]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5889
  Bounding Box: [390.00, 325.00, 474.80, 492.80]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [35, 30, 41, 40]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5884
  Bounding Box: [1264.80, 1226.40, 1538.40, 1667.20]
  Mask Area: 650 pixels
  Mask Ratio: 0.0230
  Mask BBox: [103, 100, 124, 134]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5869
  Bounding Box: [1862.40, 284.00, 1993.60, 472.80]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [150, 27, 159, 40]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5845
  Bounding Box: [80.40, 1006.40, 218.40, 1176.00]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [11, 83, 21, 95]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5815
  Bounding Box: [919.20, 944.80, 1040.80, 1010.40]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [76, 78, 85, 82]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5776
  Bounding Box: [1312.00, 623.20, 1393.60, 723.20]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [107, 53, 112, 60]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5776
  Bounding Box: [441.60, 1827.20, 566.40, 1936.00]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [39, 147, 48, 155]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5776
  Bounding Box: [1252.00, 1531.20, 1609.60, 1988.80]
  Mask Area: 783 pixels
  Mask Ratio: 0.0277
  Mask BBox: [102, 124, 129, 159]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5762
  Bounding Box: [514.80, 1012.80, 610.00, 1148.80]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [45, 84, 50, 93]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5693
  Bounding Box: [72.80, 1712.00, 205.40, 1888.00]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [10, 138, 20, 151]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5659
  Bounding Box: [1563.20, 1182.40, 1867.20, 1561.60]
  Mask Area: 399 pixels
  Mask Ratio: 0.0141
  Mask BBox: [127, 97, 149, 125]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5615
  Bounding Box: [1539.20, 534.00, 1635.20, 631.60]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [125, 46, 131, 53]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5571
  Bounding Box: [1988.80, 1572.80, 2033.60, 1678.40]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [160, 127, 162, 134]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5552
  Bounding Box: [875.20, 749.20, 1001.60, 910.40]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [73, 63, 82, 75]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5522
  Bounding Box: [1731.20, 679.20, 1952.00, 903.20]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [140, 58, 156, 74]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5435
  Bounding Box: [229.20, 904.80, 306.40, 1013.60]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [22, 75, 27, 83]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.5342
  Bounding Box: [877.60, 466.00, 1023.20, 685.20]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [73, 41, 83, 57]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.5327
  Bounding Box: [1670.40, 516.00, 1766.40, 632.80]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [135, 45, 141, 52]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.5322
  Bounding Box: [1485.60, 1966.40, 1581.60, 2046.40]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [121, 158, 127, 163]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.5317
  Bounding Box: [8.10, 1505.60, 99.90, 1822.40]
  Mask Area: 145 pixels
  Mask Ratio: 0.0051
  Mask BBox: [5, 122, 11, 146]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.5303
  Bounding Box: [1274.40, 687.60, 1348.00, 786.40]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [104, 58, 109, 65]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.5264
  Bounding Box: [1563.20, 1795.20, 1691.20, 1945.60]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [127, 145, 136, 155]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.5259
  Bounding Box: [1824.00, 1175.20, 1916.80, 1316.00]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [147, 96, 153, 106]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.5254
  Bounding Box: [546.00, 1235.20, 688.40, 1440.00]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [47, 101, 57, 116]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.5229
  Bounding Box: [81.90, 1492.80, 175.20, 1630.40]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [11, 121, 17, 131]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.5220
  Bounding Box: [1221.60, 1249.60, 1308.00, 1356.80]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [100, 102, 106, 109]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.5176
  Bounding Box: [903.20, 1152.80, 1095.20, 1260.00]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [75, 95, 89, 102]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.5151
  Bounding Box: [329.20, 1180.00, 468.40, 1316.00]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [30, 97, 40, 105]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.5142
  Bounding Box: [198.00, 555.20, 359.60, 763.20]
  Mask Area: 144 pixels
  Mask Ratio: 0.0051
  Mask BBox: [20, 48, 32, 63]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.5137
  Bounding Box: [1864.00, 1288.80, 1963.20, 1384.80]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [150, 105, 157, 111]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.5068
  Bounding Box: [1851.20, 96.40, 2004.80, 264.00]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [149, 12, 160, 24]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.5068
  Bounding Box: [518.40, 182.20, 640.00, 322.20]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [45, 19, 53, 29]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.5068
  Bounding Box: [1769.60, 904.80, 1916.80, 1064.80]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [143, 75, 153, 87]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.5039
  Bounding Box: [1441.60, 653.60, 1620.80, 847.20]
  Mask Area: 157 pixels
  Mask Ratio: 0.0056
  Mask BBox: [117, 56, 130, 70]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.5024
  Bounding Box: [206.60, 1012.80, 352.20, 1156.80]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [21, 84, 31, 94]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4980
  Bounding Box: [1215.20, 564.00, 1309.60, 681.60]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [99, 49, 106, 57]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4980
  Bounding Box: [250.20, 231.20, 395.60, 461.20]
  Mask Area: 125 pixels
  Mask Ratio: 0.0044
  Mask BBox: [24, 23, 34, 40]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4976
  Bounding Box: [1768.00, 344.00, 1902.40, 516.80]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [143, 31, 152, 44]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4971
  Bounding Box: [327.20, 461.20, 425.60, 567.60]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [30, 41, 37, 48]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4922
  Bounding Box: [1571.20, 78.00, 1692.80, 232.40]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [127, 11, 135, 22]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4922
  Bounding Box: [1049.60, 297.80, 1225.60, 448.00]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [86, 28, 99, 38]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4907
  Bounding Box: [959.20, 1398.40, 1052.00, 1496.00]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [79, 114, 86, 120]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4878
  Bounding Box: [310.40, 726.40, 485.60, 856.00]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [29, 61, 41, 70]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4834
  Bounding Box: [1900.80, 437.20, 1964.80, 519.60]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [153, 39, 157, 44]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4814
  Bounding Box: [1939.20, 1921.60, 2048.00, 2046.40]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [156, 155, 164, 163]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4810
  Bounding Box: [252.80, 551.60, 393.60, 742.00]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [24, 48, 34, 61]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4771
  Bounding Box: [1460.80, 1956.80, 1590.40, 2030.40]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [119, 157, 128, 162]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4761
  Bounding Box: [98.50, 296.80, 155.20, 382.40]
  Mask Area: 18 pixels
  Mask Ratio: 0.0006
  Mask BBox: [12, 28, 15, 33]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4756
  Bounding Box: [1643.20, 0.00, 1745.60, 79.90]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [133, 4, 140, 9]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4709
  Bounding Box: [85.50, 293.60, 310.40, 537.60]
  Mask Area: 298 pixels
  Mask Ratio: 0.0106
  Mask BBox: [11, 27, 28, 45]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4707
  Bounding Box: [315.60, 1113.60, 462.00, 1315.20]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [29, 91, 40, 105]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4697
  Bounding Box: [96.50, 1601.60, 184.80, 1819.20]
  Mask Area: 106 pixels
  Mask Ratio: 0.0038
  Mask BBox: [12, 130, 18, 146]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4678
  Bounding Box: [1923.20, 807.20, 2048.00, 1013.60]
  Mask Area: 150 pixels
  Mask Ratio: 0.0053
  Mask BBox: [155, 68, 165, 83]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4653
  Bounding Box: [529.20, 1924.80, 652.40, 2033.60]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [46, 155, 54, 162]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4614
  Bounding Box: [1356.80, 863.20, 1456.00, 1021.60]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [110, 72, 117, 83]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4580
  Bounding Box: [1667.20, 58.20, 1936.00, 329.80]
  Mask Area: 340 pixels
  Mask Ratio: 0.0120
  Mask BBox: [135, 9, 155, 29]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4565
  Bounding Box: [673.60, 1236.80, 796.00, 1432.00]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [57, 102, 66, 115]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4561
  Bounding Box: [0.30, 1228.80, 122.30, 1388.80]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [5, 100, 12, 112]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4524
  Bounding Box: [1982.40, 1300.80, 2033.60, 1427.20]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [159, 106, 162, 115]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4524
  Bounding Box: [861.60, 1740.80, 1074.40, 1932.80]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [72, 140, 87, 154]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4509
  Bounding Box: [1958.40, 256.60, 2048.00, 387.20]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [157, 25, 163, 34]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4500
  Bounding Box: [63.20, 540.40, 300.80, 751.60]
  Mask Area: 249 pixels
  Mask Ratio: 0.0088
  Mask BBox: [9, 47, 27, 62]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.4495
  Bounding Box: [0.00, 1819.20, 95.80, 1892.80]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [4, 147, 10, 151]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.4480
  Bounding Box: [493.60, 706.40, 559.20, 795.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [43, 60, 47, 66]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.4480
  Bounding Box: [1424.00, 632.40, 1614.40, 936.80]
  Mask Area: 271 pixels
  Mask Ratio: 0.0096
  Mask BBox: [116, 54, 130, 77]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.4463
  Bounding Box: [1400.80, 769.60, 1554.40, 1051.20]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [114, 65, 125, 86]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.4456
  Bounding Box: [1945.60, 680.00, 2028.80, 784.80]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [156, 58, 162, 65]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.4392
  Bounding Box: [1503.20, 584.00, 1595.20, 653.60]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [122, 50, 128, 55]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.4387
  Bounding Box: [780.80, 7.60, 902.40, 108.40]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [65, 5, 74, 12]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.4387
  Bounding Box: [1144.80, 1763.20, 1260.00, 1916.80]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [94, 142, 102, 153]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.4363
  Bounding Box: [1382.40, 808.80, 1454.40, 917.60]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [112, 68, 116, 75]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.4277
  Bounding Box: [1785.60, 848.80, 1862.40, 951.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [144, 71, 148, 78]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.4250
  Bounding Box: [554.00, 1564.80, 676.40, 1740.80]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [48, 127, 56, 139]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.4243
  Bounding Box: [6.75, 1600.00, 89.20, 1811.20]
  Mask Area: 88 pixels
  Mask Ratio: 0.0031
  Mask BBox: [5, 129, 10, 145]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.4238
  Bounding Box: [1044.80, 325.60, 1184.00, 441.60]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [86, 30, 96, 38]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.4236
  Bounding Box: [727.20, 1331.20, 869.60, 1587.20]
  Mask Area: 196 pixels
  Mask Ratio: 0.0069
  Mask BBox: [61, 108, 71, 127]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.4231
  Bounding Box: [1574.40, 1523.20, 1673.60, 1601.60]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [127, 123, 134, 129]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.4226
  Bounding Box: [0.00, 0.00, 128.00, 69.60]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [4, 3, 13, 9]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.4211
  Bounding Box: [386.40, 1097.60, 458.40, 1192.00]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [35, 90, 39, 95]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.4207
  Bounding Box: [1228.00, 1138.40, 1340.00, 1268.00]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [100, 93, 107, 103]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.4207
  Bounding Box: [1447.20, 511.20, 1546.40, 597.60]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [118, 44, 124, 50]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.4207
  Bounding Box: [430.80, 225.40, 519.60, 395.60]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [38, 22, 44, 34]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.4197
  Bounding Box: [1164.80, 1532.00, 1278.40, 1723.20]
  Mask Area: 109 pixels
  Mask Ratio: 0.0039
  Mask BBox: [95, 124, 103, 138]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.4177
  Bounding Box: [0.00, 1116.00, 118.30, 1376.80]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [4, 92, 13, 111]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.4172
  Bounding Box: [1918.40, 1356.00, 2048.00, 1588.80]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [154, 110, 164, 127]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.4131
  Bounding Box: [460.00, 461.60, 585.60, 716.80]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [40, 41, 49, 59]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.4106
  Bounding Box: [1217.60, 444.40, 1292.80, 539.60]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [100, 39, 104, 46]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.4099
  Bounding Box: [769.60, 1881.60, 990.40, 2035.20]
  Mask Area: 136 pixels
  Mask Ratio: 0.0048
  Mask BBox: [65, 151, 81, 162]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.4087
  Bounding Box: [1926.40, 11.90, 2019.20, 125.10]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [155, 5, 161, 13]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.4050
  Bounding Box: [0.30, 508.00, 77.50, 639.20]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [5, 44, 9, 53]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.4036
  Bounding Box: [1995.20, 64.60, 2048.00, 221.40]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [160, 10, 164, 21]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.4036
  Bounding Box: [1836.80, 33.60, 1964.80, 223.20]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [148, 7, 157, 21]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.4026
  Bounding Box: [1603.20, 444.80, 1734.40, 625.60]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [130, 39, 139, 52]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.4011
  Bounding Box: [1940.80, 859.20, 2040.00, 1046.40]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [156, 72, 163, 85]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.4009
  Bounding Box: [888.00, 1641.60, 1027.20, 1913.60]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [74, 133, 84, 153]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3979
  Bounding Box: [102.00, 1276.00, 373.20, 1520.80]
  Mask Area: 318 pixels
  Mask Ratio: 0.0113
  Mask BBox: [12, 104, 33, 122]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3970
  Bounding Box: [1665.60, 1531.20, 1784.00, 1665.60]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [135, 124, 143, 134]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3923
  Bounding Box: [320.40, 462.00, 398.00, 555.60]
  Mask Area: 42 pixels
  Mask Ratio: 0.0015
  Mask BBox: [30, 41, 35, 47]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3914
  Bounding Box: [1024.80, 1570.40, 1140.00, 1673.60]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [85, 127, 93, 134]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3904
  Bounding Box: [566.80, 310.40, 669.20, 419.20]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [49, 29, 56, 36]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3896
  Bounding Box: [777.60, 1152.80, 888.00, 1272.80]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [65, 95, 72, 100]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3850
  Bounding Box: [651.60, 197.80, 702.00, 319.80]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [55, 20, 58, 27]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3831
  Bounding Box: [1560.80, 399.20, 1728.00, 593.60]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [126, 36, 138, 50]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3804
  Bounding Box: [413.20, 1904.00, 546.00, 2025.60]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [37, 153, 46, 162]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3799
  Bounding Box: [992.00, 33.35, 1062.40, 131.40]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [82, 7, 86, 14]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3799
  Bounding Box: [750.00, 1422.40, 869.60, 1579.20]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [63, 116, 71, 127]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3779
  Bounding Box: [1992.00, 1036.00, 2043.20, 1170.40]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [160, 85, 163, 95]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3774
  Bounding Box: [1286.40, 884.00, 1428.80, 1044.00]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [105, 74, 115, 85]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3757
  Bounding Box: [16.80, 42.15, 126.80, 192.20]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [6, 8, 13, 19]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3730
  Bounding Box: [396.00, 831.20, 485.60, 948.00]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [35, 69, 41, 78]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3728
  Bounding Box: [524.00, 670.40, 688.00, 958.40]
  Mask Area: 238 pixels
  Mask Ratio: 0.0084
  Mask BBox: [45, 57, 57, 78]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3716
  Bounding Box: [441.20, 992.80, 534.00, 1196.00]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [39, 82, 45, 97]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3711
  Bounding Box: [622.80, 0.00, 749.20, 59.90]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [53, 4, 62, 8]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3708
  Bounding Box: [358.80, 19.75, 539.60, 147.80]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [33, 6, 46, 15]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3706
  Bounding Box: [878.40, 1040.80, 987.20, 1173.60]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [73, 86, 81, 95]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3684
  Bounding Box: [481.20, 112.40, 573.20, 222.00]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [42, 13, 48, 21]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3679
  Bounding Box: [1227.20, 1215.20, 1323.20, 1344.80]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [100, 99, 107, 109]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3667
  Bounding Box: [1830.40, 1537.60, 1920.00, 1742.40]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [147, 126, 152, 139]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3667
  Bounding Box: [1048.80, 1444.00, 1183.20, 1573.60]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [86, 117, 96, 126]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3643
  Bounding Box: [579.20, 1984.00, 704.80, 2038.40]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [50, 159, 59, 163]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3638
  Bounding Box: [2008.00, 104.50, 2048.00, 217.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [161, 13, 164, 20]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3635
  Bounding Box: [625.60, 1202.40, 712.00, 1284.00]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [53, 98, 59, 104]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3621
  Bounding Box: [957.60, 0.00, 1056.80, 65.40]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [79, 4, 86, 9]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3621
  Bounding Box: [731.20, 1980.80, 832.00, 2044.80]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [62, 159, 68, 163]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3616
  Bounding Box: [1277.60, 656.00, 1375.20, 767.20]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [104, 56, 111, 63]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3599
  Bounding Box: [980.00, 732.00, 1170.40, 914.40]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [81, 62, 94, 74]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3589
  Bounding Box: [464.80, 539.60, 557.60, 710.80]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [41, 47, 47, 59]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3567
  Bounding Box: [795.20, 69.20, 878.40, 173.60]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [67, 10, 71, 17]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3567
  Bounding Box: [1475.20, 1250.40, 1580.80, 1336.80]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [120, 102, 127, 108]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3567
  Bounding Box: [1972.80, 1283.20, 2036.80, 1507.20]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [159, 105, 163, 121]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3567
  Bounding Box: [575.20, 647.20, 840.80, 832.80]
  Mask Area: 248 pixels
  Mask Ratio: 0.0088
  Mask BBox: [49, 55, 69, 69]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [516.80, 6.30, 600.00, 93.40]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [45, 5, 50, 11]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3530
  Bounding Box: [72.80, 1641.60, 224.40, 1875.20]
  Mask Area: 165 pixels
  Mask Ratio: 0.0058
  Mask BBox: [10, 133, 21, 150]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3525
  Bounding Box: [1080.80, 1854.40, 1282.40, 2043.20]
  Mask Area: 151 pixels
  Mask Ratio: 0.0054
  Mask BBox: [89, 149, 104, 163]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3521
  Bounding Box: [323.60, 578.40, 484.40, 811.20]
  Mask Area: 168 pixels
  Mask Ratio: 0.0060
  Mask BBox: [30, 50, 41, 67]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.3501
  Bounding Box: [250.40, 907.20, 463.20, 1126.40]
  Mask Area: 191 pixels
  Mask Ratio: 0.0068
  Mask BBox: [24, 75, 40, 91]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.3491
  Bounding Box: [6.55, 1111.20, 97.40, 1309.60]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [5, 91, 11, 106]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.3469
  Bounding Box: [11.60, 6.55, 131.00, 141.20]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [5, 5, 13, 15]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.3469
  Bounding Box: [0.00, 517.60, 157.80, 756.80]
  Mask Area: 191 pixels
  Mask Ratio: 0.0068
  Mask BBox: [4, 45, 16, 63]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.3455
  Bounding Box: [973.60, 14.50, 1068.00, 92.70]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [81, 6, 87, 11]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.3425
  Bounding Box: [1110.40, 1891.20, 1204.80, 1984.00]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [91, 152, 95, 158]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.3411
  Bounding Box: [0.10, 1465.60, 86.10, 1651.20]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [5, 119, 10, 132]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.3394
  Bounding Box: [1592.00, 382.40, 1790.40, 625.60]
  Mask Area: 219 pixels
  Mask Ratio: 0.0078
  Mask BBox: [129, 34, 143, 52]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.3389
  Bounding Box: [525.60, 184.40, 654.40, 388.40]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [46, 19, 55, 34]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.3372
  Bounding Box: [2000.00, 1891.20, 2048.00, 2032.00]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [161, 152, 165, 162]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.3367
  Bounding Box: [1630.40, 88.00, 1844.80, 451.60]
  Mask Area: 365 pixels
  Mask Ratio: 0.0129
  Mask BBox: [132, 11, 148, 39]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.3364
  Bounding Box: [81.00, 300.80, 156.40, 420.80]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [11, 28, 14, 36]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.3354
  Bounding Box: [1152.00, 1697.60, 1289.60, 1860.80]
  Mask Area: 99 pixels
  Mask Ratio: 0.0035
  Mask BBox: [94, 137, 104, 149]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.3350
  Bounding Box: [1972.80, 1891.20, 2046.40, 2035.20]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [159, 152, 163, 162]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.3350
  Bounding Box: [904.00, 571.60, 1032.00, 723.60]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [75, 49, 84, 60]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.3345
  Bounding Box: [1630.40, 811.20, 1880.00, 1150.40]
  Mask Area: 366 pixels
  Mask Ratio: 0.0130
  Mask BBox: [132, 68, 150, 93]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.3342
  Bounding Box: [1232.80, 1729.60, 1303.20, 1819.20]
  Mask Area: 15 pixels
  Mask Ratio: 0.0005
  Mask BBox: [101, 140, 104, 144]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.3328
  Bounding Box: [1282.40, 700.80, 1413.60, 834.40]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [105, 59, 114, 69]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.3306
  Bounding Box: [10.20, 932.80, 70.50, 1038.40]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [5, 77, 9, 85]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.3303
  Bounding Box: [1385.60, 59.40, 1620.80, 343.00]
  Mask Area: 325 pixels
  Mask Ratio: 0.0115
  Mask BBox: [113, 9, 130, 30]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.3298
  Bounding Box: [526.40, 1578.40, 633.60, 1726.40]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [46, 128, 53, 138]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.3276
  Bounding Box: [1836.80, 19.90, 1939.20, 164.80]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [148, 6, 155, 16]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.3271
  Bounding Box: [1950.40, 816.80, 2048.00, 952.80]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [157, 68, 164, 78]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.3271
  Bounding Box: [0.00, 1341.60, 72.00, 1471.20]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [4, 109, 9, 118]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.3269
  Bounding Box: [2004.80, 200.20, 2048.00, 269.40]
  Mask Area: 17 pixels
  Mask Ratio: 0.0006
  Mask BBox: [161, 20, 163, 25]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.3254
  Bounding Box: [147.50, 1072.80, 342.80, 1284.00]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [16, 88, 30, 104]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.3245
  Bounding Box: [1614.40, 1541.60, 1761.60, 1708.80]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [131, 125, 141, 137]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.3235
  Bounding Box: [1110.40, 1880.00, 1241.60, 2017.60]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [91, 151, 100, 161]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [1204.80, 1740.80, 1276.80, 1814.40]
  Mask Area: 18 pixels
  Mask Ratio: 0.0006
  Mask BBox: [99, 140, 103, 144]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.3225
  Bounding Box: [1366.40, 804.00, 1456.00, 986.40]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [111, 67, 117, 81]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.3220
  Bounding Box: [3.15, 277.40, 177.60, 511.20]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [5, 26, 17, 43]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.3208
  Bounding Box: [1043.20, 936.00, 1116.80, 1036.80]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [86, 78, 90, 84]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.3181
  Bounding Box: [9.05, 98.70, 161.00, 308.00]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [5, 12, 16, 28]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.3179
  Bounding Box: [1873.60, 352.40, 1985.60, 531.60]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [151, 32, 159, 42]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.3149
  Bounding Box: [1456.80, 350.60, 1548.00, 547.20]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [118, 32, 124, 46]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.3120
  Bounding Box: [1684.80, 506.80, 1790.40, 642.80]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [136, 44, 143, 54]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.3118
  Bounding Box: [6.10, 1004.00, 72.30, 1152.80]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [5, 83, 8, 94]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.3118
  Bounding Box: [676.00, 1.85, 858.40, 203.80]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [57, 5, 71, 19]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.3115
  Bounding Box: [1774.40, 305.60, 1963.20, 512.80]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [143, 28, 157, 44]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.3086
  Bounding Box: [1897.60, 415.20, 2009.60, 528.00]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [153, 37, 160, 44]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.3083
  Bounding Box: [1774.40, 865.60, 1880.00, 998.40]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [143, 72, 150, 81]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.3081
  Bounding Box: [79.80, 281.60, 138.20, 364.80]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [11, 26, 14, 32]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.3081
  Bounding Box: [105.40, 281.60, 163.80, 364.80]
  Mask Area: 12 pixels
  Mask Ratio: 0.0004
  Mask BBox: [13, 27, 15, 32]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.3081
  Bounding Box: [79.80, 307.20, 138.20, 390.40]
  Mask Area: 24 pixels
  Mask Ratio: 0.0009
  Mask BBox: [11, 28, 14, 34]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.3081
  Bounding Box: [459.20, 715.20, 552.00, 791.20]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [40, 60, 47, 65]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.3076
  Bounding Box: [344.20, 1251.20, 500.00, 1491.20]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [31, 102, 43, 120]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [1256.80, 683.60, 1333.60, 775.20]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [103, 58, 108, 64]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [1256.80, 709.20, 1333.60, 800.80]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [103, 60, 108, 66]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [1282.40, 709.20, 1359.20, 800.80]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [105, 60, 110, 66]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.3074
  Bounding Box: [1995.20, 1921.60, 2048.00, 2048.00]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [160, 155, 166, 164]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [417.20, 1500.80, 542.80, 1593.60]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [37, 122, 46, 128]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [417.20, 1526.40, 542.80, 1619.20]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [37, 124, 46, 128]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.3059
  Bounding Box: [1948.80, 1298.40, 2048.00, 1408.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [157, 106, 163, 114]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.3052
  Bounding Box: [1923.20, 223.20, 2028.80, 318.00]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [155, 22, 162, 28]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.3040
  Bounding Box: [815.20, 1147.20, 924.00, 1240.00]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [68, 94, 76, 100]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.3035
  Bounding Box: [716.80, 55.90, 864.00, 198.00]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [60, 9, 71, 19]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.3030
  Bounding Box: [354.80, 1488.80, 449.20, 1567.20]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [32, 121, 39, 126]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.3020
  Bounding Box: [1073.60, 1313.60, 1161.60, 1462.40]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [88, 107, 94, 118]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.3020
  Bounding Box: [468.80, 1024.00, 593.60, 1270.40]
  Mask Area: 163 pixels
  Mask Ratio: 0.0058
  Mask BBox: [41, 84, 50, 103]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [0.00, 1472.80, 93.40, 1753.60]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [4, 120, 11, 140]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [739.60, 1091.20, 870.40, 1264.00]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [62, 90, 71, 100]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.3000
  Bounding Box: [4.10, 55.00, 72.40, 169.20]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [5, 9, 9, 17]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2998
  Bounding Box: [962.40, 678.40, 1130.40, 792.80]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [80, 57, 92, 65]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2998
  Bounding Box: [185.00, 1125.60, 449.60, 1300.00]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [19, 92, 39, 105]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2991
  Bounding Box: [182.80, 542.00, 411.20, 734.80]
  Mask Area: 195 pixels
  Mask Ratio: 0.0069
  Mask BBox: [19, 47, 36, 61]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2986
  Bounding Box: [1229.60, 1265.60, 1296.80, 1408.00]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [101, 103, 105, 113]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2981
  Bounding Box: [1883.20, 831.20, 1960.00, 906.40]
  Mask Area: 26 pixels
  Mask Ratio: 0.0009
  Mask BBox: [152, 69, 157, 74]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2979
  Bounding Box: [71.20, 1510.40, 157.20, 1641.60]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [10, 122, 16, 132]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2976
  Bounding Box: [1077.60, 26.00, 1356.00, 301.60]
  Mask Area: 329 pixels
  Mask Ratio: 0.0117
  Mask BBox: [89, 7, 109, 27]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2971
  Bounding Box: [103.10, 305.80, 175.00, 387.20]
  Mask Area: 13 pixels
  Mask Ratio: 0.0005
  Mask BBox: [13, 28, 15, 34]

