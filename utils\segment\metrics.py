
# YOLOv5 分割模型性能评估指标模块
# 该模块包含用于计算目标检测和实例分割任务的各种性能指标
# 包括精确度(Precision)、召回率(Recall)、F1分数、平均精度(AP)等

import math  # 数学函数库
import warnings  # 警告处理模块
from pathlib import Path  # 路径处理模块

import matplotlib.pyplot as plt  # 绘图库，用于生成PR曲线等可视化图表
import numpy as np  # 数值计算库
import torch  # PyTorch深度学习框架

from utils import TryExcept, threaded  # 导入工具函数：异常处理装饰器和多线程装饰器
from utils.segment.loss import box_iou


    
def smooth(y, f=0.05):
    """
    对数组进行平滑处理，使用盒式滤波器
    
    Args:
        y: 输入数组，需要进行平滑处理的数据
        f: 平滑因子，控制平滑程度，默认为0.05
    
    Returns:
        平滑后的数组
    """
    nf = round(len(y) * f * 2) // 2 + 1  # 滤波器元素数量（必须为奇数）
    p = np.ones(nf // 2)  # 填充用的1数组
    yp = np.concatenate((p * y[0], y, p * y[-1]), 0)  # 对y进行边界填充
    return np.convolve(yp, np.ones(nf) / nf, mode="valid")  # 返回平滑后的数组


def ap_per_class(tp, conf, pred_cls, target_cls, plot=False, save_dir=".", names=(), eps=1e-16, prefix=""):
    """
    计算每个类别的平均精度(Average Precision, AP)，基于召回率和精确度曲线
    
    参考来源: https://github.com/rafaelpadilla/Object-Detection-Metrics
    
    Args:
        tp: 真正例数组 (numpy数组, 形状为nx1或nx10)
        conf: 置信度分数，范围0-1 (numpy数组)
        pred_cls: 预测的类别标签 (numpy数组)
        target_cls: 真实的类别标签 (numpy数组)
        plot: 是否绘制mAP@0.5的精确度-召回率曲线，默认False
        save_dir: 图表保存目录，默认为当前目录
        names: 类别名称字典或元组
        eps: 防止除零的极小值，默认1e-16
        prefix: 保存文件的前缀
    
    Returns:
        tuple: (tp, fp, p, r, f1, ap, unique_classes)
            - tp: 真正例数量
            - fp: 假正例数量  
            - p: 精确度
            - r: 召回率
            - f1: F1分数
            - ap: 平均精度
            - unique_classes: 唯一类别索引
    """
    # 按置信度降序排序
    i = np.argsort(-conf)
    tp, conf, pred_cls = tp[i], conf[i], pred_cls[i]

    # 找到唯一的类别
    unique_classes, nt = np.unique(target_cls, return_counts=True)
    nc = unique_classes.shape[0]  # 类别数量

    # 创建精确度-召回率曲线并计算每个类别的AP
    px, py = np.linspace(0, 1, 1000), []  # 用于绘图的x轴点和y轴数据
    ap, p, r = np.zeros((nc, tp.shape[1])), np.zeros((nc, 1000)), np.zeros((nc, 1000))  # 初始化AP、精确度、召回率数组
    for ci, c in enumerate(unique_classes):
        i = pred_cls == c  # 当前类别的预测掩码
        n_l = nt[ci]  # 当前类别的真实标签数量
        n_p = i.sum()  # 当前类别的预测数量
        if n_p == 0 or n_l == 0:  # 如果没有预测或没有真实标签，跳过
            continue

        # 累积假正例(FP)和真正例(TP)
        fpc = (1 - tp[i]).cumsum(0)  # 假正例累积计数
        tpc = tp[i].cumsum(0)  # 真正例累积计数

        # 计算召回率
        recall = tpc / (n_l + eps)  # 召回率曲线
        r[ci] = np.interp(-px, -conf[i], recall[:, 0], left=0)  # 插值计算召回率（负x轴因为置信度递减）

        # 计算精确度
        precision = tpc / (tpc + fpc)  # 精确度曲线
        p[ci] = np.interp(-px, -conf[i], precision[:, 0], left=1)  # 插值计算精确度

        # 从召回率-精确度曲线计算AP
        for j in range(tp.shape[1]):
            ap[ci, j], mpre, mrec = compute_ap(recall[:, j], precision[:, j])  # 计算平均精度
            if plot and j == 0:  # 如果需要绘图且是第一个IoU阈值
                py.append(np.interp(px, mrec, mpre))  # 为mAP@0.5添加精确度数据

    # 计算F1分数（精确度和召回率的调和平均数）
    f1 = 2 * p * r / (p + r + eps)
    names = [v for k, v in names.items() if k in unique_classes]  # 只保留有数据的类别名称
    names = dict(enumerate(names))  # 转换为字典格式
    if plot:  # 如果需要绘制图表
        plot_pr_curve(px, py, ap, Path(save_dir) / f"{prefix}PR_curve.png", names)  # 绘制PR曲线
        plot_mc_curve(px, f1, Path(save_dir) / f"{prefix}F1_curve.png", names, ylabel="F1")  # 绘制F1曲线
        plot_mc_curve(px, p, Path(save_dir) / f"{prefix}P_curve.png", names, ylabel="Precision")  # 绘制精确度曲线
        plot_mc_curve(px, r, Path(save_dir) / f"{prefix}R_curve.png", names, ylabel="Recall")  # 绘制召回率曲线

    i = smooth(f1.mean(0), 0.1).argmax()  # 找到平滑后F1分数最大值的索引
    p, r, f1 = p[:, i], r[:, i], f1[:, i]  # 选择最佳阈值对应的指标
    tp = (r * nt).round()  # 计算真正例数量
    fp = (tp / (p + eps) - tp).round()  # 计算假正例数量
    return tp, fp, p, r, f1, ap, unique_classes.astype(int)  # 返回所有计算结果

def compute_ap(recall, precision):
    """
    计算平均精度(Average Precision, AP)，基于给定的召回率和精确度曲线
    
    Args:
        recall: 召回率曲线 (列表或数组)
        precision: 精确度曲线 (列表或数组)
    
    Returns:
        tuple: (ap, mpre, mrec)
            - ap: 平均精度值
            - mpre: 修正后的精确度曲线
            - mrec: 修正后的召回率曲线
    """
    # 在开始和结束位置添加哨兵值
    mrec = np.concatenate(([0.0], recall, [1.0]))  # 召回率：开始0，结束1
    mpre = np.concatenate(([1.0], precision, [0.0]))  # 精确度：开始1，结束0

    # 计算精确度包络线（单调递减）
    mpre = np.flip(np.maximum.accumulate(np.flip(mpre)))  # 确保精确度单调递减

    # 计算曲线下面积（积分）
    method = "interp"  # 计算方法：'continuous'（连续）或'interp'（插值）
    if method == "interp":
        x = np.linspace(0, 1, 101)  # 101点插值（COCO标准）
        ap = np.trapz(np.interp(x, mrec, mpre), x)  # 梯形积分法计算面积
    else:  # 'continuous' 连续方法
        i = np.where(mrec[1:] != mrec[:-1])[0]  # 找到召回率变化的点
        ap = np.sum((mrec[i + 1] - mrec[i]) * mpre[i + 1])  # 计算曲线下面积

    return ap, mpre, mrec  # 返回平均精度和修正后的曲线

def fitness(x):
    """
    通过8个指标的加权和评估模型适应度
    
    Args:
        x: 指标数组，形状为[N,8]，包含各种性能指标
    
    Returns:
        加权适应度分数，权重为[0.1, 0.9]分别对应mAP和F1
    """
    # 权重数组：[P, R, mAP@0.5, mAP@0.5:0.95, P_mask, R_mask, mAP@0.5_mask, mAP@0.5:0.95_mask]
    w = [0.0, 0.0, 0.1, 0.9, 0.0, 0.0, 0.1, 0.9]  # 主要关注mAP@0.5:0.95指标
    return (x[:, :8] * w).sum(1)  # 计算加权和作为适应度分数


def ap_per_class_box_and_mask(
    tp_m,
    tp_b,
    conf,
    pred_cls,
    target_cls,
    plot=False,
    save_dir=".",
    names=(),
):
    """
    计算边界框和掩码的每类平均精度
    
    Args:
        tp_m: 掩码的真正例数组
        tp_b: 边界框的真正例数组
        conf: 置信度分数数组
        pred_cls: 预测类别数组
        target_cls: 真实类别数组
        plot: 是否绘制曲线图，默认False
        save_dir: 图表保存目录
        names: 类别名称
    
    Returns:
        dict: 包含边界框和掩码指标的字典
            - boxes: 边界框指标（精确度、召回率、AP、F1、类别AP）
            - masks: 掩码指标（精确度、召回率、AP、F1、类别AP）
    """
    # 计算边界框的各项指标（跳过前两个返回值tp和fp）
    results_boxes = ap_per_class(
        tp_b, conf, pred_cls, target_cls, plot=plot, save_dir=save_dir, names=names, prefix="Box"
    )[2:]
    # 计算掩码的各项指标（跳过前两个返回值tp和fp）
    results_masks = ap_per_class(
        tp_m, conf, pred_cls, target_cls, plot=plot, save_dir=save_dir, names=names, prefix="Mask"
    )[2:]

    return {
        "boxes": {  # 边界框检测指标
            "p": results_boxes[0],      # 精确度
            "r": results_boxes[1],      # 召回率
            "ap": results_boxes[3],     # 平均精度
            "f1": results_boxes[2],     # F1分数
            "ap_class": results_boxes[4],  # 各类别索引
        },
        "masks": {  # 掩码分割指标
            "p": results_masks[0],      # 精确度
            "r": results_masks[1],      # 召回率
            "ap": results_masks[3],     # 平均精度
            "f1": results_masks[2],     # F1分数
            "ap_class": results_masks[4],  # 各类别索引
        },
    }


class Metric:
    """
    计算模型评估的性能指标，包括精确度、召回率、F1分数和平均精度
    用于存储和管理单个任务（如目标检测或实例分割）的评估指标
    """

    def __init__(self) -> None:
        """
        初始化性能指标属性
        包括精确度、召回率、F1分数、平均精度和类别索引
        """
        self.p = []  # 精确度数组，形状(nc,)，nc为类别数
        self.r = []  # 召回率数组，形状(nc,)
        self.f1 = []  # F1分数数组，形状(nc,)
        self.all_ap = []  # 所有IoU阈值的AP数组，形状(nc, 10)
        self.ap_class_index = []  # 类别索引数组，形状(nc,)

    @property
    def ap50(self):
        """
        所有类别在IoU=0.5阈值下的平均精度

        Returns:
            numpy.ndarray: 形状为(nc,)的数组，或空列表
        """
        return self.all_ap[:, 0] if len(self.all_ap) else []

    @property
    def ap(self):
        """
        所有类别在IoU=0.5:0.95范围内的平均精度
        
        Returns:
            numpy.ndarray: 形状为(nc,)的数组，或空列表
        """
        return self.all_ap.mean(1) if len(self.all_ap) else []
    

    @property
    def mp(self):
        """
        所有类别的平均精确度

        Returns:
            float: 平均精确度值
        """
        return self.p.mean() if len(self.p) else 0.0

    @property
    def mr(self):
        """
        所有类别的平均召回率

        Returns:
            float: 平均召回率值
        """
        return self.r.mean() if len(self.r) else 0.0

    @property
    def map50(self):
        """
        所有类别在IoU=0.5阈值下的平均AP

        Returns:
            float: mAP@0.5值
        """
        return self.all_ap[:, 0].mean() if len(self.all_ap) else 0.0

    @property
    def map(self):
        """
        所有类别在IoU=0.5:0.95范围内的平均AP

        Returns:
            float: mAP@0.5:0.95值
        """
        return self.all_ap.mean() if len(self.all_ap) else 0.0

    def mean_results(self):
        """
        返回平均结果指标
        
        Returns:
            tuple: (mp, mr, map50, map) - 平均精确度、平均召回率、mAP@0.5、mAP@0.5:0.95
        """
        return (self.mp, self.mr, self.map50, self.map)

    def class_result(self, i):
        """
        返回指定类别的结果指标
        
        Args:
            i: 类别索引
            
        Returns:
            tuple: (p[i], r[i], ap50[i], ap[i]) - 该类别的精确度、召回率、AP@0.5、AP@0.5:0.95
        """
        return (self.p[i], self.r[i], self.ap50[i], self.ap[i])

    def get_maps(self, nc):
        """
        计算并返回每个类别的平均精度(mAP)
        
        Args:
            nc: 类别总数
            
        Returns:
            numpy.ndarray: 每个类别的mAP数组，形状为(nc,)
        """
        maps = np.zeros(nc) + self.map  # 初始化为全局mAP值
        for i, c in enumerate(self.ap_class_index):  # 更新有数据的类别
            maps[c] = self.ap[i]
        return maps

    def update(self, results):
        """
        更新指标数据
        
        Args:
            results: 包含指标的元组 (p, r, ap, f1, ap_class_index)
                - p: 精确度数组
                - r: 召回率数组
                - ap: 平均精度数组
                - f1: F1分数数组
                - ap_class_index: 类别索引数组
        """
        p, r, all_ap, f1, ap_class_index = results
        self.p = p  # 更新精确度
        self.r = r  # 更新召回率
        self.all_ap = all_ap  # 更新所有IoU阈值的AP
        self.f1 = f1  # 更新F1分数
        self.ap_class_index = ap_class_index  # 更新类别索引


class Metrics:
    """
    边界框和掩码的综合指标类
    用于同时管理目标检测（边界框）和实例分割（掩码）的性能指标
    """

    def __init__(self) -> None:
        """
        初始化边界框和掩码的Metric对象
        用于在Metrics类中计算性能指标
        """
        self.metric_box = Metric()   # 边界框检测指标
        self.metric_mask = Metric()  # 掩码分割指标

    def update(self, results):
        """
        更新边界框和掩码的指标数据
        
        Args:
            results: 包含边界框和掩码指标的字典
                格式: {'boxes': Dict{}, 'masks': Dict{}}
        """
        self.metric_box.update(list(results["boxes"].values()))    # 更新边界框指标
        self.metric_mask.update(list(results["masks"].values()))  # 更新掩码指标

    def mean_results(self):
        """
        计算并返回边界框和掩码指标的平均结果
        
        Returns:
            tuple: 边界框和掩码的平均指标组合
        """
        return self.metric_box.mean_results() + self.metric_mask.mean_results()

    def class_result(self, i):
        """
        返回指定类别索引的边界框和掩码指标结果
        
        Args:
            i: 类别索引
            
        Returns:
            tuple: 该类别的边界框和掩码指标组合
        """
        return self.metric_box.class_result(i) + self.metric_mask.class_result(i)

    def get_maps(self, nc):
        """
        计算并返回边界框和掩码的平均精度(mAP)总和
        
        Args:
            nc: 类别总数
            
        Returns:
            numpy.ndarray: 边界框和掩码mAP的总和数组
        """
        return self.metric_box.get_maps(nc) + self.metric_mask.get_maps(nc)

    @property
    def ap_class_index(self):
        """
        返回平均精度的类别索引，边界框和掩码指标共享
        
        Returns:
            list: 类别索引列表
        """
        return self.metric_box.ap_class_index



# 训练和验证过程中记录的指标键名列表
KEYS = [
    "train/box_loss",           # 训练边界框损失
    "train/seg_loss",           # 训练分割损失
    "train/obj_loss",           # 训练目标检测损失
    "train/cls_loss",           # 训练分类损失
    "metrics/precision(B)",     # 边界框精确度指标
    "metrics/recall(B)",        # 边界框召回率指标
    "metrics/mAP_0.5(B)",       # 边界框mAP@0.5指标
    "metrics/mAP_0.5:0.95(B)",  # 边界框mAP@0.5:0.95指标
    "metrics/precision(M)",     # 掩码精确度指标
    "metrics/recall(M)",        # 掩码召回率指标
    "metrics/mAP_0.5(M)",       # 掩码mAP@0.5指标
    "metrics/mAP_0.5:0.95(M)",  # 掩码mAP@0.5:0.95指标
    "val/box_loss",             # 验证边界框损失
    "val/seg_loss",             # 验证分割损失
    "val/obj_loss",             # 验证目标检测损失
    "val/cls_loss",             # 验证分类损失
    "x/lr0",                    # 学习率0
    "x/lr1",                    # 学习率1
    "x/lr2",                    # 学习率2
]

# 最佳性能指标键名列表
BEST_KEYS = [
    "best/epoch",               # 最佳轮次
    "best/precision(B)",        # 最佳边界框精确度
    "best/recall(B)",           # 最佳边界框召回率
    "best/mAP_0.5(B)",          # 最佳边界框mAP@0.5
    "best/mAP_0.5:0.95(B)",     # 最佳边界框mAP@0.5:0.95
    "best/precision(M)",        # 最佳掩码精确度
    "best/recall(M)",           # 最佳掩码召回率
    "best/mAP_0.5(M)",          # 最佳掩码mAP@0.5
    "best/mAP_0.5:0.95(M)",     # 最佳掩码mAP@0.5:0.95
]


# Plots ----------------------------------------------------------------------------------------------------------------


@threaded
def plot_pr_curve(px, py, ap, save_dir=Path("pr_curve.png"), names=(), on_plot=None):
    """
    绘制精确度-召回率曲线
    
    Args:
        px (numpy.ndarray): 召回率值数组
        py (numpy.ndarray): 精确度值数组
        ap (numpy.ndarray): 平均精度值数组
        save_dir (Path): 保存图像的路径，默认为"pr_curve.png"
        names (tuple): 类别名称元组
        on_plot (callable): 绘图完成后的回调函数
    """
    # 创建图形和坐标轴
    fig, ax = plt.subplots(1, 1, figsize=(9, 6), tight_layout=True)
    py = np.stack(py, axis=1)  # 将精确度数组堆叠

    if 0 < len(names) < 21:  # 如果类别数少于21个，显示图例
        for i, y in enumerate(py.T):
            # 为每个类别绘制PR曲线
            ax.plot(px, y, linewidth=1, label=f"{names[i]} {ap[i, 0]:.3f}")  # plot(recall, precision)
    else:
        # 类别过多时，用灰色线条绘制所有曲线
        ax.plot(px, py, linewidth=1, color="grey")  # plot(recall, precision)

    # 绘制所有类别的平均PR曲线
    ax.plot(px, py.mean(1), linewidth=3, color="blue", label=f"all classes {ap[:, 0].mean():.3f} mAP@0.5")
    ax.set_xlabel("Recall")                    # 设置x轴标签
    ax.set_ylabel("Precision")                 # 设置y轴标签
    ax.set_xlim(0, 1)                          # 设置x轴范围
    ax.set_ylim(0, 1)                          # 设置y轴范围
    ax.legend(bbox_to_anchor=(1.04, 1), loc="upper left")  # 设置图例位置
    ax.set_title("Precision-Recall Curve")     # 设置图表标题
    fig.savefig(save_dir, dpi=250)             # 保存图像
    plt.close(fig)                             # 关闭图形
    if on_plot:
        on_plot(save_dir)                      # 执行回调函数


class ConfusionMatrix:
    """
    混淆矩阵类，用于计算和可视化目标检测的混淆矩阵
    更新于 https://github.com/kaanakan/object_detection_confusion_matrix
    """

    def __init__(self, nc, conf=0.25, iou_thres=0.45, task='detect'):
        """
        初始化混淆矩阵
        
        Args:
            nc (int): 类别数量
            conf (float): 置信度阈值，默认0.25
            iou_thres (float): IoU阈值，默认0.45
            task (str): 任务类型，默认'detect'
        """
        self.task = task
        self.matrix = np.zeros((nc + 1, nc + 1))  # 混淆矩阵，+1用于背景类
        self.nc = nc  # 类别数量
        self.conf = conf  # 置信度阈值
        self.iou_thres = iou_thres  # IoU阈值

    def process_batch(self, detections, labels):
        """
        返回正确预测的矩阵，用于计算TP、FP、FN
        
        Args:
            detections (Array[N, 6]): x1, y1, x2, y2, conf, class
            labels (Array[M, 5]): class, x1, y1, x2, y2
            
        Returns:
            Array[N, 10]: 正确预测矩阵
        """
        if detections is None:
            gt_classes = labels.int()
            for gc in gt_classes:
                self.matrix[self.nc, gc] += 1  # 背景FN
            return

        detections = detections[detections[:, 4] > self.conf]  # 过滤低置信度检测
        gt_classes = labels[:, 0].int()  # 真实类别
        detection_classes = detections[:, 5].int()  # 检测类别
        iou = box_iou(labels[:, 1:], detections[:, :4])  # 计算IoU

        x = torch.where(iou > self.iou_thres)  # 找到IoU大于阈值的匹配
        if x[0].shape[0]:
            matches = torch.cat((torch.stack(x, 1), iou[x[0], x[1]][:, None]), 1).cpu().numpy()  # [label, detect, iou]
            if x[0].shape[0] > 1:
                matches = matches[matches[:, 2].argsort()[::-1]]  # 按IoU降序排列
                matches = matches[np.unique(matches[:, 1], return_index=True)[1]]  # 去除重复检测
                matches = matches[matches[:, 2].argsort()[::-1]]  # 按IoU降序排列
                matches = matches[np.unique(matches[:, 0], return_index=True)[1]]  # 去除重复标签
        else:
            matches = np.zeros((0, 3))

        n = matches.shape[0] > 0
        m0, m1, _ = matches.transpose().astype(int)
        for i, gc in enumerate(gt_classes):
            j = m0 == i
            if n and sum(j) == 1:
                self.matrix[detection_classes[m1[j]], gc] += 1  # 正确预测
            else:
                self.matrix[self.nc, gc] += 1  # 背景FN

        if n:
            for i, dc in enumerate(detection_classes):
                if not any(m1 == i):
                    self.matrix[dc, self.nc] += 1  # 背景FP

    @TryExcept('WARNING ⚠️ ConfusionMatrix plot failure')
    def plot(self, normalize=True, save_dir='', names=(), on_plot=None):
        """
        绘制混淆矩阵
        
        Args:
            normalize (bool): 是否归一化，默认True
            save_dir (str): 保存目录
            names (tuple): 类别名称
            on_plot (callable): 绘图完成后的回调函数
        """
        import seaborn as sn

        array = self.matrix / ((self.matrix.sum(0).reshape(1, -1) + 1E-9) if normalize else 1)  # 归一化
        array[array < 0.005] = np.nan  # 不显示小于0.5%的值

        fig, ax = plt.subplots(1, 1, figsize=(12, 9), tight_layout=True)
        nc, nn = self.nc, len(names)  # 类别数量，名称数量
        sn.heatmap(array,
                   annot=nc < 30,
                   annot_kws={
                       "size": 8},
                   cmap='Blues',
                   fmt='.2f',
                   square=True,
                   vmin=0.0,
                   xticklabels=names + ['background FP'] if nn == nc else 'auto',
                   yticklabels=names + ['background FN'] if nn == nc else 'auto',
                   ax=ax).set_facecolor((1, 1, 1))
        ax.set_xlabel('True')
        ax.set_ylabel('Predicted')
        ax.set_title('Confusion Matrix')
        plot_fname = Path(save_dir) / 'confusion_matrix.png'
        fig.savefig(plot_fname, dpi=250)
        plt.close(fig)
        if on_plot:
            on_plot(plot_fname)

    def print(self):
        """
        打印混淆矩阵统计信息
        """
        for i in range(self.nc + 1):
            print(' '.join(map(str, self.matrix[i])))


@threaded
def plot_mc_curve(px, py, save_dir=Path("mc_curve.png"), names=(), xlabel="Confidence", ylabel="Metric", on_plot=None):
    """
    绘制指标-置信度曲线
    
    Args:
        px (numpy.ndarray): 置信度值数组
        py (numpy.ndarray): 指标值数组
        save_dir (Path): 保存图像的路径，默认为"mc_curve.png"
        names (tuple): 类别名称元组
        xlabel (str): x轴标签，默认为"Confidence"
        ylabel (str): y轴标签，默认为"Metric"
        on_plot (callable): 绘图完成后的回调函数
    """
    # 创建图形和坐标轴
    fig, ax = plt.subplots(1, 1, figsize=(9, 6), tight_layout=True)

    if 0 < len(names) < 21:  # 如果类别数少于21个，显示图例
        for i, y in enumerate(py):
            # 为每个类别绘制指标-置信度曲线
            ax.plot(px, y, linewidth=1, label=f"{names[i]}")  # plot(confidence, metric)
    else:
        # 类别过多时，用灰色线条绘制所有曲线
        ax.plot(px, py.T, linewidth=1, color="grey")  # plot(confidence, metric)

    # 计算所有类别的平均指标并进行平滑处理
    y = smooth(py.mean(0), 0.05)
    # 绘制平均指标曲线，显示最大值和对应的置信度
    ax.plot(px, y, linewidth=3, color="blue", label=f"all classes {y.max():.2f} at {px[y.argmax()]:.3f}")
    ax.set_xlabel(xlabel)                      # 设置x轴标签
    ax.set_ylabel(ylabel)                      # 设置y轴标签
    ax.set_xlim(0, 1)                          # 设置x轴范围
    ax.set_ylim(0, 1)                          # 设置y轴范围
    ax.legend(bbox_to_anchor=(1.04, 1), loc="upper left")  # 设置图例位置
    ax.set_title(f"{ylabel}-Confidence Curve") # 设置图表标题
    fig.savefig(save_dir, dpi=250)             # 保存图像
    plt.close(fig)                             # 关闭图形
    if on_plot:
        on_plot(save_dir)                      # 执行回调函数
