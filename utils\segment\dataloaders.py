# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license
"""数据加载器模块 - 用于YOLOv5分割模型的数据加载和预处理。"""

import contextlib
import glob
import math
import os
import random
from pathlib import Path
import hashlib
import json
import time
from itertools import repeat
from multiprocessing.pool import Pool, ThreadPool
from threading import Thread
from zipfile import ZipFile

import cv2
import numpy as np
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset, dataloader, distributed
from PIL import ExifTags, Image, ImageOps  # 图像处理
from tqdm import tqdm
# 导入数据增强相关模块
IMG_FORMATS = "bmp", "dng", "jpeg", "jpg", "mpo", "png", "tif", "tiff", "webp", "pfm"  # 支持的图像格式
# 已迁移的类和函数现在在本文件中定义：InfiniteDataLoader, LoadImagesAndLabels, SmartDistributedSampler, seed_worker
from .general import (
    DATASETS_DIR,
    LOGGER,
    NUM_THREADS,
    TQDM_BAR_FORMAT,
    check_dataset,
    check_requirements,
    check_yaml,
    clean_str,
    cv2,
    is_colab,
    is_kaggle,
    segments2boxes,
    unzip_file,
    xyn2xy,
    xywh2xyxy,
    xywhn2xyxy,
    xyxy2xywhn,
)
from ..torch_utils import torch_distributed_zero_first
from .augmentations import Albumentations, mixup, random_perspective

# 获取分布式训练的进程排名和世界大小
RANK = int(os.getenv("RANK", -1))
WORLD_SIZE = int(os.getenv("WORLD_SIZE", 1))
LOCAL_RANK = int(os.getenv("LOCAL_RANK", -1))  # 本地进程排名（分布式训练）

# 帮助URL
HELP_URL = "See https://docs.ultralytics.com/yolov5/tutorials/train_custom_data"

# 获取方向标签
for orientation in ExifTags.TAGS.keys():
    if ExifTags.TAGS[orientation] == "Orientation":
        break


def get_hash(paths):
    """为文件或目录路径列表生成单个SHA256哈希值。
    
    通过组合文件大小和路径来生成唯一的哈希标识，用于缓存验证。
    
    Args:
        paths: 文件或目录路径列表
        
    Returns:
        str: SHA256哈希值的十六进制字符串
    """
    size = sum(os.path.getsize(p) for p in paths if os.path.exists(p))  # 计算所有文件的总大小
    h = hashlib.sha256(str(size).encode())  # 对大小进行哈希
    h.update("".join(paths).encode())  # 对路径进行哈希
    return h.hexdigest()  # 返回十六进制哈希值


def exif_size(img):
    """返回考虑EXIF方向信息的PIL图像尺寸。
    
    根据图像的EXIF方向标签调整图像尺寸，确保返回正确的宽高。
    
    Args:
        img: PIL图像对象
        
    Returns:
        tuple: 修正后的图像尺寸(width, height)
    """
    s = img.size  # 获取原始尺寸(width, height)
    with contextlib.suppress(Exception):
        rotation = dict(img._getexif().items())[orientation]
        if rotation in [6, 8]:  # 旋转270度或90度时需要交换宽高
            s = (s[1], s[0])
    return s

def exif_transpose(image):
    """根据EXIF方向标签转置PIL图像。
    
    这是PIL ImageOps.exif_transpose()的就地版本，用于根据图像的EXIF方向信息
    自动旋转图像到正确的方向。
    
    Args:
        image: 要转置的PIL图像对象
        
    Returns:
        Image: 转置后的图像对象
    """
    exif = image.getexif()  # 获取EXIF数据
    orientation = exif.get(0x0112, 1)  # 获取方向标签，默认为1（正常）
    if orientation > 1:
        # 根据EXIF方向标签选择相应的变换方法
        method = {
            2: Image.FLIP_LEFT_RIGHT,    # 水平翻转
            3: Image.ROTATE_180,         # 旋转180度
            4: Image.FLIP_TOP_BOTTOM,    # 垂直翻转
            5: Image.TRANSPOSE,          # 转置
            6: Image.ROTATE_270,         # 旋转270度
            7: Image.TRANSVERSE,         # 反转置
            8: Image.ROTATE_90,          # 旋转90度
        }.get(orientation)
        if method is not None:
            image = image.transpose(method)  # 应用变换
            del exif[0x0112]  # 删除方向标签
            image.info["exif"] = exif.tobytes()  # 更新EXIF信息
    return image

def augment_hsv(im, hgain=0.5, sgain=0.5, vgain=0.5, im_xpl=None):
    """
    Applies HSV color-space augmentation to an image with random gains for hue, saturation, and value.
    
    Args:
        im: PPL图像 (RGB模态)
        hgain: 色调增益
        sgain: 饱和度增益  
        vgain: 亮度增益
        im_xpl: XPL图像 (X模态)，可选参数用于双模态同步增强
        
    Returns:
        None (原地修改图像) 或 双模态时返回修改后的XPL图像
    """
    if hgain or sgain or vgain:
        r = np.random.uniform(-1, 1, 3) * [hgain, sgain, vgain] + 1  # random gains
        
        # 处理PPL图像（RGB模态）
        hue, sat, val = cv2.split(cv2.cvtColor(im, cv2.COLOR_BGR2HSV))
        dtype = im.dtype  # uint8

        x = np.arange(0, 256, dtype=r.dtype)
        lut_hue = ((x * r[0]) % 180).astype(dtype)
        lut_sat = np.clip(x * r[1], 0, 255).astype(dtype)
        lut_val = np.clip(x * r[2], 0, 255).astype(dtype)

        im_hsv = cv2.merge((cv2.LUT(hue, lut_hue), cv2.LUT(sat, lut_sat), cv2.LUT(val, lut_val)))
        cv2.cvtColor(im_hsv, cv2.COLOR_HSV2BGR, dst=im)  # no return needed
        
        # 如果提供了XPL图像，应用相同的HSV变换
        if im_xpl is not None:
            hue_xpl, sat_xpl, val_xpl = cv2.split(cv2.cvtColor(im_xpl, cv2.COLOR_BGR2HSV))
            
            # 使用相同的查找表确保变换一致性
            im_hsv_xpl = cv2.merge((cv2.LUT(hue_xpl, lut_hue), cv2.LUT(sat_xpl, lut_sat), cv2.LUT(val_xpl, lut_val)))
            cv2.cvtColor(im_hsv_xpl, cv2.COLOR_HSV2BGR, dst=im_xpl)  # no return needed
            return im, im_xpl  # 返回双模态结果
    
    # 如果没有XPL图像，只返回PPL图像
    return im

def bbox_ioa(box1, box2, eps=1e-7):
    """
    Returns the intersection over box2 area given box1, box2.

    Boxes are x1y1x2y2
    box1:       np.array of shape(4)
    box2:       np.array of shape(nx4)
    returns:    np.array of shape(n)
    """
    # Get the coordinates of bounding boxes
    b1_x1, b1_y1, b1_x2, b1_y2 = box1
    b2_x1, b2_y1, b2_x2, b2_y2 = box2.T

    # Intersection area
    inter_area = (np.minimum(b1_x2, b2_x2) - np.maximum(b1_x1, b2_x1)).clip(0) * (
        np.minimum(b1_y2, b2_y2) - np.maximum(b1_y1, b2_y1)
    ).clip(0)

    # box2 area
    box2_area = (b2_x2 - b2_x1) * (b2_y2 - b2_y1) + eps

    # Intersection over box2 area
    return inter_area / box2_area

def copy_paste(im, labels, segments, p=0.5, im_xpl=None):
    """
    Applies Copy-Paste augmentation by flipping and merging segments and labels on an image.

    Args:
        im: PPL图像 (RGB模态)
        labels: 标签数组
        segments: 分割段
        p: 应用增强的概率
        im_xpl: XPL图像 (X模态)，可选参数用于双模态同步增强
        
    Returns:
        如果是单模态: (im, labels, segments)
        如果是双模态: (im, labels, segments, im_xpl)
        
    Details at https://arxiv.org/abs/2012.07177.
    """
    n = len(segments)
    if p and n:
        h, w, c = im.shape  # height, width, channels
        im_new = np.zeros(im.shape, np.uint8)
        
        # 如果是双模态，为XPL图像创建相同的掩码
        if im_xpl is not None:
            im_new_xpl = np.zeros(im_xpl.shape, np.uint8)
        
        for j in random.sample(range(n), k=round(p * n)):
            l, s = labels[j], segments[j]
            box = w - l[3], l[2], w - l[1], l[4]
            ioa = bbox_ioa(box, labels[:, 1:5])  # intersection over area
            if (ioa < 0.30).all():  # allow 30% obscuration of existing labels
                labels = np.concatenate((labels, [[l[0], *box]]), 0)
                segments.append(np.concatenate((w - s[:, 0:1], s[:, 1:2]), 1))
                cv2.drawContours(im_new, [segments[j].astype(np.int32)], -1, (1, 1, 1), cv2.FILLED)
                
                # 为XPL图像绘制相同的轮廓掩码
                if im_xpl is not None:
                    cv2.drawContours(im_new_xpl, [segments[j].astype(np.int32)], -1, (1, 1, 1), cv2.FILLED)

        # 对PPL图像应用翻转和复制粘贴
        result = cv2.flip(im, 1)  # augment segments (flip left-right)
        i = cv2.flip(im_new, 1).astype(bool)
        im[i] = result[i]  # cv2.imwrite('debug.jpg', im)  # debug
        
        # 对XPL图像应用相同的翻转和复制粘贴
        if im_xpl is not None:
            result_xpl = cv2.flip(im_xpl, 1)  # augment segments (flip left-right)
            i_xpl = cv2.flip(im_new_xpl, 1).astype(bool)
            im_xpl[i_xpl] = result_xpl[i_xpl]

    # 返回结果
    if im_xpl is not None:
        return im, labels, segments, im_xpl
    else:
        return im, labels, segments

def letterbox(im, new_shape=(640, 640), color=(114, 114, 114), auto=True, scaleFill=False, scaleup=True, stride=32):
    """Resizes and pads image to new_shape with stride-multiple constraints, returns resized image, ratio, padding."""
    shape = im.shape[:2]  # current shape [height, width]
    if isinstance(new_shape, int):
        new_shape = (new_shape, new_shape)

    # Scale ratio (new / old)
    r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
    if not scaleup:  # only scale down, do not scale up (for better val mAP)
        r = min(r, 1.0)

    # Compute padding
    ratio = r, r  # width, height ratios
    new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
    dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
    if auto:  # minimum rectangle
        dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
    elif scaleFill:  # stretch
        dw, dh = 0.0, 0.0
        new_unpad = (new_shape[1], new_shape[0])
        ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # width, height ratios

    dw /= 2  # divide padding into 2 sides
    dh /= 2

    if shape[::-1] != new_unpad:  # resize
        im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)
    top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
    left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
    im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # add border
    return im, ratio, (dw, dh)

def create_dataloader(
    path,                    # 数据集路径
    imgsz,                   # 图像尺寸
    batch_size,              # 批次大小
    stride,                  # 模型步长
    single_cls=False,        # 是否为单类别检测
    hyp=None,                # 超参数字典
    augment=False,           # 是否启用数据增强
    cache=False,             # 是否缓存图像
    pad=0.0,                 # 填充参数
    rect=False,              # 是否使用矩形训练
    rank=-1,                 # 分布式训练排名
    workers=8,               # 数据加载工作进程数
    image_weights=False,     # 是否使用图像权重
    quad=False,              # 是否使用四倍批次大小
    prefix="",               # 日志前缀
    shuffle=False,           # 是否打乱数据
    mask_downsample_ratio=1, # 掩码下采样比例
    overlap_mask=False,      # 是否允许掩码重叠
    seed=0,                  # 随机种子
):
    """创建用于训练、验证或测试YOLO模型的数据加载器，支持各种数据集选项。"""
    # 矩形训练与数据打乱不兼容
    if rect and shuffle:
        # 矩形训练与数据打乱不兼容
        LOGGER.warning("WARNING ⚠️ --rect is incompatible with DataLoader shuffle, setting shuffle=False")
        shuffle = False
    
    # 在分布式训练中，只在第一个进程中初始化数据集缓存
    with torch_distributed_zero_first(rank):
        dataset = LoadImagesAndLabelsAndMasks(
            path,
            imgsz,
            batch_size,
            augment=augment,              # 数据增强
            hyp=hyp,                      # 超参数
            rect=rect,                    # 矩形批次
            cache_images=cache,           # 图像缓存
            single_cls=single_cls,        # 单类别
            stride=int(stride),           # 步长
            pad=pad,                      # 填充
            image_weights=image_weights,  # 图像权重
            prefix=prefix,                # 前缀
            downsample_ratio=mask_downsample_ratio,  # 掩码下采样比例
            overlap=overlap_mask,         # 掩码重叠
            rank=rank,                    # 进程排名
        )

    # 确保批次大小不超过数据集大小
    batch_size = min(batch_size, len(dataset))
    
    # 计算CUDA设备数量和工作进程数
    nd = torch.cuda.device_count()  # CUDA设备数量
    nw = min([os.cpu_count() // max(nd, 1), batch_size if batch_size > 1 else 0, workers])  # 工作进程数
    
    # 设置采样器（分布式训练时使用智能分布式采样器）
    sampler = None if rank == -1 else SmartDistributedSampler(dataset, shuffle=shuffle)
    
    # 选择数据加载器类型（图像权重时只能使用DataLoader）
    loader = DataLoader if image_weights else InfiniteDataLoader
    
    # 设置随机数生成器
    generator = torch.Generator()
    generator.manual_seed(6148914691236517205 + seed + RANK)
    
    # 创建并返回数据加载器和数据集
    return loader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle and sampler is None,  # 只有在没有采样器时才打乱
        num_workers=nw,                       # 工作进程数
        sampler=sampler,                      # 采样器
        drop_last=quad,                       # 四倍批次时丢弃最后一个不完整批次
        pin_memory=True,                      # 固定内存以加速GPU传输
        collate_fn=LoadImagesAndLabelsAndMasks.collate_fn4 if quad else LoadImagesAndLabelsAndMasks.collate_fn,
        worker_init_fn=seed_worker,           # 工作进程初始化函数
        generator=generator,                  # 随机数生成器
    ), dataset


def seed_worker(worker_id):
    """为数据加载器工作进程设置随机种子以确保可重现性。"""
    worker_seed = torch.initial_seed() % 2**32
    np.random.seed(worker_seed)
    random.seed(worker_seed)


class SmartDistributedSampler(distributed.DistributedSampler):
    """智能分布式采样器，确保分布式训练中跨GPU的确定性洗牌和平衡数据分布。"""

    def __init__(self, dataset, num_replicas=None, rank=None, shuffle=True, seed=0):
        super().__init__(dataset, num_replicas, rank, shuffle, seed)

    def __iter__(self):
        if self.shuffle:
            g = torch.Generator()
            g.manual_seed(self.seed + self.epoch)
            indices = torch.randperm(len(self.dataset), generator=g).tolist()
        else:
            indices = list(range(len(self.dataset)))

        # 添加额外的样本以使其可被num_replicas整除
        indices += indices[: (self.total_size - len(indices))]
        assert len(indices) == self.total_size

        # 子集
        indices = indices[self.rank : self.total_size : self.num_replicas]
        assert len(indices) == self.num_samples

        return iter(indices)


class InfiniteDataLoader(dataloader.DataLoader):
    """无限数据加载器，通过重复使用工作进程实现无限循环地提供数据批次。"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        object.__setattr__(self, "batch_sampler", _RepeatSampler(self.batch_sampler))
        self.iterator = super().__iter__()

    def __len__(self):
        return len(self.batch_sampler.sampler)

    def __iter__(self):
        for _ in range(len(self)):
            yield next(self.iterator)


class _RepeatSampler:
    """采样器包装器，永久重复采样。"""

    def __init__(self, sampler):
        self.sampler = sampler

    def __iter__(self):
        while True:
            yield from iter(self.sampler)
            
def verify_image_label(args):
    """Verifies a single image-label pair, ensuring image format, size, and legal label values."""
    im_file, lb_file, prefix = args
    nm, nf, ne, nc, msg, segments = 0, 0, 0, 0, "", []  # number (missing, found, empty, corrupt), message, segments
    try:
        # verify images
        im = Image.open(im_file)
        im.verify()  # PIL verify
        shape = exif_size(im)  # image size
        assert (shape[0] > 9) & (shape[1] > 9), f"image size {shape} <10 pixels"
        assert im.format.lower() in IMG_FORMATS, f"invalid image format {im.format}"
        if im.format.lower() in ("jpg", "jpeg"):
            with open(im_file, "rb") as f:
                f.seek(-2, 2)
                if f.read() != b"\xff\xd9":  # corrupt JPEG
                    ImageOps.exif_transpose(Image.open(im_file)).save(im_file, "JPEG", subsampling=0, quality=100)
                    msg = f"{prefix}WARNING ⚠️ {im_file}: corrupt JPEG restored and saved"

        # verify labels
        if os.path.isfile(lb_file):
            nf = 1  # label found
            with open(lb_file) as f:
                lb = [x.split() for x in f.read().strip().splitlines() if len(x)]
                if any(len(x) > 6 for x in lb):  # is segment
                    classes = np.array([x[0] for x in lb], dtype=np.float32)
                    segments = [np.array(x[1:], dtype=np.float32).reshape(-1, 2) for x in lb]  # (cls, xy1...)
                    lb = np.concatenate((classes.reshape(-1, 1), segments2boxes(segments)), 1)  # (cls, xywh)
                lb = np.array(lb, dtype=np.float32)
            if nl := len(lb):
                assert lb.shape[1] == 5, f"labels require 5 columns, {lb.shape[1]} columns detected"
                assert (lb >= 0).all(), f"negative label values {lb[lb < 0]}"
                assert (lb[:, 1:] <= 1).all(), f"non-normalized or out of bounds coordinates {lb[:, 1:][lb[:, 1:] > 1]}"
                _, i = np.unique(lb, axis=0, return_index=True)
                if len(i) < nl:  # duplicate row check
                    lb = lb[i]  # remove duplicates
                    if segments:
                        segments = [segments[x] for x in i]
                    msg = f"{prefix}WARNING ⚠️ {im_file}: {nl - len(i)} duplicate labels removed"
            else:
                ne = 1  # label empty
                lb = np.zeros((0, 5), dtype=np.float32)
        else:
            nm = 1  # label missing
            lb = np.zeros((0, 5), dtype=np.float32)
        return im_file, lb, shape, segments, nm, nf, ne, nc, msg
    except Exception as e:
        nc = 1
        msg = f"{prefix}WARNING ⚠️ {im_file}: ignoring corrupt image/label: {e}"
        return [None, None, None, None, nm, nf, ne, nc, msg]

def img2label_paths(img_paths):
    """从对应的图像文件路径生成标签文件路径，通过将`/images/`替换为`/labels/`并将扩展名替换为`.txt`。
    
    Args:
        img_paths: 图像文件路径列表
        
    Returns:
        list: 对应的标签文件路径列表
    """
    sa, sb = f"{os.sep}images{os.sep}", f"{os.sep}labels{os.sep}"  # /images/, /labels/ 子字符串
    return [sb.join(x.rsplit(sa, 1)).rsplit(".", 1)[0] + ".txt" for x in img_paths]  # 路径转换


class LoadImagesAndLabels(Dataset):
    """为YOLOv5的训练和验证加载图像及其对应的标签。
    
    这是YOLOv5的主要数据集类，负责：
    - 加载图像和标签文件
    - 缓存标签信息以提高性能
    - 处理数据增强（马赛克、混合等）
    - 支持矩形训练和图像权重
    """

    cache_version = 0.6  # 数据集标签*.cache版本
    rand_interp_methods = [cv2.INTER_NEAREST, cv2.INTER_LINEAR, cv2.INTER_CUBIC, cv2.INTER_AREA, cv2.INTER_LANCZOS4]  # 随机插值方法

    def __init__(
        self,
        path,
        img_size=640,
        batch_size=16,
        augment=False,
        hyp=None,
        rect=False,
        image_weights=False,
        cache_images=False,
        single_cls=False,
        stride=32,
        pad=0.0,
        min_items=0,
        prefix="",
        rank=-1,
        seed=0,
    ):
        """初始化YOLOv5数据集加载器，处理图像及其标签、缓存和预处理。
        
        Args:
            path: 数据集路径或图像文件列表
            img_size: 图像尺寸
            batch_size: 批次大小
            augment: 是否启用数据增强
            hyp: 超参数字典
            rect: 是否使用矩形训练
            image_weights: 是否使用图像权重
            cache_images: 是否缓存图像到内存
            single_cls: 是否单类训练
            stride: 模型步长
            pad: 填充值
            min_items: 最小项目数
            prefix: 日志前缀
            rank: 分布式训练rank
            seed: 随机种子
        """
        self.img_size = img_size  # 图像尺寸
        self.augment = augment  # 数据增强标志
        self.hyp = hyp  # 超参数
        self.image_weights = image_weights  # 图像权重标志
        self.rect = False if image_weights else rect  # 矩形训练（图像权重时禁用）
        self.mosaic = self.augment and not self.rect  # 马赛克增强（仅训练时一次加载4张图像）
        self.mosaic_border = [-img_size // 2, -img_size // 2]  # 马赛克边界
        self.stride = stride  # 模型步长
        self.path = path  # 数据集路径
        self.albumentations = Albumentations(size=img_size) if augment else None  # Albumentations增强

        try:
            f = []  # 图像文件列表
            for p in path if isinstance(path, list) else [path]:  # 遍历路径
                p = Path(p)  # 跨平台路径
                if p.is_dir():  # 如果是目录
                    f += glob.glob(str(p / "**" / "*.*"), recursive=True)  # 递归查找所有文件
                    # f = list(p.rglob('*.*'))  # pathlib方式
                elif p.is_file():  # 如果是文件
                    with open(p) as t:  # 打开文件
                        t = t.read().strip().splitlines()  # 读取行
                        parent = str(p.parent) + os.sep  # 父目录
                        f += [x.replace("./", parent, 1) if x.startswith("./") else x for x in t]  # 转换为全局路径
                        # f += [p.parent / x.lstrip(os.sep) for x in t]  # pathlib方式转换为全局路径
                else:
                    raise FileNotFoundError(f"{prefix}{p} does not exist")  # 路径不存在错误
            self.im_files = sorted(x.replace("/", os.sep) for x in f if x.split(".")[-1].lower() in IMG_FORMATS)  # 过滤图像文件
            # self.img_files = sorted([x for x in f if x.suffix[1:].lower() in IMG_FORMATS])  # pathlib方式
            assert self.im_files, f"{prefix}No images found"  # 确保找到图像
        except Exception as e:
            raise Exception(f"{prefix}Error loading data from {path}: {e}\n{HELP_URL}") from e  # 加载数据错误

        # 检查缓存
        self.label_files = img2label_paths(self.im_files)  # 标签文件路径
        cache_path = (p if p.is_file() else Path(self.label_files[0]).parent).with_suffix(".cache")  # 缓存路径
        try:
            cache, exists = np.load(cache_path, allow_pickle=True).item(), True  # 加载字典
            assert cache["version"] == self.cache_version  # 匹配当前版本
            assert cache["hash"] == get_hash(self.label_files + self.im_files)  # 相同哈希
        except Exception:
            cache, exists = self.cache_labels(cache_path, prefix), False  # 运行缓存操作

        # 显示缓存信息
        nf, nm, ne, nc, n = cache.pop("results")  # 找到、缺失、空、损坏、总计
        if exists and LOCAL_RANK in {-1, 0}:  # 如果缓存存在且为主进程
            d = f"Scanning {cache_path}... {nf} images, {nm + ne} backgrounds, {nc} corrupt"  # 描述信息
            tqdm(None, desc=prefix + d, total=n, initial=n, bar_format=TQDM_BAR_FORMAT)  # 显示缓存结果
            if cache["msgs"]:  # 如果有消息
                LOGGER.info("\n".join(cache["msgs"]))  # 显示警告
        assert nf > 0 or not augment, f"{prefix}No labels found in {cache_path}, can not start training. {HELP_URL}"  # 确保找到标签

        # 读取缓存
        [cache.pop(k) for k in ("hash", "version", "msgs")]  # 移除项目
        labels, shapes, self.segments = zip(*cache.values())  # 解压缓存值
        nl = len(np.concatenate(labels, 0))  # 标签数量
        assert nl > 0 or not augment, f"{prefix}All labels empty in {cache_path}, can not start training. {HELP_URL}"  # 确保标签非空
        self.labels = list(labels)  # 标签列表
        self.shapes = np.array(shapes)  # 形状数组
        self.im_files = list(cache.keys())  # 更新图像文件
        self.label_files = img2label_paths(cache.keys())  # 更新标签文件

        # 过滤图像
        if min_items:  # 如果设置了最小项目数
            include = np.array([len(x) >= min_items for x in self.labels]).nonzero()[0].astype(int)  # 包含的索引
            LOGGER.info(f"{prefix}{n - len(include)}/{n} images filtered from dataset")  # 过滤信息
            self.im_files = [self.im_files[i] for i in include]  # 过滤图像文件
            self.label_files = [self.label_files[i] for i in include]  # 过滤标签文件
            self.labels = [self.labels[i] for i in include]  # 过滤标签
            self.segments = [self.segments[i] for i in include]  # 过滤分割
            self.shapes = self.shapes[include]  # 过滤形状（宽高）

        # 创建索引
        n = len(self.shapes)  # 图像数量
        bi = np.floor(np.arange(n) / batch_size).astype(int)  # 批次索引
        nb = bi[-1] + 1  # 批次数量
        self.batch = bi  # 图像的批次索引
        self.n = n  # 图像总数
        self.indices = np.arange(n)  # 索引数组
        if rank > -1:  # DDP索引（参见：SmartDistributedSampler）
            # 强制每个rank（即GPU进程）在每个epoch采样相同的数据子集
            self.indices = self.indices[np.random.RandomState(seed=seed).permutation(n) % WORLD_SIZE == RANK]

        # Update labels
        include_class = []  # filter labels to include only these classes (optional)
        self.segments = list(self.segments)
        include_class_array = np.array(include_class).reshape(1, -1)
        for i, (label, segment) in enumerate(zip(self.labels, self.segments)):
            if include_class:
                j = (label[:, 0:1] == include_class_array).any(1)
                self.labels[i] = label[j]
                if segment:
                    self.segments[i] = [segment[idx] for idx, elem in enumerate(j) if elem]
            if single_cls:  # single-class training, merge all classes into 0
                self.labels[i][:, 0] = 0

        # Rectangular Training
        if self.rect:
            # Sort by aspect ratio
            s = self.shapes  # wh
            ar = s[:, 1] / s[:, 0]  # aspect ratio
            irect = ar.argsort()
            self.im_files = [self.im_files[i] for i in irect]
            self.label_files = [self.label_files[i] for i in irect]
            self.labels = [self.labels[i] for i in irect]
            self.segments = [self.segments[i] for i in irect]
            self.shapes = s[irect]  # wh
            ar = ar[irect]

            # Set training image shapes
            shapes = [[1, 1]] * nb
            for i in range(nb):
                ari = ar[bi == i]
                mini, maxi = ari.min(), ari.max()
                if maxi < 1:
                    shapes[i] = [maxi, 1]
                elif mini > 1:
                    shapes[i] = [1, 1 / mini]

            self.batch_shapes = np.ceil(np.array(shapes) * img_size / stride + pad).astype(int) * stride

        # Cache images into RAM/disk for faster training
        if cache_images == "ram" and not self.check_cache_ram(prefix=prefix):
            cache_images = False
        self.ims = [None] * n
        self.npy_files = [Path(f).with_suffix(".npy") for f in self.im_files]
        if cache_images:
            b, gb = 0, 1 << 30  # bytes of cached images, bytes per gigabytes
            self.im_hw0, self.im_hw = [None] * n, [None] * n
            fcn = self.cache_images_to_disk if cache_images == "disk" else self.load_image
            with ThreadPool(NUM_THREADS) as pool:
                results = pool.imap(lambda i: (i, fcn(i)), self.indices)
                pbar = tqdm(results, total=len(self.indices), bar_format=TQDM_BAR_FORMAT, disable=LOCAL_RANK > 0)
                for i, x in pbar:
                    if cache_images == "disk":
                        b += self.npy_files[i].stat().st_size
                    else:  # 'ram'
                        self.ims[i], self.im_hw0[i], self.im_hw[i] = x  # im, hw_orig, hw_resized = load_image(self, i)
                        b += self.ims[i].nbytes * WORLD_SIZE
                    pbar.desc = f"{prefix}Caching images ({b / gb:.1f}GB {cache_images})"
                pbar.close()

    def check_cache_ram(self, safety_margin=0.1, prefix=""):
        """检查可用RAM是否足以缓存图像，考虑安全边际。
        
        Args:
            safety_margin: 安全边际（默认0.1表示10%）
            prefix: 日志前缀
            
        Returns:
            bool: 如果有足够RAM返回True，否则返回False
        """
        b, gb = 0, 1 << 30  # 缓存图像的字节数，每GB的字节数
        n = min(self.n, 30)  # 最多检查30张图像的外推
        for _ in range(n):
            im = cv2.imread(random.choice(self.im_files))  # 样本图像
            ratio = self.img_size / max(im.shape[0], im.shape[1])  # max(h, w)  # 比例
            b += im.nbytes * ratio**2  # 字节数
        mem_required = b * self.n / n  # 所需内存字节数
        mem = psutil.virtual_memory()
        cache = mem_required * (1 + safety_margin) < mem.available  # 检查是否有足够内存
        if not cache:
            LOGGER.info(
                f"{prefix}{mem_required / gb:.1f}GB RAM required, "
                f"{mem.available / gb:.1f}/{mem.total / gb:.1f}GB available, "
                f"{'✅ ' if cache else '❌ '}{'caching images' if cache else 'not caching images'}"
            )
        return cache

    def cache_labels(self, path=Path("./labels.cache"), prefix=""):
        """缓存数据集标签、验证图像、读取形状并跟踪数据集完整性。
        
        Args:
            path: 缓存文件路径
            prefix: 日志前缀
            
        Returns:
            dict: 包含标签、形状、分割和统计信息的缓存字典
        """
        x = {}  # 字典
        nm, nf, ne, nc, msgs = 0, 0, 0, 0, []  # 缺失、找到、空、损坏的数量，消息
        desc = f"{prefix}Scanning {path.parent / path.stem}..."
        with Pool(NUM_THREADS) as pool:
            pbar = tqdm(
                pool.imap(verify_image_label, zip(self.im_files, self.label_files, repeat(prefix))),
                desc=desc,
                total=len(self.im_files),
                bar_format=TQDM_BAR_FORMAT,
            )
            for im_file, lb, shape, segments, nm_f, nf_f, ne_f, nc_f, msg in pbar:
                nm += nm_f
                nf += nf_f
                ne += ne_f
                nc += nc_f
                if im_file:
                    x[im_file] = [lb, shape, segments]
                if msg:
                    msgs.append(msg)
                pbar.desc = f"{desc} {nf} images, {nm + ne} backgrounds, {nc} corrupt"

        pbar.close()
        if msgs:
            LOGGER.info("\n".join(msgs))
        if nf == 0:
            LOGGER.warning(f"{prefix}WARNING ⚠️ No labels found in {path}. {HELP_URL}")
        x["hash"] = get_hash(self.label_files + self.im_files)
        x["results"] = nf, nm, ne, nc, len(self.im_files)
        x["msgs"] = msgs  # 警告
        x["version"] = self.cache_version  # 缓存版本
        try:
            np.save(path, x)  # 保存缓存
            path.with_suffix(".cache.npy").rename(path)  # 移除.npy后缀
            LOGGER.info(f"{prefix}New cache created: {path}")
        except Exception as e:
            LOGGER.warning(f"{prefix}WARNING ⚠️ Cache directory {path.parent} is not writeable: {e}")  # 路径不可写
        return x

    def __len__(self):
        """返回数据集中的图像数量。"""
        return len(self.im_files)

    def __getitem__(self, index):
        """根据索引获取数据集项，处理马赛克加载、MixUp增强、图像加载、Letterbox处理、数据增强等。
        
        Args:
            index: 数据集索引
            
        Returns:
            tuple: (图像张量, 标签张量, 路径, 形状)
        """
        index = self.indices[index]  # 线性、洗牌或图像权重

        hyp = self.hyp
        mosaic = self.mosaic and random.random() < hyp["mosaic"]
        if mosaic:
            # 加载马赛克
            img, labels = self.load_mosaic(index)
            shapes = None

            # MixUp增强
            if random.random() < hyp["mixup"]:
                img, labels = mixup(img, labels, *self.load_mosaic(random.randint(0, self.n - 1)))

        else:
            # 加载图像
            img, (h0, w0), (h, w) = self.load_image(index)

            # Letterbox
            shape = self.batch_shapes[self.batch[index]] if self.rect else self.img_size  # 最终letterboxed形状
            img, ratio, pad = letterbox(img, shape, auto=False, scaleup=self.augment)
            shapes = (h0, w0), ((h / h0, w / w0), pad)  # 用于COCO mAP重新缩放

            labels = self.labels[index].copy()
            if labels.size:  # 归一化xywh到像素xyxy格式
                labels[:, 1:] = xywhn2xyxy(labels[:, 1:], ratio[0] * w, ratio[1] * h, padw=pad[0], padh=pad[1])

            if self.augment:
                img, labels = random_perspective(
                    img,
                    labels,
                    degrees=hyp["degrees"],
                    translate=hyp["translate"],
                    scale=hyp["scale"],
                    shear=hyp["shear"],
                    perspective=hyp["perspective"],
                )

        nl = len(labels)  # 标签数量
        if nl:
            labels[:, 1:5] = xyxy2xywhn(labels[:, 1:5], w=img.shape[1], h=img.shape[0], clip=True, eps=1e-3)

        if self.augment:
            # Albumentations
            img, labels = self.albumentations(img, labels)
            nl = len(labels)  # 更新后的标签数量

            # HSV色彩空间
            augment_hsv(img, hgain=hyp["hsv_h"], sgain=hyp["hsv_s"], vgain=hyp["hsv_v"])

            # 翻转上下
            if random.random() < hyp["flipud"]:
                img = np.flipud(img)
                if nl:
                    labels[:, 2] = 1 - labels[:, 2]

            # 翻转左右
            if random.random() < hyp["fliplr"]:
                img = np.fliplr(img)
                if nl:
                    labels[:, 1] = 1 - labels[:, 1]

        labels_out = torch.zeros((nl, 6))
        if nl:
            labels_out[:, 1:] = torch.from_numpy(labels)

        # 转换
        img = img.transpose((2, 0, 1))[::-1]  # HWC到CHW，BGR到RGB
        img = np.ascontiguousarray(img)

        return torch.from_numpy(img), labels_out, self.im_files[index], shapes

    def load_image(self, i):
        """按索引加载图像，返回图像和原始/调整后的尺寸。
        
        Args:
            i: 图像索引
            
        Returns:
            tuple: (图像数组, 原始尺寸, 调整后尺寸)
        """
        im, f, fn = (
            self.ims[i],
            self.im_files[i],
            self.npy_files[i],
        )
        if im is None:  # 未缓存在内存中
            if fn.exists():  # 从*.npy文件加载
                im = np.load(fn)
            else:  # 从图像文件读取
                im = cv2.imread(f)  # BGR
                assert im is not None, f"Image Not Found {f}"
            h0, w0 = im.shape[:2]  # 原始hw
            r = self.img_size / max(h0, w0)  # 调整比例
            if r != 1:  # 如果尺寸不相等
                interp = cv2.INTER_LINEAR if (self.augment or r > 1) else cv2.INTER_AREA
                im = cv2.resize(im, (math.ceil(w0 * r), math.ceil(h0 * r)), interpolation=interp)
            return im, (h0, w0), im.shape[:2]  # im, hw_original, hw_resized
        return self.ims[i], self.im_hw0[i], self.im_hw[i]  # im, hw_original, hw_resized

    def cache_images_to_disk(self, i):
        """将图像保存为*.npy文件以加快加载速度。
        
        Args:
            i: 图像索引
        """
        f = self.npy_files[i]
        if not f.exists():
            np.save(f.as_posix(), cv2.imread(self.im_files[i]))

    def load_mosaic(self, index):
        """加载4图像马赛克，用于数据增强。
        
        Args:
            index: 中心图像索引
            
        Returns:
            tuple: (马赛克图像, 标签)
        """
        labels4, segments4 = [], []
        s = self.img_size
        yc, xc = (int(random.uniform(-x, 2 * s + x)) for x in self.mosaic_border)  # 马赛克中心x, y
        indices = [index] + random.choices(self.indices, k=3)  # 3个额外的图像索引
        random.shuffle(indices)
        for i, index in enumerate(indices):
            # 加载图像
            img, _, (h, w) = self.load_image(index)

            # 放置在img4中
            if i == 0:  # 左上
                img4 = np.full((s * 2, s * 2, img.shape[2]), 114, dtype=np.uint8)  # 基础图像，填充值114
                x1a, y1a, x2a, y2a = max(xc - w, 0), max(yc - h, 0), xc, yc  # xmin, ymin, xmax, ymax（大图像）
                x1b, y1b, x2b, y2b = w - (x2a - x1a), h - (y2a - y1a), w, h  # xmin, ymin, xmax, ymax（小图像）
            elif i == 1:  # 右上
                x1a, y1a, x2a, y2a = xc, max(yc - h, 0), min(xc + w, s * 2), yc
                x1b, y1b, x2b, y2b = 0, h - (y2a - y1a), min(w, x2a - x1a), h
            elif i == 2:  # 左下
                x1a, y1a, x2a, y2a = max(xc - w, 0), yc, xc, min(s * 2, yc + h)
                x1b, y1b, x2b, y2b = w - (x2a - x1a), 0, w, min(y2a - y1a, h)
            elif i == 3:  # 右下
                x1a, y1a, x2a, y2a = xc, yc, min(xc + w, s * 2), min(s * 2, yc + h)
                x1b, y1b, x2b, y2b = 0, 0, min(w, x2a - x1a), min(y2a - y1a, h)

            img4[y1a:y2a, x1a:x2a] = img[y1b:y2b, x1b:x2b]  # img4[ymin:ymax, xmin:xmax]
            padw = x1a - x1b
            padh = y1a - y1b

            # 标签
            labels, segments = self.labels[index].copy(), self.segments[index].copy()
            if labels.size:
                labels[:, 1:] = xywhn2xyxy(labels[:, 1:], w, h, padw, padh)  # 归一化xywh到像素xyxy
                segments = [xyn2xy(x, w, h, padw, padh) for x in segments]
            labels4.append(labels)
            segments4.append(segments)

        # 连接/裁剪标签
        labels4 = np.concatenate(labels4, 0)
        for x in (labels4[:, 1:], *segments4):
            np.clip(x, 0, 2 * s, out=x)  # 裁剪到边界
        # img4, labels4 = replicate(img4, labels4)  # 复制

        # 增强
        img4, labels4, segments4 = copy_paste(img4, labels4, segments4, p=self.hyp["copy_paste"])
        img4, labels4 = random_perspective(
            img4,
            labels4,
            segments=segments4,
            degrees=self.hyp["degrees"],
            translate=self.hyp["translate"],
            scale=self.hyp["scale"],
            shear=self.hyp["shear"],
            perspective=self.hyp["perspective"],
            border=self.mosaic_border,
        )  # 边界到移除

        return img4, labels4

    def load_mosaic9(self, index):
        """加载9图像马赛克，用于数据增强。
        
        Args:
            index: 中心图像索引
            
        Returns:
            tuple: (马赛克图像, 标签)
        """
        labels9, segments9 = [], []
        s = self.img_size
        indices = [index] + random.choices(self.indices, k=8)  # 8个额外的图像索引
        random.shuffle(indices)
        hp, wp = -1, -1  # 高度、宽度上一个
        for i, index in enumerate(indices):
            # 加载图像
            img, _, (h, w) = self.load_image(index)

            # 放置在img9中
            if i == 0:  # 中心
                img9 = np.full((s * 3, s * 3, img.shape[2]), 114, dtype=np.uint8)  # 基础图像，填充值114
                h0, w0 = h, w
                c = s, s, s + w, s + h  # xmin, ymin, xmax, ymax（基础）坐标
            elif i == 1:  # 左上
                c = s - w, s - h, s, s
            elif i == 2:  # 上
                c = s, s - h, s + w, s
            elif i == 3:  # 右上
                c = s + wp, s - h, s + wp + w, s
            elif i == 4:  # 左
                c = s - w, s, s, s + h
            elif i == 5:  # 右
                c = s + w0, s, s + w0 + w, s + h
            elif i == 6:  # 左下
                c = s - w, s + hp, s, s + hp + h
            elif i == 7:  # 下
                c = s, s + h0, s + w, s + h0 + h
            elif i == 8:  # 右下
                c = s + w0, s + h0, s + w0 + w, s + h0 + h

            padx, pady = c[:2]
            x1, y1, x2, y2 = (max(x, 0) for x in c)  # 分配坐标

            # 标签
            labels, segments = self.labels[index].copy(), self.segments[index].copy()
            if labels.size:
                labels[:, 1:] = xywhn2xyxy(labels[:, 1:], w, h, padx, pady)  # 归一化xywh到像素xyxy
                segments = [xyn2xy(x, w, h, padx, pady) for x in segments]
            labels9.append(labels)
            segments9.append(segments)

            # 图像
            img9[y1:y2, x1:x2] = img[y1 - pady :, x1 - padx :]  # img9[ymin:ymax, xmin:xmax]
            hp, wp = h, w  # 高度、宽度上一个

        # 偏移
        yc, xc = (int(random.uniform(0, s)) for _ in self.mosaic_border)  # 马赛克中心x, y
        img9 = img9[yc : yc + 2 * s, xc : xc + 2 * s]

        # 连接/裁剪标签
        labels9 = np.concatenate(labels9, 0)
        labels9[:, [1, 3]] -= xc
        labels9[:, [2, 4]] -= yc
        c = np.array([xc, yc])  # 中心
        segments9 = [x - c for x in segments9]

        for x in (labels9[:, 1:], *segments9):
            np.clip(x, 0, 2 * s, out=x)  # 裁剪到边界

        # 增强
        img9, labels9 = random_perspective(
            img9,
            labels9,
            segments=segments9,
            degrees=self.hyp["degrees"],
            translate=self.hyp["translate"],
            scale=self.hyp["scale"],
            shear=self.hyp["shear"],
            perspective=self.hyp["perspective"],
            border=self.mosaic_border,
        )  # 边界到移除

        return img9, labels9

    @staticmethod
    def collate_fn(batch):
        """DataLoader的自定义整理函数，批处理图像、标签、路径和形状。
        
        Args:
            batch: 批次数据列表
            
        Returns:
            tuple: (图像张量, 标签张量, 路径列表, 形状列表)
        """
        im, label, path, shapes = zip(*batch)  # 转置
        for i, lb in enumerate(label):
            lb[:, 0] = i  # 为合并的标签张量添加目标图像索引
        return torch.stack(im, 0), torch.cat(label, 0), path, shapes

    @staticmethod
    def collate_fn4(batch):
        """DataLoader的自定义整理函数，用于4图像批次，批处理图像、标签、路径和形状。
        
        Args:
            batch: 批次数据列表
            
        Returns:
            tuple: (图像张量, 标签张量, 路径列表, 形状列表)
        """
        im, label, path, shapes = zip(*batch)  # 转置
        n = len(shapes) // 4
        im4, label4, path4, shapes4 = [], [], path[:n], shapes[:n]

        ho = torch.tensor([[0.0, 0, 0, 1, 0, 0]])
        wo = torch.tensor([[0.0, 0, 1, 0, 0, 0]])
        s = torch.tensor([[1, 1, 0.5, 0.5, 0.5, 0.5]])  # 缩放
        for i in range(n):  # zidane torch.zeros(16,3,720,1280)  # BCHW
            i *= 4
            if random.random() < 0.5:
                im1 = F.interpolate(im[i].unsqueeze(0).float(), scale_factor=2.0, mode="bilinear", align_corners=False)[
                    0
                ].type(im[i].type())
                lb = label[i]
            else:
                im1 = torch.cat((torch.cat((im[i], im[i + 1]), 1), torch.cat((im[i + 2], im[i + 3]), 1)), 2)
                lb = torch.cat((label[i], label[i + 1] + ho, label[i + 2] + wo, label[i + 3] + ho + wo), 0) * s
            im4.append(im1)
            label4.append(lb)

        for i, lb in enumerate(label4):
            lb[:, 0] = i  # 为合并的标签张量添加目标图像索引
        return torch.stack(im4, 0), torch.cat(label4, 0), path4, shapes4


class LoadImagesAndLabelsAndMasks(LoadImagesAndLabels):  # 用于训练/测试
    """加载图像、标签和分割掩码，用于训练和测试YOLO模型，支持数据增强。"""

    def __init__(
        self,
        path,                # 数据集路径
        img_size=640,        # 图像尺寸
        batch_size=16,       # 批次大小
        augment=False,       # 是否启用数据增强
        hyp=None,            # 超参数字典
        rect=False,          # 是否使用矩形训练
        image_weights=False, # 是否使用图像权重
        cache_images=False,  # 是否缓存图像
        single_cls=False,    # 是否为单类别检测
        stride=32,           # 模型步长
        pad=0,               # 填充参数
        min_items=0,         # 最小项目数
        prefix="",           # 日志前缀
        downsample_ratio=1,  # 下采样比例
        overlap=False,       # 是否允许重叠
        rank=-1,             # 分布式训练排名
        seed=0,              # 随机种子
    ):
        """初始化数据集，具备图像、标签和掩码加载功能，用于训练/测试。"""
        # 调用父类初始化方法
        super().__init__(
            path,
            img_size,
            batch_size,
            augment,
            hyp,
            rect,
            image_weights,
            cache_images,
            single_cls,
            stride,
            pad,
            min_items,
            prefix,
            rank,
            seed,
        )
        # 设置分割特有的属性
        self.downsample_ratio = downsample_ratio  # 掩码下采样比例
        self.overlap = overlap                    # 是否允许掩码重叠

    def __getitem__(self, index):
        """返回指定索引处数据集的变换项，处理索引和图像权重。"""
        index = self.indices[index]  # 线性、打乱或图像权重索引

        hyp = self.hyp
        # 根据概率决定是否使用马赛克增强
        if mosaic := self.mosaic and random.random() < hyp["mosaic"]:
            # 加载马赛克图像
            img, labels, segments = self.load_mosaic(index)
            shapes = None

            # MixUp数据增强
            if random.random() < hyp["mixup"]:
                img, labels, segments = mixup(img, labels, segments, *self.load_mosaic(random.randint(0, self.n - 1)))

        else:
            # 加载单张图像
            img, (h0, w0), (h, w) = self.load_image(index)

            # 应用letterbox变换（保持宽高比的缩放和填充）
            shape = self.batch_shapes[self.batch[index]] if self.rect else self.img_size  # 最终letterbox形状
            img, ratio, pad = letterbox(img, shape, auto=False, scaleup=self.augment)
            shapes = (h0, w0), ((h / h0, w / w0), pad)  # 用于COCO mAP重新缩放

            # 复制标签和分割数据
            labels = self.labels[index].copy()
            # 分割多边形数据：[array, array, ....], array.shape=(num_points, 2), xyxyxyxy格式
            segments = self.segments[index].copy()
            
            # 将分割多边形坐标从归一化转换为像素坐标
            if len(segments):
                for i_s in range(len(segments)):
                    segments[i_s] = xyn2xy(
                        segments[i_s],
                        ratio[0] * w,
                        ratio[1] * h,
                        padw=pad[0],
                        padh=pad[1],
                    )
            
            # 将标签从归一化xywh格式转换为像素xyxy格式
            if labels.size:
                labels[:, 1:] = xywhn2xyxy(labels[:, 1:], ratio[0] * w, ratio[1] * h, padw=pad[0], padh=pad[1])

            # 应用随机透视变换增强
            if self.augment:
                img, labels, segments = random_perspective(
                    img,
                    labels,
                    segments=segments,
                    degrees=hyp["degrees"],      # 旋转角度
                    translate=hyp["translate"],  # 平移
                    scale=hyp["scale"],          # 缩放
                    shear=hyp["shear"],          # 剪切
                    perspective=hyp["perspective"],  # 透视变换
                )

        nl = len(labels)  # 标签数量
        masks = []
        
        # 处理分割掩码
        if nl:
            # 将边界框从像素xyxy格式转换为归一化xywh格式
            labels[:, 1:5] = xyxy2xywhn(labels[:, 1:5], w=img.shape[1], h=img.shape[0], clip=True, eps=1e-3)
            
            if self.overlap:
                # 处理重叠掩码
                masks, sorted_idx = polygons2masks_overlap(
                    img.shape[:2], segments, downsample_ratio=self.downsample_ratio
                )
                masks = masks[None]  # (640, 640) -> (1, 640, 640)
                labels = labels[sorted_idx]  # 按面积排序标签
            else:
                # 处理非重叠掩码
                masks = polygons2masks(img.shape[:2], segments, color=1, downsample_ratio=self.downsample_ratio)

        # 转换掩码为张量格式
        masks = (
            torch.from_numpy(masks)
            if len(masks)
            else torch.zeros(
                1 if self.overlap else nl, 
                img.shape[0] // self.downsample_ratio, 
                img.shape[1] // self.downsample_ratio
            )
        )
        # TODO: albumentations支持
        if self.augment:
            # Albumentations数据增强
            # 有些增强不会改变边界框和掩码，
            # 所以暂时保持原样
            img, labels = self.albumentations(img, labels)
            nl = len(labels)  # 在albumentations后更新标签数量

            # HSV色彩空间增强
            augment_hsv(img, hgain=hyp["hsv_h"], sgain=hyp["hsv_s"], vgain=hyp["hsv_v"])

            # 上下翻转
            if random.random() < hyp["flipud"]:
                img = np.flipud(img)
                if nl:
                    labels[:, 2] = 1 - labels[:, 2]  # 调整y坐标
                    masks = torch.flip(masks, dims=[1])  # 翻转掩码

            # 左右翻转
            if random.random() < hyp["fliplr"]:
                img = np.fliplr(img)
                if nl:
                    labels[:, 1] = 1 - labels[:, 1]  # 调整x坐标
                    masks = torch.flip(masks, dims=[2])  # 翻转掩码

            # 裁剪增强  # labels = cutout(img, labels, p=0.5)

        # 准备输出标签
        labels_out = torch.zeros((nl, 6))
        if nl:
            labels_out[:, 1:] = torch.from_numpy(labels)

        # 转换图像格式
        img = img.transpose((2, 0, 1))[::-1]  # HWC转CHW，BGR转RGB
        img = np.ascontiguousarray(img)  # 确保内存连续性

        return (torch.from_numpy(img), labels_out, self.im_files[index], shapes, masks)

    def load_mosaic(self, index):
        """加载1张图像+3张随机图像组成4图像YOLOv5马赛克，相应调整标签和分割。"""
        labels4, segments4 = [], []
        s = self.img_size
        yc, xc = (int(random.uniform(-x, 2 * s + x)) for x in self.mosaic_border)  # 马赛克中心点x, y

        # 3个额外的图像索引
        indices = [index] + random.choices(self.indices, k=3)  # 3个额外的图像索引
        for i, index in enumerate(indices):
            # 加载图像
            img, _, (h, w) = self.load_image(index)

            # 将图像放置到img4中
            if i == 0:  # 左上角
                img4 = np.full((s * 2, s * 2, img.shape[2]), 114, dtype=np.uint8)  # 4个瓦片的基础图像
                x1a, y1a, x2a, y2a = max(xc - w, 0), max(yc - h, 0), xc, yc  # xmin, ymin, xmax, ymax (大图像)
                x1b, y1b, x2b, y2b = w - (x2a - x1a), h - (y2a - y1a), w, h  # xmin, ymin, xmax, ymax (小图像)
            elif i == 1:  # 右上角
                x1a, y1a, x2a, y2a = xc, max(yc - h, 0), min(xc + w, s * 2), yc
                x1b, y1b, x2b, y2b = 0, h - (y2a - y1a), min(w, x2a - x1a), h
            elif i == 2:  # 左下角
                x1a, y1a, x2a, y2a = max(xc - w, 0), yc, xc, min(s * 2, yc + h)
                x1b, y1b, x2b, y2b = w - (x2a - x1a), 0, w, min(y2a - y1a, h)
            elif i == 3:  # 右下角
                x1a, y1a, x2a, y2a = xc, yc, min(xc + w, s * 2), min(s * 2, yc + h)
                x1b, y1b, x2b, y2b = 0, 0, min(w, x2a - x1a), min(y2a - y1a, h)

            img4[y1a:y2a, x1a:x2a] = img[y1b:y2b, x1b:x2b]  # img4[ymin:ymax, xmin:xmax]
            padw = x1a - x1b  # 水平填充
            padh = y1a - y1b  # 垂直填充

            labels, segments = self.labels[index].copy(), self.segments[index].copy()

            if labels.size:
                labels[:, 1:] = xywhn2xyxy(labels[:, 1:], w, h, padw, padh)  # 归一化xywh转像素xyxy格式
                segments = [xyn2xy(x, w, h, padw, padh) for x in segments]
            labels4.append(labels)
            segments4.extend(segments)

        # 连接/裁剪标签
        labels4 = np.concatenate(labels4, 0)
        for x in (labels4[:, 1:], *segments4):
            np.clip(x, 0, 2 * s, out=x)  # 使用random_perspective()时进行裁剪
        # img4, labels4 = replicate(img4, labels4)  # 复制

        # 数据增强
        img4, labels4, segments4 = copy_paste(img4, labels4, segments4, p=self.hyp["copy_paste"])
        img4, labels4, segments4 = random_perspective(
            img4,
            labels4,
            segments4,
            degrees=self.hyp["degrees"],
            translate=self.hyp["translate"],
            scale=self.hyp["scale"],
            shear=self.hyp["shear"],
            perspective=self.hyp["perspective"],
            border=self.mosaic_border,
        )  # border to remove
        return img4, labels4, segments4

    @staticmethod
    def collate_fn(batch):
        """DataLoader的自定义整理函数，批处理图像、标签、路径、形状和分割掩码。"""
        img, label, path, shapes, masks = zip(*batch)  # 转置
        batched_masks = torch.cat(masks, 0)  # 连接掩码
        for i, l in enumerate(label):
            l[:, 0] = i  # 为build_targets()添加目标图像索引
        return torch.stack(img, 0), torch.cat(label, 0), path, shapes, batched_masks


def polygon2mask(img_size, polygons, color=1, downsample_ratio=1):
    """
    将多边形转换为掩码。
    
    Args:
        img_size (tuple): 图像尺寸。
        polygons (np.ndarray): [N, M], N是多边形数量，
            M是点的数量(需要被2整除)。
        color (int): 填充颜色。
        downsample_ratio (int): 下采样比例。
    """
    mask = np.zeros(img_size, dtype=np.uint8)
    polygons = np.asarray(polygons)
    polygons = polygons.astype(np.int32)
    shape = polygons.shape
    polygons = polygons.reshape(shape[0], -1, 2)
    cv2.fillPoly(mask, polygons, color=color)  # 填充多边形
    nh, nw = (img_size[0] // downsample_ratio, img_size[1] // downsample_ratio)
    # 注意：先fillPoly再resize是为了保持与mask-ratio=1时
    # 损失计算的一致性
    mask = cv2.resize(mask, (nw, nh))
    return mask


def polygons2masks(img_size, polygons, color, downsample_ratio=1):
    """
    将多个多边形转换为掩码数组。
    
    Args:
        img_size (tuple): 图像尺寸。
        polygons (list[np.ndarray]): 每个多边形是[N, M]，
            N是多边形数量，
            M是点的数量(需要被2整除)。
        color (int): 填充颜色。
        downsample_ratio (int): 下采样比例。
    """
    masks = []
    for si in range(len(polygons)):
        mask = polygon2mask(img_size, [polygons[si].reshape(-1)], color, downsample_ratio)
        masks.append(mask)
    return np.array(masks)


def polygons2masks_overlap(img_size, segments, downsample_ratio=1):
    """返回一个重叠掩码，按面积大小排序处理重叠区域。"""
    masks = np.zeros(
        (img_size[0] // downsample_ratio, img_size[1] // downsample_ratio),
        dtype=np.int32 if len(segments) > 255 else np.uint8,
    )
    areas = []  # 存储每个分割区域的面积
    ms = []     # 存储每个掩码
    
    # 为每个分割生成掩码并计算面积
    for si in range(len(segments)):
        mask = polygon2mask(
            img_size,
            [segments[si].reshape(-1)],
            downsample_ratio=downsample_ratio,
            color=1,
        )
        ms.append(mask)
        areas.append(mask.sum())  # 计算掩码面积
    
    # 按面积从大到小排序
    areas = np.asarray(areas)
    index = np.argsort(-areas)  # 降序排列索引
    ms = np.array(ms)[index]
    
    # 按面积大小顺序叠加掩码，处理重叠
    for i in range(len(segments)):
        mask = ms[i] * (i + 1)  # 给每个掩码分配唯一ID
        masks = masks + mask
        masks = np.clip(masks, a_min=0, a_max=i + 1)  # 限制值范围
    
    return masks, index


class LoadDualModalImagesAndMasks(LoadImagesAndLabelsAndMasks):
    """加载双模态图像(PPL和XPL)、标签和分割掩码，用于多模态训练/测试。"""

    def __init__(
        self,
        path,                # PPL图像路径
        img_size=640,        # 图像尺寸
        batch_size=16,       # 批次大小
        augment=False,       # 是否启用数据增强
        hyp=None,            # 超参数字典
        rect=False,          # 是否使用矩形训练
        image_weights=False, # 是否使用图像权重
        cache_images=False,  # 是否缓存图像
        single_cls=False,    # 是否为单类别检测
        stride=32,           # 模型步长
        pad=0,               # 填充参数
        min_items=0,         # 最小项目数
        prefix="",           # 日志前缀
        downsample_ratio=1,  # 下采样比例
        overlap=False,       # 是否允许重叠
        rank=-1,             # 分布式训练排名
        seed=0,              # 随机种子
        xpl_path=None,       # XPL图像路径
    ):
        """初始化双模态数据集，具备PPL和XPL图像加载能力。"""
        # 首先初始化父类
        super().__init__(
            path,
            img_size,
            batch_size,
            augment,
            hyp,
            rect,
            image_weights,
            cache_images,
            single_cls,
            stride,
            pad,
            min_items,
            prefix,
            downsample_ratio,
            overlap,
            rank,
            seed,
        )
        
        # 双模态特定属性
        self.xpl_path = xpl_path
        self.ppl_files = self.im_files.copy()  # PPL图像(原始)
        self.xpl_files = []  # XPL图像
        self.modal_pairs = {}  # PPL到XPL文件的映射
        self.missing_xpl = []  # 跟踪缺失的XPL文件
        
        # 构建双模态文件对
        self._build_modal_pairs()
        
        LOGGER.info(f"{prefix}双模态数据集已初始化: {len(self.ppl_files)} PPL图像, "
                   f"{len(self.xpl_files)} XPL图像, {len(self.missing_xpl)} 缺失XPL文件")

    def _build_modal_pairs(self):
        """基于文件名匹配构建PPL和XPL图像的文件对。"""
        if not self.xpl_path:
            # 如果未提供XPL路径，使用PPL图像作为两种模态
            LOGGER.warning("XPL path not provided, using PPL images for both modalities")
            self.xpl_files = self.ppl_files.copy()
            self.modal_pairs = {ppl: ppl for ppl in self.ppl_files}
            return
            
        xpl_dir = Path(self.xpl_path)
        if not xpl_dir.exists():
            # 如果XPL目录不存在，使用PPL图像作为两种模态
            LOGGER.warning(f"XPL directory {xpl_dir} does not exist, using PPL images for both modalities")
            self.xpl_files = self.ppl_files.copy()
            self.modal_pairs = {ppl: ppl for ppl in self.ppl_files}
            return
            
        # 创建文件名到XPL路径的映射
        xpl_file_map = {}
        for ext in ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.tif', '*.tiff']:
            for xpl_file in xpl_dir.rglob(ext):
                stem = xpl_file.stem
                xpl_file_map[stem] = str(xpl_file)
                
        # 将PPL文件与XPL文件进行匹配
        for ppl_file in self.ppl_files:
            ppl_stem = Path(ppl_file).stem
            if ppl_stem in xpl_file_map:
                xpl_file = xpl_file_map[ppl_stem]
                self.xpl_files.append(xpl_file)
                self.modal_pairs[ppl_file] = xpl_file
            else:
                # 缺少XPL文件，使用PPL作为备用
                self.xpl_files.append(ppl_file)
                self.modal_pairs[ppl_file] = ppl_file
                self.missing_xpl.append(ppl_stem)
                
    def load_image(self, i):
        """在索引i处加载双模态图像（PPL和XPL）。"""
        # 加载PPL图像（原始方法）
        ppl_img, (h0, w0), (h, w) = super().load_image(i)
        
        # 加载XPL图像
        xpl_path = self.xpl_files[i]
        xpl_img = cv2.imread(xpl_path)  # BGR格式
        assert xpl_img is not None, f"XPL Image Not Found {xpl_path}"
        
        # 如果需要，调整XPL图像尺寸以匹配PPL图像
        if xpl_img.shape[:2] != (h, w):
            xpl_img = cv2.resize(xpl_img, (w, h), interpolation=cv2.INTER_LINEAR)
            
        return (ppl_img, xpl_img), (h0, w0), (h, w)
        
    def __getitem__(self, index):
        """返回指定索引处数据集的双模态变换项。"""
        index = self.indices[index]  # 线性、随机或图像权重索引

        hyp = self.hyp
        if mosaic := self.mosaic and random.random() < hyp["mosaic"]:
            # 为双模态加载马赛克
            (ppl_img, xpl_img), labels, segments = self.load_mosaic(index)
            shapes = None

            # 双模态的MixUp数据增强
            if random.random() < hyp["mixup"]:
                (ppl_img2, xpl_img2), labels2, segments2 = self.load_mosaic(random.randint(0, self.n - 1))
                ppl_img, labels, segments, xpl_img = mixup(ppl_img, labels, segments, ppl_img2, labels2, segments2, im_xpl=xpl_img, im2_xpl=xpl_img2)

        else:
            # 加载双模态图像
            (ppl_img, xpl_img), (h0, w0), (h, w) = self.load_image(index)

            # 使用相同参数对两个图像进行letterbox处理
            shape = self.batch_shapes[self.batch[index]] if self.rect else self.img_size
            ppl_img, ratio, pad = letterbox(ppl_img, shape, auto=False, scaleup=self.augment)
            xpl_img, _, _ = letterbox(xpl_img, shape, auto=False, scaleup=self.augment)
            shapes = (h0, w0), ((h / h0, w / w0), pad)

            labels = self.labels[index].copy()
            segments = self.segments[index].copy()
            if len(segments):
                for i_s in range(len(segments)):
                    segments[i_s] = xyn2xy(
                        segments[i_s],
                        ratio[0] * w,
                        ratio[1] * h,
                        padw=pad[0],
                        padh=pad[1],
                    )
            if labels.size:
                labels[:, 1:] = xywhn2xyxy(labels[:, 1:], ratio[0] * w, ratio[1] * h, padw=pad[0], padh=pad[1])

            if self.augment:
                # 对两个图像应用相同的随机透视变换
                ppl_img, labels, segments, xpl_img = random_perspective(
                    ppl_img,
                    labels,
                    segments=segments,
                    degrees=hyp["degrees"],
                    translate=hyp["translate"],
                    scale=hyp["scale"],
                    shear=hyp["shear"],
                    perspective=hyp["perspective"],
                    im_xpl=xpl_img,
                )

        nl = len(labels)
        masks = []
        if nl:
            labels[:, 1:5] = xyxy2xywhn(labels[:, 1:5], w=ppl_img.shape[1], h=ppl_img.shape[0], clip=True, eps=1e-3)
            if self.overlap:
                masks, sorted_idx = polygons2masks_overlap(
                    ppl_img.shape[:2], segments, downsample_ratio=self.downsample_ratio
                )
                masks = masks[None]
                labels = labels[sorted_idx]
            else:
                masks = polygons2masks(ppl_img.shape[:2], segments, color=1, downsample_ratio=self.downsample_ratio)

        masks = (
            torch.from_numpy(masks)
            if len(masks)
            else torch.zeros(
                1 if self.overlap else nl, ppl_img.shape[0] // self.downsample_ratio, ppl_img.shape[1] // self.downsample_ratio
            )
        )

        if self.augment:
            # 对两个图像应用相同的增强
            ppl_img, labels = self.albumentations(ppl_img, labels)
            xpl_img, _ = self.albumentations(xpl_img, labels)
            nl = len(labels)

            # HSV颜色空间增强
            augment_hsv(ppl_img, hgain=hyp["hsv_h"], sgain=hyp["hsv_s"], vgain=hyp["hsv_v"])
            augment_hsv(xpl_img, hgain=hyp["hsv_h"], sgain=hyp["hsv_s"], vgain=hyp["hsv_v"])

            # 上下翻转
            if random.random() < hyp["flipud"]:
                ppl_img = np.flipud(ppl_img)
                xpl_img = np.flipud(xpl_img)
                if nl:
                    labels[:, 2] = 1 - labels[:, 2]
                    masks = torch.flip(masks, dims=[1])

            # 左右翻转
            if random.random() < hyp["fliplr"]:
                ppl_img = np.fliplr(ppl_img)
                xpl_img = np.fliplr(xpl_img)
                if nl:
                    labels[:, 1] = 1 - labels[:, 1]
                    masks = torch.flip(masks, dims=[2])

        labels_out = torch.zeros((nl, 6))
        if nl:
            labels_out[:, 1:] = torch.from_numpy(labels)

        # 转换两个图像格式
        ppl_img = ppl_img.transpose((2, 0, 1))[::-1]  # HWC转CHW，BGR转RGB
        ppl_img = np.ascontiguousarray(ppl_img)
        xpl_img = xpl_img.transpose((2, 0, 1))[::-1]  # HWC转CHW，BGR转RGB
        xpl_img = np.ascontiguousarray(xpl_img)

        return (torch.from_numpy(ppl_img), torch.from_numpy(xpl_img), labels_out, self.im_files[index], shapes, masks)
        
    def load_mosaic(self, index):
        """为双模态图像加载4图像马赛克。"""
        labels4, segments4 = [], []
        s = self.img_size
        yc, xc = (int(random.uniform(-x, 2 * s + x)) for x in self.mosaic_border)

        indices = [index] + random.choices(self.indices, k=3)
        for i, index in enumerate(indices):
            # 加载双模态图像
            (ppl_img, xpl_img), _, (h, w) = self.load_image(index)

            # 在马赛克中放置图像
            if i == 0:  # 左上角
                ppl_img4 = np.full((s * 2, s * 2, ppl_img.shape[2]), 114, dtype=np.uint8)
                xpl_img4 = np.full((s * 2, s * 2, xpl_img.shape[2]), 114, dtype=np.uint8)
                x1a, y1a, x2a, y2a = max(xc - w, 0), max(yc - h, 0), xc, yc
                x1b, y1b, x2b, y2b = w - (x2a - x1a), h - (y2a - y1a), w, h
            elif i == 1:  # 右上角
                x1a, y1a, x2a, y2a = xc, max(yc - h, 0), min(xc + w, s * 2), yc
                x1b, y1b, x2b, y2b = 0, h - (y2a - y1a), min(w, x2a - x1a), h
            elif i == 2:  # 左下角
                x1a, y1a, x2a, y2a = max(xc - w, 0), yc, xc, min(s * 2, yc + h)
                x1b, y1b, x2b, y2b = w - (x2a - x1a), 0, w, min(y2a - y1a, h)
            elif i == 3:  # 右下角
                x1a, y1a, x2a, y2a = xc, yc, min(xc + w, s * 2), min(s * 2, yc + h)
                x1b, y1b, x2b, y2b = 0, 0, min(w, x2a - x1a), min(y2a - y1a, h)

            ppl_img4[y1a:y2a, x1a:x2a] = ppl_img[y1b:y2b, x1b:x2b]
            xpl_img4[y1a:y2a, x1a:x2a] = xpl_img[y1b:y2b, x1b:x2b]
            padw = x1a - x1b
            padh = y1a - y1b

            labels, segments = self.labels[index].copy(), self.segments[index].copy()
            if labels.size:
                labels[:, 1:] = xywhn2xyxy(labels[:, 1:], w, h, padw, padh)
                segments = [xyn2xy(x, w, h, padw, padh) for x in segments]
            labels4.append(labels)
            segments4.extend(segments)

        # 连接/裁剪标签
        labels4 = np.concatenate(labels4, 0)
        for x in (labels4[:, 1:], *segments4):
            np.clip(x, 0, 2 * s, out=x)

        # 数据增强 - 确保双模态图像同步变换
        ppl_img4, labels4, segments4, xpl_img4 = copy_paste(ppl_img4, labels4, segments4, p=self.hyp["copy_paste"], im_xpl=xpl_img4)
        
        # 双模态图像同步透视变换
        ppl_img4, labels4, segments4, xpl_img4 = random_perspective(
            ppl_img4,
            labels4,
            segments4,
            degrees=self.hyp["degrees"],
            translate=self.hyp["translate"],
            scale=self.hyp["scale"],
            shear=self.hyp["shear"],
            perspective=self.hyp["perspective"],
            border=self.mosaic_border,
            im_xpl=xpl_img4,
        )
        return (ppl_img4, xpl_img4), labels4, segments4
        
    @staticmethod
    def collate_fn(batch):
        """双模态DataLoader的自定义整理函数。"""
        ppl_img, xpl_img, label, path, shapes, masks = zip(*batch)
        batched_masks = torch.cat(masks, 0)
        for i, l in enumerate(label):
            l[:, 0] = i
        return torch.stack(ppl_img, 0), torch.stack(xpl_img, 0), torch.cat(label, 0), path, shapes, batched_masks
        
    @staticmethod
    def collate_fn4(batch):
        """双模态DataLoader的四倍批处理自定义整理函数。"""
        ppl_img, xpl_img, label, path, shapes, masks = zip(*batch)
        n = len(shapes) // 4
        ppl_img4, xpl_img4, label4, path4, shapes4 = [], [], [], path[:n], shapes[:n]
        ho = torch.tensor([[0.0, 0, 0, 1, 0, 0]])
        for i in range(n):
            i *= 4
            if random.random() < 0.5:
                im1 = F.interpolate(ppl_img[i].unsqueeze(0).float(), scale_factor=2.0, mode="bilinear", align_corners=False)[
                    0
                ].type(ppl_img[i].type())
                im2 = F.interpolate(xpl_img[i].unsqueeze(0).float(), scale_factor=2.0, mode="bilinear", align_corners=False)[
                    0
                ].type(xpl_img[i].type())
                l = label[i]
            else:
                im1 = torch.cat((torch.cat((ppl_img[i], ppl_img[i + 1]), 1), torch.cat((ppl_img[i + 2], ppl_img[i + 3]), 1)), 2)
                im2 = torch.cat((torch.cat((xpl_img[i], xpl_img[i + 1]), 1), torch.cat((xpl_img[i + 2], xpl_img[i + 3]), 1)), 2)
                l = torch.cat((label[i], label[i + 1] + ho, label[i + 2] + ho, label[i + 3] + ho), 0)
            ppl_img4.append(im1)
            xpl_img4.append(im2)
            label4.append(l)

        for i, l in enumerate(label4):
            l[:, 0] = i
        return torch.stack(ppl_img4, 0), torch.stack(xpl_img4, 0), torch.cat(label4, 0), path4, shapes4, torch.cat(masks, 0)


def create_multimodal_dataloader(
    path,
    imgsz,
    batch_size,
    stride,
    single_cls=False,
    hyp=None,
    augment=False,
    cache=False,
    pad=0.0,
    rect=False,
    rank=-1,
    workers=8,
    image_weights=False,
    quad=False,
    prefix="",
    shuffle=False,
    mask_downsample_ratio=1,
    overlap_mask=False,
    seed=0,
    xpl_path=None,
    multimodal=False,
):
    """创建支持双模态的多模态训练/测试数据加载器。"""
    if rect and shuffle:
        LOGGER.warning("WARNING ⚠️ --rect is incompatible with DataLoader shuffle, setting shuffle=False")
        shuffle = False
        
    with torch_distributed_zero_first(rank):
        if multimodal and xpl_path:
            # 创建双模态数据集
            dataset = LoadDualModalImagesAndMasks(
                path,
                imgsz,
                batch_size,
                augment=augment,
                hyp=hyp,
                rect=rect,
                cache_images=cache,
                single_cls=single_cls,
                stride=int(stride),
                pad=pad,
                image_weights=image_weights,
                prefix=prefix,
                downsample_ratio=mask_downsample_ratio,
                overlap=overlap_mask,
                rank=rank,
                xpl_path=xpl_path,
            )
        else:
            # 创建单模态数据集
            dataset = LoadImagesAndLabelsAndMasks(
                path,
                imgsz,
                batch_size,
                augment=augment,
                hyp=hyp,
                rect=rect,
                cache_images=cache,
                single_cls=single_cls,
                stride=int(stride),
                pad=pad,
                image_weights=image_weights,
                prefix=prefix,
                downsample_ratio=mask_downsample_ratio,
                overlap=overlap_mask,
                rank=rank,
            )

    batch_size = min(batch_size, len(dataset))
    nd = torch.cuda.device_count()
    nw = min([os.cpu_count() // max(nd, 1), batch_size if batch_size > 1 else 0, workers])
    sampler = None if rank == -1 else SmartDistributedSampler(dataset, shuffle=shuffle)
    loader = DataLoader if image_weights else InfiniteDataLoader
    generator = torch.Generator()
    generator.manual_seed(6148914691236517205 + seed + RANK)
    
    # 使用适当的整理函数
    if multimodal and xpl_path:
        collate_fn = LoadDualModalImagesAndMasks.collate_fn4 if quad else LoadDualModalImagesAndMasks.collate_fn
    else:
        collate_fn = LoadImagesAndLabelsAndMasks.collate_fn4 if quad else LoadImagesAndLabelsAndMasks.collate_fn
        
    return loader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle and sampler is None,
        num_workers=nw,
        sampler=sampler,
        drop_last=quad,
        pin_memory=True,
        collate_fn=collate_fn,
        worker_init_fn=seed_worker,
        generator=generator,
    ), dataset
    
class LoadImages:
    """YOLOv5图像数据加载器。
    
    用于加载图像文件，支持单个文件、目录、glob模式和文件列表。
    例如：`python detect.py --source image.jpg`
    """

    def __init__(self, path, img_size=640, stride=32, auto=True, transforms=None, xpl_path=None, multimodal=False):
        """初始化YOLOv5图像加载器。
        
        支持glob模式、目录和路径列表。
        
        Args:
            path: 文件路径、目录路径、glob模式或路径列表
            img_size: 图像尺寸
            stride: 模型步长
            auto: 是否自动调整
            transforms: 图像变换
            xpl_path: XPL图像路径（双模态模式下使用）
            multimodal: 是否启用双模态模式
        """
        if isinstance(path, str) and Path(path).suffix == ".txt":  # txt文件，每行一个图像路径
            path = Path(path).read_text().rsplit()
        files = []  # 文件列表
        for p in sorted(path) if isinstance(path, (list, tuple)) else [path]:
            p = str(Path(p).resolve())  # 解析为绝对路径
            if "*" in p:
                files.extend(sorted(glob.glob(p, recursive=True)))  # glob模式匹配
            elif os.path.isdir(p):
                files.extend(sorted(glob.glob(os.path.join(p, "*.*"))))  # 目录中的所有文件
            elif os.path.isfile(p):
                files.append(p)  # 单个文件
            else:
                raise FileNotFoundError(f"{p} does not exist")  # 路径不存在

        # 只保留图像文件
        self.files = [x for x in files if x.split(".")[-1].lower() in IMG_FORMATS]  # 图像文件
        self.nf = len(self.files)  # 文件总数

        self.img_size = img_size  # 图像尺寸
        self.stride = stride  # 步长
        self.mode = "image"  # 模式
        self.auto = auto  # 自动调整
        self.transforms = transforms  # 变换（可选）
        self.multimodal = multimodal  # 双模态模式
        
        # 处理XPL图像路径（双模态模式）
        self.xpl_files = []
        if multimodal and xpl_path:
            if isinstance(xpl_path, str) and Path(xpl_path).suffix == ".txt":
                xpl_path = Path(xpl_path).read_text().rsplit()
            xpl_files = []
            for p in sorted(xpl_path) if isinstance(xpl_path, (list, tuple)) else [xpl_path]:
                p = str(Path(p).resolve())
                if "*" in p:
                    xpl_files.extend(sorted(glob.glob(p, recursive=True)))
                elif os.path.isdir(p):
                    xpl_files.extend(sorted(glob.glob(os.path.join(p, "*.*"))))
                elif os.path.isfile(p):
                    xpl_files.append(p)
                else:
                    raise FileNotFoundError(f"XPL path {p} does not exist")
            
            self.xpl_files = [x for x in xpl_files if x.split(".")[-1].lower() in IMG_FORMATS]
            
            # 确保XPL图像数量与RGB图像数量匹配
            if len(self.xpl_files) != self.nf:
                raise ValueError(f"XPL images count ({len(self.xpl_files)}) does not match RGB images count ({self.nf})")
        
        assert self.nf > 0, (
            f"No images found in {p}. Supported formats are:\nimages: {IMG_FORMATS}"
        )  # 确保找到了文件

    def __iter__(self):
        """通过重置计数初始化迭代器并返回迭代器对象本身。
        
        Returns:
            self: 返回自身实例
        """
        self.count = 0  # 重置计数
        return self

    def __next__(self):
        """前进到数据集中的下一个文件，如果到达末尾则引发StopIteration。
        
        Returns:
            tuple: 单模态模式: (文件路径, 处理后图像, 原始图像, None, 描述字符串)
                  双模态模式: (ppl_img_tensor, xpl_img_tensor, empty_labels, path, shapes, empty_masks)
            
        Raises:
            StopIteration: 当到达数据集末尾时
        """
        if self.count == self.nf:
            raise StopIteration  # 到达末尾
        path = self.files[self.count]  # 当前文件路径

        # 读取RGB图像
        self.count += 1  # 计数递增
        im0 = cv2.imread(path)  # 读取BGR图像
        assert im0 is not None, f"RGB Image Not Found {path}"  # 确保图像存在
        s = f"image {self.count}/{self.nf} {path}: "  # 图像描述

        # 保存原始图像尺寸
        h0, w0 = im0.shape[:2]  # 原始高度和宽度
        
        if self.transforms:
            im_rgb = self.transforms(im0)  # 应用变换
            # 如果使用自定义变换，假设输出尺寸与输入相同
            r = 1.0  # 缩放比例
            shapes = (h0, w0), ((h0, w0), (r, r))  # 原始尺寸和缩放信息
        else:
            im_rgb, ratio, pad = letterbox(im0, self.img_size, stride=self.stride, auto=self.auto)  # 填充调整大小
            h, w = im_rgb.shape[:2]  # 处理后的高度和宽度
            shapes = (h0, w0), ((h, w), ratio)  # 原始尺寸和缩放信息
            im_rgb = im_rgb.transpose((2, 0, 1))[::-1]  # HWC转CHW，BGR转RGB
            im_rgb = np.ascontiguousarray(im_rgb)  # 确保内存连续

        if self.multimodal and self.xpl_files:
            # 双模态模式：同时加载XPL图像，返回与predict.py期望的格式匹配
            xpl_path = self.xpl_files[self.count - 1]  # 对应的XPL图像路径
            im0_xpl = cv2.imread(xpl_path)  # 读取XPL图像
            assert im0_xpl is not None, f"XPL Image Not Found {xpl_path}"  # 确保XPL图像存在
            
            if self.transforms:
                im_xpl = self.transforms(im0_xpl)  # 应用变换
            else:
                im_xpl, _, _ = letterbox(im0_xpl, self.img_size, stride=self.stride, auto=self.auto)  # 填充调整大小
                im_xpl = im_xpl.transpose((2, 0, 1))[::-1]  # HWC转CHW，BGR转RGB
                im_xpl = np.ascontiguousarray(im_xpl)  # 确保内存连续
            
            # 返回与predict.py期望的格式匹配：(path, ppl_im, xpl_im, im0s, vid_cap, s)
            return path, im_rgb, im_xpl, im0, None, s
        else:
            # 单模态模式：保持原有功能
            return path, im_rgb, im0, None, s  # 返回路径、处理图像、原图、None、描述



    def __len__(self):
        """返回数据集中的文件数量。
        
        Returns:
            int: 文件总数
        """
        return self.nf  # 文件数量