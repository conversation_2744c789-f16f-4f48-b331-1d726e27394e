Image: tile_0057.png
Total detections: 300
==================================================
Detection 1:
  Class: object (ID: 0)
  Confidence: 0.8711
  Bounding Box: [685.60, 817.60, 920.80, 1070.40]
  Mask Area: 244 pixels
  Mask Ratio: 0.0086
  Mask BBox: [58, 68, 75, 87]

Detection 2:
  Class: object (ID: 0)
  Confidence: 0.8623
  Bounding Box: [1146.40, 1020.80, 1380.00, 1318.40]
  Mask Area: 305 pixels
  Mask Ratio: 0.0108
  Mask BBox: [94, 84, 111, 106]

Detection 3:
  Class: object (ID: 0)
  Confidence: 0.8579
  Bounding Box: [1368.00, 313.20, 1566.40, 534.00]
  Mask Area: 178 pixels
  Mask Ratio: 0.0063
  Mask BBox: [111, 29, 126, 45]

Detection 4:
  Class: object (ID: 0)
  Confidence: 0.8481
  Bounding Box: [520.00, 1299.20, 777.60, 1624.00]
  Mask Area: 384 pixels
  Mask Ratio: 0.0136
  Mask BBox: [45, 106, 64, 130]

Detection 5:
  Class: object (ID: 0)
  Confidence: 0.8428
  Bounding Box: [522.40, 1788.80, 764.80, 2048.00]
  Mask Area: 313 pixels
  Mask Ratio: 0.0111
  Mask BBox: [45, 144, 63, 163]

Detection 6:
  Class: object (ID: 0)
  Confidence: 0.8403
  Bounding Box: [1033.60, 206.80, 1182.40, 382.00]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [85, 21, 95, 33]

Detection 7:
  Class: object (ID: 0)
  Confidence: 0.8364
  Bounding Box: [1004.80, 1740.80, 1176.00, 1974.40]
  Mask Area: 176 pixels
  Mask Ratio: 0.0062
  Mask BBox: [83, 140, 95, 158]

Detection 8:
  Class: object (ID: 0)
  Confidence: 0.8354
  Bounding Box: [21.30, 1736.00, 224.80, 1982.40]
  Mask Area: 226 pixels
  Mask Ratio: 0.0080
  Mask BBox: [6, 140, 21, 157]

Detection 9:
  Class: object (ID: 0)
  Confidence: 0.8345
  Bounding Box: [309.40, 1108.00, 556.00, 1407.20]
  Mask Area: 249 pixels
  Mask Ratio: 0.0088
  Mask BBox: [30, 91, 47, 113]

Detection 10:
  Class: object (ID: 0)
  Confidence: 0.8311
  Bounding Box: [1189.60, 1386.40, 1354.40, 1546.40]
  Mask Area: 114 pixels
  Mask Ratio: 0.0040
  Mask BBox: [97, 113, 109, 124]

Detection 11:
  Class: object (ID: 0)
  Confidence: 0.8281
  Bounding Box: [137.40, 1113.60, 325.80, 1392.00]
  Mask Area: 209 pixels
  Mask Ratio: 0.0074
  Mask BBox: [15, 91, 28, 111]

Detection 12:
  Class: object (ID: 0)
  Confidence: 0.8271
  Bounding Box: [830.40, 1390.40, 1064.00, 1779.20]
  Mask Area: 466 pixels
  Mask Ratio: 0.0165
  Mask BBox: [69, 113, 87, 142]

Detection 13:
  Class: object (ID: 0)
  Confidence: 0.8213
  Bounding Box: [1742.40, 1105.60, 1979.20, 1385.60]
  Mask Area: 282 pixels
  Mask Ratio: 0.0100
  Mask BBox: [141, 91, 158, 112]

Detection 14:
  Class: object (ID: 0)
  Confidence: 0.8203
  Bounding Box: [1777.60, 1585.60, 1972.80, 1832.00]
  Mask Area: 229 pixels
  Mask Ratio: 0.0081
  Mask BBox: [143, 128, 158, 147]

Detection 15:
  Class: object (ID: 0)
  Confidence: 0.8188
  Bounding Box: [1509.60, 1580.80, 1796.80, 1891.20]
  Mask Area: 295 pixels
  Mask Ratio: 0.0105
  Mask BBox: [122, 128, 144, 151]

Detection 16:
  Class: object (ID: 0)
  Confidence: 0.8149
  Bounding Box: [220.80, 1768.00, 378.80, 1976.00]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [22, 143, 33, 158]

Detection 17:
  Class: object (ID: 0)
  Confidence: 0.8115
  Bounding Box: [600.80, 1624.00, 769.60, 1796.80]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [51, 131, 63, 143]

Detection 18:
  Class: object (ID: 0)
  Confidence: 0.8086
  Bounding Box: [1529.60, 440.80, 1702.40, 663.20]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [124, 39, 136, 55]

Detection 19:
  Class: object (ID: 0)
  Confidence: 0.7939
  Bounding Box: [998.40, 845.60, 1192.00, 1060.00]
  Mask Area: 161 pixels
  Mask Ratio: 0.0057
  Mask BBox: [82, 71, 97, 86]

Detection 20:
  Class: object (ID: 0)
  Confidence: 0.7935
  Bounding Box: [1076.00, 1539.20, 1320.80, 1776.00]
  Mask Area: 248 pixels
  Mask Ratio: 0.0088
  Mask BBox: [89, 125, 107, 142]

Detection 21:
  Class: object (ID: 0)
  Confidence: 0.7881
  Bounding Box: [1315.20, 680.00, 1563.20, 897.60]
  Mask Area: 257 pixels
  Mask Ratio: 0.0091
  Mask BBox: [107, 58, 126, 73]

Detection 22:
  Class: object (ID: 0)
  Confidence: 0.7837
  Bounding Box: [1838.40, 1825.60, 2046.40, 2036.80]
  Mask Area: 237 pixels
  Mask Ratio: 0.0084
  Mask BBox: [148, 147, 163, 163]

Detection 23:
  Class: object (ID: 0)
  Confidence: 0.7808
  Bounding Box: [506.00, 1048.00, 694.80, 1268.80]
  Mask Area: 122 pixels
  Mask Ratio: 0.0043
  Mask BBox: [44, 86, 58, 103]

Detection 24:
  Class: object (ID: 0)
  Confidence: 0.7754
  Bounding Box: [1624.00, 169.60, 1880.00, 427.60]
  Mask Area: 250 pixels
  Mask Ratio: 0.0089
  Mask BBox: [131, 18, 150, 37]

Detection 25:
  Class: object (ID: 0)
  Confidence: 0.7749
  Bounding Box: [1907.20, 572.00, 2048.00, 798.40]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [153, 49, 164, 66]

Detection 26:
  Class: object (ID: 0)
  Confidence: 0.7734
  Bounding Box: [1648.00, 408.40, 1779.20, 545.20]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [133, 36, 142, 46]

Detection 27:
  Class: object (ID: 0)
  Confidence: 0.7729
  Bounding Box: [669.60, 259.60, 853.60, 425.60]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [57, 25, 70, 37]

Detection 28:
  Class: object (ID: 0)
  Confidence: 0.7676
  Bounding Box: [123.20, 605.60, 232.40, 702.40]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [14, 52, 22, 58]

Detection 29:
  Class: object (ID: 0)
  Confidence: 0.7661
  Bounding Box: [1326.40, 1680.00, 1515.20, 1846.40]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [108, 136, 122, 148]

Detection 30:
  Class: object (ID: 0)
  Confidence: 0.7661
  Bounding Box: [587.60, 971.20, 758.00, 1179.20]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [50, 80, 63, 96]

Detection 31:
  Class: object (ID: 0)
  Confidence: 0.7622
  Bounding Box: [1295.20, 1246.40, 1434.40, 1472.00]
  Mask Area: 104 pixels
  Mask Ratio: 0.0037
  Mask BBox: [106, 102, 116, 118]

Detection 32:
  Class: object (ID: 0)
  Confidence: 0.7554
  Bounding Box: [1185.60, 1788.80, 1275.20, 2048.00]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [97, 144, 103, 162]

Detection 33:
  Class: object (ID: 0)
  Confidence: 0.7544
  Bounding Box: [1702.40, 1830.40, 1843.20, 1993.60]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [137, 147, 147, 158]

Detection 34:
  Class: object (ID: 0)
  Confidence: 0.7515
  Bounding Box: [1554.40, 780.80, 1763.20, 905.60]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [126, 65, 141, 74]

Detection 35:
  Class: object (ID: 0)
  Confidence: 0.7485
  Bounding Box: [1319.20, 25.50, 1583.20, 258.80]
  Mask Area: 235 pixels
  Mask Ratio: 0.0083
  Mask BBox: [108, 6, 126, 23]

Detection 36:
  Class: object (ID: 0)
  Confidence: 0.7461
  Bounding Box: [1063.20, 1310.40, 1244.00, 1528.00]
  Mask Area: 199 pixels
  Mask Ratio: 0.0071
  Mask BBox: [88, 107, 101, 123]

Detection 37:
  Class: object (ID: 0)
  Confidence: 0.7441
  Bounding Box: [400.00, 838.40, 599.20, 1088.00]
  Mask Area: 198 pixels
  Mask Ratio: 0.0070
  Mask BBox: [36, 70, 50, 88]

Detection 38:
  Class: object (ID: 0)
  Confidence: 0.7437
  Bounding Box: [1552.80, 316.60, 1676.80, 441.60]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [126, 29, 134, 38]

Detection 39:
  Class: object (ID: 0)
  Confidence: 0.7305
  Bounding Box: [358.80, 1553.60, 600.40, 1851.20]
  Mask Area: 258 pixels
  Mask Ratio: 0.0091
  Mask BBox: [33, 126, 50, 148]

Detection 40:
  Class: object (ID: 0)
  Confidence: 0.7251
  Bounding Box: [859.20, 1908.80, 1033.60, 2046.40]
  Mask Area: 101 pixels
  Mask Ratio: 0.0036
  Mask BBox: [72, 155, 84, 163]

Detection 41:
  Class: object (ID: 0)
  Confidence: 0.7173
  Bounding Box: [788.80, 1820.80, 905.60, 1955.20]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [66, 147, 74, 156]

Detection 42:
  Class: object (ID: 0)
  Confidence: 0.7124
  Bounding Box: [1596.80, 11.20, 1756.80, 180.60]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [129, 5, 141, 18]

Detection 43:
  Class: object (ID: 0)
  Confidence: 0.7119
  Bounding Box: [1581.60, 1256.00, 1761.60, 1459.20]
  Mask Area: 169 pixels
  Mask Ratio: 0.0060
  Mask BBox: [128, 103, 141, 117]

Detection 44:
  Class: object (ID: 0)
  Confidence: 0.7070
  Bounding Box: [1580.80, 876.80, 1884.80, 1192.00]
  Mask Area: 416 pixels
  Mask Ratio: 0.0147
  Mask BBox: [128, 73, 151, 97]

Detection 45:
  Class: object (ID: 0)
  Confidence: 0.6948
  Bounding Box: [1659.20, 596.40, 1892.80, 783.20]
  Mask Area: 183 pixels
  Mask Ratio: 0.0065
  Mask BBox: [134, 51, 151, 63]

Detection 46:
  Class: object (ID: 0)
  Confidence: 0.6924
  Bounding Box: [1303.20, 968.00, 1404.00, 1121.60]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [106, 80, 113, 90]

Detection 47:
  Class: object (ID: 0)
  Confidence: 0.6919
  Bounding Box: [1447.20, 1816.00, 1606.40, 1995.20]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [118, 146, 129, 158]

Detection 48:
  Class: object (ID: 0)
  Confidence: 0.6914
  Bounding Box: [670.80, 1168.80, 840.80, 1327.20]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [57, 96, 69, 107]

Detection 49:
  Class: object (ID: 0)
  Confidence: 0.6899
  Bounding Box: [8.90, 228.80, 83.00, 375.20]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [5, 22, 9, 33]

Detection 50:
  Class: object (ID: 0)
  Confidence: 0.6826
  Bounding Box: [0.00, 83.00, 146.80, 279.00]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [4, 11, 14, 23]

Detection 51:
  Class: object (ID: 0)
  Confidence: 0.6816
  Bounding Box: [764.00, 1385.60, 861.60, 1576.00]
  Mask Area: 94 pixels
  Mask Ratio: 0.0033
  Mask BBox: [64, 113, 71, 127]

Detection 52:
  Class: object (ID: 0)
  Confidence: 0.6782
  Bounding Box: [1382.40, 1088.00, 1654.40, 1296.00]
  Mask Area: 219 pixels
  Mask Ratio: 0.0078
  Mask BBox: [112, 89, 133, 105]

Detection 53:
  Class: object (ID: 0)
  Confidence: 0.6772
  Bounding Box: [229.60, 775.20, 434.80, 1015.20]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [22, 65, 37, 83]

Detection 54:
  Class: object (ID: 0)
  Confidence: 0.6753
  Bounding Box: [1816.00, 1424.00, 2004.80, 1676.80]
  Mask Area: 206 pixels
  Mask Ratio: 0.0073
  Mask BBox: [146, 116, 160, 134]

Detection 55:
  Class: object (ID: 0)
  Confidence: 0.6704
  Bounding Box: [1705.60, 1452.00, 1830.40, 1564.00]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [138, 118, 145, 126]

Detection 56:
  Class: object (ID: 0)
  Confidence: 0.6665
  Bounding Box: [1944.00, 1186.40, 2043.20, 1380.00]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [156, 98, 163, 111]

Detection 57:
  Class: object (ID: 0)
  Confidence: 0.6660
  Bounding Box: [1544.80, 1948.80, 1681.60, 2035.20]
  Mask Area: 51 pixels
  Mask Ratio: 0.0018
  Mask BBox: [125, 157, 135, 162]

Detection 58:
  Class: object (ID: 0)
  Confidence: 0.6646
  Bounding Box: [1880.00, 190.20, 2040.00, 501.60]
  Mask Area: 257 pixels
  Mask Ratio: 0.0091
  Mask BBox: [151, 19, 163, 43]

Detection 59:
  Class: object (ID: 0)
  Confidence: 0.6597
  Bounding Box: [868.00, 945.60, 1034.40, 1148.80]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [72, 78, 84, 93]

Detection 60:
  Class: object (ID: 0)
  Confidence: 0.6577
  Bounding Box: [224.00, 629.20, 333.60, 728.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [22, 54, 28, 60]

Detection 61:
  Class: object (ID: 0)
  Confidence: 0.6572
  Bounding Box: [1429.60, 250.00, 1520.80, 318.80]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [116, 24, 122, 28]

Detection 62:
  Class: object (ID: 0)
  Confidence: 0.6562
  Bounding Box: [208.60, 1950.40, 297.80, 2033.60]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [21, 157, 27, 162]

Detection 63:
  Class: object (ID: 0)
  Confidence: 0.6562
  Bounding Box: [1444.00, 910.40, 1560.80, 1048.00]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [117, 76, 125, 84]

Detection 64:
  Class: object (ID: 0)
  Confidence: 0.6528
  Bounding Box: [1172.00, 0.70, 1328.80, 267.60]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [96, 5, 107, 23]

Detection 65:
  Class: object (ID: 0)
  Confidence: 0.6475
  Bounding Box: [1064.00, 471.60, 1374.40, 832.00]
  Mask Area: 496 pixels
  Mask Ratio: 0.0176
  Mask BBox: [88, 41, 111, 68]

Detection 66:
  Class: object (ID: 0)
  Confidence: 0.6445
  Bounding Box: [668.80, 360.80, 905.60, 648.00]
  Mask Area: 261 pixels
  Mask Ratio: 0.0092
  Mask BBox: [57, 33, 74, 54]

Detection 67:
  Class: object (ID: 0)
  Confidence: 0.6396
  Bounding Box: [1013.60, 1204.80, 1130.40, 1318.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [84, 99, 92, 106]

Detection 68:
  Class: object (ID: 0)
  Confidence: 0.6392
  Bounding Box: [289.40, 1952.00, 480.00, 2032.00]
  Mask Area: 76 pixels
  Mask Ratio: 0.0027
  Mask BBox: [27, 157, 41, 162]

Detection 69:
  Class: object (ID: 0)
  Confidence: 0.6313
  Bounding Box: [1529.60, 137.60, 1633.60, 321.20]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [124, 15, 131, 29]

Detection 70:
  Class: object (ID: 0)
  Confidence: 0.6260
  Bounding Box: [950.40, 59.40, 1179.20, 241.40]
  Mask Area: 156 pixels
  Mask Ratio: 0.0055
  Mask BBox: [79, 9, 96, 22]

Detection 71:
  Class: object (ID: 0)
  Confidence: 0.6235
  Bounding Box: [1264.00, 1761.60, 1409.60, 1992.00]
  Mask Area: 129 pixels
  Mask Ratio: 0.0046
  Mask BBox: [103, 142, 114, 159]

Detection 72:
  Class: object (ID: 0)
  Confidence: 0.6230
  Bounding Box: [1654.40, 1561.60, 1792.00, 1667.20]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [134, 126, 143, 134]

Detection 73:
  Class: object (ID: 0)
  Confidence: 0.6211
  Bounding Box: [1439.20, 1282.40, 1595.20, 1397.60]
  Mask Area: 57 pixels
  Mask Ratio: 0.0020
  Mask BBox: [117, 105, 128, 113]

Detection 74:
  Class: object (ID: 0)
  Confidence: 0.6211
  Bounding Box: [1876.80, 1366.40, 2030.40, 1465.60]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [151, 111, 162, 117]

Detection 75:
  Class: object (ID: 0)
  Confidence: 0.6172
  Bounding Box: [551.20, 527.20, 702.40, 764.00]
  Mask Area: 142 pixels
  Mask Ratio: 0.0050
  Mask BBox: [48, 46, 58, 63]

Detection 76:
  Class: object (ID: 0)
  Confidence: 0.6099
  Bounding Box: [33.45, 276.80, 199.40, 512.80]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [7, 27, 19, 44]

Detection 77:
  Class: object (ID: 0)
  Confidence: 0.6079
  Bounding Box: [443.20, 1955.20, 532.00, 2025.60]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [39, 157, 44, 162]

Detection 78:
  Class: object (ID: 0)
  Confidence: 0.5986
  Bounding Box: [1716.80, 3.55, 1860.80, 144.00]
  Mask Area: 97 pixels
  Mask Ratio: 0.0034
  Mask BBox: [139, 5, 149, 15]

Detection 79:
  Class: object (ID: 0)
  Confidence: 0.5962
  Bounding Box: [939.20, 6.70, 1160.00, 119.30]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [78, 5, 94, 13]

Detection 80:
  Class: object (ID: 0)
  Confidence: 0.5962
  Bounding Box: [0.00, 0.95, 115.80, 109.00]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [4, 5, 12, 12]

Detection 81:
  Class: object (ID: 0)
  Confidence: 0.5952
  Bounding Box: [1592.00, 1838.40, 1736.00, 2014.40]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [129, 148, 139, 161]

Detection 82:
  Class: object (ID: 0)
  Confidence: 0.5947
  Bounding Box: [1324.80, 1488.00, 1520.00, 1611.20]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [108, 121, 122, 129]

Detection 83:
  Class: object (ID: 0)
  Confidence: 0.5913
  Bounding Box: [1428.80, 1588.80, 1521.60, 1665.60]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [116, 129, 122, 134]

Detection 84:
  Class: object (ID: 0)
  Confidence: 0.5908
  Bounding Box: [53.90, 978.40, 222.40, 1186.40]
  Mask Area: 130 pixels
  Mask Ratio: 0.0046
  Mask BBox: [9, 81, 21, 96]

Detection 85:
  Class: object (ID: 0)
  Confidence: 0.5884
  Bounding Box: [303.40, 567.60, 503.20, 785.60]
  Mask Area: 185 pixels
  Mask Ratio: 0.0066
  Mask BBox: [28, 49, 43, 65]

Detection 86:
  Class: object (ID: 0)
  Confidence: 0.5825
  Bounding Box: [1371.20, 577.60, 1502.40, 660.00]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [112, 50, 119, 55]

Detection 87:
  Class: object (ID: 0)
  Confidence: 0.5815
  Bounding Box: [555.60, 488.00, 675.60, 582.40]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [50, 43, 56, 47]

Detection 88:
  Class: object (ID: 0)
  Confidence: 0.5815
  Bounding Box: [1396.80, 1812.80, 1502.40, 2004.80]
  Mask Area: 80 pixels
  Mask Ratio: 0.0028
  Mask BBox: [114, 146, 121, 160]

Detection 89:
  Class: object (ID: 0)
  Confidence: 0.5752
  Bounding Box: [709.20, 500.00, 1090.40, 916.00]
  Mask Area: 565 pixels
  Mask Ratio: 0.0200
  Mask BBox: [60, 44, 89, 75]

Detection 90:
  Class: object (ID: 0)
  Confidence: 0.5718
  Bounding Box: [1524.80, 1405.60, 1691.20, 1535.20]
  Mask Area: 82 pixels
  Mask Ratio: 0.0029
  Mask BBox: [124, 114, 136, 123]

Detection 91:
  Class: object (ID: 0)
  Confidence: 0.5718
  Bounding Box: [404.40, 416.80, 492.40, 560.00]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [36, 37, 41, 47]

Detection 92:
  Class: object (ID: 0)
  Confidence: 0.5703
  Bounding Box: [460.40, 1507.20, 538.80, 1587.20]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [40, 122, 46, 127]

Detection 93:
  Class: object (ID: 0)
  Confidence: 0.5645
  Bounding Box: [798.40, 8.40, 899.20, 91.40]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [67, 5, 74, 11]

Detection 94:
  Class: object (ID: 0)
  Confidence: 0.5640
  Bounding Box: [1576.00, 924.00, 1675.20, 1028.00]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [128, 77, 133, 83]

Detection 95:
  Class: object (ID: 0)
  Confidence: 0.5610
  Bounding Box: [23.40, 1761.60, 353.80, 1963.20]
  Mask Area: 349 pixels
  Mask Ratio: 0.0124
  Mask BBox: [6, 142, 31, 157]

Detection 96:
  Class: object (ID: 0)
  Confidence: 0.5596
  Bounding Box: [90.50, 772.80, 339.20, 1110.40]
  Mask Area: 362 pixels
  Mask Ratio: 0.0128
  Mask BBox: [12, 65, 30, 90]

Detection 97:
  Class: object (ID: 0)
  Confidence: 0.5576
  Bounding Box: [50.60, 1641.60, 210.20, 1737.60]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [8, 133, 19, 139]

Detection 98:
  Class: object (ID: 0)
  Confidence: 0.5464
  Bounding Box: [1076.80, 1343.20, 1324.80, 1536.80]
  Mask Area: 238 pixels
  Mask Ratio: 0.0084
  Mask BBox: [89, 109, 107, 124]

Detection 99:
  Class: object (ID: 0)
  Confidence: 0.5454
  Bounding Box: [1952.00, 1069.60, 2038.40, 1191.20]
  Mask Area: 58 pixels
  Mask Ratio: 0.0021
  Mask BBox: [157, 88, 163, 97]

Detection 100:
  Class: object (ID: 0)
  Confidence: 0.5361
  Bounding Box: [104.90, 354.60, 335.60, 620.00]
  Mask Area: 248 pixels
  Mask Ratio: 0.0088
  Mask BBox: [13, 32, 30, 52]

Detection 101:
  Class: object (ID: 0)
  Confidence: 0.5322
  Bounding Box: [1624.00, 1488.80, 1764.80, 1646.40]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [131, 121, 141, 132]

Detection 102:
  Class: object (ID: 0)
  Confidence: 0.5234
  Bounding Box: [968.00, 18.85, 1121.60, 126.00]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [80, 6, 91, 13]

Detection 103:
  Class: object (ID: 0)
  Confidence: 0.5225
  Bounding Box: [934.40, 593.60, 1080.00, 729.60]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [77, 51, 88, 60]

Detection 104:
  Class: object (ID: 0)
  Confidence: 0.5190
  Bounding Box: [829.60, 1171.20, 1016.80, 1440.00]
  Mask Area: 262 pixels
  Mask Ratio: 0.0093
  Mask BBox: [69, 96, 83, 116]

Detection 105:
  Class: object (ID: 0)
  Confidence: 0.5176
  Bounding Box: [40.80, 1379.20, 351.20, 1696.00]
  Mask Area: 425 pixels
  Mask Ratio: 0.0151
  Mask BBox: [8, 112, 31, 136]

Detection 106:
  Class: object (ID: 0)
  Confidence: 0.5161
  Bounding Box: [1841.60, 961.60, 2011.20, 1102.40]
  Mask Area: 105 pixels
  Mask Ratio: 0.0037
  Mask BBox: [148, 80, 161, 90]

Detection 107:
  Class: object (ID: 0)
  Confidence: 0.5122
  Bounding Box: [811.20, 53.00, 1070.40, 508.00]
  Mask Area: 446 pixels
  Mask Ratio: 0.0158
  Mask BBox: [68, 9, 87, 43]

Detection 108:
  Class: object (ID: 0)
  Confidence: 0.5024
  Bounding Box: [11.10, 1918.40, 201.20, 2048.00]
  Mask Area: 123 pixels
  Mask Ratio: 0.0044
  Mask BBox: [5, 154, 19, 164]

Detection 109:
  Class: object (ID: 0)
  Confidence: 0.5010
  Bounding Box: [1932.80, 861.60, 2048.00, 1037.60]
  Mask Area: 128 pixels
  Mask Ratio: 0.0045
  Mask BBox: [155, 72, 164, 85]

Detection 110:
  Class: object (ID: 0)
  Confidence: 0.5000
  Bounding Box: [557.20, 378.00, 706.00, 493.20]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [48, 34, 59, 42]

Detection 111:
  Class: object (ID: 0)
  Confidence: 0.5000
  Bounding Box: [486.40, 729.60, 590.40, 822.40]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [42, 61, 49, 67]

Detection 112:
  Class: object (ID: 0)
  Confidence: 0.4966
  Bounding Box: [1598.40, 934.40, 1825.60, 1203.20]
  Mask Area: 281 pixels
  Mask Ratio: 0.0100
  Mask BBox: [129, 77, 146, 97]

Detection 113:
  Class: object (ID: 0)
  Confidence: 0.4966
  Bounding Box: [365.20, 1715.20, 562.00, 1961.60]
  Mask Area: 147 pixels
  Mask Ratio: 0.0052
  Mask BBox: [33, 138, 47, 156]

Detection 114:
  Class: object (ID: 0)
  Confidence: 0.4956
  Bounding Box: [308.80, 364.00, 419.20, 448.80]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [29, 33, 36, 39]

Detection 115:
  Class: object (ID: 0)
  Confidence: 0.4954
  Bounding Box: [182.20, 36.50, 612.80, 398.00]
  Mask Area: 853 pixels
  Mask Ratio: 0.0302
  Mask BBox: [19, 7, 51, 35]

Detection 116:
  Class: object (ID: 0)
  Confidence: 0.4917
  Bounding Box: [759.60, 1923.20, 876.00, 2035.20]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [64, 155, 71, 162]

Detection 117:
  Class: object (ID: 0)
  Confidence: 0.4873
  Bounding Box: [44.25, 1200.00, 133.20, 1321.60]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [8, 98, 14, 107]

Detection 118:
  Class: object (ID: 0)
  Confidence: 0.4814
  Bounding Box: [872.80, 342.80, 951.20, 459.60]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [73, 31, 78, 38]

Detection 119:
  Class: object (ID: 0)
  Confidence: 0.4810
  Bounding Box: [499.60, 1164.80, 653.20, 1350.40]
  Mask Area: 85 pixels
  Mask Ratio: 0.0030
  Mask BBox: [44, 96, 55, 109]

Detection 120:
  Class: object (ID: 0)
  Confidence: 0.4805
  Bounding Box: [1654.40, 1464.80, 1827.20, 1604.80]
  Mask Area: 107 pixels
  Mask Ratio: 0.0038
  Mask BBox: [134, 119, 146, 129]

Detection 121:
  Class: object (ID: 0)
  Confidence: 0.4746
  Bounding Box: [2019.20, 1414.40, 2048.00, 1512.00]
  Mask Area: 16 pixels
  Mask Ratio: 0.0006
  Mask BBox: [162, 115, 164, 121]

Detection 122:
  Class: object (ID: 0)
  Confidence: 0.4722
  Bounding Box: [1352.00, 872.00, 1500.80, 982.40]
  Mask Area: 79 pixels
  Mask Ratio: 0.0028
  Mask BBox: [110, 73, 121, 80]

Detection 123:
  Class: object (ID: 0)
  Confidence: 0.4688
  Bounding Box: [1106.40, 1972.80, 1194.40, 2043.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [91, 159, 97, 163]

Detection 124:
  Class: object (ID: 0)
  Confidence: 0.4678
  Bounding Box: [26.70, 288.20, 336.80, 572.00]
  Mask Area: 373 pixels
  Mask Ratio: 0.0132
  Mask BBox: [7, 27, 30, 48]

Detection 125:
  Class: object (ID: 0)
  Confidence: 0.4658
  Bounding Box: [1772.80, 147.00, 1952.00, 304.20]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [143, 16, 156, 27]

Detection 126:
  Class: object (ID: 0)
  Confidence: 0.4658
  Bounding Box: [892.80, 1788.80, 1017.60, 1939.20]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [74, 144, 83, 155]

Detection 127:
  Class: object (ID: 0)
  Confidence: 0.4644
  Bounding Box: [1554.40, 1552.00, 1651.20, 1648.00]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [126, 126, 132, 131]

Detection 128:
  Class: object (ID: 0)
  Confidence: 0.4619
  Bounding Box: [404.80, 1787.20, 558.40, 1966.40]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [36, 147, 45, 156]

Detection 129:
  Class: object (ID: 0)
  Confidence: 0.4595
  Bounding Box: [1025.60, 1955.20, 1147.20, 2038.40]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [85, 157, 93, 163]

Detection 130:
  Class: object (ID: 0)
  Confidence: 0.4570
  Bounding Box: [230.40, 1280.80, 354.00, 1410.40]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [22, 105, 31, 114]

Detection 131:
  Class: object (ID: 0)
  Confidence: 0.4570
  Bounding Box: [0.00, 1308.80, 97.70, 1489.60]
  Mask Area: 84 pixels
  Mask Ratio: 0.0030
  Mask BBox: [4, 107, 11, 120]

Detection 132:
  Class: object (ID: 0)
  Confidence: 0.4556
  Bounding Box: [1443.20, 1055.20, 1561.60, 1162.40]
  Mask Area: 39 pixels
  Mask Ratio: 0.0014
  Mask BBox: [118, 87, 125, 94]

Detection 133:
  Class: object (ID: 0)
  Confidence: 0.4548
  Bounding Box: [144.10, 458.80, 328.40, 608.40]
  Mask Area: 98 pixels
  Mask Ratio: 0.0035
  Mask BBox: [16, 40, 27, 51]

Detection 134:
  Class: object (ID: 0)
  Confidence: 0.4524
  Bounding Box: [638.80, 250.40, 868.80, 474.40]
  Mask Area: 215 pixels
  Mask Ratio: 0.0076
  Mask BBox: [54, 24, 71, 41]

Detection 135:
  Class: object (ID: 0)
  Confidence: 0.4504
  Bounding Box: [1300.80, 1578.40, 1392.00, 1692.80]
  Mask Area: 46 pixels
  Mask Ratio: 0.0016
  Mask BBox: [106, 128, 112, 136]

Detection 136:
  Class: object (ID: 0)
  Confidence: 0.4495
  Bounding Box: [1616.00, 1800.00, 1734.40, 1931.20]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [131, 145, 139, 154]

Detection 137:
  Class: object (ID: 0)
  Confidence: 0.4451
  Bounding Box: [807.20, 1083.20, 885.60, 1168.00]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [68, 89, 73, 95]

Detection 138:
  Class: object (ID: 0)
  Confidence: 0.4446
  Bounding Box: [1192.00, 927.20, 1308.80, 1055.20]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [98, 77, 106, 86]

Detection 139:
  Class: object (ID: 0)
  Confidence: 0.4426
  Bounding Box: [1564.00, 342.80, 1696.00, 453.20]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [127, 31, 136, 39]

Detection 140:
  Class: object (ID: 0)
  Confidence: 0.4373
  Bounding Box: [1152.00, 217.80, 1252.80, 334.60]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [94, 22, 101, 30]

Detection 141:
  Class: object (ID: 0)
  Confidence: 0.4348
  Bounding Box: [954.40, 4.60, 1165.60, 202.60]
  Mask Area: 206 pixels
  Mask Ratio: 0.0073
  Mask BBox: [79, 5, 95, 19]

Detection 142:
  Class: object (ID: 0)
  Confidence: 0.4321
  Bounding Box: [25.20, 1366.40, 292.40, 1625.60]
  Mask Area: 259 pixels
  Mask Ratio: 0.0092
  Mask BBox: [7, 111, 26, 130]

Detection 143:
  Class: object (ID: 0)
  Confidence: 0.4268
  Bounding Box: [1963.20, 1724.80, 2033.60, 1836.80]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [158, 139, 162, 147]

Detection 144:
  Class: object (ID: 0)
  Confidence: 0.4258
  Bounding Box: [0.00, 482.40, 150.40, 801.60]
  Mask Area: 192 pixels
  Mask Ratio: 0.0068
  Mask BBox: [4, 42, 15, 66]

Detection 145:
  Class: object (ID: 0)
  Confidence: 0.4243
  Bounding Box: [0.00, 14.90, 103.20, 156.80]
  Mask Area: 73 pixels
  Mask Ratio: 0.0026
  Mask BBox: [4, 6, 12, 16]

Detection 146:
  Class: object (ID: 0)
  Confidence: 0.4197
  Bounding Box: [1704.00, 1820.80, 1985.60, 2006.40]
  Mask Area: 246 pixels
  Mask Ratio: 0.0087
  Mask BBox: [138, 147, 159, 160]

Detection 147:
  Class: object (ID: 0)
  Confidence: 0.4177
  Bounding Box: [879.20, 1147.20, 1058.40, 1409.60]
  Mask Area: 209 pixels
  Mask Ratio: 0.0074
  Mask BBox: [73, 94, 86, 114]

Detection 148:
  Class: object (ID: 0)
  Confidence: 0.4148
  Bounding Box: [1488.00, 581.20, 1614.40, 780.00]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [121, 50, 130, 63]

Detection 149:
  Class: object (ID: 0)
  Confidence: 0.4143
  Bounding Box: [1284.00, 1243.20, 1536.80, 1457.60]
  Mask Area: 171 pixels
  Mask Ratio: 0.0061
  Mask BBox: [105, 102, 124, 117]

Detection 150:
  Class: object (ID: 0)
  Confidence: 0.4121
  Bounding Box: [1170.40, 292.80, 1428.00, 562.40]
  Mask Area: 288 pixels
  Mask Ratio: 0.0102
  Mask BBox: [96, 27, 115, 47]

Detection 151:
  Class: object (ID: 0)
  Confidence: 0.4102
  Bounding Box: [333.80, 1611.20, 584.80, 1905.60]
  Mask Area: 301 pixels
  Mask Ratio: 0.0107
  Mask BBox: [31, 130, 49, 152]

Detection 152:
  Class: object (ID: 0)
  Confidence: 0.4070
  Bounding Box: [1104.80, 702.00, 1324.00, 910.40]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [91, 59, 107, 75]

Detection 153:
  Class: object (ID: 0)
  Confidence: 0.4058
  Bounding Box: [1856.00, 1352.00, 2022.40, 1499.20]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [149, 110, 161, 121]

Detection 154:
  Class: object (ID: 0)
  Confidence: 0.4050
  Bounding Box: [232.80, 800.80, 543.20, 1036.00]
  Mask Area: 331 pixels
  Mask Ratio: 0.0117
  Mask BBox: [23, 67, 46, 84]

Detection 155:
  Class: object (ID: 0)
  Confidence: 0.4026
  Bounding Box: [564.00, 1604.80, 617.60, 1681.60]
  Mask Area: 14 pixels
  Mask Ratio: 0.0005
  Mask BBox: [49, 130, 52, 134]

Detection 156:
  Class: object (ID: 0)
  Confidence: 0.4004
  Bounding Box: [1756.80, 1976.00, 1888.00, 2036.80]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [142, 159, 151, 163]

Detection 157:
  Class: object (ID: 0)
  Confidence: 0.4001
  Bounding Box: [1646.40, 353.20, 1809.60, 545.20]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [133, 32, 145, 46]

Detection 158:
  Class: object (ID: 0)
  Confidence: 0.3999
  Bounding Box: [871.20, 1795.20, 944.80, 1884.80]
  Mask Area: 33 pixels
  Mask Ratio: 0.0012
  Mask BBox: [73, 145, 77, 151]

Detection 159:
  Class: object (ID: 0)
  Confidence: 0.3989
  Bounding Box: [599.60, 926.40, 803.20, 1155.20]
  Mask Area: 182 pixels
  Mask Ratio: 0.0064
  Mask BBox: [51, 77, 66, 94]

Detection 160:
  Class: object (ID: 0)
  Confidence: 0.3979
  Bounding Box: [0.00, 1573.60, 72.30, 1771.20]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [4, 127, 8, 142]

Detection 161:
  Class: object (ID: 0)
  Confidence: 0.3979
  Bounding Box: [36.00, 1590.40, 205.60, 1756.80]
  Mask Area: 124 pixels
  Mask Ratio: 0.0044
  Mask BBox: [7, 129, 20, 141]

Detection 162:
  Class: object (ID: 0)
  Confidence: 0.3975
  Bounding Box: [1785.60, 1985.60, 1913.60, 2043.20]
  Mask Area: 29 pixels
  Mask Ratio: 0.0010
  Mask BBox: [144, 160, 151, 163]

Detection 163:
  Class: object (ID: 0)
  Confidence: 0.3938
  Bounding Box: [1203.20, 1793.60, 1302.40, 2033.60]
  Mask Area: 102 pixels
  Mask Ratio: 0.0036
  Mask BBox: [98, 145, 105, 162]

Detection 164:
  Class: object (ID: 0)
  Confidence: 0.3928
  Bounding Box: [1873.60, 546.00, 1976.00, 644.40]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [151, 47, 158, 54]

Detection 165:
  Class: object (ID: 0)
  Confidence: 0.3909
  Bounding Box: [1536.00, 1303.20, 1750.40, 1524.00]
  Mask Area: 235 pixels
  Mask Ratio: 0.0083
  Mask BBox: [124, 106, 140, 123]

Detection 166:
  Class: object (ID: 0)
  Confidence: 0.3904
  Bounding Box: [1601.60, 652.00, 1697.60, 776.00]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [130, 55, 136, 64]

Detection 167:
  Class: object (ID: 0)
  Confidence: 0.3904
  Bounding Box: [1419.20, 1586.40, 1518.40, 1702.40]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [115, 128, 122, 136]

Detection 168:
  Class: object (ID: 0)
  Confidence: 0.3896
  Bounding Box: [1163.20, 1400.00, 1323.20, 1564.80]
  Mask Area: 117 pixels
  Mask Ratio: 0.0041
  Mask BBox: [95, 114, 107, 126]

Detection 169:
  Class: object (ID: 0)
  Confidence: 0.3884
  Bounding Box: [878.40, 424.00, 1075.20, 639.20]
  Mask Area: 133 pixels
  Mask Ratio: 0.0047
  Mask BBox: [73, 38, 87, 53]

Detection 170:
  Class: object (ID: 0)
  Confidence: 0.3884
  Bounding Box: [496.40, 758.40, 712.40, 969.60]
  Mask Area: 193 pixels
  Mask Ratio: 0.0068
  Mask BBox: [43, 64, 59, 79]

Detection 171:
  Class: object (ID: 0)
  Confidence: 0.3831
  Bounding Box: [521.60, 1223.20, 672.00, 1352.80]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [45, 100, 56, 109]

Detection 172:
  Class: object (ID: 0)
  Confidence: 0.3826
  Bounding Box: [1177.60, 933.60, 1280.00, 1032.80]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [96, 77, 103, 84]

Detection 173:
  Class: object (ID: 0)
  Confidence: 0.3752
  Bounding Box: [729.60, 1785.60, 817.60, 1849.60]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [61, 144, 67, 148]

Detection 174:
  Class: object (ID: 0)
  Confidence: 0.3733
  Bounding Box: [1888.00, 852.00, 2044.80, 1056.80]
  Mask Area: 158 pixels
  Mask Ratio: 0.0056
  Mask BBox: [152, 71, 163, 86]

Detection 175:
  Class: object (ID: 0)
  Confidence: 0.3730
  Bounding Box: [1414.40, 1651.20, 1513.60, 1766.40]
  Mask Area: 40 pixels
  Mask Ratio: 0.0014
  Mask BBox: [115, 133, 121, 141]

Detection 176:
  Class: object (ID: 0)
  Confidence: 0.3674
  Bounding Box: [1317.60, 1697.60, 1511.20, 1931.20]
  Mask Area: 229 pixels
  Mask Ratio: 0.0081
  Mask BBox: [107, 137, 122, 154]

Detection 177:
  Class: object (ID: 0)
  Confidence: 0.3672
  Bounding Box: [996.00, 131.40, 1176.80, 354.60]
  Mask Area: 175 pixels
  Mask Ratio: 0.0062
  Mask BBox: [82, 15, 95, 31]

Detection 178:
  Class: object (ID: 0)
  Confidence: 0.3608
  Bounding Box: [691.60, 301.80, 905.60, 582.40]
  Mask Area: 253 pixels
  Mask Ratio: 0.0090
  Mask BBox: [59, 28, 74, 49]

Detection 179:
  Class: object (ID: 0)
  Confidence: 0.3584
  Bounding Box: [1025.60, 1067.20, 1155.20, 1176.00]
  Mask Area: 70 pixels
  Mask Ratio: 0.0025
  Mask BBox: [85, 88, 93, 95]

Detection 180:
  Class: object (ID: 0)
  Confidence: 0.3557
  Bounding Box: [1700.80, 534.40, 1819.20, 604.00]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [137, 46, 146, 51]

Detection 181:
  Class: object (ID: 0)
  Confidence: 0.3523
  Bounding Box: [1676.80, 406.80, 1827.20, 540.40]
  Mask Area: 90 pixels
  Mask Ratio: 0.0032
  Mask BBox: [135, 36, 146, 46]

Detection 182:
  Class: object (ID: 0)
  Confidence: 0.3518
  Bounding Box: [0.00, 240.80, 106.80, 406.80]
  Mask Area: 69 pixels
  Mask Ratio: 0.0024
  Mask BBox: [4, 23, 12, 35]

Detection 183:
  Class: object (ID: 0)
  Confidence: 0.3508
  Bounding Box: [1467.20, 1305.60, 1582.40, 1396.80]
  Mask Area: 52 pixels
  Mask Ratio: 0.0018
  Mask BBox: [119, 106, 127, 113]

Detection 184:
  Class: object (ID: 0)
  Confidence: 0.3479
  Bounding Box: [1608.00, 0.00, 1825.60, 178.40]
  Mask Area: 222 pixels
  Mask Ratio: 0.0079
  Mask BBox: [130, 3, 146, 17]

Detection 185:
  Class: object (ID: 0)
  Confidence: 0.3455
  Bounding Box: [246.20, 1275.20, 407.20, 1417.60]
  Mask Area: 91 pixels
  Mask Ratio: 0.0032
  Mask BBox: [24, 104, 35, 114]

Detection 186:
  Class: object (ID: 0)
  Confidence: 0.3452
  Bounding Box: [785.60, 454.40, 1081.60, 798.40]
  Mask Area: 436 pixels
  Mask Ratio: 0.0154
  Mask BBox: [66, 40, 88, 66]

Detection 187:
  Class: object (ID: 0)
  Confidence: 0.3442
  Bounding Box: [1744.00, 1742.40, 1824.00, 1835.20]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [141, 141, 146, 147]

Detection 188:
  Class: object (ID: 0)
  Confidence: 0.3435
  Bounding Box: [48.80, 1616.00, 206.80, 1811.20]
  Mask Area: 115 pixels
  Mask Ratio: 0.0041
  Mask BBox: [8, 131, 20, 145]

Detection 189:
  Class: object (ID: 0)
  Confidence: 0.3433
  Bounding Box: [1772.80, 436.80, 1856.00, 548.80]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [143, 39, 148, 46]

Detection 190:
  Class: object (ID: 0)
  Confidence: 0.3416
  Bounding Box: [212.20, 640.00, 323.00, 758.40]
  Mask Area: 50 pixels
  Mask Ratio: 0.0018
  Mask BBox: [21, 54, 28, 63]

Detection 191:
  Class: object (ID: 0)
  Confidence: 0.3411
  Bounding Box: [1372.80, 1809.60, 1580.80, 2008.00]
  Mask Area: 137 pixels
  Mask Ratio: 0.0049
  Mask BBox: [112, 146, 127, 158]

Detection 192:
  Class: object (ID: 0)
  Confidence: 0.3403
  Bounding Box: [0.70, 1625.60, 68.30, 1795.20]
  Mask Area: 49 pixels
  Mask Ratio: 0.0017
  Mask BBox: [5, 131, 8, 144]

Detection 193:
  Class: object (ID: 0)
  Confidence: 0.3391
  Bounding Box: [916.00, 192.00, 1178.40, 425.60]
  Mask Area: 216 pixels
  Mask Ratio: 0.0077
  Mask BBox: [76, 19, 96, 37]

Detection 194:
  Class: object (ID: 0)
  Confidence: 0.3386
  Bounding Box: [1686.40, 897.60, 1990.40, 1142.40]
  Mask Area: 267 pixels
  Mask Ratio: 0.0095
  Mask BBox: [136, 75, 159, 93]

Detection 195:
  Class: object (ID: 0)
  Confidence: 0.3354
  Bounding Box: [963.20, 615.20, 1092.80, 743.20]
  Mask Area: 59 pixels
  Mask Ratio: 0.0021
  Mask BBox: [80, 53, 88, 62]

Detection 196:
  Class: object (ID: 0)
  Confidence: 0.3347
  Bounding Box: [0.00, 0.00, 153.00, 248.20]
  Mask Area: 164 pixels
  Mask Ratio: 0.0058
  Mask BBox: [4, 4, 15, 23]

Detection 197:
  Class: object (ID: 0)
  Confidence: 0.3342
  Bounding Box: [1987.20, 1427.20, 2041.60, 1539.20]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [160, 116, 163, 123]

Detection 198:
  Class: object (ID: 0)
  Confidence: 0.3342
  Bounding Box: [728.40, 0.00, 851.20, 72.40]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [61, 4, 70, 9]

Detection 199:
  Class: object (ID: 0)
  Confidence: 0.3333
  Bounding Box: [1207.20, 1274.40, 1413.60, 1530.40]
  Mask Area: 210 pixels
  Mask Ratio: 0.0074
  Mask BBox: [99, 104, 114, 123]

Detection 200:
  Class: object (ID: 0)
  Confidence: 0.3328
  Bounding Box: [657.60, 433.20, 955.20, 731.60]
  Mask Area: 392 pixels
  Mask Ratio: 0.0139
  Mask BBox: [56, 38, 78, 61]

Detection 201:
  Class: object (ID: 0)
  Confidence: 0.3320
  Bounding Box: [546.00, 502.40, 788.00, 731.20]
  Mask Area: 271 pixels
  Mask Ratio: 0.0096
  Mask BBox: [47, 44, 65, 61]

Detection 202:
  Class: object (ID: 0)
  Confidence: 0.3311
  Bounding Box: [754.80, 1656.00, 889.60, 1825.60]
  Mask Area: 92 pixels
  Mask Ratio: 0.0033
  Mask BBox: [63, 134, 72, 146]

Detection 203:
  Class: object (ID: 0)
  Confidence: 0.3281
  Bounding Box: [311.60, 1277.60, 438.80, 1418.40]
  Mask Area: 83 pixels
  Mask Ratio: 0.0029
  Mask BBox: [29, 104, 38, 114]

Detection 204:
  Class: object (ID: 0)
  Confidence: 0.3276
  Bounding Box: [779.20, 1792.00, 889.60, 1923.20]
  Mask Area: 74 pixels
  Mask Ratio: 0.0026
  Mask BBox: [65, 144, 73, 154]

Detection 205:
  Class: object (ID: 0)
  Confidence: 0.3264
  Bounding Box: [855.20, 320.00, 981.60, 471.20]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [71, 29, 78, 38]

Detection 206:
  Class: object (ID: 0)
  Confidence: 0.3247
  Bounding Box: [1921.60, 1218.40, 2043.20, 1461.60]
  Mask Area: 132 pixels
  Mask Ratio: 0.0047
  Mask BBox: [155, 100, 163, 117]

Detection 207:
  Class: object (ID: 0)
  Confidence: 0.3242
  Bounding Box: [804.80, 1800.00, 915.20, 1924.80]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [67, 145, 75, 154]

Detection 208:
  Class: object (ID: 0)
  Confidence: 0.3186
  Bounding Box: [533.60, 766.80, 728.00, 926.40]
  Mask Area: 121 pixels
  Mask Ratio: 0.0043
  Mask BBox: [46, 64, 60, 76]

Detection 209:
  Class: object (ID: 0)
  Confidence: 0.3186
  Bounding Box: [314.80, 1902.40, 498.80, 2040.00]
  Mask Area: 118 pixels
  Mask Ratio: 0.0042
  Mask BBox: [29, 153, 42, 163]

Detection 210:
  Class: object (ID: 0)
  Confidence: 0.3179
  Bounding Box: [1568.00, 0.00, 1731.20, 157.80]
  Mask Area: 140 pixels
  Mask Ratio: 0.0050
  Mask BBox: [127, 2, 139, 16]

Detection 211:
  Class: object (ID: 0)
  Confidence: 0.3174
  Bounding Box: [1356.80, 1170.40, 1425.60, 1260.00]
  Mask Area: 27 pixels
  Mask Ratio: 0.0010
  Mask BBox: [110, 96, 114, 102]

Detection 212:
  Class: object (ID: 0)
  Confidence: 0.3167
  Bounding Box: [1241.60, 936.00, 1387.20, 1099.20]
  Mask Area: 111 pixels
  Mask Ratio: 0.0039
  Mask BBox: [101, 78, 112, 89]

Detection 213:
  Class: object (ID: 0)
  Confidence: 0.3149
  Bounding Box: [111.30, 585.20, 224.80, 686.80]
  Mask Area: 63 pixels
  Mask Ratio: 0.0022
  Mask BBox: [13, 50, 21, 57]

Detection 214:
  Class: object (ID: 0)
  Confidence: 0.3140
  Bounding Box: [378.00, 405.60, 470.80, 549.60]
  Mask Area: 65 pixels
  Mask Ratio: 0.0023
  Mask BBox: [34, 36, 40, 46]

Detection 215:
  Class: object (ID: 0)
  Confidence: 0.3137
  Bounding Box: [1460.80, 168.60, 1636.80, 333.00]
  Mask Area: 100 pixels
  Mask Ratio: 0.0035
  Mask BBox: [119, 18, 131, 30]

Detection 216:
  Class: object (ID: 0)
  Confidence: 0.3120
  Bounding Box: [866.40, 1793.60, 994.40, 1912.00]
  Mask Area: 87 pixels
  Mask Ratio: 0.0031
  Mask BBox: [72, 145, 81, 153]

Detection 217:
  Class: object (ID: 0)
  Confidence: 0.3113
  Bounding Box: [922.40, 504.00, 1090.40, 740.00]
  Mask Area: 155 pixels
  Mask Ratio: 0.0055
  Mask BBox: [77, 44, 89, 61]

Detection 218:
  Class: object (ID: 0)
  Confidence: 0.3096
  Bounding Box: [1840.00, 837.60, 1913.60, 916.00]
  Mask Area: 28 pixels
  Mask Ratio: 0.0010
  Mask BBox: [148, 70, 153, 75]

Detection 219:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [1406.40, 1564.00, 1496.00, 1641.60]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [114, 127, 120, 132]

Detection 220:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [1432.00, 1564.00, 1521.60, 1641.60]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [116, 127, 122, 132]

Detection 221:
  Class: object (ID: 0)
  Confidence: 0.3093
  Bounding Box: [1406.40, 1590.40, 1496.00, 1667.20]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [114, 129, 120, 134]

Detection 222:
  Class: object (ID: 0)
  Confidence: 0.3086
  Bounding Box: [1864.00, 0.00, 2008.00, 55.60]
  Mask Area: 37 pixels
  Mask Ratio: 0.0013
  Mask BBox: [150, 4, 160, 7]

Detection 223:
  Class: object (ID: 0)
  Confidence: 0.3066
  Bounding Box: [932.80, 1136.80, 1118.40, 1357.60]
  Mask Area: 220 pixels
  Mask Ratio: 0.0078
  Mask BBox: [77, 93, 91, 110]

Detection 224:
  Class: object (ID: 0)
  Confidence: 0.3049
  Bounding Box: [83.30, 1469.60, 364.00, 1739.20]
  Mask Area: 266 pixels
  Mask Ratio: 0.0094
  Mask BBox: [11, 119, 32, 139]

Detection 225:
  Class: object (ID: 0)
  Confidence: 0.3040
  Bounding Box: [1342.40, 0.00, 1488.00, 120.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [109, 4, 120, 11]

Detection 226:
  Class: object (ID: 0)
  Confidence: 0.3032
  Bounding Box: [1181.60, 881.60, 1322.40, 1060.80]
  Mask Area: 126 pixels
  Mask Ratio: 0.0045
  Mask BBox: [97, 73, 107, 86]

Detection 227:
  Class: object (ID: 0)
  Confidence: 0.3018
  Bounding Box: [0.00, 538.80, 104.60, 747.60]
  Mask Area: 119 pixels
  Mask Ratio: 0.0042
  Mask BBox: [4, 47, 12, 62]

Detection 228:
  Class: object (ID: 0)
  Confidence: 0.3015
  Bounding Box: [1025.60, 1969.60, 1123.20, 2024.00]
  Mask Area: 34 pixels
  Mask Ratio: 0.0012
  Mask BBox: [85, 158, 91, 162]

Detection 229:
  Class: object (ID: 0)
  Confidence: 0.3013
  Bounding Box: [1235.20, 1276.00, 1352.00, 1412.00]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [101, 104, 109, 114]

Detection 230:
  Class: object (ID: 0)
  Confidence: 0.3005
  Bounding Box: [783.20, 68.40, 844.00, 161.80]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [66, 10, 69, 16]

Detection 231:
  Class: object (ID: 0)
  Confidence: 0.2983
  Bounding Box: [140.40, 571.60, 248.40, 691.60]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [15, 49, 23, 58]

Detection 232:
  Class: object (ID: 0)
  Confidence: 0.2976
  Bounding Box: [904.80, 279.40, 1096.80, 516.80]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [75, 26, 89, 44]

Detection 233:
  Class: object (ID: 0)
  Confidence: 0.2971
  Bounding Box: [167.60, 1516.80, 364.00, 1721.60]
  Mask Area: 186 pixels
  Mask Ratio: 0.0066
  Mask BBox: [18, 123, 32, 138]

Detection 234:
  Class: object (ID: 0)
  Confidence: 0.2969
  Bounding Box: [706.80, 0.00, 815.20, 63.20]
  Mask Area: 38 pixels
  Mask Ratio: 0.0013
  Mask BBox: [60, 4, 67, 8]

Detection 235:
  Class: object (ID: 0)
  Confidence: 0.2959
  Bounding Box: [1572.80, 910.40, 1688.00, 1001.60]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [127, 76, 134, 82]

Detection 236:
  Class: object (ID: 0)
  Confidence: 0.2917
  Bounding Box: [1768.00, 718.80, 1873.60, 876.80]
  Mask Area: 89 pixels
  Mask Ratio: 0.0032
  Mask BBox: [143, 61, 150, 72]

Detection 237:
  Class: object (ID: 0)
  Confidence: 0.2910
  Bounding Box: [1028.80, 1324.00, 1097.60, 1396.00]
  Mask Area: 20 pixels
  Mask Ratio: 0.0007
  Mask BBox: [85, 108, 89, 112]

Detection 238:
  Class: object (ID: 0)
  Confidence: 0.2903
  Bounding Box: [281.60, 1399.20, 458.40, 1501.60]
  Mask Area: 81 pixels
  Mask Ratio: 0.0029
  Mask BBox: [26, 114, 39, 121]

Detection 239:
  Class: object (ID: 0)
  Confidence: 0.2900
  Bounding Box: [322.80, 1688.00, 489.20, 1928.00]
  Mask Area: 146 pixels
  Mask Ratio: 0.0052
  Mask BBox: [30, 136, 42, 154]

Detection 240:
  Class: object (ID: 0)
  Confidence: 0.2881
  Bounding Box: [659.20, 435.20, 727.20, 516.00]
  Mask Area: 31 pixels
  Mask Ratio: 0.0011
  Mask BBox: [56, 38, 60, 44]

Detection 241:
  Class: object (ID: 0)
  Confidence: 0.2871
  Bounding Box: [140.20, 610.00, 299.00, 740.40]
  Mask Area: 93 pixels
  Mask Ratio: 0.0033
  Mask BBox: [15, 52, 27, 61]

Detection 242:
  Class: object (ID: 0)
  Confidence: 0.2866
  Bounding Box: [552.00, 1600.00, 629.60, 1670.40]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [48, 129, 53, 134]

Detection 243:
  Class: object (ID: 0)
  Confidence: 0.2861
  Bounding Box: [1540.80, 1391.20, 1707.20, 1504.80]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [125, 113, 137, 121]

Detection 244:
  Class: object (ID: 0)
  Confidence: 0.2856
  Bounding Box: [731.60, 1963.20, 857.60, 2043.20]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [62, 158, 70, 163]

Detection 245:
  Class: object (ID: 0)
  Confidence: 0.2825
  Bounding Box: [318.00, 378.80, 458.00, 494.00]
  Mask Area: 77 pixels
  Mask Ratio: 0.0027
  Mask BBox: [29, 34, 39, 42]

Detection 246:
  Class: object (ID: 0)
  Confidence: 0.2815
  Bounding Box: [1101.60, 1996.80, 1181.60, 2048.00]
  Mask Area: 22 pixels
  Mask Ratio: 0.0008
  Mask BBox: [91, 160, 96, 163]

Detection 247:
  Class: object (ID: 0)
  Confidence: 0.2815
  Bounding Box: [1127.20, 1996.80, 1207.20, 2048.00]
  Mask Area: 16 pixels
  Mask Ratio: 0.0006
  Mask BBox: [93, 160, 97, 163]

Detection 248:
  Class: object (ID: 0)
  Confidence: 0.2812
  Bounding Box: [1378.40, 1028.00, 1625.60, 1247.20]
  Mask Area: 177 pixels
  Mask Ratio: 0.0063
  Mask BBox: [112, 85, 130, 101]

Detection 249:
  Class: object (ID: 0)
  Confidence: 0.2808
  Bounding Box: [1596.80, 0.00, 1740.80, 115.40]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [129, 4, 139, 13]

Detection 250:
  Class: object (ID: 0)
  Confidence: 0.2798
  Bounding Box: [1883.20, 988.80, 2036.80, 1158.40]
  Mask Area: 116 pixels
  Mask Ratio: 0.0041
  Mask BBox: [152, 82, 163, 94]

Detection 251:
  Class: object (ID: 0)
  Confidence: 0.2795
  Bounding Box: [223.20, 600.00, 432.00, 774.40]
  Mask Area: 154 pixels
  Mask Ratio: 0.0055
  Mask BBox: [22, 51, 37, 64]

Detection 252:
  Class: object (ID: 0)
  Confidence: 0.2776
  Bounding Box: [1985.60, 1397.60, 2036.80, 1498.40]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [160, 114, 163, 121]

Detection 253:
  Class: object (ID: 0)
  Confidence: 0.2776
  Bounding Box: [2011.20, 1397.60, 2048.00, 1498.40]
  Mask Area: 18 pixels
  Mask Ratio: 0.0006
  Mask BBox: [162, 114, 164, 121]

Detection 254:
  Class: object (ID: 0)
  Confidence: 0.2776
  Bounding Box: [2011.20, 1423.20, 2048.00, 1524.00]
  Mask Area: 15 pixels
  Mask Ratio: 0.0005
  Mask BBox: [162, 116, 164, 122]

Detection 255:
  Class: object (ID: 0)
  Confidence: 0.2776
  Bounding Box: [1856.00, 433.60, 2000.00, 564.00]
  Mask Area: 108 pixels
  Mask Ratio: 0.0038
  Mask BBox: [149, 38, 160, 48]

Detection 256:
  Class: object (ID: 0)
  Confidence: 0.2756
  Bounding Box: [1143.20, 1777.60, 1256.80, 2024.00]
  Mask Area: 113 pixels
  Mask Ratio: 0.0040
  Mask BBox: [95, 143, 102, 161]

Detection 257:
  Class: object (ID: 0)
  Confidence: 0.2744
  Bounding Box: [1498.40, 118.80, 1617.60, 321.60]
  Mask Area: 64 pixels
  Mask Ratio: 0.0023
  Mask BBox: [124, 15, 130, 28]

Detection 258:
  Class: object (ID: 0)
  Confidence: 0.2739
  Bounding Box: [554.00, 391.20, 698.80, 579.20]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [48, 35, 58, 48]

Detection 259:
  Class: object (ID: 0)
  Confidence: 0.2732
  Bounding Box: [1436.00, 1308.00, 1570.40, 1418.40]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [117, 107, 126, 114]

Detection 260:
  Class: object (ID: 0)
  Confidence: 0.2732
  Bounding Box: [1897.60, 1348.80, 2048.00, 1444.80]
  Mask Area: 66 pixels
  Mask Ratio: 0.0023
  Mask BBox: [153, 110, 164, 116]

Detection 261:
  Class: object (ID: 0)
  Confidence: 0.2732
  Bounding Box: [1779.20, 468.40, 1856.00, 578.00]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [143, 41, 148, 49]

Detection 262:
  Class: object (ID: 0)
  Confidence: 0.2720
  Bounding Box: [814.40, 4.50, 934.40, 91.60]
  Mask Area: 41 pixels
  Mask Ratio: 0.0015
  Mask BBox: [68, 5, 76, 11]

Detection 263:
  Class: object (ID: 0)
  Confidence: 0.2717
  Bounding Box: [1285.60, 1549.60, 1388.00, 1676.80]
  Mask Area: 56 pixels
  Mask Ratio: 0.0020
  Mask BBox: [105, 126, 112, 134]

Detection 264:
  Class: object (ID: 0)
  Confidence: 0.2708
  Bounding Box: [1606.40, 869.60, 1897.60, 1092.00]
  Mask Area: 301 pixels
  Mask Ratio: 0.0107
  Mask BBox: [130, 72, 152, 89]

Detection 265:
  Class: object (ID: 0)
  Confidence: 0.2698
  Bounding Box: [1656.00, 1380.80, 1832.00, 1580.80]
  Mask Area: 153 pixels
  Mask Ratio: 0.0054
  Mask BBox: [134, 112, 147, 127]

Detection 266:
  Class: object (ID: 0)
  Confidence: 0.2693
  Bounding Box: [9.15, 1363.20, 78.00, 1515.20]
  Mask Area: 48 pixels
  Mask Ratio: 0.0017
  Mask BBox: [5, 111, 10, 122]

Detection 267:
  Class: object (ID: 0)
  Confidence: 0.2690
  Bounding Box: [834.40, 1793.60, 949.60, 1915.20]
  Mask Area: 78 pixels
  Mask Ratio: 0.0028
  Mask BBox: [70, 145, 78, 153]

Detection 268:
  Class: object (ID: 0)
  Confidence: 0.2683
  Bounding Box: [1542.40, 239.60, 1740.80, 460.40]
  Mask Area: 167 pixels
  Mask Ratio: 0.0059
  Mask BBox: [125, 23, 139, 39]

Detection 269:
  Class: object (ID: 0)
  Confidence: 0.2673
  Bounding Box: [1841.60, 802.40, 1924.80, 911.20]
  Mask Area: 54 pixels
  Mask Ratio: 0.0019
  Mask BBox: [148, 67, 154, 75]

Detection 270:
  Class: object (ID: 0)
  Confidence: 0.2673
  Bounding Box: [1296.80, 0.00, 1412.00, 124.40]
  Mask Area: 67 pixels
  Mask Ratio: 0.0024
  Mask BBox: [106, 4, 114, 13]

Detection 271:
  Class: object (ID: 0)
  Confidence: 0.2666
  Bounding Box: [1564.80, 1769.60, 1750.40, 2009.60]
  Mask Area: 103 pixels
  Mask Ratio: 0.0036
  Mask BBox: [127, 143, 140, 160]

Detection 272:
  Class: object (ID: 0)
  Confidence: 0.2664
  Bounding Box: [1463.20, 1040.00, 1580.00, 1142.40]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [119, 87, 127, 93]

Detection 273:
  Class: object (ID: 0)
  Confidence: 0.2644
  Bounding Box: [1319.20, 956.00, 1434.40, 1124.00]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [108, 79, 113, 90]

Detection 274:
  Class: object (ID: 0)
  Confidence: 0.2622
  Bounding Box: [1824.00, 8.25, 2048.00, 164.80]
  Mask Area: 215 pixels
  Mask Ratio: 0.0076
  Mask BBox: [147, 5, 164, 16]

Detection 275:
  Class: object (ID: 0)
  Confidence: 0.2610
  Bounding Box: [1529.60, 1920.00, 1673.60, 2019.20]
  Mask Area: 60 pixels
  Mask Ratio: 0.0021
  Mask BBox: [124, 154, 134, 161]

Detection 276:
  Class: object (ID: 0)
  Confidence: 0.2607
  Bounding Box: [1533.60, 3.75, 1616.00, 108.80]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [124, 5, 130, 12]

Detection 277:
  Class: object (ID: 0)
  Confidence: 0.2607
  Bounding Box: [0.00, 1121.60, 84.10, 1278.40]
  Mask Area: 75 pixels
  Mask Ratio: 0.0027
  Mask BBox: [4, 92, 10, 103]

Detection 278:
  Class: object (ID: 0)
  Confidence: 0.2605
  Bounding Box: [1388.00, 1976.00, 1522.40, 2036.80]
  Mask Area: 47 pixels
  Mask Ratio: 0.0017
  Mask BBox: [113, 159, 122, 163]

Detection 279:
  Class: object (ID: 0)
  Confidence: 0.2605
  Bounding Box: [830.40, 1787.20, 1009.60, 1944.00]
  Mask Area: 127 pixels
  Mask Ratio: 0.0045
  Mask BBox: [69, 144, 82, 155]

Detection 280:
  Class: object (ID: 0)
  Confidence: 0.2603
  Bounding Box: [1353.60, 0.00, 1473.60, 85.60]
  Mask Area: 43 pixels
  Mask Ratio: 0.0015
  Mask BBox: [110, 4, 119, 9]

Detection 281:
  Class: object (ID: 0)
  Confidence: 0.2598
  Bounding Box: [1707.20, 1982.40, 1822.40, 2033.60]
  Mask Area: 36 pixels
  Mask Ratio: 0.0013
  Mask BBox: [138, 159, 146, 162]

Detection 282:
  Class: object (ID: 0)
  Confidence: 0.2595
  Bounding Box: [1308.80, 1509.60, 1486.40, 1632.00]
  Mask Area: 72 pixels
  Mask Ratio: 0.0026
  Mask BBox: [107, 122, 120, 131]

Detection 283:
  Class: object (ID: 0)
  Confidence: 0.2590
  Bounding Box: [334.60, 1675.20, 470.40, 1860.80]
  Mask Area: 71 pixels
  Mask Ratio: 0.0025
  Mask BBox: [31, 136, 40, 149]

Detection 284:
  Class: object (ID: 0)
  Confidence: 0.2583
  Bounding Box: [1862.40, 474.80, 1980.80, 580.40]
  Mask Area: 68 pixels
  Mask Ratio: 0.0024
  Mask BBox: [150, 42, 158, 49]

Detection 285:
  Class: object (ID: 0)
  Confidence: 0.2583
  Bounding Box: [1119.20, 1984.00, 1226.40, 2044.80]
  Mask Area: 30 pixels
  Mask Ratio: 0.0011
  Mask BBox: [92, 159, 99, 163]

Detection 286:
  Class: object (ID: 0)
  Confidence: 0.2566
  Bounding Box: [48.55, 1360.00, 235.00, 1572.80]
  Mask Area: 165 pixels
  Mask Ratio: 0.0058
  Mask BBox: [8, 111, 22, 126]

Detection 287:
  Class: object (ID: 0)
  Confidence: 0.2563
  Bounding Box: [1696.00, 0.00, 1820.80, 127.50]
  Mask Area: 95 pixels
  Mask Ratio: 0.0034
  Mask BBox: [137, 4, 146, 13]

Detection 288:
  Class: object (ID: 0)
  Confidence: 0.2559
  Bounding Box: [1330.40, 1520.80, 1508.00, 1688.00]
  Mask Area: 148 pixels
  Mask Ratio: 0.0052
  Mask BBox: [108, 123, 121, 135]

Detection 289:
  Class: object (ID: 0)
  Confidence: 0.2549
  Bounding Box: [1448.00, 540.00, 1640.00, 760.80]
  Mask Area: 162 pixels
  Mask Ratio: 0.0057
  Mask BBox: [118, 47, 132, 63]

Detection 290:
  Class: object (ID: 0)
  Confidence: 0.2546
  Bounding Box: [122.80, 527.60, 278.00, 704.40]
  Mask Area: 139 pixels
  Mask Ratio: 0.0049
  Mask BBox: [14, 46, 25, 59]

Detection 291:
  Class: object (ID: 0)
  Confidence: 0.2534
  Bounding Box: [93.20, 769.60, 279.60, 1035.20]
  Mask Area: 210 pixels
  Mask Ratio: 0.0074
  Mask BBox: [12, 65, 25, 84]

Detection 292:
  Class: object (ID: 0)
  Confidence: 0.2532
  Bounding Box: [428.40, 1920.00, 526.00, 2016.00]
  Mask Area: 45 pixels
  Mask Ratio: 0.0016
  Mask BBox: [38, 154, 45, 161]

Detection 293:
  Class: object (ID: 0)
  Confidence: 0.2532
  Bounding Box: [454.00, 1920.00, 551.60, 2016.00]
  Mask Area: 35 pixels
  Mask Ratio: 0.0012
  Mask BBox: [40, 154, 45, 161]

Detection 294:
  Class: object (ID: 0)
  Confidence: 0.2532
  Bounding Box: [428.40, 1945.60, 526.00, 2041.60]
  Mask Area: 44 pixels
  Mask Ratio: 0.0016
  Mask BBox: [38, 156, 44, 162]

Detection 295:
  Class: object (ID: 0)
  Confidence: 0.2532
  Bounding Box: [454.00, 1945.60, 551.60, 2041.60]
  Mask Area: 32 pixels
  Mask Ratio: 0.0011
  Mask BBox: [40, 156, 44, 162]

Detection 296:
  Class: object (ID: 0)
  Confidence: 0.2527
  Bounding Box: [1739.20, 758.80, 1816.00, 844.80]
  Mask Area: 23 pixels
  Mask Ratio: 0.0008
  Mask BBox: [140, 64, 145, 68]

Detection 297:
  Class: object (ID: 0)
  Confidence: 0.2522
  Bounding Box: [0.00, 0.00, 131.50, 178.80]
  Mask Area: 134 pixels
  Mask Ratio: 0.0047
  Mask BBox: [3, 3, 14, 17]

Detection 298:
  Class: object (ID: 0)
  Confidence: 0.2512
  Bounding Box: [1458.40, 935.20, 1576.80, 1069.60]
  Mask Area: 55 pixels
  Mask Ratio: 0.0019
  Mask BBox: [118, 78, 127, 87]

Detection 299:
  Class: object (ID: 0)
  Confidence: 0.2512
  Bounding Box: [1562.40, 1908.80, 1697.60, 2014.40]
  Mask Area: 62 pixels
  Mask Ratio: 0.0022
  Mask BBox: [127, 154, 136, 161]

Detection 300:
  Class: object (ID: 0)
  Confidence: 0.2512
  Bounding Box: [1243.20, 1468.00, 1499.20, 1604.80]
  Mask Area: 174 pixels
  Mask Ratio: 0.0062
  Mask BBox: [102, 119, 120, 129]

